import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  NewOperator,
  OperatorParametersView,
  OperatorView,
  OperatorViewBase,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export async function getOperators() {
  const url = `${getSettings().administrationApiUrl}/operators`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorViewBase[];
}

export async function getOperator(id: string) {
  const url = `${getSettings().administrationApiUrl}/operators/${id}`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorView;
}

export async function createOperator(operator: NewOperator) {
  const url = `${getSettings().administrationApiUrl}/operators`;
  return await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(operator),
  });
}

export async function updateOperator(id: string, operator: NewOperator) {
  const url = `${getSettings().administrationApiUrl}/operators/${id}`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(operator),
  });
}

export async function deleteOperator(id: string) {
  const url = `${getSettings().administrationApiUrl}/operators/${id}`;
  return await commonFetch(url, {
    method: 'DELETE',
    credentials: 'include',
  });
}

export async function getOperatorParameters() {
  const url = `${getSettings().administrationApiUrl}/operatorGroups/parameters`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorParametersView;
}
