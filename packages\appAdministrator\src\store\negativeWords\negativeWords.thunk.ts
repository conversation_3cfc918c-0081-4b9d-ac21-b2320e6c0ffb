import { createAsyncThunk } from '@reduxjs/toolkit';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import {
  getCurrentOperatorFullNameString,
  getCurrentOperatorUsernameString,
} from '@monorepo/common/src/managers/currentOperatorManager';

import {
  AddNegativeWord,
  NegativeWord,
  UpdateNegativeWord,
} from '../../@types/generated/administration';
import { getSettings } from '../../helpers/appSettings';

export const getAllNegativeWords = createAsyncThunk('negativeWords/all', async () => {
  const response = await commonFetch(`${getSettings().administrationApiUrl}/negative-words`, {
    credentials: 'include',
  });

  return (await response.json()) as NegativeWord[];
});

export const createNegativeWord = createAsyncThunk(
  'negativeWords/create',
  async (
    word: Omit<AddNegativeWord, 'code' | 'comment' | 'addedBy' | 'addedByFio'>,
    { dispatch },
  ) => {
    await commonFetch(`${getSettings().administrationApiUrl}/negative-words`, {
      method: 'POST',
      credentials: 'include',
      body: JSON.stringify({
        ...word,
        code: word.name,
        comment: '',
        addedBy: getCurrentOperatorUsernameString(),
        addedByFio: getCurrentOperatorFullNameString(),
      } satisfies AddNegativeWord),
      headers: {
        'Content-Type': 'application/json',
      },
    });
    dispatch(getAllNegativeWords());
  },
);

export const updateNegativeWord = createAsyncThunk(
  'negativeWords/update',
  async (
    word: Omit<UpdateNegativeWord, 'code' | 'comment' | 'addedBy' | 'addedByFio'> & {
      id: NegativeWord['id'];
    },
    { dispatch },
  ) => {
    await commonFetch(`${getSettings().administrationApiUrl}/negative-words/${word.id}`, {
      method: 'PUT',
      credentials: 'include',
      body: JSON.stringify({
        ...word,
        code: word.name,
        comment: '',
        addedBy: getCurrentOperatorUsernameString(),
        addedByFio: getCurrentOperatorFullNameString(),
      } satisfies UpdateNegativeWord),
      headers: {
        'Content-Type': 'application/json',
      },
    });
    dispatch(getAllNegativeWords());
  },
);

export const deleteNegativeWord = createAsyncThunk(
  'negativeWords/delete',
  async (id: NegativeWord['id'], { dispatch }) => {
    if (!id) return;

    await commonFetch(
      `${getSettings().administrationApiUrl}/negative-words/${id}?deletedBy=${getCurrentOperatorUsernameString()}`,
      {
        method: 'DELETE',
        credentials: 'include',
      },
    );
    dispatch(getAllNegativeWords());
  },
);
