import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IAutoTemplatesStore } from './autoTemplates.slice';
import { getAllAutoFolderTemplates, selectAutoTemplate } from './autoTemplates.thunk';

const getFolderTemplatesReducers = (builder: ActionReducerMapBuilder<IAutoTemplatesStore>) =>
  builder
    .addCase(getAllAutoFolderTemplates.pending, (state) => {
      state.loading = true;
      state.error = undefined;
      state.selectedTemplate = null;
    })
    .addCase(getAllAutoFolderTemplates.fulfilled, (state, action) => {
      state.folders = action.payload;
      state.loading = false;
    })
    .addCase(getAllAutoFolderTemplates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

const getTemplatesReducers = (builder: ActionReducerMapBuilder<IAutoTemplatesStore>) =>
  builder.addCase(selectAutoTemplate.fulfilled, (state, action) => {
    state.selectedTemplate = action.payload;
  });

export default (builder: ActionReducerMapBuilder<IAutoTemplatesStore>) => {
  getFolderTemplatesReducers(builder);
  getTemplatesReducers(builder);

  return builder;
};
