import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import {
  AttentionIcon,
  AttentionIconSize,
  AttentionIconType,
  Button,
  ButtonVariant,
  Colors,
  Loader,
  LoaderSize,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { deleteChatAsync } from '../../../../../services/internalChat.service';

interface IDeleteModalProps extends HTMLAttributes<HTMLDivElement> {
  chatId: string;
  closeCallback: () => void;
}

const DeleteModal: React.FC<IDeleteModalProps> = ({ chatId, closeCallback }) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [err, setErr] = React.useState<Error>();

  const handleConfirm = async () => {
    try {
      setErr(undefined);
      setIsLoading(true);
      await deleteChatAsync(chatId);
      closeCallback();
    } catch (e) {
      setErr(e);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className={clsx(utils.textCenter, utils.p6)}>
        {err ? (
          <AlertError error={err} title={getLocaleMessageById('app.error.deleteChat')} />
        ) : (
          <>
            <AttentionIcon type={AttentionIconType.Attention} size={AttentionIconSize.Big} />
            <Text
              variant={TextVariant.SubheadMedium}
              as="div"
              className={clsx(utils.mT6, utils.mB1)}
            >
              {getLocaleMessageById('app.deleteChat.header')}
            </Text>
            <Text variant={TextVariant.CaptionMedium} color={Colors.OnyxBlack70} as="div">
              {getLocaleMessageById(isLoading ? 'app.deleteChat.progress' : 'app.deleteChat.text')}
            </Text>
          </>
        )}
      </div>
      <div className={utils.pX6}>
        <Button
          onClick={handleConfirm}
          disabled={isLoading}
          className={clsx(utils.dBlock, utils.w100, utils.mB2)}
        >
          {isLoading ? (
            <Loader size={LoaderSize.Auto} />
          ) : (
            getLocaleMessageById('app.button.delete')
          )}
        </Button>
        <Button
          onClick={closeCallback}
          className={clsx(utils.dBlock, utils.w100)}
          variant={ButtonVariant.Transparent}
          disabled={isLoading}
        >
          {getLocaleMessageById('app.button.cancel')}
        </Button>
      </div>
    </div>
  );
};

export default DeleteModal;
