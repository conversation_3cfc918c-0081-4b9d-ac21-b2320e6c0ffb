import React from 'react';

import { ITextareaProps } from '@product.front/ui-kit/dist/types/components/Textarea/Textarea';
import clsx from 'clsx';

import { grids, Radio, Textarea, utils } from '@product.front/ui-kit';

import {
  AutomationServiceResponseBodyType,
  IFrontAutomationService,
  IFrontAutomationServiceInvalidReasons,
} from '../../../@types/automationService.types';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { formFieldConstraintsResource } from '../../../resources/automationService.resources';
import ParameterEditor from '../ParameterEditor';

import FieldGroupSubHeader from './FieldGroupSubHeader';
import FieldInvalidMessage from './FieldInvalidMessage';
import FieldsGroupHeader from './FieldsGroupHeader';
import FieldsGroupWrapper from './FieldsGroupWrapper';

interface IBlockResponseProps {
  automationService: IFrontAutomationService;
  onFieldValueChange: (fieldName: string) => ({ value }: { value?: any }) => void;
  disabled: boolean;
  readonly: boolean;
  invalidReasons: IFrontAutomationServiceInvalidReasons;
}

const BlockResponse: React.FC<IBlockResponseProps> = ({
  automationService,
  onFieldValueChange,
  disabled,
  readonly,
  invalidReasons,
}) => {
  const responseBodyRef = React.useRef<HTMLTextAreaElement>(null);

  React.useEffect(() => {
    if (!responseBodyRef.current || !invalidReasons.responseBody) return;
    responseBodyRef.current.focus();
  }, [responseBodyRef, invalidReasons]);

  return (
    <FieldsGroupWrapper>
      <FieldsGroupHeader>
        {getLocaleMessageById('app.automationService.response')}
      </FieldsGroupHeader>
      <div>
        <FieldGroupSubHeader>
          {getLocaleMessageById('app.automationService.responseBody')}
        </FieldGroupSubHeader>
        <div className={clsx(utils.dFlex, utils.gap4)}>
          <Radio
            value={AutomationServiceResponseBodyType.JSON}
            checked={automationService.responseBodyType === AutomationServiceResponseBodyType.JSON}
            onChange={onFieldValueChange('responseBodyType')}
            disabled={disabled}
            name="responseBodyType"
            label={getLocaleMessageById('app.automationService.responseTypeJSON')}
            readOnly={readonly}
          />
          <Radio
            value={AutomationServiceResponseBodyType.XML}
            checked={automationService.responseBodyType === AutomationServiceResponseBodyType.XML}
            onChange={onFieldValueChange('responseBodyType')}
            disabled={disabled}
            name="responseBodyType"
            label={getLocaleMessageById('app.automationService.responseTypeXML')}
            readOnly={readonly}
          />
          <Radio
            value={AutomationServiceResponseBodyType.Text}
            checked={automationService.responseBodyType === AutomationServiceResponseBodyType.Text}
            onChange={onFieldValueChange('responseBodyType')}
            disabled={disabled}
            name="responseBodyType"
            label={getLocaleMessageById('app.automationService.responseTypeTEXT')}
            readOnly={readonly}
          />
          <Radio
            value={AutomationServiceResponseBodyType.File}
            checked={automationService.responseBodyType === AutomationServiceResponseBodyType.File}
            disabled={disabled}
            onChange={onFieldValueChange('responseBodyType')}
            name="responseBodyType"
            label={getLocaleMessageById('app.automationService.responseTypeFILE')}
            readOnly={readonly}
          />
        </div>
        {automationService.responseBodyType !== AutomationServiceResponseBodyType.File && (
          <div className={clsx(utils.mT4, grids.row)}>
            <Textarea
              rows={4}
              wrapperClassName={grids.col12}
              label={getLocaleMessageById('app.automationService.responseBody')}
              disabled={disabled}
              value={automationService.responseBody}
              onChange={onFieldValueChange('responseBody')}
              {...((formFieldConstraintsResource.responseBody || {}) as ITextareaProps)}
              required={[
                AutomationServiceResponseBodyType.XML,
                AutomationServiceResponseBodyType.JSON,
              ].includes(automationService.responseBodyType)}
              ref={responseBodyRef}
              message={
                invalidReasons.responseBody ? (
                  <FieldInvalidMessage>{invalidReasons.responseBody}</FieldInvalidMessage>
                ) : undefined
              }
              readOnly={readonly}
            />
          </div>
        )}
      </div>
      {automationService.responseParameters.length > 0 && (
        <FieldGroupSubHeader>
          {getLocaleMessageById('app.automationService.responseParameters')}
        </FieldGroupSubHeader>
      )}
      {automationService.responseParameters.map((parameter) => (
        <ParameterEditor
          key={parameter.key}
          disabled={disabled}
          parameter={parameter}
          onChange={(updatedParameter) => {
            onFieldValueChange('responseParameters')({
              value: automationService.responseParameters.map((rp) =>
                rp.key === parameter.key ? updatedParameter : rp,
              ),
            });
          }}
          readonly={readonly}
        />
      ))}
    </FieldsGroupWrapper>
  );
};
export default BlockResponse;
