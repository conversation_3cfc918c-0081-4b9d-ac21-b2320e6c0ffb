import { IFrontOperatorGroup } from '../../../../../@types/operatorGroup';
import { IFrontKpiThresholdValue } from '../../../../../@types/parameters';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import {
  IValidationResult as IOperatorValidationResult,
  defaultValidationResult as defaultOperatorValidationResult,
  validateSessionSettings,
  validateStatusSettings,
} from '../../Operators/OperatorEditor/validator';

export interface IValidationResult {
  isErrored: boolean;
  main: {
    isErrored: boolean;
    name: string;
  };
  sessions: IOperatorValidationResult['sessions'];
  statuses: IOperatorValidationResult['statuses'];
  kpi: {
    isErrored: boolean;
    kpiThresholds: Record<string, string>;
  };
}

export const defaultValidationResult: IValidationResult = {
  isErrored: false,
  main: {
    isErrored: false,
    name: '',
  },
  sessions: defaultOperatorValidationResult.sessions,
  statuses: defaultOperatorValidationResult.statuses,
  kpi: {
    isErrored: false,
    kpiThresholds: {},
  },
};

const requiredMessage = getLocaleMessageById('app.common.validation.required');

export const validateOperatorGroup = (
  operatorGroup: IFrontOperatorGroup,
  kpiThresholds: IFrontKpiThresholdValue[],
) => {
  const validationResult: IValidationResult = {
    ...defaultValidationResult,
    main: { ...defaultValidationResult.main },
    sessions: { ...defaultValidationResult.sessions, channelsErrors: {} },
    statuses: { ...defaultValidationResult.statuses, statusesErrors: {} },
    kpi: { ...defaultValidationResult.kpi, kpiThresholds: {} },
  };

  if (!operatorGroup.name) {
    validationResult.isErrored = true;
    validationResult.main.isErrored = true;
    validationResult.main.name = requiredMessage;
  }

  validateSessionSettings(operatorGroup.sessionSettings, validationResult);
  validateStatusSettings(operatorGroup.statusSettings, validationResult);

  operatorGroup.kpiSettings.forEach((kpiSetting) => {
    const kpiThreshold = kpiThresholds.find((kpi) => kpi.code === kpiSetting.code);
    if (!kpiThreshold) return;

    if (
      kpiSetting.target != null &&
      (kpiSetting.target < kpiThreshold.minValue ||
        (kpiThreshold.maxValue && kpiSetting.target > kpiThreshold.maxValue))
    ) {
      validationResult.isErrored = true;
      validationResult.kpi.isErrored = true;
      validationResult.kpi.kpiThresholds[kpiSetting.code] = getLocaleMessageById(
        'app.common.validation.range',
        {
          min: kpiThreshold.minValue,
          max: kpiThreshold.maxValue ?? '∞',
        },
      );
    }
  });

  return validationResult;
};
