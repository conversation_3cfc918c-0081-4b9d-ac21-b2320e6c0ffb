import { Node } from 'reactflow';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

export enum NodeType {
  Default = 'default',
  Start = 'start',
  Finish = 'Finish',
  Input = 'input',
  Output = 'output',
  Custom = 'custom',
  Router = 'router',
  Service = 'service',
  Subscript = 'subscript',
  Zero = 'zero',
  Terminal = 'terminal',
  Rating = 'rating',
  Scenario = 'scenario',
}

export interface NodeWithStepData {
  step: IFrontStep;
  isSelected?: boolean;
}

export type ScripDialogStepNode = Node<NodeWithStepData>;
