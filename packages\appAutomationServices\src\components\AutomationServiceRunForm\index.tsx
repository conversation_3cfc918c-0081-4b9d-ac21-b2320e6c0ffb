import React, { FormEvent } from 'react';

import clsx from 'clsx';

import {
  grids,
  Input,
  Text,
  TextVariant,
  utils,
  Button,
  ButtonVariant,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import {
  ExecuteResultStatus,
  IFrontAutomationService,
  IRequestFile,
} from '../../@types/automationService.types';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import { clearResult } from '../../store/automationServices/automationServices.slice';
import { executeAutomationService } from '../../store/automationServices/automationServices.thunk';
import {
  useAutomationServicesAppDispatch,
  useAutomationServicesAppSelector,
} from '../../store/hooks';

import FilesBlock from './components/FilesBlock';
import ResponseSection from './components/ResponseSection';

interface IServiceRunFormProps {
  automationService: IFrontAutomationService;
  onSuccess: () => void;
  onFail: () => void;
  onCancel: () => void;
}

const ServiceRunForm: React.FC<IServiceRunFormProps> = ({
  automationService,
  onSuccess,
  onFail,
  onCancel,
}) => {
  const dispatch = useAutomationServicesAppDispatch();

  const { executionResult } = useAutomationServicesAppSelector((state) => state.automationServices);

  const [values, setValues] = React.useState<Record<string, string>>(
    automationService.requestParameters.reduce((acc, { key }) => ({ ...acc, [key]: '' }), {}),
  );

  const [isLoading, setIsLoading] = React.useState(false);

  const [files, setFiles] = React.useState<Record<string, IRequestFile>>({});

  React.useEffect(() => {
    if (!executionResult) return;

    if (executionResult.error || executionResult.status === ExecuteResultStatus.Fail) {
      onFail();
    } else {
      onSuccess();
    }

    setIsLoading(false);
  }, [executionResult, onSuccess, onFail]);

  React.useEffect(
    () => () => {
      dispatch(clearResult());
    },
    [dispatch],
  );

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!automationService.id) return;

    if ((e.target as HTMLFormElement).checkValidity()) {
      setIsLoading(true);
      dispatch(
        executeAutomationService({
          serviceId: automationService.id,
          params: values,
          files: Object.values(files),
        }),
      );
    }
  };

  const updateValue =
    (key: string) =>
    ({ value }: { value?: string }) =>
      setValues((cur) => ({ ...cur, [key]: value || '' }));

  return (
    <form onSubmit={handleSubmit}>
      <article className={clsx(utils.scrollbar, utils.overflowYAuto)} style={{ maxHeight: '70vh' }}>
        <section className={utils.pX5}>
          <Text
            as="h3"
            variant={TextVariant.SubheadSemibold}
            className={clsx(utils.mT5, utils.mB3)}
          >
            {getLocaleMessageById('app.automationService.requestParameters')}
          </Text>
          {automationService.requestParameters.map((pr) => {
            return (
              <div className={clsx(utils.pB3, grids.row, utils.alignItemsCenter)} key={pr.key}>
                <Text as="div" className={grids.col3} title={pr.reachDescription} noWrap ellipsis>
                  {pr.description}
                </Text>
                <Input
                  wrapperClassName={grids.col9}
                  required
                  value={values[pr.key]}
                  onChange={updateValue(pr.key)}
                  placeholder={getLocaleMessageById('app.automationService.testRunValue')}
                  disabled={isLoading}
                />
              </div>
            );
          })}
          {automationService.canUploadFiles && (
            <FilesBlock files={files} onChange={(value) => setFiles(value)} disabled={isLoading} />
          )}
        </section>
        <ResponseSection
          isLoading={isLoading}
          responseBodyType={automationService.responseBodyType}
          executionResult={executionResult}
        />
      </article>
      {executionResult?.error && (
        <AlertError
          error={executionResult.error}
          header={getLocaleMessageById('app.automationService.requestError')}
          className={utils.m4}
          style={{ minWidth: 0 }}
        />
      )}
      <footer
        className={clsx(
          utils.dFlex,
          utils.justifyContentEnd,
          utils.pX5,
          utils.pY3,
          utils.gap2,
          utils.borderTop,
        )}
      >
        <Button variant={ButtonVariant.Secondary} onClick={onCancel}>
          {getLocaleMessageById('app.automationService.cancel')}
        </Button>
        <Button type="submit" disabled={isLoading}>
          {getLocaleMessageById('app.automationService.run')}
        </Button>
      </footer>
    </form>
  );
};

export default ServiceRunForm;
