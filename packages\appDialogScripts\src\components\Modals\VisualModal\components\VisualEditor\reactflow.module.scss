/* stylelint-disable selector-class-pattern */
/* stylelint-disable selector-pseudo-class-no-unknown */
:global {
  .react-flow__node {
    font-family: inherit;
  }

  .react-flow__node-default {
    background-color: var(--palette-lagenta-30) !important;
  }

  .react-flow__node-input {
    background-color: var(--palette-grassios-30) !important;
  }

  .react-flow__node-output {
    background-color: var(--palette-amenaza-30) !important;
  }

  .react-flow__node-custom {
    border-radius: 4px;
  }

  .react-flow__node-diamond {
    position: relative;

    display: flex;
    align-items: center;
    justify-content: center;

    padding: 2em 3em;

    text-align: center;

    svg {
      position: absolute;
      z-index: -1;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      width: 100%;
      height: 100%;
    }

    path.inner {
      fill: transparent;
      stroke: orange;
    }
  }

  .react-flow__handle {
    width: 8px;
    height: 8px;
    background: var(--palette-onyxBlack-80);

    &:before {
      --width: 4px;

      content: '';

      position: absolute;
      top: calc(var(--width) * -1);
      left: calc(var(--width) * -1);

      width: 100%;
      height: 100%;
      border: var(--width) solid transparent;
      border-radius: 50%;
    }

    &:hover {
      background: var(--palette-onyxBlack-70);
    }
  }

  .react-flow__handle-left {
    left: -5px;
  }

  .react-flow__handle-right {
    right: -5px;
  }
}
