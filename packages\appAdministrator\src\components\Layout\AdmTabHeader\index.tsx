import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { utils, Text, TextVariant } from '@product.front/ui-kit';

interface IAdmTabHeaderProps extends HTMLAttributes<HTMLDivElement> {
  header: React.ReactNode;
}

const AdmTabHeader: React.FC<IAdmTabHeaderProps> = ({
  header,
  children,
  style,
  className,
  ...rest
}) => {
  return (
    <header
      className={clsx(
        'qa-admin-tab-header',
        utils.pX6,
        utils.dFlex,
        utils.gap2,
        utils.alignItemsCenter,
        className,
      )}
      style={{ ...(style ?? {}), height: 'var(--st-app-header-height)' }}
      {...rest}
    >
      {header && (
        <Text variant={TextVariant.SubheadSemibold} className={utils.mRauto}>
          {header}
        </Text>
      )}
      {children && (
        <aside
          className={clsx(
            utils.mLauto,
            utils.dFlex,
            utils.dFlex,
            utils.gap2,
            utils.alignItemsCenter,
            'qa-admin-tab-header-actions',
          )}
        >
          {children}
        </aside>
      )}
    </header>
  );
};

export default AdmTabHeader;
