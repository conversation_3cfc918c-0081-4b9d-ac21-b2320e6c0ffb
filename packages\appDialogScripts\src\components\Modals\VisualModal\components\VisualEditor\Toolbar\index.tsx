import React from 'react';

import clsx from 'clsx';

import { utils, Checkbox } from '@product.front/ui-kit';

import IconExit from '@product.front/icons/dist/icons17/MainStuff/IconExit';
import IconSettings from '@product.front/icons/dist/icons17/MainStuff/IconSettings';
import IconFlags from '@product.front/icons/dist/icons17/Smiles/IconFlags';
import IconHierarchy from '@product.front/icons/dist/icons17/Sorting/IconHierarchy';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { FrontStepType } from '@monorepo/dialog-scripts/src/@types/script';

import { AppConfigContext } from '../../../../../../context/appConfig';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import {
  useDialogScriptsAppDispatch,
  useDialogScriptsAppSelector,
} from '../../../../../../store/hooks';
import { toggleDrawAutoRelations } from '../../../../../../store/oneScript/oneScript.slice';
import { NodeType } from '../../../types/scriptDialogsVisualEditorTypes';
import IconRouter from '../IconRouter';
import IconStep from '../IconStep';

import ToolbarButton from './components/ToolbarButton';

import styles from './styles.module.scss';

import '../reactflow.module.scss';

interface IToolbar {
  children?: React.ReactNode;
}

const Toolbar: React.FC<IToolbar> = ({ children }) => {
  const onDragStart = (event: React.DragEvent<HTMLSpanElement>, nodeType: NodeType) => {
    event.stopPropagation();
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };
  const dispatch = useDialogScriptsAppDispatch();
  const { drawAutoRelations, script } = useDialogScriptsAppSelector((state) => state.oneScript);
  const { stepTypes, disableAutoRelation } = React.useContext(AppConfigContext);

  const disabled = script?.status === ScriptStatus.Archive;

  const stepTypesAddButtonsMap: Record<FrontStepType, React.ReactNode> = {
    [FrontStepType.Step]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeCustom)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconStep />}
        localeMessageId="app.editor.blockTypeStep"
        nodeType={NodeType.Custom}
      />
    ),
    [FrontStepType.Router]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeRouter)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconRouter />}
        localeMessageId="app.editor.blockTypeRouter"
        nodeType={NodeType.Router}
      />
    ),
    [FrontStepType.Service]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeService)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconSettings />}
        localeMessageId="app.editor.blockTypeService"
        nodeType={NodeType.Service}
      />
    ),
    [FrontStepType.Subscript]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeSubscript)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconHierarchy />}
        localeMessageId="app.editor.blockTypeSubscript"
        nodeType={NodeType.Subscript}
      />
    ),
    [FrontStepType.Scenario]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeScenario)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconExit />}
        localeMessageId="app.editor.blockTypeScenario"
        nodeType={NodeType.Scenario}
      />
    ),
    [FrontStepType.Terminal]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeTerminal)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconFlags />}
        localeMessageId="app.editor.blockTypeTerminal"
        nodeType={NodeType.Terminal}
      />
    ),
    [FrontStepType.Rating]: (
      <ToolbarButton
        className={clsx(styles.toolbarNode, styles.toolbarNodeRating)}
        onDragStart={onDragStart}
        disabled={disabled}
        icon={<IconStep />}
        localeMessageId="app.editor.blockTypeRating"
        nodeType={NodeType.Rating}
      />
    ),
  };

  return (
    <footer className={clsx(utils.p3)}>
      <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
        {stepTypes.map((type) => (
          <React.Fragment key={type}>{stepTypesAddButtonsMap[type]}</React.Fragment>
        ))}
        {disableAutoRelation === false && (
          <Checkbox
            checked={drawAutoRelations}
            className={utils.mL2}
            label={getLocaleMessageById('app.editor.toggleAutoRelations')}
            onChange={() => dispatch(toggleDrawAutoRelations())}
          />
        )}
      </div>
      {children}
    </footer>
  );
};
export default Toolbar;
