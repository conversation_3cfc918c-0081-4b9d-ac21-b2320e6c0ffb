import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { Loader, LoaderSize, utils } from '@product.front/ui-kit';

const FullScreenLoader: React.FC<HTMLAttributes<HTMLDivElement>> = ({ children }) => {
  return (
    <div
      style={{ height: '100vh', flexGrow: 1 }}
      className={clsx(utils.dFlex, utils.alignItemsCenter, utils.justifyContentCenter)}
    >
      <Loader size={LoaderSize.Big} title={children as string} />
    </div>
  );
};

export default FullScreenLoader;
