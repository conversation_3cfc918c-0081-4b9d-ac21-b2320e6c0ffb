import { createSlice } from '@reduxjs/toolkit';

import { IPRODUCTServices } from '@monorepo/services/src/@types/IPRODUCTServices';

import extraReducers from './filterData.extraReducers';

type IAllFilterData = Awaited<ReturnType<IPRODUCTServices['filterData']['all']>>;

export interface IFilterDataStore {
  channels: IAllFilterData['availableChannels'];
  queues: IAllFilterData['availableQueues'];
  requestStatuses: IAllFilterData['availableRequestStatuses'];
}

const initialState: IFilterDataStore = {
  channels: [],
  queues: [],
  requestStatuses: [],
};

const filterDataSlice = createSlice({
  name: 'filterData',
  initialState,
  reducers: {},
  extraReducers,
});

export default filterDataSlice.reducer;
