import React from 'react';

import { IButtonProps } from '@product.front/ui-kit/dist/types/components/Button/Button';
import clsx from 'clsx';

import {
  Button,
  Colors,
  FloatingTooltip,
  LoaderSize,
  OverlayLoader,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

interface IAdmToolbarCtaButtonProps extends IButtonProps {
  tooltip?: string;
  hotkey?: string[];
  loading?: boolean;
}

const AdmToolbarCtaButton: React.FC<IAdmToolbarCtaButtonProps> = ({
  loading = false,
  tooltip,
  children,
  hotkey,
  disabled,
  ...rest
}) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children: ch }) => {
    if (!tooltip && !hotkey) return <>{ch}</>;
    return (
      <FloatingTooltip
        tooltip={
          <>
            {tooltip}
            {hotkey && (
              <div className={utils.mT1} style={{ opacity: 0.4 }}>
                {hotkey.map((k) => (
                  <Text
                    variant={TextVariant.TinySemibold}
                    as="kbd"
                    key={k}
                    color={Colors.OnyxBlack50}
                    className={clsx(
                      utils.border,
                      utils.radius1,
                      utils.mR1,
                      utils.dInlineBlock,
                      utils.pX1,
                    )}
                  >
                    {k}
                  </Text>
                ))}
              </div>
            )}
          </>
        }
      >
        {ch}
      </FloatingTooltip>
    );
  };
  return (
    <OverlayLoader
      wrapperClassName={clsx(utils.dFlex, utils.alignItemsCenter, utils.flexBasis0, utils.mL2)}
      loading={loading}
      loaderSize={LoaderSize.Auto}
    >
      <Wrapper>
        <Button disabled={loading || disabled} {...rest}>
          {children}
        </Button>
      </Wrapper>
    </OverlayLoader>
  );
};

export default AdmToolbarCtaButton;
