import React from 'react';

import clsx from 'clsx';

import { Button, JumbotronType, showModal, utils } from '@product.front/ui-kit';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { getLocaleMessageById } from './localeHelper';

export const notifySuccessModal = (header: string) => {
  showConfirmModal({
    type: JumbotronType.Success,
    header: header,
    canCancel: false,
  });
};

export const notifyErrorModal = (header: string, error: Error) => {
  showModal({
    canClose: false,
    children: (close) => (
      <div className={clsx(utils.flexCentredBlock, utils.positionRelative)}>
        <JumbotronError header={header} error={error} style={{ minWidth: 320, maxWidth: 420 }}>
          <Button onClick={close} className={utils.mT5}>
            {getLocaleMessageById('app.common.OK')}
          </Button>
        </JumbotronError>
      </div>
    ),
  });
};
