import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IMailingsStore } from './mailings.slice';
import {
  getAllMailings,
  getAvailableChannels,
  getMailingsMetrics,
  getQueuesForMailings,
  getSenders,
} from './mailings.thunk';

const getAllMailingsReducers = (builder: ActionReducerMapBuilder<IMailingsStore>) =>
  builder
    // get all mailings
    .addCase(getAllMailings.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllMailings.fulfilled, (state, action) => {
      state.loading = false;
      state.periodicMailings = action.payload.periodicMailings ?? [];
      state.thresholdMailings = action.payload.thresholdMailings ?? [];
    })
    .addCase(getAllMailings.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    })
    // get metrics
    .addCase(getMailingsMetrics.pending, (state) => {
      state.metricsLoading = true;
      state.metricsError = '';
    })
    .addCase(getMailingsMetrics.fulfilled, (state, action) => {
      state.metricsLoading = false;
      state.metrics = action.payload;
    })
    .addCase(getMailingsMetrics.rejected, (state, action) => {
      state.metricsLoading = false;
      state.metricsError = action.error.message ?? '';
    })
    // get queues
    .addCase(getQueuesForMailings.pending, (state) => {
      state.queuesLoading = true;
      state.queuesError = undefined;
    })
    .addCase(getQueuesForMailings.fulfilled, (state, action) => {
      state.queuesLoading = false;
      state.queues = action.payload;
    })
    .addCase(getQueuesForMailings.rejected, (state, action) => {
      state.queuesLoading = false;
      state.queuesError = action.error;
    })
    // get channels
    .addCase(getAvailableChannels.pending, (state) => {
      state.channelsLoading = true;
      state.channelsError = undefined;
    })
    .addCase(getAvailableChannels.fulfilled, (state, action) => {
      state.channelsLoading = false;
      state.channels = action.payload;
    })
    .addCase(getAvailableChannels.rejected, (state, action) => {
      state.channelsLoading = false;
      state.channelsError = action.error;
    });

const getSendersReducers = (builder: ActionReducerMapBuilder<IMailingsStore>) => {
  builder
    .addCase(getSenders.pending, (state) => {
      state.sendersLoading = true;
      state.sendersError = undefined;
    })
    .addCase(getSenders.fulfilled, (state, action) => {
      state.senders = action.payload;
      state.sendersLoading = false;
    })
    .addCase(getSenders.rejected, (state, action) => {
      state.sendersLoading = false;
      state.sendersError = action.error;
    });
};

export default (builder: ActionReducerMapBuilder<IMailingsStore>) => {
  getAllMailingsReducers(builder);
  getSendersReducers(builder);

  return builder;
};
