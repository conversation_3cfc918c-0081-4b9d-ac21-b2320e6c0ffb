import React from 'react';

import { ITreeViewItem } from '@product.front/ui-kit/dist/types/components/TreeView/TreeView';
import clsx from 'clsx';

import {
  MenuItem,
  TreeView,
  utils,
  Text,
  FolderIcon,
  FolderIconSize,
  FolderIconState,
} from '@product.front/ui-kit';

import { IFrontFolder } from '../../../../@types/files';
import { getFolderByPath, isFolder } from '../../../../helpers/files';
import { selectDisplayedFolder, setSelectedItem } from '../../../../store/files/files.slice';
import { getFoldersAndFiles } from '../../../../store/files/files.thunk';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';

import { handleDropOnFolder } from './dnd.helper';

import styles from './styles.module.scss';

const FilesTreeView: React.FC = () => {
  const dispatch = useAdministratorAppDispatch();

  const { foldersAndFiles, displayedFolder } = useAdministratorAppSelector((store) => store.files);

  const mapSampleData = (data: IFrontFolder): ITreeViewItem => {
    return {
      key: data.path,
      isOpen: displayedFolder && data.path.includes(displayedFolder.path),
      component: ({ isOpen, level, children }) => (
        <MenuItem
          style={{ paddingLeft: 16 + level * 16, opacity: children?.length ? 1 : 0.9 }}
          className={clsx(utils.gap2, utils.pX3)}
          onClick={() => {
            dispatch(selectDisplayedFolder(data));
            data.parent && dispatch(setSelectedItem(data));
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            (e.currentTarget as HTMLButtonElement).classList.add(styles.dragHover);
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            (e.currentTarget as HTMLButtonElement).classList.remove(styles.dragHover);
          }}
          onDrop={handleDropOnFolder(data, () => {
            dispatch(getFoldersAndFiles());
          })}
          active={displayedFolder && data.path === displayedFolder.path}
        >
          <FolderIcon
            state={data.items.length ? FolderIconState.WithFile : FolderIconState.Empty}
            size={FolderIconSize.Small}
            style={{ opacity: isOpen ? 1 : 0.9 }}
            className={utils.flexShrink0}
          />
          <Text ellipsis>{data.name}</Text>
        </MenuItem>
      ),
      children: data.items?.length ? data.items.filter(isFolder).map(mapSampleData) : undefined,
    };
  };

  if (!foldersAndFiles.length) return null;

  const treeSample: ITreeViewItem[] = foldersAndFiles.filter(isFolder).map(mapSampleData);

  return (
    <div
      className={clsx(utils.overflowYAuto, utils.scrollbar, utils.pY2)}
      onKeyDown={(e) => {
        if (displayedFolder && e.key === 'Backspace') {
          const f = getFolderByPath(foldersAndFiles, displayedFolder.parent);
          f && dispatch(selectDisplayedFolder(f));
        }
      }}
      onClick={() => {}}
      tabIndex={-1}
      role="button"
    >
      <TreeView items={treeSample} />
    </div>
  );
};

export default FilesTreeView;
