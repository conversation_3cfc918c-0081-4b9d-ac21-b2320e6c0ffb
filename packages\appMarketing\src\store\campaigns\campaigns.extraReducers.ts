import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { getLocaleMessageById } from '../../helpers/localeHelper';

import { ICampaignsStore } from './campaigns.slice';
import {
  createOrUpdateCampaign,
  deleteCampaign,
  getCampaigns,
  getClients,
  getClientsFromFile,
  getResults,
  getStatistics,
} from './campaigns.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<ICampaignsStore>) =>
  builder
    .addCase(getCampaigns.pending, (state) => {
      state.loading = true;
    })
    .addCase(getCampaigns.rejected, (state) => {
      state.loading = false;
    })
    .addCase(getCampaigns.fulfilled, (state, action) => {
      state.loading = false;
      state.campaigns = action.payload;
    })
    .addCase(createOrUpdateCampaign.pending, (state) => {
      state.saving = true;
    })
    .addCase(createOrUpdateCampaign.rejected, (state, action) => {
      console.error(action.error.message);

      const errText = getLocaleMessageById('app.campaigns.form.results.saveError');
      const errArgs = getNotificationArgsByError(errText, action.error);
      getPlatformPopupNotificationManager().notifyError(...errArgs);

      state.saving = false;
    })
    .addCase(createOrUpdateCampaign.fulfilled, (state, action) => {
      getPlatformPopupNotificationManager().notifySuccess(
        getLocaleMessageById('app.campaigns.form.results.saveSuccess'),
      );
      state.saving = false;
      state.campaignInEdit = { ...action.payload };
      state.selectedCampaign = { ...action.payload };
    })
    .addCase(deleteCampaign.fulfilled, (state) => {
      getPlatformPopupNotificationManager().notifySuccess(
        getLocaleMessageById('app.campaigns.form.results.deleteSuccess'),
      );

      state.selectedCampaign = null;
    })
    .addCase(deleteCampaign.rejected, (_state, action) => {
      const errText = getLocaleMessageById('app.campaigns.form.results.deleteError');
      const errArgs = getNotificationArgsByError(errText, action.error);
      getPlatformPopupNotificationManager().notifyError(...errArgs);

      console.error('deleteCampaign.rejected', action.error.message);
    })
    .addCase(getResults.pending, (state) => {
      state.resultsLoading = true;
      state.resultsError = undefined;
      state.campaignResults = [];
    })
    .addCase(getResults.rejected, (state, action) => {
      state.resultsLoading = false;
      state.resultsError = action.error;
    })
    .addCase(getResults.fulfilled, (state, action) => {
      state.resultsLoading = false;
      state.campaignResults = action.payload;
    })
    .addCase(getClientsFromFile.pending, (state) => {
      state.clientsLoading = true;
      state.clientsError = undefined;
      state.clients = [];
    })
    .addCase(getClientsFromFile.rejected, (state, action) => {
      state.clientsLoading = false;
      state.clientsError = action.error;
    })
    .addCase(getClientsFromFile.fulfilled, (state, action) => {
      state.clientsLoading = false;
      state.clients = action.payload.value;
    })
    .addCase(getClients.pending, (state) => {
      state.clientsLoading = true;
      state.clientsError = undefined;
      state.clients = [];
    })
    .addCase(getClients.rejected, (state, action) => {
      state.clientsLoading = false;
      state.clientsError = action.error;
    })
    .addCase(getClients.fulfilled, (state, action) => {
      state.clientsLoading = false;
      state.clients = action.payload.value;
    })
    .addCase(getStatistics.pending, (state) => {
      state.statisticsLoading = true;
      state.statisticsError = undefined;
      state.statistics = null;
    })
    .addCase(getStatistics.rejected, (state, action) => {
      state.statisticsLoading = false;
      state.statisticsError = action.error;
    })
    .addCase(getStatistics.fulfilled, (state, action) => {
      state.statisticsLoading = false;
      state.statistics = action.payload;
    });

export default extraReducers;
