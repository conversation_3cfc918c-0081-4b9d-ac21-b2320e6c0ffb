{"app.info.pageTitle": "Personal Area", "app.info.tabMyKPI": "KPI", "app.info.tabMyQueues": "My Queues", "app.info.tabMyRequests": "My Requests", "app.info.tabMyEvaluations": "My Evaluations", "app.info.onLine": "on line", "app.info.inWork": "in work", "app.info.inTalk": "in conversation", "app.info.hour": "h", "app.info.min": "m", "app.info.sec": "s", "app.info.myKpi.processed": "processed", "app.info.myKpi.closed": "closed", "app.info.myKpi.missed": "missed", "app.info.myKpi.aht": "AHT", "app.info.myKpi.acsi": "ACSI", "app.info.myKpi.assi": "ASSI", "app.info.myKpi.response": "response", "app.info.myKpi.title.processed": "Processed", "app.info.myKpi.title.closed": "Closed", "app.info.myKpi.title.missed": "Missed", "app.info.myKpi.title.aht": "Average processing time", "app.info.myKpi.title.acsi": "Average assessment of the quality of work set by the client", "app.info.myKpi.title.assi": "Average assessment of the quality of work, set by a supervisor", "app.info.myKpi.title.response": "Response", "app.info.myKpi.table.queue": "Queue", "app.info.myKpi.table.operatorsAwaiting": "Expecting processing", "app.info.myKpi.table.activeOperators": "Active operators", "app.info.myKpi.table.asa": "ASA", "app.info.myKpi.table.aht": "AHT", "app.info.myRequests.showHistory": "Show messages history", "app.info.myRequests.takeToWork": "Take to work", "app.info.myRequests.emptyHeader": "No requests were found", "app.info.myRequests.emptyText": "Specify parameters of request", "app.common.minutes.short": "m", "app.common.seconds.short": "s", "app.info.myRequests.table.id": "ID", "app.info.myRequests.table.queue": "Queue", "app.info.myRequests.table.channel": "Channel", "app.info.myRequests.table.theme": "Theme", "app.info.myRequests.table.type": "Request type", "app.info.myRequests.table.liveTime": "Request life time", "app.info.myRequests.table.queueTime": "In queue time", "app.info.myRequests.table.clientId": "Client ID", "app.info.myRequests.table.clientFio": "Full name of the client", "app.info.myRequests.table.sender": "Sender", "app.info.myRequests.table.recipient": "Recipient", "app.info.myRequests.table.priority": "Priority", "app.info.myRequests.table.repeated": "Repeated", "app.info.myRequests.table.registrationTime": "Registration time", "app.info.myRequests.table.lastDistributionTime": "Last distribution time", "app.info.myRequests.table.status": "Status", "app.info.myRequests.table.postponedUntil": "Delayed", "app.info.myRequests.table.lastChangedByFio": "Last changed by", "app.info.myRequests.table.sa": "SA", "app.info.myRequests.table.ht": "HT", "app.info.myRequests.table.wt": "WT", "app.info.myRequests.table.1rt": "1RT", "app.info.myRequests.table.acw": "ACW", "app.info.myRequests.table.csi": "CSI", "app.info.myRequests.table.ssi": "SSI", "app.info.myRequests.table.incomingMessagesNumber": "In.messages", "app.info.myRequests.table.outgoingMessagesNumber": "Out.messages", "app.info.myRequests.table.attachmentsNumber": "Attachments", "app.info.myRequests.table.lost": "Lost", "app.info.myRequests.table.clientType": "Client type", "app.info.myRequests.table.answerUntil": "Reply until", "app.info.myRequests.table.processedByFio": "Processed", "app.info.myRequests.table.timeInStatus": "Time in status", "app.common.in": "in", "app.info.requests.common.yes": "Yes", "app.info.requests.common.no": "No", "app.info.requests.type.outgoing": "Outgoing", "app.info.requests.type.incoming": "Incoming", "app.common.close": "Close", "app.messagesModal.title": "#{requestId} - Chat history", "app.buttons.tooltip.refresh": "Update", "app.buttons.tooltip.download": "Export to Excel", "app.myTimeline": "My working time", "app.myKpi": "My KPI", "app.queueKpi": "Queues KPI", "app.kpiForToday": "KPI for today", "app.lastUpdateTime": "Updated in", "app.kpi.workingTime": "work time", "app.kpi.workKpi": "working indexes", "app.kpi.speedKpi": "speed indexes", "app.kpi.qualityKpi": "quality indexes", "app.info.myRequests.table.title.attachmentsNumber": "Number of attachments in request", "app.table.attachments.none": "Without attachments", "app.table.attachments.has": "With attachments", "app.info.myRequests.table.externalClientId": "External ID", "app.info.myRequests.table.title.externalClientId": "Client ID in the external system", "app.info.onBreak": "in breaks", "app.common.onlyEmpty": "Only empty", "app.common.onlyNotEmpty": "Only filled", "app.common.from": "From", "app.common.to": "To", "app.common.error": "Error getting data", "app.common.updateError": "Failed to update the data, repeat the attempt"}