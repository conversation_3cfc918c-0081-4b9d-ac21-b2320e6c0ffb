import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  CanClearBehavior,
  ChannelIconSize,
  FloatingDropdown,
  grids,
  IconButton,
  Input,
  Menu,
  MenuItem,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';
import ChannelLabel from '@monorepo/common/src/components/ChannelLabel';
import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';
import { Channel } from '@monorepo/services/src/@types/generated/productApi';

import { IFrontSessionSettings } from '../../../../../@types/operator';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { hackNumberInput } from '../../../../../helpers/numberInputHack';

import { IValidationResult } from './validator';

interface ISessionSettingsProps {
  addressData: Channel[];
  sessionSettings: IFrontSessionSettings;
  onUpdateSessionSettings: (sessionSettings: IFrontSessionSettings) => void;
  validationResult: IValidationResult['sessions'];
}

const SessionsSettings = ({
  addressData,
  sessionSettings,
  onUpdateSessionSettings,
  validationResult,
}: ISessionSettingsProps) => {
  const [sessionNumber, setSessionNumber] = React.useState(sessionSettings.maxSessions);
  const [channelsMap, setChannelsMap] = React.useState<Partial<Record<UcmmChannel, number>>>(
    sessionSettings.channelSessions.reduce(
      (acc, { channel, maxSessions }) => ({
        ...acc,
        [channel]: maxSessions,
      }),
      {},
    ),
  );

  const notUsedAddressData = React.useMemo(
    () =>
      addressData.filter(
        (address) => !Object.keys(channelsMap).some((channel) => channel === address.code),
      ),
    [addressData, channelsMap],
  );

  const updateSessionSettings = React.useCallback(() => {
    const newSessionSettings = {
      maxSessions: sessionNumber,
      channelSessions: Object.entries(channelsMap).map(([channel, maxSessions]) => ({
        channel: channel as UcmmChannel,
        maxSessions,
      })),
    } satisfies IFrontSessionSettings;

    onUpdateSessionSettings(newSessionSettings);
  }, [channelsMap, onUpdateSessionSettings, sessionNumber]);

  React.useEffect(() => {
    updateSessionSettings();
  }, [updateSessionSettings]);

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4)}>
      <Input
        label={getLocaleMessageById('operators.modal.form.sessionNumber')}
        type="number"
        min={0}
        max={100}
        step={1}
        maxLength={3}
        value={sessionNumber?.toString()}
        onChange={({ value }) => setSessionNumber(value ? Number(value) : null)}
        canClearBehavior={CanClearBehavior.Value}
        isInvalid={!!validationResult?.maxSessions}
        message={<InputErrorMessage>{validationResult.maxSessions}</InputErrorMessage>}
        onInput={hackNumberInput(3)}
      />
      <div className={clsx(utils.dFlex, utils.justifyContentBetween)}>
        <Text variant={TextVariant.SubheadSemibold}>
          {getLocaleMessageById('operators.modal.form.sessionLimits')}
        </Text>
        <FloatingDropdown
          menu={
            <Menu>
              {notUsedAddressData.map((address) => (
                <MenuItem
                  key={address.code}
                  className={utils.gap2}
                  onClick={() =>
                    setChannelsMap((current) => ({
                      ...current,
                      [address.code as UcmmChannel]: sessionNumber ?? 0,
                    }))
                  }
                >
                  <ChannelLabel addresses={addressData} channel={address.code as UcmmChannel} />
                </MenuItem>
              ))}
            </Menu>
          }
        >
          <Button variant={ButtonVariant.Transparent} className={clsx(utils.gap2, utils.p0)}>
            <IconAdd />
            {getLocaleMessageById('operators.modal.button.addChannelLimit')}
          </Button>
        </FloatingDropdown>
      </div>
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
        {(Object.keys(channelsMap) as UcmmChannel[]).map((channel) => (
          <div key={channel} className={clsx(grids.row, utils.gap2, utils.alignItemsCenter)}>
            <ChannelLabel
              className={grids.col3}
              addresses={addressData}
              channel={channel}
              channelIconSize={ChannelIconSize.Medium}
            />
            <Input
              wrapperClassName={grids.col8}
              type="number"
              min={0}
              max={sessionNumber || 100}
              value={channelsMap[channel]?.toString() ?? ''}
              onChange={({ value }) =>
                setChannelsMap((current) => ({
                  ...current,
                  [channel]: Number(value) || 0,
                }))
              }
              isInvalid={!!validationResult.channelsErrors[channel]}
              message={
                <InputErrorMessage>{validationResult.channelsErrors[channel]}</InputErrorMessage>
              }
              onInput={hackNumberInput(3)}
            />
            <IconButton
              className={grids.col1}
              onClick={() =>
                setChannelsMap((current) => {
                  delete current[channel];
                  return { ...current };
                })
              }
            >
              <IconTrash />
            </IconButton>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SessionsSettings;
