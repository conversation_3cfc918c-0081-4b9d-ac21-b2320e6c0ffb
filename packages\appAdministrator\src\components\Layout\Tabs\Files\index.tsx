import React, { KeyboardEvent } from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  Input,
  Jumbotron,
  JumbotronType,
  ResizablePanel,
  ResizerPosition,
  utils,
} from '@product.front/ui-kit';

import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconSearch from '@product.front/icons/dist/icons17/MainStuff/IconSearch';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';

import { IAdmTabComponent } from '../../../../@types/components';
import { getFolderByPath, isFolder, normalizePathForApi } from '../../../../helpers/files';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  getTabAsideSize,
  setTabAsideSize,
  tabAsideSizeMax,
  tabAsideSizeMin,
} from '../../../../helpers/resize.helper';
import { downloadTrustedFile } from '../../../../services/files';
import {
  selectDisplayedFolder,
  setSearchString,
  setSelectedItem,
} from '../../../../store/files/files.slice';
import { getFoldersAndFiles } from '../../../../store/files/files.thunk';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import FilesFolderPreview from './FilesFolderPreview';
import FilesToolbar from './FilesToolbar';
import FilesTreeView from './FilesTreeView';

const FilesTab: React.FC<IAdmTabComponent> = ({ name, tab }) => {
  const dispatch = useAdministratorAppDispatch();
  const searchDebounceRef = React.useRef<ReturnType<typeof setTimeout>>(null);

  const { loading, error, foldersAndFiles, searchString, displayedFolder, selectedItem } =
    useAdministratorAppSelector((store) => store.files);

  React.useEffect(() => {
    dispatch(getFoldersAndFiles());
  }, [dispatch]);

  const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (!displayedFolder) return;
    const cur = displayedFolder.items.findIndex((item) => item.path === selectedItem?.path) ?? 0;
    if (e.key === 'ArrowRight') {
      dispatch(
        setSelectedItem(
          cur + 1 < displayedFolder.items.length
            ? displayedFolder.items[cur + 1]
            : displayedFolder.items[0],
        ),
      );
    } else if (e.key === 'ArrowLeft') {
      dispatch(
        setSelectedItem(
          cur - 1 >= 0
            ? displayedFolder.items[cur - 1]
            : displayedFolder.items[displayedFolder.items.length - 1],
        ),
      );
    } else if (selectedItem && e.key === 'Enter') {
      if (isFolder(selectedItem)) {
        dispatch(selectDisplayedFolder(selectedItem));
      } else {
        downloadTrustedFile(normalizePathForApi(selectedItem.path), selectedItem.name);
      }
    } else if (displayedFolder && e.key === 'Backspace') {
      const f = getFolderByPath(foldersAndFiles, displayedFolder.parent);
      f && dispatch(selectDisplayedFolder(f));
    }
  };

  return (
    <AdmTabWrapper
      className="qa-administration-files-tab"
      onKeyDown={handleKeyDown}
      tabIndex={0}
      style={{ outline: 'none' }}
    >
      <AdmTabHeader header={name}>
        <Input
          wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1, utils.mR4)}
          placeholder={getLocaleMessageById('files.list.search')}
          type="search"
          canClearBehavior={CanClearBehavior.Value}
          value={searchString}
          onChange={({ value }) => {
            dispatch(setSearchString(value || ''));
            searchDebounceRef.current && clearTimeout(searchDebounceRef.current);
            searchDebounceRef.current = setTimeout(() => dispatch(getFoldersAndFiles()), 700);
          }}
          preContent={<IconSearch className={clsx(utils.mL2)} />}
        />
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('files.action.reload')}
          onClick={() => dispatch(getFoldersAndFiles())}
        >
          <IconRefresh />
        </AdmToolbarIconButton>

        <FilesToolbar />
      </AdmTabHeader>
      <AdmTabBody noPadding flexRow loading={loading}>
        <ResizablePanel
          resizerPosition={ResizerPosition.Right}
          min={tabAsideSizeMin}
          max={tabAsideSizeMax}
          onResize={setTabAsideSize(tab)}
          size={getTabAsideSize(tab)}
          className={utils.flexShrink0}
        >
          <div
            className={clsx(
              utils.h100,
              utils.dFlex,
              utils.flexColumn,
              utils.scrollbar,
              utils.overflowAuto,
            )}
          >
            {!error && !loading && foldersAndFiles.length === 0 && (
              <Jumbotron
                type={JumbotronType.Info}
                header={getLocaleMessageById('files.list.empty')}
              />
            )}
            {error ? (
              <JumbotronError error={error} header={getLocaleMessageById('files.list.error')} />
            ) : (
              <FilesTreeView />
            )}
          </div>
        </ResizablePanel>
        <div
          className={clsx(
            utils.flexBasis0,
            utils.flexGrow1,
            utils.dFlex,
            utils.alignItemsCenter,
            utils.justifyContentCenter,
          )}
        >
          <FilesFolderPreview />
        </div>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default FilesTab;
