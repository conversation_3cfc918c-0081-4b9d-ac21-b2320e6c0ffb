{"app.info.pageTitle": "Личный кабинет", "app.info.tabMyKPI": "KPI", "app.info.tabMyQueues": "Мои очереди", "app.info.tabMyRequests": "Мои обращения", "app.info.tabMyEvaluations": "Мои оценки", "app.info.onLine": "на линии", "app.info.inWork": "в работе", "app.info.inTalk": "в разговоре", "app.info.hour": "ч", "app.info.min": "м", "app.info.sec": "с", "app.info.myKpi.processed": "обработано", "app.info.myKpi.closed": "закрыто", "app.info.myKpi.missed": "пропущено", "app.info.myKpi.aht": "AHT", "app.info.myKpi.acsi": "ACSI", "app.info.myKpi.assi": "ASSI", "app.info.myKpi.response": "отклик", "app.info.myKpi.title.processed": "Обработано", "app.info.myKpi.title.closed": "Закрыто", "app.info.myKpi.title.missed": "Пропущено", "app.info.myKpi.title.aht": "Среднее время обработки обращения", "app.info.myKpi.title.acsi": "Средняя оценка качества работы, выставленная контактным лицом", "app.info.myKpi.title.assi": "Средняя оценка качества работы, выставленная супервизором", "app.info.myKpi.title.response": "Отклик", "app.info.myKpi.table.queue": "Очередь", "app.info.myKpi.table.operatorsAwaiting": "Ожидают обработки", "app.info.myKpi.table.activeOperators": "Активных операторов", "app.info.myKpi.table.asa": "ASA", "app.info.myKpi.table.aht": "AHT", "app.info.myRequests.showHistory": "Показать историю сообщений", "app.info.myRequests.takeToWork": "Взять в работу", "app.info.myRequests.emptyHeader": "Обращений не найдено", "app.info.myRequests.emptyText": "Уточните параметры запроса", "app.common.minutes.short": "м", "app.common.seconds.short": "с", "app.info.myRequests.table.id": "ID", "app.info.myRequests.table.queue": "Очередь", "app.info.myRequests.table.channel": "<PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.theme": "Тема", "app.info.myRequests.table.type": "Тип обращения", "app.info.myRequests.table.liveTime": "Время жизни обращения", "app.info.myRequests.table.queueTime": "Время в очереди", "app.info.myRequests.table.clientId": "ID контактного лица", "app.info.myRequests.table.clientFio": "ФИО контактного лица", "app.info.myRequests.table.sender": "Отправитель", "app.info.myRequests.table.recipient": "Получатель", "app.info.myRequests.table.priority": "Приоритет", "app.info.myRequests.table.repeated": "Повторное", "app.info.myRequests.table.registrationTime": "Регистрация", "app.info.myRequests.table.lastDistributionTime": "Последнее распределение", "app.info.myRequests.table.status": "Статус", "app.info.myRequests.table.postponedUntil": "Отложено до", "app.info.myRequests.table.lastChangedByFio": "Кем изменен", "app.info.myRequests.table.sa": "SA", "app.info.myRequests.table.ht": "HT", "app.info.myRequests.table.wt": "WT", "app.info.myRequests.table.1rt": "1RT", "app.info.myRequests.table.acw": "ACW", "app.info.myRequests.table.csi": "CSI", "app.info.myRequests.table.ssi": "SSI", "app.info.myRequests.table.incomingMessagesNumber": "Вх. сообщений", "app.info.myRequests.table.outgoingMessagesNumber": "Исх. сообщений", "app.info.myRequests.table.attachmentsNumber": "Вложения", "app.info.myRequests.table.lost": "Потеряно", "app.info.myRequests.table.clientType": "Тип контактного лица", "app.info.myRequests.table.answerUntil": "Ответить до", "app.info.myRequests.table.processedByFio": "Обработано", "app.info.myRequests.table.timeInStatus": "Время в статусе", "app.common.in": "в", "app.info.requests.common.yes": "Да", "app.info.requests.common.no": "Нет", "app.info.requests.type.outgoing": "Исходящее", "app.info.requests.type.incoming": "Входящее", "app.common.close": "Закрыть", "app.messagesModal.title": "#{requestId} - История переписки", "app.buttons.tooltip.refresh": "Обновить", "app.buttons.tooltip.download": "Выгрузить в excel", "app.myTimeline": "Моё рабочее время", "app.myKpi": "Мои KPI", "app.queueKpi": "KPI очередей", "app.kpiForToday": "Показатели KPI за сегодня", "app.lastUpdateTime": "Обновлено в", "app.kpi.workingTime": "рабочая смена", "app.kpi.workKpi": "показатели работы", "app.kpi.speedKpi": "показатели скорости", "app.kpi.qualityKpi": "показатели качества", "app.info.myRequests.table.title.attachmentsNumber": "Количество вложений в обращении", "app.table.attachments.none": "Без вложений", "app.table.attachments.has": "С вложениями", "app.info.myRequests.table.externalClientId": "Внешний ID", "app.info.myRequests.table.title.externalClientId": "Идентифика<PERSON>ор контактного лица во внешней системе", "app.info.onBreak": "в перерывах", "app.common.onlyEmpty": "Только пустые", "app.common.onlyNotEmpty": "Только заполненные", "app.common.from": "С", "app.common.to": "По", "app.common.error": "Ошибка получения данных", "app.common.updateError": "Не удалось обновить данные, повторите попытку"}