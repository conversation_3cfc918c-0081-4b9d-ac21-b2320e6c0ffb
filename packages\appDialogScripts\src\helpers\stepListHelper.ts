import { FrontStepType, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from './localeHelper';

type OneOfFrontStepType = {
  [P in FrontStepType]?: number;
};
let nonameCounter: OneOfFrontStepType = {};
let nonameCache: Record<IFrontStep['code'], string> = {};

export const initStepNames = () => {
  nonameCounter = {};
  nonameCache = {};
};

const stepDefaultNameResource: Record<FrontStepType, string> = {
  [FrontStepType.Step]: getLocaleMessageById('app.editor.blockTypeStep'),
  [FrontStepType.Router]: getLocaleMessageById('app.editor.blockTypeRouter'),
  [FrontStepType.Service]: getLocaleMessageById('app.editor.blockTypeService'),
  [FrontStepType.Subscript]: getLocaleMessageById('app.editor.blockTypeSubscript'),
  [FrontStepType.Scenario]: getLocaleMessageById('app.editor.blockTypeScenario'),
  [FrontStepType.Terminal]: getLocaleMessageById('app.editor.blockTypeTerminal'),
  [FrontStepType.Rating]: getLocaleMessageById('app.editor.blockTypeRating'),
};
export const getNoNameNameForStep = (step: IFrontStep, withNonamePostfix = true): string => {
  if (nonameCache[step.code]) {
    return nonameCache[step.code];
  }
  if (!step.name && !step.subscript?.name) {
    if (!nonameCounter[step.type]) nonameCounter[step.type] = 0;
    nonameCounter[step.type]!++;
  }
  const name =
    stepDefaultNameResource[step.type] + (withNonamePostfix ? ` ${nonameCounter[step.type]}` : '');

  nonameCache[step.code] = name;
  return name;
};
