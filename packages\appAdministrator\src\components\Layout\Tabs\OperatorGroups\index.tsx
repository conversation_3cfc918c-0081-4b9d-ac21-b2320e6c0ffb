import React from 'react';

import { ITreeViewItem } from '@product.front/ui-kit/dist/types/components/TreeView/TreeView';
import { ColumnDef } from '@tanstack/react-table';
import clsx from 'clsx';

import {
  Loader,
  MenuItem,
  ResizablePanel,
  ResizerPosition,
  showModal,
  Table,
  TreeView,
  utils,
} from '@product.front/ui-kit';

import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';
import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';
import IconUserGroup from '@product.front/icons/dist/icons17/Person&Doc/IconUserGroup';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import InfoMessage from '@monorepo/common/src/components/Table/InfoMessage';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IAdmTabComponent } from '../../../../@types/components';
import { IFrontOperatorGroup, IFrontOperatorGroupBase } from '../../../../@types/operatorGroup';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  getTabAsideSize,
  setTabAsideSize,
  tabAsideSizeMax,
  tabAsideSizeMin,
} from '../../../../helpers/resize.helper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import {
  deleteOperatorGroup,
  getAllOperatorGroups,
  getOperatorGroup,
  saveOperatorGroup,
} from '../../../../store/operatorGroups/operatorGroups.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import OperatorGroupEditor from './OperatorGroupEditor';

const OperatorGroupsTab: React.FC<IAdmTabComponent> = ({ name, tab }) => {
  const dispatch = useAdministratorAppDispatch();

  const {
    error,
    loading,
    selectedLoading,
    selectedError,
    operatorGroupsMap,
    selectedOperatorGroup,
  } = useAdministratorAppSelector((store) => store.operatorGroups);

  const mapDataToTree = React.useCallback(
    (data: IFrontOperatorGroupBase): ITreeViewItem => {
      return {
        key: data.id,
        isOpen: !data.parentId,
        component: ({ isOpen, level, children }) => (
          <MenuItem
            style={{ paddingLeft: level * 20 + 20 }}
            className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}
            onClick={(event) => {
              if ((event.target as HTMLDivElement).tagName !== 'BUTTON') return;

              event.stopPropagation();
              dispatch(getOperatorGroup(data.id));
            }}
            active={data.id === selectedOperatorGroup?.id}
          >
            {children?.length ? (
              <IconDropRight style={{ rotate: isOpen ? '90deg' : '0deg' }} />
            ) : (
              <IconUserGroup />
            )}
            &nbsp;
            {data.name}
          </MenuItem>
        ),
        children: data.childOperatorGroupsIds.length
          ? data.childOperatorGroupsIds.map((key) => mapDataToTree(operatorGroupsMap[key]))
          : undefined,
      };
    },
    [dispatch, operatorGroupsMap, selectedOperatorGroup?.id],
  );

  const openOperatorGroupEditor = (group: IFrontOperatorGroup | null) => {
    showModal({
      header: getLocaleMessageById(
        group ? 'operatorGroups.modal.header.edit' : 'operatorGroups.modal.header.create',
        {
          operatorGroupName: group?.name,
        },
      ),
      children: (close) => (
        <OperatorGroupEditor
          operatorGroup={group}
          operatorGroupsMap={operatorGroupsMap}
          onSubmit={async (operatorGroup) => {
            await dispatch(saveOperatorGroup(operatorGroup)).unwrap();
          }}
          onClose={close}
        />
      ),
      flushBody: true,
      canClose: false,
    });
  };

  const updateOperatorGroups = React.useCallback(() => {
    dispatch(getAllOperatorGroups());
  }, [dispatch]);

  React.useEffect(() => {
    updateOperatorGroups();
  }, [updateOperatorGroups]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name} className={utils.borderBottom}>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.refresh')}
          onClick={updateOperatorGroups}
        >
          <IconRefresh />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.delete')}
          disabled={!selectedOperatorGroup}
          onClick={() =>
            selectedOperatorGroup &&
            showConfirmModal({
              header: getLocaleMessageById('operatorGroups.delete.confirm', {
                operatorGroupName: selectedOperatorGroup.name,
              }),
              onConfirm: () => dispatch(deleteOperatorGroup(selectedOperatorGroup.id)),
            })
          }
        >
          <IconTrash />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.edit')}
          disabled={!selectedOperatorGroup}
          onClick={() => openOperatorGroupEditor(selectedOperatorGroup)}
        >
          <IconEdit />
        </AdmToolbarIconButton>
        <AdmToolbarCtaButton onClick={() => openOperatorGroupEditor(null)}>
          {getLocaleMessageById('app.common.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody loading={loading} noPadding flexRow>
        <ResizablePanel
          resizerPosition={ResizerPosition.Right}
          min={tabAsideSizeMin}
          max={tabAsideSizeMax}
          onResize={setTabAsideSize(tab)}
          size={getTabAsideSize(tab)}
          className={clsx(utils.flexShrink0)}
        >
          <div
            className={clsx(
              utils.dFlex,
              utils.flexColumn,
              utils.overflowAuto,
              utils.scrollbar,
              utils.h100,
              utils.w100,
            )}
          >
            {error ? (
              <JumbotronError header={getLocaleMessageById('operatorGroups.error')} error={error} />
            ) : (
              <TreeView
                items={Object.keys(operatorGroupsMap)
                  .filter((key) => !operatorGroupsMap[key].parentId)
                  .map((key) => mapDataToTree(operatorGroupsMap[key]))}
              />
            )}
          </div>
        </ResizablePanel>

        <div
          className={clsx(
            utils.w100,
            utils.overflowAuto,
            utils.scrollbar,
            utils.dFlex,
            utils.flexColumn,
            utils.p4,
          )}
        >
          {selectedError && <AlertError error={selectedError} />}
          {selectedLoading && (
            <div className={clsx(utils.flexCentredBlock, utils.p6)}>
              <Loader />
            </div>
          )}
          {!selectedError && !selectedLoading && (
            <>
              {selectedOperatorGroup ? (
                <Table<Required<IFrontOperatorGroup['operators']>[number]>
                  data={selectedOperatorGroup?.operators ?? []}
                  columns={
                    [
                      {
                        accessorKey: 'fullName',
                        accessorFn: (row) =>
                          [row.lastName, row.firstName, row.middleName].filter(Boolean).join(' '),
                        header: getLocaleMessageById('operatorGroups.table.operatorName'),
                        maxSize: 300,
                        enableResizing: true,
                        enableSorting: true,
                      },
                      {
                        accessorKey: 'login',
                        header: getLocaleMessageById('operatorGroups.table.operatorLogin'),
                        maxSize: 300,
                        enableResizing: true,
                        enableSorting: true,
                      },
                      {
                        accessorKey: 'groups',
                        accessorFn: (row) => row.groups.map((group) => group.name).join(', '),
                        header: getLocaleMessageById('operatorGroups.table.groups'),
                        enableResizing: true,
                        enableSorting: true,
                      },
                    ] as ColumnDef<Required<IFrontOperatorGroup['operators']>[number]>[]
                  }
                  rendererAfter={() =>
                    (selectedOperatorGroup?.operators ?? []).length === 0 && (
                      <InfoMessage
                        header={getLocaleMessageById('operatorGroups.operators.empty')}
                      />
                    )
                  }
                />
              ) : (
                <InfoMessage header={getLocaleMessageById('operatorGroups.notSelected')} />
              )}
            </>
          )}
        </div>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default OperatorGroupsTab;
