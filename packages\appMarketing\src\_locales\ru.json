{"app.marketing.pageTitle": "Марк<PERSON><PERSON><PERSON>нг", "app.marketing.tabCampaigns": "Кампании", "app.marketing.tabOffers": "Предложения", "app.table.offers.header.name": "Название предложения", "app.table.offers.header.type": "Тип предложения", "app.table.offers.header.from": "Начало", "app.table.offers.header.to": "Завершение", "app.table.offers.header.campaigns": "Кампании", "app.table.offers.header.changedBy": "Кем изменено", "app.table.offers.header.changedDate": "Изменено", "app.offers.buttons.add.tooltip": "Создать предложение", "app.offers.buttons.copy.tooltip": "Копировать предложение", "app.offers.buttons.delete.tooltip": "Удалить предложение", "app.offer.notSelected": "Выберите предложение или создайте новое", "app.offer.form.save": "Сохранить", "app.offer.form.cancel": "Отменить", "app.common.required": "Поле обязательно для заполнения", "app.offer.form.input.name": "Название*", "app.offer.form.input.description": "Описание*", "app.offer.form.input.externalReference": "Описание (URL)", "app.offer.form.input.dateFrom": "Дата начала*", "app.offer.form.input.dateTo": "Дата окончания*", "app.offer.form.input.type": "Тип предложения*", "app.offer.form.input.textType.textarea": "Текст", "app.offer.form.input.textType.wysiwyg": "HTML", "app.offer.form.input.text": "Текст предложения*", "app.offer.form.input.automaticOfferValue": "Шаблон*", "app.offer.form.input.automaticOfferValuePreview": "Текст шаблона", "app.offer.form.input.script": "Скрипт для оператора*", "app.offer.form.input.availableForOperator": "Возможность подключения оператором", "app.offer.form.input.connectionType.manual": "Подключение вручную", "app.offer.form.input.connectionType.auto": "Подключение автоматически", "app.offer.form.input.parameters": "Параметры подключения (описание, ссылка, скрипт)*", "app.offers.notFound": "По заданным параметрам ничего не найдено", "app.offers.error.noCascade": "Предложение связано с кампаниями, каскадное удаление невозможно", "app.campaigns.create": "Создать", "app.campaigns.sort.dateFromAsc": "По дате начала (по возрастанию)", "app.campaigns.sort.dateFromDesc": "По дате начала (по убыванию)", "app.campaigns.sort.dateToAsc": "По дате окончания (по возрастанию)", "app.campaigns.sort.dateToDesc": "По дате окончания (по убыванию)", "app.campaigns.sort.priorityAsc": "Сначала низкий приоритет", "app.campaigns.sort.priorityDesc": "Сначала высокий приоритет", "app.campaigns.type.incoming": "Входящие", "app.campaigns.type.outgoing": "Исходящие", "app.campaigns.displayStatus.new": "Новые", "app.campaigns.displayStatus.planned": "Запланированные", "app.campaigns.displayStatus.active": "Активные", "app.campaigns.displayStatus.paused": "Приостановленные", "app.campaigns.displayStatus.finished": "Завершенные", "app.campaigns.displayStatus.all": "Все", "app.campaigns.notFound": "Нет доступных кампаний", "app.campaigns.card.accomplishment": "выполнение", "app.campaigns.card.feedback": "отклик", "app.campaigns.form.back": "Назад", "app.campaigns.form.closeConfirmHeader": "Подтвердите действие", "app.campaigns.form.closeConfirmText": "Вы уверены, что хотите покинуть страницу? Данные кампании не будут сохранены.", "app.campaigns.form.deleteHeader": "Подтвердите удаление", "app.campaigns.form.deleteText": "Вы действительно хотите удалить кампанию \"{name}\"?", "app.campaign.form.campaignActive": "Кампания активна", "app.campaigns.form.save": "Сохранить", "app.campaigns.form.cancel": "Отмена", "app.campaigns.form.tabs.config": "Настройки", "app.campaigns.form.tabs.clients": "Контактные лица", "app.campaigns.form.label.name": "Название*", "app.campaigns.form.label.description": "Описание", "app.campaigns.form.label.type": "Тип кампании", "app.campaigns.form.label.typeIncoming": "Входящая", "app.campaigns.form.label.typeOutgoing": "Исходящая", "app.campaigns.form.label.isInformational": "Информационная рассылка", "app.campaigns.form.label.priority": "Приоритет*", "app.campaigns.form.label.availableForBot": "Может предлагать бот", "app.campaigns.form.label.dateFrom": "Дата начала*", "app.campaigns.form.label.dateTo": "Дата окончания*", "app.campaigns.form.label.channels": "Каналы*", "app.campaigns.form.label.channelsSelectAll": "Все каналы", "app.campaigns.form.label.channel": "<PERSON>анал*", "app.campaigns.form.label.queue": "Очередь кампании*", "app.campaigns.form.label.startTime": "Часы работы с*", "app.campaigns.form.label.stopTime": "Часы работы по*", "app.campaigns.form.label.notIgnoreClientTimeZone": "Учитывать часовой пояс", "app.campaigns.form.label.responseWaitingTime": "Время ожидания (сек)", "app.campaigns.form.label.offer": "Предложение*", "app.campaigns.form.offerPreview.header": "Параметры предложения", "app.campaigns.form.offerPreview.notSelected": "Выберите предложение", "app.campaigns.form.offerPreview.name": "Название:", "app.campaigns.form.offerPreview.dateFrom": "Дата начала:", "app.campaigns.form.offerPreview.dateTo": "Дата окончания:", "app.campaigns.form.offerPreview.text": "Текст предложения:", "app.campaigns.form.offerPreview.availableForOperator": "Подключение оператором:", "app.campaigns.form.offerPreview.connectionType": "Способ подключения:", "app.campaigns.form.offerPreview.connectionTypeAuto": "Автоматически", "app.campaigns.form.offerPreview.connectionTypeManual": "Вручн<PERSON>ю", "app.common.yes": "Да", "app.common.no": "Нет", "app.campaigns.form.addOffer.button": "Создать", "app.campaigns.form.addOffer.header": "Создать предложение", "app.campaigns.form.inactive.header": "Подтвердите сохранение", "app.campaigns.form.inactive.text": "Кампания неактивна. Продолжить?", "app.campaigns.form.clients.manualFilter": "Задать фильтр", "app.campaigns.form.clients.fileFilter": "Загрузить из файла", "app.campaigns.form.clients.filterParameters.button": "Настроить фильтр", "app.campaigns.form.clients.filterParameters.header": "Параметры фильтра", "app.campaigns.form.clients.filterParameters.empty.title": "Фильтры не заданы", "app.campaigns.form.clients.filterParameters.empty.text": "Будет использована вся база контактных лиц", "app.campaigns.form.clients.clientsList.button": "Выбрать файл", "app.campaigns.form.clients.clientsList.header": "Список контактных лиц", "app.campaigns.form.clients.clientsList.empty": "Ни один файл не выбран", "app.campaigns.form.clients.clientsList.emptyFile": "Контактные лица в файле не найдены", "app.campaigns.form.tabs.statistics": "Статистика", "app.campaigns.form.tabs.results": "Результаты", "app.campaign.results.header": "Результаты кампании", "app.campaign.results.export": "Выгрузить", "app.campaign.results.table.header.contactDate": "Дата контакта", "app.campaign.results.table.header.status": "Статус предложения", "app.campaign.results.table.header.result": "Результат", "app.campaign.results.table.header.rejectReason": "Причина отказа", "app.campaign.results.table.header.fio": "ФИО", "app.campaign.results.table.header.comment": "Комментарий", "app.campaign.resultOfferStatus.offered": "Предложено", "app.campaign.resultOfferStatus.answered": "Получен ответ", "app.campaign.resultOfferStatus.connected": "Предложение подключено", "app.campaign.resultOfferResult.pending": "Ожидается ответ", "app.campaign.resultOfferResult.agreed": "Согласен", "app.campaign.resultOfferResult.rejected": "Отказ", "app.campaign.resultOfferResult.responseUnknown": "Ответ не распознан", "app.campaign.resultOfferResult.noResponse": "Ответ не поступал", "app.campaign.results.empty": "Нет результатов", "app.offer.deleteHeader": "Подтвердите удаление", "app.offer.deleteText": "Вы действительно хотите удалить предложение \"{name}\"?", "app.offer.copyHeader": "Подтвердите копирование", "app.offer.copyText": "Вы действительно хотите копировать предложение \"{name}\"?", "app.campaigns.form.clients.table.header.channel": "<PERSON><PERSON><PERSON><PERSON>", "app.campaigns.form.clients.table.header.address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.campaigns.form.clients.filterSelector.header": "Настройки фильтра", "app.campaigns.form.clients.filterSelector.empty": "По заданным параметрам ничего не найдено", "app.campaigns.form.clients.filterSelector.found": "Найдено {number} результатов", "app.campaigns.form.clients.filterSelector.apply": "Выбрать", "app.campaigns.form.clients.filterSelector.cancel": "Отмена", "app.campaigns.form.clients.tabChange.header": "Подтвердите действие", "app.campaigns.form.clients.tabChange.text": "Вы уверены, что хотите перейти на другую вкладку? Заданный список контактных лиц не будет сохранен.", "app.campaigns.form.clients.fileParser.header": "Обработка файла", "app.campaigns.form.clients.fileParser.apply": "Загрузить", "app.campaigns.form.clients.fileParser.cancel": "Отмена", "app.campaigns.form.clients.fileParser.processing": "Обработка", "app.uploadFile.results.success": "Обработка завершена без ошибок.", "app.uploadFile.results.processingResults": "Обработан<PERSON> {count} из {totalCount} ячеек", "app.uploadFile.modals.processingLogTitle": "Ход распознавания", "app.campaigns.form.statistics.conversion": "Конверсия", "app.campaigns.form.statistics.results": "Статистика результатов", "app.campaigns.form.statistics.offersTotal": "Предложено всего", "app.campaigns.form.statistics.refusals": "Статистика отказов", "app.campaigns.form.statistics.refusalsTotal": "Отказов всего", "app.campaigns.form.statistics.campaignFunnel": "Воронка кампании", "app.campaigns.form.statistics.OfferedNumber": "Предложено", "app.campaigns.form.statistics.RefusedNumber": "Отклонено", "app.campaigns.form.statistics.Volume": "Объем кампании (всего)", "app.campaigns.form.statistics.UnrecognizedNumber": "Не распознано", "app.campaigns.form.statistics.AnswerNumber": "Получено ответов", "app.campaigns.form.statistics.AcceptedNumber": "Согласен", "app.campaigns.form.statistics.ConnectedNumber": "Подключено", "app.campaigns.form.statistics.noRefusals": "Нет отказов", "app.campaigns.form.statistics.noResults": "Нет результатов", "app.campaign.filter.required": "Фильтры по контактным лицам обязательны для исходящей кампании", "app.campaigns.form.results.saveSuccess": "Кампания сохранена", "app.campaigns.form.results.saveError": "Ошибка сохранения кампании", "app.campaigns.form.results.deleteSuccess": "Кампания удалена", "app.campaigns.form.results.deleteError": "Ошибка удаления кампании"}