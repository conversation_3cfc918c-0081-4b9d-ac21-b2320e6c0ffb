import {
  AutomationServiceAuthType,
  AutomationServiceHeader,
  AutomationServiceParameterDescription,
  AutomationServiceRequestBodyType,
  AutomationServiceRequestType,
  AutomationServiceResponseBodyType,
  AutomationServiceType,
  IFrontAutomationService,
} from '../@types/automationService.types';
import {
  AddUpdateRestServiceDto,
  AddUpdateScriptServiceDto,
  AddUpdateServiceDtoCommon,
  AuthPropsDto,
  AuthTypes,
  ContentTypes,
  HttpMethods,
  ServiceParamDto,
  ServiceParamTypes,
  ServiceType,
  StringStringKeyValuePair,
} from '../@types/generated/automationServices';

const mapFrontAutomationServiceTypeToBack = (frontType: AutomationServiceType): ServiceType => {
  const map = {
    [AutomationServiceType.REST]: ServiceType.Rest,
    [AutomationServiceType.SOAP]: ServiceType.Soap,
    [AutomationServiceType.Plugin]: ServiceType.Plugin,
    [AutomationServiceType.Code]: ServiceType.Script,
  };

  return map[frontType];
};

const mapFrontAutomationServiceAuthTypeToBack = (
  frontAuthType: AutomationServiceAuthType,
): AuthTypes => {
  const map = {
    [AutomationServiceAuthType.NoAuth]: AuthTypes.None,
    [AutomationServiceAuthType.Basic]: AuthTypes.Basic,
    [AutomationServiceAuthType.Bearer]: AuthTypes.Bearer,
    [AutomationServiceAuthType.ApiKey]: AuthTypes.ApiKey,
  };

  return map[frontAuthType];
};

const mapFrontAutomationServiceRequestTypeToBack = (
  frontRequestType: AutomationServiceRequestType,
): HttpMethods => {
  const map = {
    [AutomationServiceRequestType.GET]: HttpMethods.GET,
    [AutomationServiceRequestType.POST]: HttpMethods.POST,
  };

  return map[frontRequestType];
};

const mapFrontAutomationServiceRequestBodyTypeToBack = (
  frontRequestBodyType: AutomationServiceRequestBodyType,
): ContentTypes => {
  const map = {
    [AutomationServiceRequestBodyType.JSON]: ContentTypes.JSON,
    [AutomationServiceRequestBodyType.XML]: ContentTypes.XML,
    [AutomationServiceRequestBodyType.Text]: ContentTypes.Text,
  };

  return map[frontRequestBodyType];
};

const mapFrontAutomationServiceResponseBodyTypeToBack = (
  frontResponseBodyType: AutomationServiceResponseBodyType,
): ContentTypes => {
  const map = {
    [AutomationServiceResponseBodyType.JSON]: ContentTypes.JSON,
    [AutomationServiceResponseBodyType.XML]: ContentTypes.XML,
    [AutomationServiceResponseBodyType.Text]: ContentTypes.Text,
    [AutomationServiceResponseBodyType.File]: ContentTypes.File,
  };

  return map[frontResponseBodyType];
};

const mapFrontAutomationServiceToBackAuthProps = (
  automationService: IFrontAutomationService,
): AuthPropsDto => {
  const authType = mapFrontAutomationServiceAuthTypeToBack(automationService.authType);
  return {
    authType,
    login: authType === AuthTypes.Basic ? automationService.authLogin : null,
    password:
      (
        {
          [AuthTypes.Basic]: automationService.authPassword,
          [AuthTypes.Bearer]: automationService.authToken,
          [AuthTypes.ApiKey]: automationService.authHeaderValue,
        } as any
      )[authType] ?? null,
    header: authType === AuthTypes.ApiKey ? automationService.authHeaderName : null,
  };
};

const mapFrontAutomationServiceRequestParametersToBack = (
  requestParameters: AutomationServiceParameterDescription,
): ServiceParamDto => {
  return {
    type: ServiceParamTypes.Input,
    key: requestParameters.key,
    name: requestParameters.description,
    description: requestParameters.reachDescription,
    // Пока не сделано на UI Значение которое будет представлено если параметр не передан
    defaultValue: null,
  };
};

const mapFrontAutomationServiceResponseParametersToBack = (
  requestParameters: AutomationServiceParameterDescription,
): ServiceParamDto => {
  return {
    type: ServiceParamTypes.Output,
    key: requestParameters.key,
    name: requestParameters.description,
    description: requestParameters.reachDescription,
    // Пока не сделано на UI Значение которое будет представлено если параметр не передан
    defaultValue: null,
  };
};

const mapFrontAutomationServiceRequestHeadersToBack = (
  requestHeaders: AutomationServiceHeader,
): StringStringKeyValuePair => {
  return {
    key: requestHeaders.name,
    value: requestHeaders.value,
  };
};

const mapFrontAutomationServiceToAddUpdateServiceDtoCommon = (
  automationService: IFrontAutomationService,
): AddUpdateServiceDtoCommon => {
  return {
    name: automationService.name,
    description: automationService.description,
    code: automationService.code,
    system: automationService.system ?? null,
    serviceType: mapFrontAutomationServiceTypeToBack(automationService.type),
    parameters: [
      ...(automationService.requestParameters?.map(
        mapFrontAutomationServiceRequestParametersToBack,
      ) || []),
      ...(automationService.responseParameters?.map(
        mapFrontAutomationServiceResponseParametersToBack,
      ) || []),
    ],
  };
};

export const mapFrontAutomationServiceToAddUpdateRestServiceDto = (
  automationService: IFrontAutomationService,
): AddUpdateRestServiceDto => {
  return {
    ...mapFrontAutomationServiceToAddUpdateServiceDtoCommon(automationService),
    authProps: mapFrontAutomationServiceToBackAuthProps(automationService),
    method: mapFrontAutomationServiceRequestTypeToBack(automationService.requestType),
    contentType: mapFrontAutomationServiceRequestBodyTypeToBack(automationService.requestBodyType),
    headers:
      automationService.requestHeaders?.map(mapFrontAutomationServiceRequestHeadersToBack) || [],
    url: automationService.requestUrl,
    body: automationService.requestBody,
    canUploadFiles: automationService.canUploadFiles,
    response: {
      body: automationService.responseBody,
      contentType: mapFrontAutomationServiceResponseBodyTypeToBack(
        automationService.responseBodyType,
      ),
      headers: [],
    },
  };
};

export const mapFrontAutomationServiceToAddUpdateScriptServiceDto = (
  automationService: IFrontAutomationService,
): AddUpdateScriptServiceDto => {
  return {
    ...mapFrontAutomationServiceToAddUpdateServiceDtoCommon(automationService),
    body: automationService.requestBody,
  };
};
