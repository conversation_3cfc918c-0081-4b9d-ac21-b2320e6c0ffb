{
  "infrastructureServicesUrl": "//{transform:AppHostname}/awp/infrastructure/",
  "communicationServicesUrl": "//{transform:AppHostname}/awp/communication/",
  "crpmWorkExecutionConfigurationProviderServiceUrl": "//{transform:AppHostname}/crpm/configuration/",
  "crpmWorkCoordinationHubAddress": "//{transform:AppHostname}/crpm/workcoordination/workExecution",
  "crpmConfigurationReaderServiceUrl": "//{transform:AppHostname}/crpm/configuration/",
  "crpmPresentationServiceUrl": "//{transform:AppHostname}/crpm/presentation/",
  "crpmRequestManagingServiceUrl": "//{transform:AppHostname}/crpm/requestmanaging/",
  "crpmStateProcessingServiceUrl": "//{transform:AppHostname}/crpm/stateprocessing/",
  "productChatHistory": "//{transform:AppHostname}/core/product-api/graphql",
  "productOperatorChatService": "//{transform:AppHostname}/messages/operator-chat/chathub",
  "productOperatorChatApi": "//{transform:AppHostname}/messages/operator-chat",
  "productFileServer": "//{transform:AppHostname}/attachments/file-manager/api/uploaded/file/v2",
  "productPublicFileServer": "//{transform:DmzDNS}/attachments/file-manager/api/uploaded/file",
  "productFileServerApiUrl": "//{transform:AppHostname}/attachments/file-manager/api",
  "productInternalChatSignalR": "//{transform:AppHostname}/internal-chat/chat-api/hubs/chat",
  "productInternalChatApi": "//{transform:AppHostname}/internal-chat/chat-api",
  "calcServiceUrl": "//{transform:AppHostname}/statistics/calc",
  "requestConfigurationUrl": "//{transform:AppHostname}/crpm/configuration-api",
  "productMarketingService": "//{transform:AppHostname}/marketing/webapi",
  "iceServers": [{transform:STUN}, {transform:TURN}],
  "productEventServiceHub": "//{transform:AppHostname}/statistics/event/hubs/eventreceiver",
  "productFreeswitchApi": "//{transform:AppHostname}/channels/voice/freeswitch-call-control/api/",
  "productClientCardBaseUrl": "//{transform:AppHostname}/core/product-api/api/ClientCardBlock/",
  "productApiUrl": "//{transform:AppHostname}/core/product-api/",
  "ucmmPresentationBaseUrl": "//{transform:AppHostname}/ucm/presentation",
  "ucmmManagingBaseUrl": "//{transform:AppHostname}/ucm/managing",
  "productTextTemplatePresentationUrl": "//{transform:AppHostname}/templates/presentation",
  "productTextTemplateManagementUrl": "//{transform:AppHostname}/templates/management",
  "credentialsProviderServicesUrl": "//{transform:AppHostname}/credman/utilization/",
  "dataPresentationServiceUrl": "//{transform:AppHostname}/core/data-presentation/",
  "dialogScriptsManagementUrl": "//{transform:AppHostname}/dialoguescripts/management",
  "userSessionServiceUrl": "//{transform:AppHostname}/core/session-service",
  "evaluationFormsUrl": "//{transform:AppHostname}/evaluations/management",
  "automationServicesManagementUrl": "//{transform:AppHostname}/automations/webapi",
  "commentsApiUrl": "//{transform:AppHostname}/comments/api",
  "webarmLogsBus": "//{transform:AppHostname}/logging/node-logger/",
  "ticketingApiUrl": "//{transform:AppHostname}/ticketing/internal-api",
  "callControlEventHub": "//{transform:AppHostname}/call-control/cc",
  "externalHost": "{transform:DmzDNS}",
  "administrationApiUrl": "//{transform:AppHostname}/core/settings",
  "screenSharingSite": "//{transform:AppHostname}/screensharing/",
  "screenViewerSite": "//{transform:AppHostname}/screenviewer/",
  "webarmToolsSite": "//{transform:AppHostname}/webarm-tools/",
  "videochatSocketUrl": "//{transform:AppHostname}/core/signal-service/chathub",
  "serviceSystemsApiUrl": "//{transform:AppHostname}/service-systems"
}
