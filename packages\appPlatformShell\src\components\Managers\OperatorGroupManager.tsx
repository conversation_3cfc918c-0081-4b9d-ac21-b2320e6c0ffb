import { Guid } from 'guid-typescript';

import {
  IOperatorGroupManager,
  OperatorGroupCustomAttribute,
} from '@monorepo/common/src/platform/awp-web-interfaces';
import { ProxyBase } from '@monorepo/common/src/platform/utils';

export class OperatorGroupManager extends ProxyBase implements IOperatorGroupManager {
  constructor(baseUrl: string) {
    super(baseUrl, 'OperatorGroups/');
  }
  async getOperatorGroupsIdsWithParentGroupsInHierarchy(operatorId: Guid): Promise<Guid[]> {
    const params = new URLSearchParams();
    params.append('operatorId', operatorId.toString());
    return await this.getDataFromAction('GetOperatorGroupsIdsWithParentGroupsInHierarchy', params);
  }

  async getCustomAttributes(
    groupId: Guid,
    codes: string[],
  ): Promise<OperatorGroupCustomAttribute[]> {
    const params = new URLSearchParams();
    params.append('operatorGroupId', groupId.toString());
    return await this.postDataToActionWithResult('GetCustomAttributes', params, codes);
  }
}
