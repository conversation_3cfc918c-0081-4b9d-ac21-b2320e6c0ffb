import { ColumnDef } from '@tanstack/react-table';
import { Guid } from 'guid-typescript';

import { getFeatureFlag } from '@monorepo/common/src/helpers/featureFlagsHelper';

import { IFrontOperatorBase } from '../../../../@types/operator';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorFio } from '../../../../helpers/operatorHelper';

const commonColumnProps = {
  enableSorting: true,
  enableColumnFilter: true,
} satisfies Partial<ColumnDef<IFrontOperatorBase>>;

const getColumns = (allOperators: IFrontOperatorBase[]) => {
  return (
    [
      {
        ...commonColumnProps,
        accessorKey: 'login',
        header: getLocaleMessageById('operators.table.login'),
      },
      {
        ...commonColumnProps,
        accessorKey: 'lastName',
        header: getLocaleMessageById('operators.table.surname'),
        meta: {
          defaultSorting: 'asc',
        },
      },
      {
        ...commonColumnProps,
        accessorKey: 'firstName',
        header: getLocaleMessageById('operators.table.name'),
      },
      {
        ...commonColumnProps,
        accessorKey: 'middleName',
        header: getLocaleMessageById('operators.table.middleName'),
      },
      {
        ...commonColumnProps,
        accessorKey: 'groups',
        accessorFn: (row) => row.groups.map((group) => group.name).join(', '),
        header: getLocaleMessageById('operators.table.divisions'),
      },
      getFeatureFlag('isCuratorOfOperators') && {
        ...commonColumnProps,
        accessorKey: 'curatorId',
        accessorFn: (row) => row.curatorName,
        header: getLocaleMessageById('operators.table.curator'),
        meta: {
          filter: {
            filterFn: 'checkboxFilterFn',
            filterProps: {
              filterData: [
                { value: Guid.EMPTY, label: '(Пусто)' },
                ...allOperators
                  .map((operator) => ({
                    value: operator.id,
                    label: getOperatorFio(operator),
                  }))
                  .sort((o1, o2) => (o1.label ?? '-').localeCompare(o2.label ?? '-')),
              ],
            },
          },
        },
      },
    ] as ColumnDef<IFrontOperatorBase>[]
  ).filter(Boolean);
};

export default getColumns;
