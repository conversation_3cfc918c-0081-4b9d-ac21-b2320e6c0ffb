{"app.info.pageTitle": "Жеке бөлме", "app.info.tabMyKPI": "KPI", "app.info.tabMyQueues": "Менің кезектерім", "app.info.tabMyRequests": "Менің өтініштерім", "app.info.tabMyEvaluations": "Менің белгілерім", "app.info.onLine": "түзу", "app.info.inWork": "жұмыста", "app.info.inTalk": "Әңгімелесу кезінде", "app.info.hour": "у", "app.info.min": "м", "app.info.sec": "с", "app.info.myKpi.processed": "Өңделген", "app.info.myKpi.closed": "жабық", "app.info.myKpi.missed": "жіб<PERSON><PERSON><PERSON><PERSON> алған", "app.info.myKpi.aht": "AHT", "app.info.myKpi.acsi": "ACSI", "app.info.myKpi.assi": "ASSI", "app.info.myKpi.response": "жа<PERSON><PERSON><PERSON>", "app.info.myKpi.title.processed": "Өңделген", "app.info.myKpi.title.closed": "Жабық", "app.info.myKpi.title.missed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> алған", "app.info.myKpi.title.aht": "Орташа өңдеу уақыты", "app.info.myKpi.title.acsi": "Клиент белгілейтін жұмыс сапасының орташа бағасы", "app.info.myKpi.title.assi": "Жетекші орнатқан жұмыс сапасының орташа бағасы", "app.info.myKpi.title.response": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myKpi.table.queue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myKpi.table.operatorsAwaiting": "Өңдеуді күту", "app.info.myKpi.table.activeOperators": "Белсенді операторлар", "app.info.myKpi.table.asa": "ASA", "app.info.myKpi.table.aht": "AHT", "app.info.myRequests.showHistory": "Хабарламалардың тарихын көрсету", "app.info.myRequests.takeToWork": "Жұмысқа қабылдау", "app.info.myRequests.emptyHeader": "Ешқандай өтініштер табылған жоқ", "app.info.myRequests.emptyText": "Сұраныстың параметрлерін тексеріңіз", "app.common.minutes.short": "м", "app.common.seconds.short": "бірге", "app.info.myRequests.table.id": "ID", "app.info.myRequests.table.queue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.channel": "Арна", "app.info.myRequests.table.theme": "Тақырып", "app.info.myRequests.table.type": "Айналым түрі", "app.info.myRequests.table.liveTime": "Өмір Өмірі", "app.info.myRequests.table.queueTime": "Желіде уақыт", "app.info.myRequests.table.clientId": "Жеке куәлік", "app.info.myRequests.table.clientFio": "Клиенттің Т.А.Ә.", "app.info.myRequests.table.sender": "Ж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.recipient": "Алу<PERSON>ы", "app.info.myRequests.table.priority": "Артықшылық", "app.info.myRequests.table.repeated": "Қайталанды", "app.info.myRequests.table.registrationTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.lastDistributionTime": "Соңғы тарату", "app.info.myRequests.table.status": "Мәртебе", "app.info.myRequests.table.postponedUntil": "Кешіктірілді", "app.info.myRequests.table.lastChangedByFio": "Кім өзгерді", "app.info.myRequests.table.sa": "SA", "app.info.myRequests.table.ht": "HT", "app.info.myRequests.table.wt": "WT", "app.info.myRequests.table.1rt": "1RT", "app.info.myRequests.table.acw": "ACW", "app.info.myRequests.table.csi": "CSI", "app.info.myRequests.table.ssi": "SSI", "app.info.myRequests.table.incomingMessagesNumber": "Vx.х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.outgoingMessagesNumber": "Тыс.х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.attachmentsNumber": "Тіркемелер", "app.info.myRequests.table.lost": "Жоғалған", "app.info.myRequests.table.clientType": "Клиент түрі", "app.info.myRequests.table.answerUntil": "Ж<PERSON><PERSON><PERSON><PERSON> беру", "app.info.myRequests.table.processedByFio": "Өңделген", "app.info.myRequests.table.timeInStatus": "Күйдің уақыты", "app.common.in": "-да", "app.info.requests.common.yes": "Иә", "app.info.requests.common.no": "Жоқ", "app.info.requests.type.outgoing": "Шығыс", "app.info.requests.type.incoming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.common.close": "Жабық", "app.messagesModal.title": "#{requestId} - Тарих тарихы", "app.buttons.tooltip.refresh": "Жаңарту", "app.buttons.tooltip.download": "Excel-ге түсіру", "app.myTimeline": "Менің жұмыс уақытым", "app.myKpi": "FEI KPI", "app.queueKpi": "КПИ кезектері", "app.kpiForToday": "Бүгінгі күнге арналған KPI көрсеткіштері", "app.lastUpdateTime": "Жаңартылды", "app.kpi.workingTime": "Жұмыс ауысымы", "app.kpi.workKpi": "Жұмыс көрсеткіштері", "app.kpi.speedKpi": "Жылдамдық көрсеткіштері", "app.kpi.qualityKpi": "Тәрбие көрсеткіштері", "app.info.myRequests.table.title.attachmentsNumber": "Айналымдағы инвестициялар саны", "app.table.attachments.none": "Инвестицияларсыз", "app.table.attachments.has": "Қосымшалармен", "app.info.myRequests.table.externalClientId": "Сыртқы идентификатор", "app.info.myRequests.table.title.externalClientId": "Сыртқы жүйедегі клиент идентификаторы", "app.info.onBreak": "үзілістерде", "app.common.onlyEmpty": "Тек бос", "app.common.onlyNotEmpty": "Тек толтырылған", "app.common.from": "Бірге", "app.common.to": "Ішінде", "app.common.error": "Деректерді алу қатесі", "app.common.updateError": "Деректерді жаңарта алмады, әрекетті қайталаңыз"}