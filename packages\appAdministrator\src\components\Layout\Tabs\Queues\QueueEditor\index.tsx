import React from 'react';

import clsx from 'clsx';

import {
  AttentionIcon,
  AttentionIconSize,
  AttentionIconType,
  Button,
  ButtonVariant,
  Input,
  ITab,
  OverlayLoader,
  Select,
  Tabs,
  Textarea,
  utils,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';
import { getFeatureFlag } from '@monorepo/common/src/helpers/featureFlagsHelper';
import { checkObjectsEqual } from '@monorepo/common/src/helpers/objectsHelper';

import {
  IFrontQueueKpiParameter,
  IFrontRoutingAttribute,
  IFrontBot,
  IFrontAutoHandler,
} from '../../../../../@types/parameters';
import {
  IFrontDistributionRule,
  IFrontQueue,
  IFrontQueueAutoHandler,
  IFrontQueueBot,
  IFrontQueueKpi,
  IFrontRoutingRuleSet,
} from '../../../../../@types/queue';
import { needConfirmWhenCompareFalse } from '../../../../../helpers/confirmSave.helper';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import {
  mapFullQueueFrontToNewDto,
  mapQueueParametersToFront,
} from '../../../../../mappers/queues';
import { createQueue, getQueueParameters, updateQueue } from '../../../../../services/queues';
import OperatorsSettings from '../../OperatorGroups/OperatorGroupEditor/OperatorsSettings';

import AutoHandlerSettings from './AutoHandlersSettings';
import ChatBotSettings from './ChatBotSettings';
import DistributionSettings from './DistributionSettings';
import KpiSettings from './KpiSettings';
import OperatorGroupsSettings from './OperatorGroupSettings';
import RoutingSettings from './RoutingSettings';
import { defaultValidationResult, IValidationResult, validateQueue } from './validator';

import styles from './styles.module.scss';

enum QueueEditorTab {
  General,
  Routing,
  Operators,
  OperatorGroups,
  Autohandlers,
  ChatBot,
  Kpi,
}

interface IQueueEditorProps {
  fullQueue: IFrontQueue | null;
  onSubmit: () => void;
  onClose?: () => void;
}

const QueueEditor = ({ fullQueue, onSubmit, onClose }: IQueueEditorProps) => {
  const [currentTab, setCurrentTab] = React.useState<QueueEditorTab>(QueueEditorTab.General);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();
  const [saveError, setSaveError] = React.useState<Error>();
  const [name, setName] = React.useState('');
  const [description, setDescription] = React.useState('');
  const [weight, setWeight] = React.useState(0);
  const [division, setDivision] = React.useState('');
  const [operators, setOperators] = React.useState<IFrontQueue['operators']>(
    fullQueue?.operators ?? [],
  );
  const [operatorGroups, setOperatorGroups] = React.useState<IFrontQueue['operatorGroups']>(
    fullQueue?.operatorGroups ?? [],
  );
  const [distributionRuleType, setDistributionRuleType] = React.useState('');
  const [kpiSettings, setKpiSettings] = React.useState<Record<string, IFrontQueueKpi>>({});
  const [buttonBot, setButtonBot] = React.useState<IFrontQueueBot>({ enabled: false, code: null });
  const [intelligentBot, setIntelligentBot] = React.useState<IFrontQueueBot>({
    enabled: false,
    code: null,
  });
  const [ratingBot, setRatingBot] = React.useState<IFrontQueueBot>({ enabled: false, code: null });
  const [routingRules, setRoutingRules] = React.useState<IFrontRoutingRuleSet[]>([]);
  const [queueAutoHandlersMap, setQueueAutoHandlersMap] = React.useState<
    Record<string, IFrontQueueAutoHandler>
  >({});

  const [divisions, setDivisions] = React.useState<{ id: string; name: string }[]>([]);
  const [routingAttributes, setRoutingAttributes] = React.useState<IFrontRoutingAttribute[]>([]);
  const [distributionRules, setDistributionRules] = React.useState<IFrontDistributionRule[]>([]);
  const [autoHandlers, setAutoHandlers] = React.useState<IFrontAutoHandler[]>([]);
  const [buttonBots, setButtonBots] = React.useState<IFrontBot[]>([]);
  const [intelligentBots, setIntelligentBots] = React.useState<IFrontBot[]>([]);
  const [ratingBots, setRatingBots] = React.useState<IFrontBot[]>([]);
  const [kpiParameters, setKpiParameters] = React.useState<IFrontQueueKpiParameter[]>([]);

  const [validationResult, setValidationResult] =
    React.useState<IValidationResult>(defaultValidationResult);

  const tabs: ITab[] = [
    {
      label: (
        <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
          {getLocaleMessageById('queues.editor.tabs.general')}
          {validationResult.main.isErrored && (
            <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
          )}
        </span>
      ),
      value: QueueEditorTab.General,
    },
    {
      label: (
        <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
          {getLocaleMessageById('queues.editor.tabs.routing')}
          {validationResult.routing.isErrored && (
            <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
          )}
        </span>
      ),
      value: QueueEditorTab.Routing,
    },
    {
      label: (
        <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
          {getLocaleMessageById('queues.editor.tabs.operators')}
          {validationResult.operators.isErrored && (
            <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
          )}
        </span>
      ),
      value: QueueEditorTab.Operators,
    },
    {
      label: getLocaleMessageById('queues.editor.tabs.operatorGroups'),
      value: QueueEditorTab.OperatorGroups,
    },
    {
      label: (
        <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
          {getLocaleMessageById('queues.editor.tabs.autohandlers')}
          {validationResult.autoHandlers.isErrored && (
            <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
          )}
        </span>
      ),
      value: QueueEditorTab.Autohandlers,
    },
    {
      label: (
        <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
          {getLocaleMessageById('queues.editor.tabs.chatBot')}
          {validationResult.chatBot.isErrored && (
            <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
          )}
        </span>
      ),
      value: QueueEditorTab.ChatBot,
    },
    {
      label: (
        <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
          {getLocaleMessageById('queues.editor.tabs.kpi')}
          {validationResult.kpi.isErrored && (
            <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
          )}
        </span>
      ),
      value: QueueEditorTab.Kpi,
    },
  ];

  const tabComponents = {
    [QueueEditorTab.General]: (
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4)}>
        <Input
          label={getLocaleMessageById('queues.editor.name')}
          value={name}
          onChange={({ value }) => setName(value ?? '')}
          isInvalid={!!validationResult.main.name}
          message={<InputErrorMessage>{validationResult.main.name}</InputErrorMessage>}
        />
        <Textarea
          label={getLocaleMessageById('queues.editor.description')}
          value={description}
          onChange={({ value }) => setDescription(value ?? '')}
          rows={7}
          maxLength={1000}
          isInvalid={!!validationResult.main.description}
          message={<InputErrorMessage>{validationResult.main.description}</InputErrorMessage>}
        />
        {getFeatureFlag('isDivisionEnabled') && (
          <Select
            label={getLocaleMessageById('queues.editor.division')}
            data={divisions.map((d) => ({
              value: d.id,
              text: d.name,
            }))}
            value={division}
            onChange={({ value }) => setDivision(value ?? '')}
          />
        )}
        <Input
          label={getLocaleMessageById('queues.editor.weight')}
          type="number"
          value={weight.toString()}
          onChange={({ value }) => setWeight(Number(value ?? 0))}
          isInvalid={!!validationResult.main.weight}
          message={<InputErrorMessage>{validationResult.main.weight}</InputErrorMessage>}
        />

        <DistributionSettings
          value={distributionRuleType}
          onChange={setDistributionRuleType}
          distributionRules={distributionRules}
          validationResult={validationResult.distribution}
        />
      </div>
    ),
    [QueueEditorTab.Routing]: (
      <RoutingSettings
        routingAttributes={routingAttributes}
        routingRuleSets={routingRules}
        onChange={setRoutingRules}
        validationResult={validationResult.routing}
      />
    ),
    [QueueEditorTab.Operators]: (
      <OperatorsSettings
        operators={operators}
        onChangeOperators={(newOperators) => setOperators(newOperators as IFrontQueue['operators'])}
        withPriority
        validationResult={validationResult.operators}
      />
    ),
    [QueueEditorTab.OperatorGroups]: (
      <OperatorGroupsSettings
        operatorGroups={operatorGroups}
        handleOperatorGroupsChange={setOperatorGroups}
      />
    ),
    [QueueEditorTab.Autohandlers]: (
      <AutoHandlerSettings
        autoHandlers={autoHandlers}
        queueAutoHandlersMap={queueAutoHandlersMap}
        onChange={(newAutoHandler) =>
          setQueueAutoHandlersMap((current) => ({
            ...current,
            [newAutoHandler.code]: newAutoHandler,
          }))
        }
        validationResult={validationResult.autoHandlers}
      />
    ),
    [QueueEditorTab.ChatBot]: (
      <ChatBotSettings
        buttonBot={buttonBot}
        buttonBots={buttonBots}
        onButtonBotChange={setButtonBot}
        intelligentBot={intelligentBot}
        intelligentBots={intelligentBots}
        onIntelligentBotChange={setIntelligentBot}
        ratingBot={ratingBot}
        ratingBots={ratingBots}
        onRatingBotChange={setRatingBot}
        validationResult={validationResult.chatBot}
      />
    ),
    [QueueEditorTab.Kpi]: (
      <KpiSettings
        kpiParameters={kpiParameters}
        values={kpiSettings}
        onChange={(value) => setKpiSettings((current) => ({ ...current, [value.code]: value }))}
        validationResult={validationResult.kpi}
      />
    ),
  };

  const newQueue: IFrontQueue = React.useMemo(
    () => ({
      id: fullQueue?.id ?? 0,
      name,
      description,
      weight,
      division,
      isDefault: fullQueue?.isDefault ?? false,
      isService: fullQueue?.isService ?? false,
      operators,
      operatorGroups,
      distributionRuleType:
        (distributionRuleType || distributionRules.find((rule) => rule.isDefault)?.ruleType) ?? '',
      kpiParameters: Object.values(kpiSettings),
      buttonBot,
      intelligentBot,
      ratingBot,
      routingRules,
      autoHandlers: Object.keys(queueAutoHandlersMap).map((key) => queueAutoHandlersMap[key]),
    }),
    [
      name,
      description,
      weight,
      division,
      operators,
      operatorGroups,
      queueAutoHandlersMap,
      distributionRuleType,
      buttonBot,
      intelligentBot,
      ratingBot,
      kpiSettings,
      routingRules,
      fullQueue,
      distributionRules,
    ],
  );

  const handleSubmit = async () => {
    const newValidationResult = validateQueue(newQueue, kpiParameters);
    setValidationResult(newValidationResult);
    if (newValidationResult.isErrored) return;

    setLoading(true);
    setSaveError(undefined);
    try {
      if (fullQueue?.id) {
        await updateQueue(fullQueue.id, mapFullQueueFrontToNewDto(newQueue));
      } else {
        await createQueue(mapFullQueueFrontToNewDto(newQueue));
      }
      onSubmit();
      onClose?.();
    } catch (err) {
      console.error('Error saving queue', err);
      setSaveError(err);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const queueParameters = await getQueueParameters();
        const frontQueueParameters = mapQueueParametersToFront(queueParameters);

        setDivisions(frontQueueParameters.divisions);

        setRoutingAttributes(frontQueueParameters.routingAttributes);

        setDistributionRules(frontQueueParameters.distributionRules);

        setAutoHandlers(frontQueueParameters.autoHandlers);

        setButtonBots(frontQueueParameters.buttonBots);
        setIntelligentBots(frontQueueParameters.intelligentBots);
        setRatingBots(frontQueueParameters.ratingBots);

        setKpiParameters(frontQueueParameters.kpiParameters);
      } catch (e) {
        console.error('Error starting queue editor', e);
        setError(e);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  React.useEffect(() => {
    setName(fullQueue?.name ?? '');
    setDescription(fullQueue?.description ?? '');
    setWeight(fullQueue?.weight ?? 0);
    setDivision(fullQueue?.division ?? '');
    setOperators(fullQueue?.operators ?? []);
    setOperatorGroups(fullQueue?.operatorGroups ?? []);
    setDistributionRuleType(
      fullQueue?.distributionRuleType ??
        distributionRules.find((rule) => rule.isDefault)?.ruleType ??
        '',
    );
    setButtonBot(fullQueue?.buttonBot ?? { enabled: false, code: null });
    setIntelligentBot(fullQueue?.intelligentBot ?? { enabled: false, code: null });
    setRatingBot(fullQueue?.ratingBot ?? { enabled: false, code: null });
    setKpiSettings(
      fullQueue?.kpiParameters.reduce(
        (accum, value) => ({
          ...accum,
          [value.code]: value,
        }),
        {},
      ) ?? {},
    );
    setRoutingRules(fullQueue?.routingRules ?? []);
    setQueueAutoHandlersMap(
      fullQueue?.autoHandlers.reduce((accumulator, value) => {
        return {
          ...accumulator,
          [value.code]: value,
        };
      }, {}) ?? {},
    );
  }, [fullQueue, distributionRules]);

  React.useEffect(() => {
    if (!validationResult.isErrored) return;

    setValidationResult(validateQueue(newQueue, kpiParameters));
  }, [kpiParameters, newQueue, validationResult.isErrored]);

  return (
    <OverlayLoader
      wrapperClassName={clsx(utils.dFlex, utils.flexColumn, styles.editor)}
      loading={loading}
    >
      {error && (
        <div className={clsx(utils.flexCentredBlock, utils.flexGrow1, utils.p6)}>
          <AlertError error={error} header={getLocaleMessageById('queues.editor.initError')} />
        </div>
      )}
      {!error && (
        <>
          <Tabs
            className={clsx(utils.pT2, utils.pX6)}
            tabs={tabs}
            onChange={({ value }) => setCurrentTab(value)}
            value={currentTab}
          />
          <div
            className={clsx(
              utils.flexBasis0,
              utils.flexGrow1,
              utils.pX6,
              utils.pY4,
              utils.overflowAuto,
              utils.scrollbar,
            )}
          >
            {tabComponents[currentTab]}
          </div>
        </>
      )}
      <footer
        className={clsx(
          utils.borderTop,
          utils.pX6,
          utils.pY4,
          utils.dFlex,
          utils.gap2,
          utils.justifyContentEnd,
        )}
      >
        {saveError && (
          <AlertError
            className={utils.flexGrow1}
            header={getLocaleMessageById('queues.save.error')}
            error={saveError}
          />
        )}
        <Button
          variant={ButtonVariant.Secondary}
          onClick={() => {
            if (error) return onClose?.();
            const hasNoChanges = checkObjectsEqual(fullQueue, newQueue);
            needConfirmWhenCompareFalse(hasNoChanges, onClose);
          }}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button disabled={!!error} onClick={handleSubmit}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </footer>
    </OverlayLoader>
  );
};

export default QueueEditor;
