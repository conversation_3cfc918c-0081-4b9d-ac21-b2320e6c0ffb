import React from 'react';

import clsx from 'clsx';

import {
  Button,
  FloatingTooltip,
  IconButton,
  Modal,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconArchive from '@product.front/icons/dist/icons17/MainStuff/IconArchive';
import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconPlay from '@product.front/icons/dist/icons17/MainStuff/IconPlay';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconPublish from '@product.front/icons/dist/icons17/MainStuff/IconShare1';

import ErrorBoundaryContainer from '@monorepo/common/src/common/components/Errors/ErrorBoundary/Container';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { AutomationServiceStatus } from '../../@types/automationService.types';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import {
  emptyAutomationService,
  setCurrentAutomationService,
} from '../../store/automationService/automationService.slice';
import {
  archiveAutomationService,
  getAutomationServices,
  publishAutomationService,
} from '../../store/automationServices/automationServices.thunk';
import {
  useAutomationServicesAppDispatch,
  useAutomationServicesAppSelector,
} from '../../store/hooks';
import ServiceEditForm from '../AutomationServiceEditForm';
import { runAutomationService } from '../AutomationServiceRunForm/automationServiceRunHelper';

const Header = () => {
  const dispatch = useAutomationServicesAppDispatch();

  const { automationService, initialAutomationService } = useAutomationServicesAppSelector(
    (state) => state.automationService,
  );
  const { selectedAutomationService } = useAutomationServicesAppSelector(
    (state) => state.automationServices,
  );

  const closeEditor = () => dispatch(setCurrentAutomationService(null));

  const handleEditorClose = () => {
    if (JSON.stringify(automationService) !== JSON.stringify(initialAutomationService)) {
      showConfirmModal({
        header: getLocaleMessageById('app.modals.closeAutomationService.header'),
        text: getLocaleMessageById('app.modals.closeAutomationService.text'),
        onConfirm: closeEditor,
      });
    } else {
      closeEditor();
    }
  };

  return (
    <header
      className={clsx(
        utils.dFlex,
        utils.gap4,
        utils.alignItemsCenter,
        utils.justifyContentBetween,
        utils.pX6,
        utils.pY4,
        utils.pB4,
        utils.borderBottom,
      )}
    >
      <div className={clsx(utils.dFlex, utils.gap5, utils.alignItemsCenter)}>
        <Text variant={TextVariant.HeadlineSemibold}>
          {getLocaleMessageById('app.header.title')}
        </Text>
        <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.refresh')}>
          <IconButton onClick={() => dispatch(getAutomationServices())}>
            <IconRefresh />
          </IconButton>
        </FloatingTooltip>
      </div>
      <div className={clsx(utils.dFlex, utils.gap5, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
          <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.publish')}>
            <IconButton
              onClick={() =>
                selectedAutomationService &&
                dispatch(publishAutomationService(selectedAutomationService.id!))
              }
              disabled={
                !selectedAutomationService ||
                selectedAutomationService?.status !== AutomationServiceStatus.Draft
              }
            >
              <IconPublish />
            </IconButton>
          </FloatingTooltip>
          <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.archive')}>
            <IconButton
              onClick={() =>
                selectedAutomationService &&
                dispatch(archiveAutomationService(selectedAutomationService.id!))
              }
              disabled={
                !selectedAutomationService ||
                selectedAutomationService?.status === AutomationServiceStatus.Archive
              }
            >
              <IconArchive />
            </IconButton>
          </FloatingTooltip>
        </div>
        <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
          <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.edit')}>
            <IconButton
              onClick={() => dispatch(setCurrentAutomationService(selectedAutomationService))}
              disabled={!selectedAutomationService}
            >
              <IconEdit />
            </IconButton>
          </FloatingTooltip>
        </div>
        <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
          <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.testRun')}>
            <IconButton
              onClick={() =>
                selectedAutomationService &&
                runAutomationService(selectedAutomationService).catch(console.error)
              }
              disabled={
                !selectedAutomationService ||
                selectedAutomationService?.status === AutomationServiceStatus.Archive
              }
            >
              <IconPlay />
            </IconButton>
          </FloatingTooltip>
        </div>
        <FloatingTooltip tooltip={getLocaleMessageById('app.header.button.create')}>
          <Button onClick={() => dispatch(setCurrentAutomationService(emptyAutomationService))}>
            {getLocaleMessageById('app.header.button.create')}
          </Button>
        </FloatingTooltip>
      </div>
      {automationService && (
        <Modal
          style={{ width: 750 }}
          flushBody
          header={getLocaleMessageById('app.automationService.title')}
          onClose={handleEditorClose}
        >
          <ErrorBoundaryContainer canRetry>
            <ServiceEditForm onCancel={() => dispatch(setCurrentAutomationService(null))} />
          </ErrorBoundaryContainer>
        </Modal>
      )}
    </header>
  );
};

export default Header;
