import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IQueuesStore } from './queues.slice';
import { getAllQueues } from './queues.thunk';

const getAllQueuesReducers = (builder: ActionReducerMapBuilder<IQueuesStore>) =>
  builder
    .addCase(getAllQueues.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllQueues.fulfilled, (state, action) => {
      state.loading = false;
      state.queues = action.payload;
    })
    .addCase(getAllQueues.rejected, (state, action) => {
      state.queues = [];
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<IQueuesStore>) => {
  getAllQueuesReducers(builder);

  return builder;
};
