import { getFormattedTime } from '@monorepo/common/src/helpers/dateHelper';
import { mapUCMMChannelToGraphQLChannelType } from '@monorepo/common/src/mappers/graphQlMappers';

import { IFrontRequest, IOdataRequest, RequestType } from '../@types/Requests';
import { getLocaleMessageById } from '../helpers/localeHelper';

const getFormattedTimeString = (value: number) =>
  getFormattedTime(
    value,
    `mm${getLocaleMessageById('app.common.minutes.short')} ss${getLocaleMessageById(
      'app.common.seconds.short',
    )}`,
  );

const mapOdataRequestTypeToFrontString = (type: RequestType) => {
  const requestTypeString = {
    [RequestType.Incoming]: getLocaleMessageById('app.info.requests.type.incoming'),
    [RequestType.Outgoing]: getLocaleMessageById('app.info.requests.type.outgoing'),
  };

  return requestTypeString[type];
};

export default (data: IOdataRequest): IFrontRequest => ({
  id: data.Id,
  queue: data.QueueTitle,
  channel: mapUCMMChannelToGraphQLChannelType(data.Channel),
  theme: data.Title,
  type: mapOdataRequestTypeToFrontString(data.RequestType),
  liveTime: getFormattedTimeString(data.RequestLifeTime),
  queueTime: getFormattedTimeString(data.InQueueTime),
  clientId: data.ContactPersonId,
  clientFio: data.ContactPersonFullName,
  externalClientId: data.ClientId,
  sender: data.Originator,
  recipient: data.Source,
  priority: data.Priority,
  repeated: data.RepeatedRequestId
    ? getLocaleMessageById('app.info.requests.common.yes')
    : getLocaleMessageById('app.info.requests.common.no'),
  registrationTime: data.TimeRegistered,
  lastDistributionTime: data.AssignedToLastTime ?? '',
  status: data.StatusName,
  postponedUntil: data.PostponeTime,
  lastChangedByFio: data.StatusModifiedBy,
  sa: getFormattedTimeString(data.SpeedAnswer),
  ht: getFormattedTimeString(data.HandleTime),
  wt: getFormattedTimeString(data.WT),
  '1rt': getFormattedTimeString(data.FirstResponsePrepareTime),
  acw: getFormattedTimeString(data.CW),
  csi: data.CSI,
  ssi: data.SSI,
  incomingMessagesNumber: data.TotalIncomingMessages,
  outgoingMessagesNumber: data.TotalOutgoingMessages,
  attachmentsNumber: data.TotalAttachments,
  lost: data.LostSmsFlag
    ? getLocaleMessageById('app.info.requests.common.yes')
    : getLocaleMessageById('app.info.requests.common.no'),
  clientType: '',
  answerUntil: data.TargetResponseTime,
  processedByFio: data.ExecutorName,
  timeInStatus: getFormattedTimeString(data.StatusTime),
});
