import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { getLocaleMessageById } from './localeHelper';

export const needConfirmWhenCompareFalse = (isEqual: boolean, callback?: () => void) => {
  if (isEqual) {
    callback?.();
    return;
  }
  showConfirmModal({
    header: getLocaleMessageById('app.common.confirmCancel.header'),
    text: getLocaleMessageById('app.common.confirmCancel.text'),
    onConfirm: callback,
  });
};
