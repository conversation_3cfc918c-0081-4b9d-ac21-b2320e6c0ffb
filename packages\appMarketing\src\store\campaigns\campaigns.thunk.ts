import { createAsyncThunk } from '@reduxjs/toolkit';

import { IFrontCampaign } from '../../@types/campaign';
import { DisplayCampaignStatus } from '../../@types/generated/marketing';
import {
  createCampaignAsync,
  deleteCampaignAsync,
  getCampaignResultsAsync,
  getCampaignsAsync,
  getContactPersonsAsync,
  getContactPersonsFromFileAsync,
  getStatisticsAsync,
  updateCampaignAsync,
  uploadFilesDataAsync,
} from '../../helpers/api';
import { getResultReasons } from '../settings/settings.thunk';

import { updateCampaignField } from './campaigns.slice';

export const getCampaigns = createAsyncThunk(
  'campaigns/getCampaigns',
  async (status: DisplayCampaignStatus | null) => {
    return await getCampaignsAsync(status);
  },
);

export const createOrUpdateCampaign = createAsyncThunk(
  'campaigns/createOrUpdate',
  async (campaign: IFrontCampaign) => {
    return await (campaign.id ? updateCampaignAsync : createCampaignAsync)(campaign);
  },
);

export const deleteCampaign = createAsyncThunk(
  'campaigns/deleteCampaign',
  async (campaignId: string) => {
    await deleteCampaignAsync(campaignId);
  },
);

export const getResults = createAsyncThunk(
  'campaigns/getResults',
  async (campaignId: string, { dispatch }) => {
    await dispatch(getResultReasons());
    return await getCampaignResultsAsync(campaignId);
  },
);

export const getClientsFromFile = createAsyncThunk(
  'campaigns/clientsFromFile',
  async (fileId: string) => {
    return await getContactPersonsFromFileAsync(fileId);
  },
);

export const getClients = createAsyncThunk(
  'campaigns/clients',
  async ({ channels, filterString }: { channels: number[]; filterString: string }) => {
    return await getContactPersonsAsync(channels, filterString);
  },
);

export const uploadFilesData = createAsyncThunk(
  'campaigns/uploadFilesData',
  async (addresses: string[], { dispatch }) => {
    const fileId = await uploadFilesDataAsync(addresses);
    dispatch(updateCampaignField({ fieldName: 'fileUploadingId', fieldValue: fileId }));
  },
);

export const getStatistics = createAsyncThunk(
  'campaigns/statistics',
  async (campaignId: string) => {
    return await getStatisticsAsync(campaignId);
  },
);
