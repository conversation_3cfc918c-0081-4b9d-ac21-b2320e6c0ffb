import { IFrontBlackListAddress } from '../@types/blackList';
import { BlackListAddress } from '../@types/generated/administration';

export const mapBlackListAddressToFront = (
  blackList: BlackListAddress,
): IFrontBlackListAddress => ({
  id: blackList.id,
  address: blackList.address,
  dateCreated: blackList.dateCreated,
  deletedBy: blackList.deletedBy ?? undefined,
  dateDeleted: blackList.dateDeleted ?? undefined,
  dueDate: blackList.dueDate,
  addedBy: blackList.addedBy,
  addedByFio: blackList.addedByFio,
  comment: blackList.comment ?? '',
});
