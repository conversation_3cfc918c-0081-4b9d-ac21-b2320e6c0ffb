import { createAsyncThunk } from '@reduxjs/toolkit';

import { getCurrentOperatorUsernameString } from '@monorepo/common/src/managers/currentOperatorManager';

import { RootState } from '..';
import { mapBlackListAddressToFront } from '../../mappers/blackList';
import * as api from '../../services/blackList';

export const getAllBlackListAddresses = createAsyncThunk(
  'blackListAddresses/all',
  async (_, { getState }) => {
    const state = getState() as RootState;
    return (await api.getBlackListAddresses(state.blackList.includeDeleted)).map(
      mapBlackListAddressToFront,
    );
  },
);

export const deleteBlackListAddresses = createAsyncThunk(
  'blackListAddresses/delete',
  async (id: string, { dispatch }) => {
    await api.deleteBlackListAddress(id, getCurrentOperatorUsernameString());
    dispatch(getAllBlackListAddresses());
  },
);
