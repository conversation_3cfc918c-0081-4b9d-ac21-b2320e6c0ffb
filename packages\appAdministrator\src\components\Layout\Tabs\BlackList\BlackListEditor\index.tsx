import React, { FormEvent } from 'react';

import clsx from 'clsx';

import { Button, ButtonVariant, Input, Textarea, utils } from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { getInputDate } from '@monorepo/common/src/helpers/dateHelper';
import {
  getCurrentOperatorFullNameString,
  getCurrentOperatorUsernameString,
} from '@monorepo/common/src/managers/currentOperatorManager';

import { IFrontBlackListAddress } from '../../../../../@types/blackList';
import { needConfirmWhenCompareFalse } from '../../../../../helpers/confirmSave.helper';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { addBlackListAddress, updateBlackListAddress } from '../../../../../services/blackList';

import styles from './styles.module.scss';

interface IBlackListEditorProps {
  blackListAddress: IFrontBlackListAddress | null;
  onSubmit: () => void;
  onClose?: () => void;
}

interface FormElements extends HTMLFormControlsCollection {
  address: HTMLInputElement;
  dueDate: HTMLInputElement;
  comment: HTMLInputElement;
}

const BlackListEditor = ({ blackListAddress, onSubmit, onClose }: IBlackListEditorProps) => {
  const [isSaving, setIsSaving] = React.useState(false);
  const [err, setErr] = React.useState<Error>();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const elements = e.currentTarget.elements as FormElements;

    try {
      setErr(undefined);
      setIsSaving(true);

      if (blackListAddress) {
        await updateBlackListAddress(blackListAddress.id, {
          address: elements.address.value,
          dueDate: new Date(elements.dueDate.value).toISOString(),
          comment: elements.comment.value,
        });
      } else {
        await addBlackListAddress({
          addedBy: getCurrentOperatorUsernameString(),
          addedByFio: getCurrentOperatorFullNameString(),
          address: elements.address.value,
          dueDate: new Date(elements.dueDate.value).toISOString(),
          comment: elements.comment.value,
        });
      }

      onSubmit();
      onClose?.();
    } catch (error) {
      setErr(error);
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = (e: FormEvent<HTMLFormElement>) => {
    const elements = e.currentTarget.elements as FormElements;
    e.preventDefault();

    const defaultModel = {
      address: blackListAddress?.address ?? '',
      dueDate: blackListAddress?.dueDate
        ? new Date(blackListAddress.dueDate).toISOString().substring(0, 16)
        : '',
      comment: blackListAddress?.comment ?? '',
    };

    const currentModel = {
      address: elements.address.value,
      dueDate: elements.dueDate.value
        ? new Date(elements.dueDate.value).toISOString().substring(0, 16)
        : '',
      comment: elements.comment.value,
    };
    const hasNoChanges =
      defaultModel.address === currentModel.address &&
      defaultModel.dueDate === currentModel.dueDate &&
      defaultModel.comment === currentModel.comment;

    needConfirmWhenCompareFalse(hasNoChanges, onClose);
  };

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn, styles.editor)}>
      <div
        className={clsx(
          utils.flexBasis0,
          utils.flexGrow1,
          utils.pX6,
          utils.pY4,
          utils.overflowAuto,
          utils.scrollbar,
        )}
      >
        <form
          id="BlackListEditorForm"
          className={clsx(utils.dFlex, utils.flexColumn, utils.gap5)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <Input
            name="address"
            label={getLocaleMessageById('blackList.modal.field.address')}
            value={blackListAddress?.address ?? ''}
            disabled={isSaving}
            required
          />
          <Input
            name="dueDate"
            label={getLocaleMessageById('blackList.modal.field.dueDate')}
            value={
              blackListAddress?.dueDate ? getInputDate(new Date(blackListAddress?.dueDate)) : ''
            }
            type="datetime-local"
            required
            disabled={isSaving}
            min={getInputDate(new Date())}
          />
          <Textarea
            name="comment"
            label={getLocaleMessageById('blackList.modal.field.comment')}
            value={blackListAddress?.comment ?? ''}
            rows={5}
            disabled={isSaving}
          />
        </form>
      </div>
      <footer className={clsx(utils.borderTop, utils.pX6, utils.pY4, utils.dFlex, utils.gap2)}>
        {err && (
          <AlertError header={getLocaleMessageById('blackList.modal.saveError')} error={err} />
        )}
        <Button
          variant={ButtonVariant.Secondary}
          type="reset"
          form="BlackListEditorForm"
          disabled={isSaving}
          className={utils.mLauto}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button type="submit" form="BlackListEditorForm" disabled={isSaving}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </footer>
    </div>
  );
};

export default BlackListEditor;
