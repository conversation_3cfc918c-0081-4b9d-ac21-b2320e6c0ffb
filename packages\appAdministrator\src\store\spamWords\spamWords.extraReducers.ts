import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { ISpamWordsStore } from './spamWords.slice';
import { getAllSpamWords } from './spamWords.thunk';

const getSpamWordsReducers = (builder: ActionReducerMapBuilder<ISpamWordsStore>) =>
  builder
    .addCase(getAllSpamWords.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllSpamWords.fulfilled, (state, action) => {
      state.spamWords = action.payload;
      state.loading = false;
    })
    .addCase(getAllSpamWords.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<ISpamWordsStore>) => {
  getSpamWordsReducers(builder);

  return builder;
};
