import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { ISettingsStore } from './settings.slice';
import {
  getChannels,
  getClientTableView,
  getOfferTypes,
  getQueues,
  getResultReasons,
} from './settings.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<ISettingsStore>) =>
  builder
    .addCase(getOfferTypes.fulfilled, (state, action) => {
      state.offerTypes = action.payload;
    })
    .addCase(getChannels.fulfilled, (state, action) => {
      state.channels = action.payload;
    })
    .addCase(getQueues.fulfilled, (state, action) => {
      state.queues = action.payload;
    })
    .addCase(getClientTableView.fulfilled, (state, action) => {
      state.clientTableView = action.payload.columnSettings;
    })
    .addCase(getResultReasons.fulfilled, (state, action) => {
      state.resultReasons = action.payload;
    });

export default extraReducers;
