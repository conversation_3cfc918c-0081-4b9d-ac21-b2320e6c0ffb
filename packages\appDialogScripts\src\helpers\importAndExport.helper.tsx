import React from 'react';

import { showModal } from '@product.front/ui-kit';

import { IFrontAttachmentWithRealFile } from '@monorepo/common/src/@types/frontendChat';
import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import { getFormattedDateTime } from '@monorepo/common/src/helpers/dateHelper';
import { downloadFile, parseJsonFile } from '@monorepo/common/src/helpers/fileHelpers';
import { getDialogScriptAttachmentUrl } from '@monorepo/common/src/helpers/getAttachmentsUrl';
import { unzipFilesAsync, zipFilesAsync } from '@monorepo/common/src/helpers/zipHelper';
import { IFrontScript } from '@monorepo/dialog-scripts/src/@types/script';

import ModalExport from '../components/Header/ModalExport';
import ModalImport from '../components/Header/ModalImport';
import { getSettings } from '../config/appSettings';

import { getLocaleMessageById } from './localeHelper';

const getScriptArtefactNameByExtention = (extention: string) => `__index__.${extention}`;

export const exportDialogScript = async (
  script: IFrontScript,
  fileExtension: string,
  progressPrepare: (total: number, progress: number, label: string) => void,
  progressExport: (total: number, progress: number, label: string) => void,
) => {
  const files: File[] = [];

  const str = JSON.stringify(script, null, 2);
  const mainFileBlob = new Blob([str], { type: 'application/json' });

  files.push(new File([mainFileBlob], getScriptArtefactNameByExtention(fileExtension)));

  const attachments = script.steps.reduce<IFrontAttachmentWithRealFile[]>((acc, step) => {
    step.attachments?.forEach((f) => acc.push(f));
    return acc;
  }, []);

  for (const [i, atta] of attachments.entries()) {
    const name = atta.externalId ?? atta.name;
    progressPrepare(attachments.length, i, atta.name);
    if (atta.file) {
      files.push(new File([atta.file], name, { type: atta.mime }));
      progressPrepare(attachments.length, i + 1, atta.name);
      continue;
    }

    const attaUrl = atta.url?.length
      ? atta.url
      : getDialogScriptAttachmentUrl(getSettings().dialogScriptsManagementUrl, atta.externalId);

    const response = await commonFetch(attaUrl);
    const fileData = await response.blob();

    files.push(new File([fileData], name, { type: fileData.type }));
    progressPrepare(attachments.length, i + 1, atta.name);
  }

  const totalAttachmentsSize = files.reduce((acc, file) => acc + file.size, 0);
  let restAttachmentsSize = totalAttachmentsSize;

  const dateStr = getFormattedDateTime(new Date(), 'YYYY-MM-DD_hh-mm-ss');
  const filename = `${script.name}_${dateStr}.${fileExtension}`;
  const zip = await zipFilesAsync(filename, files, (_, total, progress) => {
    restAttachmentsSize -= total - progress;
    progressExport(totalAttachmentsSize, totalAttachmentsSize - restAttachmentsSize, filename);
  });
  await downloadFile(zip);
};

export const exportDialogScriptWithModal = async (script: IFrontScript, fileExtension: string) => {
  return new Promise<void>((resolve, reject) => {
    showModal({
      style: { width: 320 },
      canClose: false,
      header: getLocaleMessageById('app.tooltip.export'),
      children: (onClose) => (
        <ModalExport
          script={script}
          fileExtension={fileExtension}
          onComplete={() => {
            resolve();
            onClose?.();
          }}
          onError={(e) => {
            reject(e);
            onClose?.();
          }}
        />
      ),
    });
  });
};

export const importDialogScript = async (
  file: File,
  fileExtension: string,
  progressPrepare: (total: number, progress: number, label: string) => void,
  progressImport: (total: number, progress: number, label: string) => void,
): Promise<IFrontScript> => {
  let restImportSize = file.size;

  const files = await unzipFilesAsync(file, (total, progress) => {
    restImportSize -= total - progress;
    progressImport(file.size, file.size - restImportSize, file.name);
  });

  if (!files.length) throw new Error(`Wrong ${fileExtension} file. Has no files`);

  const scriptFileName = getScriptArtefactNameByExtention(fileExtension);
  const scriptFile = files.find((f) => f.name === scriptFileName);

  if (!scriptFile) throw new Error(`${fileExtension} parse failed. ${scriptFileName} missing`);

  const importedScript = await parseJsonFile<IFrontScript>(scriptFile);

  const attachmentsCount = files.length - 1;

  let attachmentsInc = 0;

  for (const step of importedScript.steps) {
    if (!step.attachments) continue;

    for (const atta of step.attachments) {
      const name = atta.externalId ?? atta.name;
      const targetFile = files.find((f) => f.name === name);

      progressPrepare(attachmentsCount, attachmentsInc, atta.name);

      if (!targetFile) {
        console.warn(`Attachment "${name}" is not in ${file.name}`);
        progressPrepare(attachmentsCount, attachmentsInc + 1, atta.name);
        continue;
      }

      const formData = new FormData();
      formData.append('file', targetFile, atta.name);

      const response = await commonFetch(
        `${getSettings().dialogScriptsManagementUrl}/api/attachments`,
        {
          method: 'POST',
          credentials: 'include',
          body: formData,
        },
      );

      const attaData = await response.json();

      Object.assign(atta, attaData);
      progressPrepare(attachmentsCount, attachmentsInc + 1, atta.name);
      attachmentsInc++;
    }
  }

  return importedScript;
};

export const importDialogScriptWithModal = async (
  file: File,
  fileExtension: string,
): Promise<IFrontScript> => {
  return new Promise<IFrontScript>((resolve, reject) => {
    showModal({
      style: { width: 320 },
      canClose: false,
      header: getLocaleMessageById('app.tooltip.export'),
      children: (onClose) => (
        <ModalImport
          file={file}
          fileExtension={fileExtension}
          onComplete={(script) => {
            resolve(script);
            onClose?.();
          }}
          onError={(e) => {
            reject(e);
            onClose?.();
          }}
        />
      ),
    });
  });
};
