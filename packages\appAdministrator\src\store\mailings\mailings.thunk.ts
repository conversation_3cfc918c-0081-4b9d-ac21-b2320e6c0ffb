import { createAsyncThunk } from '@reduxjs/toolkit';

import { getProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';

import { getMailingsAsync, getMailingsMetricsAsync } from '../../services/mailings';
import { getQueues } from '../../services/queues';

export const getAllMailings = createAsyncThunk('mailings/all', async () => {
  return getMailingsAsync();
});

export const getQueuesForMailings = createAsyncThunk('mailings/queues', async () => {
  return getQueues();
});

export const getMailingsMetrics = createAsyncThunk('mailings/metrics', async () => {
  return getMailingsMetricsAsync();
});

export const getAvailableChannels = createAsyncThunk('mailings/channels', async () => {
  return await getProductServicesManager().addressTypes.available();
});

export const getSenders = createAsyncThunk('mailings/senders', () =>
  getProductServicesManager().senders.all(),
);
