import { ChatType } from '../../../../@types/generated/signalr';
import { IChat } from '../../../../@types/signalRTypes';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorName } from '../../../../helpers/operatorHelper';

export function getChatInfo(selectedChat?: IChat): string {
  if (!selectedChat) return '';

  const parts = [];
  if (selectedChat?.ownerId) {
    const operator = selectedChat.participants?.find((pp) => pp.id === selectedChat.ownerId);
    parts.push(
      `${getLocaleMessageById('app.chatCreatedBy')} ${operator ? getOperatorName(operator) : '-'}`,
    );
  } else if (selectedChat.type === ChatType.Global) {
    parts.push(getLocaleMessageById('app.chatCreatedBySystem'));
  }

  if (selectedChat.createdAt) {
    parts.push(
      new Date(Date.parse(selectedChat.createdAt))
        .toLocaleString()
        .slice(0, 17)
        .replace(',', getLocaleMessageById('app.time.at')),
    );
  }

  return parts.join(' ');
}
