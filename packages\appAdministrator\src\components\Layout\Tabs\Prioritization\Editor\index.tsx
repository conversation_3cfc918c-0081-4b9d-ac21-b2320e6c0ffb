import React from 'react';

import clsx from 'clsx';

import {
  utils,
  Button,
  ButtonVariant,
  OverlayLoader,
  Input,
  Select,
  Text,
  TextVariant,
  IconButton,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';
import { checkObjectsEqual } from '@monorepo/common/src/helpers/objectsHelper';

import {
  IFrontPrioritizationAttribute,
  IFrontPrioritizationRule,
  RightPartType,
} from '../../../../../@types/prioritization';
import { needConfirmWhenCompareFalse } from '../../../../../helpers/confirmSave.helper';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { mapPrioritizationParameterDtoToFront } from '../../../../../mappers/prioritization';
import { getPrioritizationAttributes } from '../../../../../services/prioritization';

import ParameterValueInput from './ParameterValueInput';
import PriorityInput from './PriorityInput';
import {
  defaultValidationResult,
  IValidationResult,
  validatePrioritizationRule,
} from './validator';

import styles from './styles.module.scss';

const getEmptyValue = () => ({ key: new Date().toString(), value: '', priority: 1 });

interface IPrioritizationRuleEditorProps {
  rule: IFrontPrioritizationRule | null;
  onSubmit: (rule: IFrontPrioritizationRule) => Promise<void>;
  onClose?: () => void;
}

const PrioritizationRuleEditor = ({ rule, onSubmit, onClose }: IPrioritizationRuleEditorProps) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();
  const [title, setTitle] = React.useState('');
  const [attributeId, setAttributeId] = React.useState<number>(0);
  const [comparisonRule, setComparisonRule] = React.useState<string>('');
  const [values, setValues] = React.useState<IFrontPrioritizationRule['values']>([]);

  const [attributes, setAttributes] = React.useState<IFrontPrioritizationAttribute[]>([]);

  const [validationResult, setValidationResult] = React.useState<IValidationResult>({
    ...defaultValidationResult,
  });

  const newRule = React.useMemo(
    () => ({
      id: rule?.id ?? 0,
      isEnabled: rule?.isEnabled ?? false,
      title,
      attributeId,
      comparisonRule,
      values,
    }),
    [rule, title, attributeId, comparisonRule, values],
  );

  const selectedAttribute = React.useMemo(
    () => attributes.find((a) => a.id === attributeId),
    [attributes, attributeId],
  );

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const newValidationResult = validatePrioritizationRule(newRule, selectedAttribute);
    setValidationResult(newValidationResult);
    if (newValidationResult.isErrored) {
      return;
    }
    setLoading(true);
    setError(undefined);
    try {
      await onSubmit(newRule);
      onClose?.();
    } catch (err) {
      console.error('Error saving rule', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    (async () => {
      setLoading(true);
      const parameters = await getPrioritizationAttributes();
      setAttributes(parameters.map(mapPrioritizationParameterDtoToFront));
      setLoading(false);
    })();
  }, []);

  React.useEffect(() => {
    setTitle(rule?.title ?? '');
    setAttributeId(rule?.attributeId ?? 0);
    setComparisonRule(rule?.comparisonRule ?? '');
    setValues(rule?.values ?? [{ ...getEmptyValue() }]);
  }, [rule]);

  React.useEffect(() => {
    if (selectedAttribute?.rightPartType !== RightPartType.None) return;

    setValues([{ ...getEmptyValue(), priority: rule?.values[0].priority ?? 1 }]);
  }, [rule?.values, selectedAttribute]);

  React.useEffect(() => {
    if (!validationResult.isErrored) return;

    setValidationResult(validatePrioritizationRule(newRule, selectedAttribute));
  }, [newRule, selectedAttribute, validationResult.isErrored]);

  const onPriorityChange = (key: string, value: number) => {
    setValues((current) =>
      current.map((curV) => {
        if (curV.key === key) return { ...curV, priority: value };

        return curV;
      }),
    );
  };

  const onParameterValueChange =
    (parameter: IFrontPrioritizationRule['values'][number]) => (value: string) => {
      const updateParameter = (currentParameter: IFrontPrioritizationRule['values'][number]) => {
        if (currentParameter.key !== parameter.key) return currentParameter;

        return { ...currentParameter, value };
      };

      setValues((current) => current.map(updateParameter));
    };

  const deleteParameter = (key: string) => {
    setValues((current) => current.filter((curV) => curV.key !== key));
  };

  return (
    <OverlayLoader loading={loading}>
      <form className={clsx(utils.dFlex, utils.flexColumn, styles.editor)} onSubmit={handleSubmit}>
        <div
          className={clsx(
            utils.pX6,
            utils.pY4,
            utils.flexBasis0,
            utils.flexGrow1,
            utils.overflowAuto,
            utils.scrollbar,
            utils.dFlex,
            utils.flexColumn,
            utils.gap5,
          )}
        >
          <label className={clsx(utils.dGrid, utils.gap2)}>
            <Text variant={TextVariant.BodySemibold}>
              {getLocaleMessageById('prioritization.editor.form.title')}
            </Text>
            <Input
              value={title}
              onChange={({ value }) => setTitle(value ?? '')}
              required
              isInvalid={!!validationResult.title}
              message={<InputErrorMessage>{validationResult.title}</InputErrorMessage>}
            />
          </label>
          <Text variant={TextVariant.BodySemibold}>
            {getLocaleMessageById('prioritization.editor.form.condition')}
          </Text>
          <div className={clsx(utils.dFlex, utils.gap2)}>
            <Select
              wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1)}
              label={getLocaleMessageById('prioritization.editor.form.if')}
              value={attributeId?.toString() ?? ''}
              data={attributes.map((a) => ({ value: a.id.toString(), text: a.displayName }))}
              onChange={({ value }) => {
                setAttributeId(value ? Number(value) : 0);
                setValues(rule?.values ?? [{ ...getEmptyValue() }]);
              }}
              required
              isInvalid={!!validationResult.attributeId}
              message={<InputErrorMessage>{validationResult.attributeId}</InputErrorMessage>}
            />
            {selectedAttribute?.rightPartType === RightPartType.None ? (
              <PriorityInput
                valueKey={values[0].key}
                value={values[0].priority}
                onChange={(value) => onPriorityChange(values[0].key, value)}
                validationResult={validationResult}
              />
            ) : (
              <Select
                wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1)}
                placeholder={getLocaleMessageById('prioritization.editor.form.action')}
                value={comparisonRule}
                data={
                  selectedAttribute?.comparisonRules.map((r) => ({
                    value: r.code,
                    text: r.name,
                  })) ?? []
                }
                onChange={({ value }) => setComparisonRule(value ?? '')}
                disabled={!attributeId}
                required
                isInvalid={!!validationResult.comparisonRule}
                message={<InputErrorMessage>{validationResult.comparisonRule}</InputErrorMessage>}
              />
            )}
          </div>
          {selectedAttribute?.rightPartType !== RightPartType.None &&
            values.map((v) => (
              <div
                key={v.key}
                className={clsx(utils.w100, utils.dFlex, utils.gap2, utils.alignItemsCenter)}
              >
                <IconDropRight />
                <ParameterValueInput
                  type={selectedAttribute?.rightPartType}
                  data={selectedAttribute?.options.map((o) => ({
                    value: o.value,
                    text: o.name,
                  }))}
                  value={v.value}
                  onChange={onParameterValueChange(v)}
                  disabled={!attributeId}
                  isInvalid={!!validationResult.values[v.key]?.value}
                  message={
                    <InputErrorMessage>{validationResult.values[v.key]?.value}</InputErrorMessage>
                  }
                />
                <PriorityInput
                  valueKey={v.key}
                  value={v.priority}
                  onChange={(value) => onPriorityChange(v.key, value)}
                  validationResult={validationResult}
                />
                <IconButton disabled={values.length <= 1} onClick={() => deleteParameter(v.key)}>
                  <IconTrash />
                </IconButton>
              </div>
            ))}
          {selectedAttribute?.rightPartType !== RightPartType.None && (
            <Button
              className={utils.gap2}
              style={{ alignSelf: 'flex-start' }}
              variant={ButtonVariant.Secondary}
              onClick={() => setValues((current) => [...current, getEmptyValue()])}
            >
              <IconAdd />
              {getLocaleMessageById('prioritization.editor.form.addValue')}
            </Button>
          )}
        </div>
        <footer
          className={clsx(
            utils.borderTop,
            utils.pX6,
            utils.pY4,
            utils.dFlex,
            utils.gap2,
            utils.justifyContentEnd,
          )}
        >
          {error && (
            <AlertError
              className={utils.flexGrow1}
              header={getLocaleMessageById('prioritization.save.error')}
              error={error}
            />
          )}
          <Button
            variant={ButtonVariant.Secondary}
            onClick={() => {
              const isObjectsEqual = checkObjectsEqual(rule, newRule);
              needConfirmWhenCompareFalse(isObjectsEqual, onClose);
            }}
          >
            {getLocaleMessageById('app.common.cancel')}
          </Button>
          <Button type="submit">{getLocaleMessageById('app.common.save')}</Button>
        </footer>
      </form>
    </OverlayLoader>
  );
};

export default PrioritizationRuleEditor;
