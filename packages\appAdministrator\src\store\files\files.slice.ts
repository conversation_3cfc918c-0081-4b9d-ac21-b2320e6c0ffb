import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { FrontFileOrFolder, IFrontFolder } from '../../@types/files';

import extraReducers from './files.extraReducers';

export interface IFilesStore {
  loading: boolean;
  error?: SerializedError;
  foldersAndFiles: FrontFileOrFolder[];
  displayedFolder?: IFrontFolder;
  selectedItem?: FrontFileOrFolder;
  searchString: string;
}

const initialState: IFilesStore = {
  loading: false,
  foldersAndFiles: [],
  searchString: '',
};

const filesSlice = createSlice({
  name: 'subjects',
  initialState,
  reducers: {
    selectDisplayedFolder(state, action: PayloadAction<IFrontFolder | undefined>) {
      state.displayedFolder = action.payload;
      state.selectedItem = action.payload?.items?.[0];
    },
    setSelectedItem(state, action: PayloadAction<FrontFileOrFolder | undefined>) {
      state.selectedItem = action.payload;
    },
    setSearchString(state, action: PayloadAction<string>) {
      state.searchString = action.payload;
    },
  },
  extraReducers,
});

export const { selectDisplayedFolder, setSelectedItem, setSearchString } = filesSlice.actions;

export default filesSlice.reducer;
