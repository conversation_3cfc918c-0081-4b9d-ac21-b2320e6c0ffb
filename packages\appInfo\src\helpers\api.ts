import { Guid } from 'guid-typescript';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import { TimePeriod, timePeriods } from '@monorepo/common/src/helpers/timePeriods';

import { IOdataRequest } from '../@types/Requests';
import { getSettings } from '../config/appSettings';
import mapOdataRequestToFront from '../mappers/mapOdataRequestToFront';

interface IGetRequestsWithoutPaginationParams {
  operatorId: Guid;
  timePeriod: TimePeriod;
  additionalFilter?: string;
  sortString?: string;
}

export const getRequestsWithoutPagination = async ({
  operatorId,
  timePeriod,
  additionalFilter,
  sortString,
}: IGetRequestsWithoutPaginationParams) => {
  const timeData = timePeriods[timePeriod];

  const timeFrom = timeData.from ? ` and TimeRegistered gt ${timeData.from}` : '';
  const timeTo = timeData.to ? ` and TimeRegistered lt ${timeData.to}` : '';

  const appSettings = getSettings();

  const data = await commonFetch(
    `${
      appSettings.dataPresentationServiceUrl
    }DataPresentation/Requests?$filter=ExecutorId eq ${operatorId}${timeFrom}${timeTo}${
      additionalFilter ?? ''
    }&$orderBy=${sortString}`,
    { credentials: 'include' },
  );
  const json = (await data.json()) as {
    error?: { message: string };
    value: IOdataRequest[];
  };

  if (json.error) {
    throw new Error(json.error.message);
  }

  return json.value.map(mapOdataRequestToFront);
};
