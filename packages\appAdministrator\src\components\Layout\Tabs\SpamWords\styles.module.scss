.quickFilter {
  --textColor: var(--palette-moodBlue-70);
  --backgroundColor: var(--palette-hollywoodSmile);

  cursor: pointer;

  border: none;

  color: var(--textColor);
  text-decoration: underline;
  text-decoration-color: transparent;
  text-transform: uppercase;

  background: var(--backgroundColor);

  transition:
    text-decoration-color 0.2s ease-in-out,
    color 0.2s ease-in-out,
    background-color 0.2s ease-in-out;
}

.quickFilter[data-active='true'] {
  color: var(--backgroundColor);
  background-color: var(--textColor);
}

.quickFilter:hover,
.quickFilter:focus-within {
  text-decoration-color: var(--textColor);
}

.word {
  cursor: pointer;
  display: inline-block;
  width: max-content;
  padding: 0 4px;
}

.word:hover,
.word:focus-within {
  background-color: var(--palette-moodBlue-20);
}

.word[data-active='true'] {
  color: var(--palette-hollywoodSmile);
  background-color: var(--palette-moodBlue-70);
}
