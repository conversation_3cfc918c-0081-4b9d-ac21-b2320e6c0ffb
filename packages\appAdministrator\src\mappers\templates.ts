import { helpers } from '@product.front/ui-kit';

import { IFrontAttachment, UcmmChannel } from '@monorepo/common/src/@types/frontendChat';
import {
  FrontTemplateStatus,
  IFrontTemplate,
  IFrontTemplateVersion,
} from '@monorepo/common/src/@types/templates';
import { normalizeUrl } from '@monorepo/common/src/common/helpers/url.helper';

import {
  AnswerTemplate,
  AnswerTemplateAddEdit,
  Attachment,
  AutoReplyAddEdit,
  AutoReplyTemplate,
  ChannelVersion,
  PersonalTemplate,
  TemplateStatus,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export const mapTemplateAttachToFront =
  (baseAttachmentsUrl: string) =>
  (a: Attachment): IFrontAttachment => {
    return {
      id: a.id,
      name: a.name,
      url: normalizeUrl([baseAttachmentsUrl, a.externalId].join('/')),
      extension: a.extension ?? helpers.getExtensionByFileName(a.name) ?? 'unknown',
      externalId: a.externalId ?? undefined,
    };
  };

export const getCurrentVersion = (template: AnswerTemplate | AutoReplyTemplate) =>
  template.versions.find(
    (v) =>
      new Date(v.interval.from) <= new Date() &&
      (!v.interval.to || new Date(v.interval.to) >= new Date()),
  );

export const mapTemplateStatusToFront = (status: TemplateStatus): FrontTemplateStatus =>
  ({
    [TemplateStatus.Archived]: FrontTemplateStatus.Archived,
    [TemplateStatus.Draft]: FrontTemplateStatus.Draft,
    [TemplateStatus.Published]: FrontTemplateStatus.Published,
  })[status];

const mapTemplateVersionToFront = (version: ChannelVersion | undefined): IFrontTemplateVersion => ({
  id: version?.id ?? '',
  contents: [
    ...(version?.channelContents?.map((c) => ({
      id: '',
      text: c.text ?? '',
      channel: c.channel as UcmmChannel,
    })) ?? []),
    ...[
      {
        id: '',
        channel: UcmmChannel.Default,
        text: version?.content.text ?? '',
      },
    ],
  ],
  attachments:
    version?.attachments?.map(mapTemplateAttachToFront(getSettings().productPublicFileServer)) ??
    [],
  interval: {
    from: version?.interval.from ?? '',
    to: version?.interval.to ?? null,
  },
});

export const mapTemplateToFrontTemplate = (
  template: AnswerTemplate | AutoReplyTemplate,
): IFrontTemplate => {
  const currentVersion = template.versions.find(
    (v) =>
      new Date(v.interval.from) <= new Date() &&
      (!v.interval.to || new Date(v.interval.to) >= new Date()),
  );
  return {
    id: template.id,
    code: template.code,
    status: mapTemplateStatusToFront(
      (template as AnswerTemplate).status ?? TemplateStatus.Published,
    ),
    title: template.title,
    folderId: template.folderId ?? null,
    ownerId: null,
    currentVersion: mapTemplateVersionToFront(currentVersion),
    keyWords: template.keywords?.map(({ word }) => ({ id: '', word })) ?? [],
    versions: template.versions.map((v) => mapTemplateVersionToFront(v)),
  };
};

export const mapPersonalTemplateToFrontTemplate = (template: PersonalTemplate): IFrontTemplate => {
  const currentVersion: ChannelVersion = {
    id: '',
    channelContents: [],
    interval: {
      from: new Date().toISOString(),
      to: null,
    },
    content: template.content,
    attachments: template.attachments,
  };
  return {
    id: template.id,
    code: template.code,
    status: mapTemplateStatusToFront(TemplateStatus.Published),
    title: template.title,
    folderId: template.folderId ?? null,
    ownerId: null,
    currentVersion: mapTemplateVersionToFront(currentVersion),
    keyWords: template.keywords?.map(({ word }) => ({ id: '', word })) ?? [],
    versions: [],
  };
};

export const mapFrontTemplateStatusToDto = (status: FrontTemplateStatus): TemplateStatus =>
  ({
    [FrontTemplateStatus.Archived]: TemplateStatus.Archived,
    [FrontTemplateStatus.Draft]: TemplateStatus.Draft,
    [FrontTemplateStatus.Published]: TemplateStatus.Published,
  })[status];

const mapFrontTemplateVersionToDto = (version: IFrontTemplateVersion) => ({
  id: version.id || undefined,
  content: {
    text: version.contents.find((c) => c.channel === UcmmChannel.Default)?.text ?? '',
  },
  attachments: version.attachments.map((a) => ({
    id: a.id || undefined,
    name: a.name,
    externalId: a.externalId ?? a.url.split('/').pop() ?? '',
    extension: a.extension,
    url: a.url,
  })),
  interval: version.interval,
  channelContents: version.contents
    .filter((c) => c.channel !== UcmmChannel.Default)
    .map((c) => ({ text: c.text, channel: c.channel })),
});

export const mapFrontTemplateToDto = (
  template: IFrontTemplate,
  kind: 'AnswerTemplateAddEdit' | 'AutoReplyAddEdit',
): AnswerTemplateAddEdit | AutoReplyAddEdit => ({
  id: template.id || undefined,
  code: template.code,
  status: mapFrontTemplateStatusToDto(template.status),
  title: template.title,
  folderId: template.folderId || undefined,
  keywords: template.keyWords?.map(({ word }) => ({ word })) ?? [],
  kind,
  versions: template.versions.map(mapFrontTemplateVersionToDto),
});
