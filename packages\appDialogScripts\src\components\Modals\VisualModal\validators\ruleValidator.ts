import {
  IFrontRule,
  IFrontRuleInvalidReasons,
  IFrontStep,
} from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../../helpers/localeHelper';

export const getRuleInvalidReasons = (rule: IFrontRule, nextStep?: IFrontStep) => {
  const reasons: IFrontRuleInvalidReasons = {};

  if (!rule.transferTo || (rule.transferTo === 'default' && !nextStep)) {
    reasons.transition = getLocaleMessageById('app.modals.formValidation.invalidRelation');
  }

  return reasons;
};

export const getRuleDeepInvalidReasons = (rule: IFrontRule) => {
  const reasons: IFrontRuleInvalidReasons = {};

  if (!rule.conditions.length) {
    reasons.conditionsRequired = getLocaleMessageById('app.modals.formValidation.listItemRequired');
  }

  if (rule.conditions.find((x) => !x.value || !x.variable)) {
    reasons.conditionsRequired = getLocaleMessageById('app.modals.formValidation.valueRequired');
  }

  if (!rule.name) {
    reasons.ruleName = getLocaleMessageById('app.modals.formValidation.valueRequired');
  }

  return reasons;
};
