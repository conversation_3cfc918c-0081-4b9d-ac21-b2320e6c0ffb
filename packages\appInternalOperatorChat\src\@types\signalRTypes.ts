import { Attachment, Chat, Message } from './generated/signalr';

export enum SignalMethods {
  GetProfile = 'GetProfile',
  GetMessageHistory = 'GetMessageHistory',
  SendMessage = 'SendMessage',
  ReceiveMessage = 'receivemessage',
  ReceiveOperatorChats = 'receiveoperatorchats',
  ReceiveNotification = 'ReceiveNotification',
  Read = 'Read',
  CreateChat = 'CreateChat',
  UpdateChat = 'UpdateChat',
  AddUsersToChat = 'AddUsersToChat',
  RemoveUsersFromChat = 'RemoveUsersFromChat',
  ConnectToChat = 'ConnectToChat',
  DeleteChat = 'DeleteChat',
}

export interface IChat extends Chat {
  isSelected: boolean;
  newDividerPrevMessage?: string;
}

export interface IMessageHistoryRequestData {
  chatId: string;
  offset: number;
  limit: number;
}

export interface IMessageSendRequestData {
  chatId: string;
  text: string;
  attachments?: Attachment[] | null;
  replyMessage: Message | null;
}
