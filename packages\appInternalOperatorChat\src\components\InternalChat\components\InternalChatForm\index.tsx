import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { Dropdown, DropdownPosition, IconButton, Tooltip, utils } from '@product.front/ui-kit';

import IconClip from '@product.front/icons/dist/icons17/Chat&Mail/IconClip';
import IconSmile from '@product.front/icons/dist/icons17/Chat&Mail/IconSmile';

import ChatEmojiPicker from '@monorepo/common/src/components/Chat/ChatEmojiPicker';
import ChatInput from '@monorepo/common/src/components/Chat/ChatInput';
import ChatReplyMessage from '@monorepo/common/src/components/Chat/ChatReplyMessage';

import { Attachment } from '../../../../@types/generated/signalr';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorName } from '../../../../helpers/operatorHelper';
import { mapBackendAttachModelToFront } from '../../../../mappers/dtoMappers';
import { useInternalChatDispatch, useInternalChatSelector } from '../../../../store/hooks';
import {
  dropUnreadCountForChat,
  markChatMessagesRead,
  sendMessageData,
  setReplyMessage,
} from '../../../../store/internalChat.slice';
import InternalChatFile from '../InternalChatFile';

import styles from './styles.module.scss';

export function getExtensionByFileName(fileName: string): string | undefined {
  // eslint-disable-next-line sonarjs/slow-regex
  const r = /.*\.(.+)$/.exec(fileName);
  return r ? r[1] : undefined;
}

const InternalChatForm: React.FC<HTMLAttributes<HTMLDivElement>> = ({ className, ...rest }) => {
  const dispatch = useInternalChatDispatch();

  const { isMessageSending, chats, chatSendError, replyMessage } = useInternalChatSelector(
    (state) => state.internalChat,
  );

  const selectedChat = chats.find((chat) => chat.isSelected);

  const inpRef = React.useRef<HTMLTextAreaElement>(null);
  const fileInpRef = React.useRef<HTMLInputElement>(null);

  const [inputValue, setInputValue] = React.useState<string>('');
  const [files, setFiles] = React.useState<File[]>([]);
  const [uploadedFiles, setUploadedFiles] = React.useState<{ [nameSizeKey: string]: Attachment }>(
    {},
  );

  React.useEffect(() => {
    const input = inpRef.current;
    if (!input) {
      return;
    }

    const handleInputFocus = () => {
      if (selectedChat?.id) {
        dispatch(markChatMessagesRead(selectedChat.id));
        dispatch(dropUnreadCountForChat(selectedChat.id));
      }
    };

    /**
     * Костылёк: считаем что пользователь прочитал чат когда сфокусировался на поле ввода
     */
    input.addEventListener('focus', handleInputFocus);

    return () => input.removeEventListener('focus', handleInputFocus);
  }, [dispatch, inpRef, selectedChat]);

  const handleInputChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLSelectElement> = (
    e,
  ) => {
    setInputValue(e.currentTarget.value);
  };

  return (
    <>
      <footer
        className={clsx(utils.pX6, utils.pY4, styles.chatForm, utils.borderTop, className)}
        {...rest}
      >
        <Tooltip tooltip={getLocaleMessageById('app.tooltip.addAttachment')}>
          <IconButton
            className={clsx(utils.mR1, utils.mLn2)}
            onClick={() => fileInpRef.current && fileInpRef.current.click()}
          >
            <IconClip />
          </IconButton>
        </Tooltip>
        <input
          ref={fileInpRef}
          type="file"
          onChange={(e) => {
            const oldFiles = Array.from(files);
            const newFiles = Array.from(e.target.files || []).filter(
              (f) => !files.find((fi) => f.name === fi.name && f.size === fi.size),
            );

            setFiles([...oldFiles, ...newFiles]);

            e.target.value = '';
          }}
          multiple
          className={styles.hiddenFileInput}
        />
        <Dropdown
          menu={
            <ChatEmojiPicker
              className={utils.mYn3}
              onSelect={(s) => {
                const inp = inpRef.current;
                if (!inp) {
                  return;
                }

                inp.setRangeText(s);
                const selectionTarget = inp.selectionStart + s.length;
                inp.setSelectionRange(selectionTarget, selectionTarget);
                inp.dispatchEvent(new Event('input', { bubbles: true }));
                inp.focus();
              }}
            />
          }
          closeOnMenuClick={false}
          position={DropdownPosition.TopLeft}
          className={clsx(utils.mR1)}
        >
          <Tooltip tooltip={getLocaleMessageById('app.tooltip.smiles')}>
            <IconButton>
              <IconSmile />
            </IconButton>
          </Tooltip>
        </Dropdown>
        <ChatInput
          className={clsx(styles.inp, utils.mL3)}
          ref={inpRef}
          submitDisabled={files.length === 0 && inputValue === ''}
          allowEmpty={files.length !== 0}
          onSend={(text) => {
            if (!selectedChat?.id) {
              throw new Error('Selected chat is not defined (for send)');
            }
            dispatch(
              sendMessageData({
                chatId: selectedChat.id,
                text,
                attachments: Object.values(uploadedFiles),
                // @todo понять почему не null после удаления сообщения ответа
                replyMessage: replyMessage?.id ? replyMessage : null,
              }),
            );
            dispatch(dropUnreadCountForChat(selectedChat.id));
            dispatch(markChatMessagesRead(selectedChat.id));
            setFiles([]);
            setUploadedFiles({});
            if (fileInpRef.current) {
              fileInpRef.current.value = '';
            }
            setInputValue('');

            setTimeout(() => inpRef.current && inpRef.current.focus(), 600);
          }}
          sending={isMessageSending}
          error={chatSendError?.message}
          placeholder={getLocaleMessageById('app.placeholder.message')}
          onChange={handleInputChange}
          onPaste={(e) => {
            if (e.clipboardData.files) {
              const oldFiles = Array.from(files);
              const newFiles = Array.from(e.clipboardData.files || []).filter(
                (f) => !files.find((fi) => f.name === fi.name && f.size === fi.size),
              );
              setFiles([...oldFiles, ...newFiles]);
            }
          }}
        >
          <>
            {replyMessage?.id && (
              <div className={clsx(utils.mX3, utils.pY3, utils.borderBottom)}>
                <ChatReplyMessage
                  from={replyMessage.author ? getOperatorName(replyMessage.author) : ''}
                  body={replyMessage?.text || ''}
                  onClose={() => {
                    dispatch(setReplyMessage(null));
                    inpRef.current && inpRef.current.focus();
                  }}
                  attachments={replyMessage.attachments?.map(mapBackendAttachModelToFront)}
                  disable={isMessageSending}
                />
              </div>
            )}
            {files.length > 0 && (
              <section
                className={clsx(utils.dFlex, utils.p2, utils.borderBottom)}
                style={{ flexWrap: 'wrap' }}
              >
                {files.map((file: File) => {
                  const fileSizeKey = file.name + file.size;
                  return (
                    <InternalChatFile
                      url={uploadedFiles[fileSizeKey]?.url}
                      file={file}
                      key={fileSizeKey}
                      onLoad={(attachment) => {
                        setUploadedFiles((alreadyLoaded) => {
                          return { ...alreadyLoaded, [fileSizeKey]: attachment };
                        });
                      }}
                      onRemove={() => {
                        delete uploadedFiles[fileSizeKey];
                        setFiles(
                          files.filter((f) => !(f.name === file.name && f.size === file.size)),
                        );
                      }}
                      className={styles.attachItem}
                    />
                  );
                })}
              </section>
            )}
          </>
        </ChatInput>
      </footer>
    </>
  );
};

export default InternalChatForm;
