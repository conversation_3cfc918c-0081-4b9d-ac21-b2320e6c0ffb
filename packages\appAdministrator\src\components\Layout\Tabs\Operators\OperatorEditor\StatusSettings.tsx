import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  FloatingDropdown,
  grids,
  IconButton,
  Input,
  Menu,
  MenuItem,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { IFrontStatusSettings } from '../../../../../@types/operator';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { IStatusData } from '../../OperatorGroups/OperatorGroupEditor';

import { IValidationResult } from './validator';

interface IStatusSettingsProps {
  statusSettings: IFrontStatusSettings;
  onUpdateStatusSettings: (statusSettings: IFrontStatusSettings) => void;
  statusData: IStatusData;
  validationResult: IValidationResult['statuses'];
}

const StatusSettings = ({
  statusSettings,
  onUpdateStatusSettings,
  statusData,
  validationResult,
}: IStatusSettingsProps) => {
  const [maxBreakTime, setMaxBreakTime] = React.useState<number | null>(
    statusSettings.maxBreakTime ?? null,
  );
  const [statusMap, setStatusMap] = React.useState<Record<string, number>>(
    statusSettings.maxStatusTime.reduce(
      (acc, { status, maxTime }) => ({
        ...acc,
        [status]: maxTime,
      }),
      {},
    ),
  );

  const statusesMap: Record<string, IStatusData[number]> = React.useMemo(() => {
    return statusData.reduce((acc, status) => ({ ...acc, [status.code]: status }), {});
  }, [statusData]);

  const filteredStatusesMap: Record<string, IStatusData[number]> = React.useMemo(() => {
    return Object.keys(statusesMap)
      .filter((key) => !Object.keys(statusMap).some((code) => key === code))
      .reduce(
        (acc, status) => ({
          ...acc,
          [status]: statusesMap[status],
        }),
        {},
      );
  }, [statusesMap, statusMap]);

  const updateStatusSettings = React.useCallback(() => {
    const newStatusSettings = {
      maxBreakTime,
      maxStatusTime: Object.entries(statusMap).map(([status, maxTime]) => ({
        status,
        maxTime,
      })),
    } satisfies IFrontStatusSettings;

    onUpdateStatusSettings(newStatusSettings);
  }, [maxBreakTime, onUpdateStatusSettings, statusMap]);

  React.useEffect(() => {
    updateStatusSettings();
  }, [updateStatusSettings]);

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4, utils.alignItemsStart)}>
      <Text variant={TextVariant.SubheadSemibold}>
        {getLocaleMessageById('operators.modal.form.breakLimit')}
      </Text>
      {maxBreakTime !== null ? (
        <div className={clsx(grids.row, utils.gap2, utils.alignItemsCenter, utils.w100)}>
          <Text className={grids.col3} variant={TextVariant.BodySemibold}>
            {getLocaleMessageById('operatorGroups.editor.maxBreakTime')}
          </Text>
          <Input
            wrapperClassName={grids.col4}
            label={getLocaleMessageById('app.common.hours')}
            min={0}
            max={24}
            value={Math.floor(maxBreakTime / (60 * 60)).toString()}
            onChange={({ value }) =>
              setMaxBreakTime((current) => Number(value) * 60 * 60 + ((current ?? 0) % (60 * 60)))
            }
            type="number"
            isInvalid={!!validationResult.maxBreakTime}
          />
          <Input
            wrapperClassName={grids.col4}
            label={getLocaleMessageById('app.common.minutes')}
            min={0}
            max={59}
            value={(Math.floor(maxBreakTime / 60) % 60).toString()}
            onChange={({ value }) =>
              setMaxBreakTime(
                (current) => (current ? current - (current % (60 * 60)) : 0) + Number(value) * 60,
              )
            }
            type="number"
            isInvalid={!!validationResult.maxBreakTime}
          />
          <IconButton className={grids.col1} onClick={() => setMaxBreakTime(null)}>
            <IconTrash />
          </IconButton>
          {validationResult.maxBreakTime && (
            <div className={grids.col12}>
              <InputErrorMessage>{validationResult.maxBreakTime}</InputErrorMessage>
            </div>
          )}
        </div>
      ) : (
        <Button
          variant={ButtonVariant.Transparent}
          className={clsx(utils.gap2, utils.p0)}
          onClick={() => setMaxBreakTime(60 * 60)}
        >
          <IconAdd />
          {getLocaleMessageById('app.common.add')}
        </Button>
      )}
      <Text variant={TextVariant.SubheadSemibold}>
        {getLocaleMessageById('operators.modal.form.statusLimits')}
      </Text>
      {Object.keys(statusMap).map((code) => (
        <div key={code} className={clsx(grids.row, utils.gap2, utils.alignItemsCenter, utils.w100)}>
          <Text className={grids.col3} variant={TextVariant.BodySemibold}>
            {statusesMap[code].title}
          </Text>
          <Input
            wrapperClassName={grids.col4}
            label={getLocaleMessageById('app.common.hours')}
            min={0}
            value={Math.floor(statusMap[code] / (60 * 60)).toString()}
            onChange={({ value }) =>
              setStatusMap((current) => ({
                ...current,
                [code]: Number(value) * 60 * 60 + ((current[code] ?? 0) % (60 * 60)),
              }))
            }
            type="number"
            isInvalid={!!validationResult.statusesErrors[code]}
          />
          <Input
            wrapperClassName={grids.col4}
            label={getLocaleMessageById('app.common.minutes')}
            min={0}
            max={59}
            value={(Math.floor(statusMap[code] / 60) % 60).toString()}
            onChange={({ value }) =>
              setStatusMap((current) => ({
                ...current,
                [code]:
                  (current[code] ? current[code] - (current[code] % (60 * 60)) : 0) +
                  Number(value) * 60,
              }))
            }
            type="number"
            isInvalid={!!validationResult.statusesErrors[code]}
          />
          <IconButton
            className={grids.col1}
            onClick={() =>
              setStatusMap((current) => {
                delete current[code];
                return { ...current };
              })
            }
          >
            <IconTrash />
          </IconButton>
          {validationResult.statusesErrors[code] && (
            <div className={grids.col12}>
              <InputErrorMessage>{validationResult.statusesErrors[code]}</InputErrorMessage>
            </div>
          )}
        </div>
      ))}
      {Object.keys(filteredStatusesMap).length > 0 && (
        <FloatingDropdown
          menu={
            <Menu>
              {Object.keys(filteredStatusesMap).map((code) => (
                <MenuItem
                  key={code}
                  onClick={() =>
                    setStatusMap((current) => ({ ...current, [code]: maxBreakTime ?? 0 }))
                  }
                >
                  {statusesMap[code].title}
                </MenuItem>
              ))}
            </Menu>
          }
        >
          <Button variant={ButtonVariant.Transparent} className={clsx(utils.gap2, utils.p0)}>
            <IconAdd />
            {getLocaleMessageById('operatorGroups.editor.addStatusLimit')}
          </Button>
        </FloatingDropdown>
      )}
    </div>
  );
};

export default StatusSettings;
