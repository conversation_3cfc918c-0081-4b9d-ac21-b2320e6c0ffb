import React from 'react';

import clsx from 'clsx';

import {
  Avatar,
  AvatarVariant,
  Colors,
  IconButton,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconChat from '@product.front/icons/dist/icons17/Chat&Mail/IconChat';
import IconMore from '@product.front/icons/dist/icons17/Sorting/IconMore';

import ErrorBoundaryContainer from '@monorepo/common/src/common/components/Errors/ErrorBoundary/Container';
import { getPluralGroup } from '@monorepo/common/src/helpers/localeHelper';

import { getAbbr } from '../../../../helpers/abbrHelper';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorName } from '../../../../helpers/operatorHelper';
import { useInternalChatDispatch, useInternalChatSelector } from '../../../../store/hooks';
import { toggleChatInfo } from '../../../../store/internalChat.slice';
import InternalChatForm from '../InternalChatForm';
import InternalChatHeader from '../InternalChatHeader';
import InternalChatMessagesList from '../InternalChatMesagesList';

import styles from './styles.module.scss';

const InternalChatMain = () => {
  const dispatch = useInternalChatDispatch();

  const { chats } = useInternalChatSelector((state) => state.internalChat);

  const selectedChat = chats.find((chat) => chat.isSelected);

  if (!selectedChat) {
    return (
      <section
        className={clsx(
          utils.h100,
          utils.dFlex,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
          styles.chatsMain,
        )}
      >
        <Text
          variant={TextVariant.CaptionMedium}
          color={Colors.OnyxBlack60}
          className={clsx(utils.alignItemsCenter, utils.textCenter, utils.mB6)}
        >
          <IconChat width={64} className={utils.mR2} />
          <br />
          {getLocaleMessageById('app.chat.chatNotSelected')}
        </Text>
      </section>
    );
  }

  const chatMembers =
    selectedChat?.participants?.map((operator) => getOperatorName(operator)) || [];

  const chatMembersCount = chatMembers?.length;

  return (
    <section className={clsx(utils.h100, styles.chatsMain)}>
      <ErrorBoundaryContainer canRetry>
        <InternalChatHeader className={clsx(utils.borderBottom)}>
          <div
            className={clsx(utils.dFlex, utils.alignItemsCenter, utils.pY2)}
            style={{ minWidth: 0, flexGrow: 1 }}
          >
            <Avatar
              alt={getAbbr(selectedChat.name || '')}
              colorize={selectedChat.id}
              className={clsx(utils.mR4)}
              variant={AvatarVariant.Dark}
            />
            <div style={{ minWidth: 0, flexGrow: 3, width: 0 }}>
              <Text variant={TextVariant.SubheadMedium} as="div">
                {selectedChat.name}
              </Text>
              <Text
                variant={TextVariant.CaptionMedium}
                noWrap
                ellipsis
                className={styles.chatMembersIntro}
                color={Colors.OnyxBlack70}
              >
                <small title={chatMembers?.join('\n')}>
                  <strong>
                    {getLocaleMessageById('app.chat.membersNumber', {
                      count: chatMembersCount,
                      countGroup: getPluralGroup(chatMembersCount),
                    })}
                  </strong>
                  : {[...chatMembers].join(', ')}
                </small>
              </Text>
            </div>
            <div>
              <IconButton onClick={() => dispatch(toggleChatInfo())}>
                <IconMore />
              </IconButton>
            </div>
          </div>
        </InternalChatHeader>
        <div className={clsx(utils.dFlex, utils.flexColumn, styles.mainContainer)}>
          <InternalChatMessagesList className={styles.listMsgs} />
          <InternalChatForm />
        </div>
      </ErrorBoundaryContainer>
    </section>
  );
};

export default InternalChatMain;
