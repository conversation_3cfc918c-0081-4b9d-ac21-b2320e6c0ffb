import React, { useEffect, useState } from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import { InputSize, MenuItem, Select, Text, utils } from '@product.front/ui-kit';

import IconPopUp from '@product.front/icons/dist/icons17/Other/IconPopUp';
import IconHierarchy from '@product.front/icons/dist/icons17/Sorting/IconHierarchy';

import { IFrontStep } from '../../../../../@types/script';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { useDialogScriptsAppSelector } from '../../../../../store/hooks';
import InputErrorMessage from '../../../../InputErrorMessage';

interface IVariableSelectProps {
  placeHolderResourceId: string;
  variables: Record<string, IFrontStep['variableName']>;
  disabled?: boolean;
  variable?: string | null;
  onChange: (variable: string | null, variableName: string | null) => void;
  wrapperClassName: string;
  size: InputSize;
  message?: React.ReactNode;
  isInvalid?: boolean;
  viewLabel?: boolean;
}

enum IFrontVariableType {
  Local,
  Sources,
}

const VariableSelect = ({
  placeHolderResourceId,
  variables,
  disabled,
  variable,
  onChange,
  wrapperClassName,
  size,
  isInvalid,
  message,
  viewLabel = true,
}: IVariableSelectProps) => {
  const { sources } = useDialogScriptsAppSelector((state) => state.externalData);

  const placeHolder = getLocaleMessageById(placeHolderResourceId);

  const [allVariables, setAllVariables] = useState<ISelectDataItem[]>([]);
  const [selectedVariable, setSelectedVariable] = useState<string | undefined | null>(undefined);

  useEffect(() => {
    const av = [
      ...(
        Object.entries(variables).map(([code, name]) => ({
          value: code,
          text: name || code,
          data: {
            type: IFrontVariableType.Local,
          },
        })) as ISelectDataItem[]
      ).sort((a, b) => a.text.localeCompare(b.text) ?? 0),
      ...sources.map(({ key, label }, index) => ({
        text: label ? label : '-',
        value: key ?? `unknown-${index}`,
        data: {
          type: IFrontVariableType.Sources,
        },
      })),
    ];
    setAllVariables(av);
    setSelectedVariable(av.filter((x) => x.value === variable) ? variable : null);
  }, [sources, variable, variables]);

  return (
    <Select
      label={viewLabel ? placeHolder : undefined}
      placeholder={placeHolder}
      size={size}
      wrapperClassName={wrapperClassName}
      isInvalid={isInvalid}
      message={isInvalid && <InputErrorMessage>{message}</InputErrorMessage>}
      data={allVariables}
      renderOption={({ id, element, onClick, active }) => (
        <MenuItem
          key={id}
          id={id ?? ''}
          tabIndex={-1}
          active={active}
          disabled={element.disabled}
          title={element.text}
          onClick={onClick}
          ellipsis
        >
          <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
            {{
              [IFrontVariableType.Sources]: <IconPopUp className={utils.flexShrink0} />,
              [IFrontVariableType.Local]: <IconHierarchy className={utils.flexShrink0} />,
            }[element.data?.type as IFrontVariableType] || null}
            <Text ellipsis>{element.text}</Text>
          </div>
        </MenuItem>
      )}
      value={selectedVariable}
      onChange={({ value, text }) => {
        console.log('onChange', value);
        onChange(value ?? null, text);
      }}
      onClear={() => {
        console.log('onClear');
      }}
      disabled={disabled}
      withDebounce
    />
  );
};

export default VariableSelect;
