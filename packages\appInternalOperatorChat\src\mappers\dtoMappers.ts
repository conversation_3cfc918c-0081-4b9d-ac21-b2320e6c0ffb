import { helpers } from '@product.front/ui-kit';

import { IFrontAttachment } from '@monorepo/common/src/@types/frontendChat';

import { Attachment } from '../@types/generated/signalr';

export function mapBackendAttachModelToFront(atta: Attachment): IFrontAttachment {
  if (!atta.id) {
    throw new Error('Expected file id');
  }

  return {
    id: atta.id,
    url: atta.url,
    name: atta.fileName,
    extension: helpers.getExtensionByFileName(atta.fileName) || '',
    externalId: atta.fileName.split('/').pop() ?? '',
  };
}
