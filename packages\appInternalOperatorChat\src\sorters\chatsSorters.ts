import { IChat } from '../@types/signalRTypes';

export const sortChatsByLastMessageDate = (chatA: IChat, chatB: IChat) => {
  const { lastMessage: lastA = {} } = chatA;
  const { lastMessage: lastB = {} } = chatB;

  const dateA = new Date(lastA?.createDate || 0);
  const dateB = new Date(lastB?.createDate || 0);

  if (dateA > dateB) return -1;
  if (dateA < dateB) return 1;
  return 0;
};

export const sortChatsByHasNewMessages = (chatA: IChat, chatB: IChat) => {
  const { unreadedMessagesCount: countA = 0 } = chatA;
  const { unreadedMessagesCount: countB = 0 } = chatB;

  const hasNewInA = countA > 0;
  const hasNewInB = countB > 0;

  if (hasNewInA > hasNewInB) return -1;
  if (hasNewInA < hasNewInB) return 1;
  return 0;
};
