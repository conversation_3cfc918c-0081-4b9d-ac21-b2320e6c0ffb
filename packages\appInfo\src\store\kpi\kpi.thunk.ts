import { createAsyncThunk } from '@reduxjs/toolkit';
import { Guid } from 'guid-typescript';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import { IGoalKpi, IOperatorKpi, IOperatorQueue, IQueueKpi } from '../../@types/KpiData';
import { getSettings } from '../../config/appSettings';

export const getAllKpiData = createAsyncThunk('kpi/getAllData', async (operatorId: Guid) => {
  const url = `${getSettings().calcServiceUrl}/api/Kpi/operator/${operatorId}/all-kpis`;
  const response = await commonFetch(url, { credentials: 'include' });

  const json = (await response.json()) as {
    alarmSettings: IGoalKpi;
    total: IOperatorKpi;
    operatorQueues: IQueueKpi[];
    kpiSettings: IOperatorQueue[];
  };

  if (!json.alarmSettings || !json.total || !json.operatorQueues || !json.kpiSettings) {
    throw new Error('Corrupted data from API');
  }

  return json;
});
