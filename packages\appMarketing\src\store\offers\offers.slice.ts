import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { TemplateMessage } from '../../@types/generated/marketing';
import { IOffer } from '../../@types/offer';

import extraReducers from './offers.extraReducers';

export interface IOffersStore {
  offers: IOffer[];
  selectedOffer: IOffer | null;
  neuronetTemplates: TemplateMessage[];
  whatsappTemplates: TemplateMessage[];
  saving: boolean;
  loading: boolean;
}

const initialState: IOffersStore = {
  offers: [],
  selectedOffer: null,
  neuronetTemplates: [],
  whatsappTemplates: [],
  saving: false,
  loading: false,
};

const offersSlice = createSlice({
  name: 'offers',
  initialState,
  reducers: {
    setSelectedOffer: (state, action: PayloadAction<IOffer | null>) => {
      state.selectedOffer = action.payload;
    },
  },
  extraReducers,
});

export const { setSelectedOffer } = offersSlice.actions;

export default offersSlice.reducer;
