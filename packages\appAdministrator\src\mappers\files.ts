import { helpers } from '@product.front/ui-kit';

import { IFrontAttachment } from '@monorepo/common/src/@types/frontendChat';

import {
  FolderContentDTO,
  FolderContentType,
  FrontFileOrFolder,
  IFrontFile,
  IFrontFolder,
  rootFilesFolder,
} from '../@types/files';
import { isFolder, normalizePathForApi } from '../helpers/files';
import { getLocaleMessageById } from '../helpers/localeHelper';

const getFlatFrontFileOrFolderArr = (list: FolderContentDTO[]): FrontFileOrFolder[] => {
  return list.map((item) => {
    return item.contentType === 'folder'
      ? ({
          path: '/',
          parent: item.parentFolder,
          name: item.contentName,
          type: FolderContentType.Folder,
          items: [],
        } satisfies IFrontFolder)
      : ({
          path: '/',
          parent: item.parentFolder,
          name: item.contentName,
          type: FolderContentType.File,
          extension: helpers.getExtensionByFileName(item.contentName) ?? 'unknown',
        } satisfies IFrontFile);
  });
};

export const mapFolderContentDTOToFront = (list: FolderContentDTO[]): FrontFileOrFolder[] => {
  const flatFolders: FrontFileOrFolder[] = getFlatFrontFileOrFolderArr(list);

  const getItemsForFolder = (path: string) => {
    return flatFolders
      .filter((f) => f.parent === path)
      .map((f) => {
        f.path = `${path}/${f.name}`;

        if (isFolder(f)) {
          f.items = getItemsForFolder(f.path);
        }

        return f;
      });
  };

  return [
    {
      path: rootFilesFolder,
      parent: '',
      name: getLocaleMessageById('files.list.root'),
      type: FolderContentType.Folder,
      items: flatFolders
        .filter((current) => current.parent === rootFilesFolder)
        .map((rootItem) => {
          rootItem.path = `${rootFilesFolder}/${rootItem.name}`;

          if (isFolder(rootItem)) {
            rootItem.items = getItemsForFolder(rootItem.path);
          }

          return rootItem;
        }),
    } satisfies IFrontFolder,
  ];
};

export const mapTrustedFileToIFrontAttachment = (file: IFrontFile): IFrontAttachment => {
  return {
    id: file.path,
    name: file.name,
    url: normalizePathForApi(file.path),
    extension: file.extension,
    mime: undefined,
    size: undefined,
    isInlined: false,
    contentId: '',
    externalId: '',
  };
};
