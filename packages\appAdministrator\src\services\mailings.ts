import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  CreatePeriodicMailing,
  CreateThresholdMailing,
  GetMailingsResult,
  MailingMetric,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export async function getMailingsMetricsAsync() {
  const url = `${getSettings().administrationApiUrl}/mailings/metrics`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });
  return (await response.json()) as MailingMetric[];
}

export async function getMailingsAsync() {
  const url = `${getSettings().administrationApiUrl}/mailings`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as GetMailingsResult;
}

export async function addMailingAsync(model: CreatePeriodicMailing | CreateThresholdMailing) {
  const url = `${getSettings().administrationApiUrl}/mailings`;
  const response = await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(model),
  });

  return (await response.json()) as string;
}

export async function updateMailingAsync(
  id: string,
  model: CreatePeriodicMailing | CreateThresholdMailing,
) {
  const url = `${getSettings().administrationApiUrl}/mailings/${id}`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(model),
  });
}

export async function deleteMailingAsync(id: string) {
  const url = `${getSettings().administrationApiUrl}/mailings/${id}`;
  return await commonFetch(url, {
    method: 'DELETE',
    credentials: 'include',
  });
}
