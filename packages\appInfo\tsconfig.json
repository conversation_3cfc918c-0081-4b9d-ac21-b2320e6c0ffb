{"extends": "@product.front/typescript-config", "compilerOptions": {"jsx": "react-jsx", "composite": true, "incremental": true, "declaration": true, "declarationDir": "dist/types", "declarationMap": true, "allowSyntheticDefaultImports": true, "emitDeclarationOnly": true}, "include": ["src/_locales/*.json", "src/**/*", "../../node_modules/@product.front/typescript-config/reset.d.ts"], "exclude": ["node_modules", "dist", "src/**/*.test*"]}