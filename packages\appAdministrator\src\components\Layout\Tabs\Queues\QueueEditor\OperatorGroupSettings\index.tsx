import React from 'react';

import clsx from 'clsx';

import { <PERSON><PERSON>, ButtonVariant, OverlayLoader, utils } from '@product.front/ui-kit';

import IconDropLeft from '@product.front/icons/dist/icons17/MainStuff/IconDropLeft';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import { OperatorGroupTreeView } from '../../../../../../@types/generated/administration';
import { IFrontOperatorGroupBase } from '../../../../../../@types/operatorGroup';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { getOperatorGroups } from '../../../../../../services/operatorGroups';

import OperatorGroupsTable from './OperatorGroupsTable';

import styles from './styles.module.scss';

interface IOperatorGroupsSettingsProps {
  operatorGroups: string[];
  handleOperatorGroupsChange: (operatorGroups: string[]) => void;
}

export interface IFrontOperatorGroupBaseWithChecked extends IFrontOperatorGroupBase {
  checked: boolean;
}

const OperatorGroupsSettings = ({
  operatorGroups,
  handleOperatorGroupsChange,
}: IOperatorGroupsSettingsProps) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();
  const [availableOperatorGroups, setAvailableOperatorGroups] = React.useState<
    IFrontOperatorGroupBaseWithChecked[]
  >([]);
  const [selectedOperatorGroups, setSelectedOperatorGroups] = React.useState<
    IFrontOperatorGroupBaseWithChecked[]
  >([]);

  React.useEffect(() => {
    const filterOperatorGroupsSelected = (operatorGroup: OperatorGroupTreeView) =>
      operatorGroups.find((value) => value === operatorGroup.id);
    const filterOperatorGroupsNotSelected = (operatorGroup: OperatorGroupTreeView) =>
      !operatorGroups.find((value) => value === operatorGroup.id);

    const mapOperatorDtoToFrontWithChecked = (
      operatorGroup: OperatorGroupTreeView,
    ): IFrontOperatorGroupBaseWithChecked => ({
      id: operatorGroup.id,
      name: operatorGroup.name,
      parentId: operatorGroup.id,
      childOperatorGroupsIds: [],
      checked: false,
    });

    (async () => {
      try {
        setLoading(true);
        setError(undefined);
        const operatorGroupsDto = await getOperatorGroups();

        setAvailableOperatorGroups(
          operatorGroupsDto
            .filter(filterOperatorGroupsNotSelected)
            .map(mapOperatorDtoToFrontWithChecked),
        );
        setSelectedOperatorGroups(
          operatorGroupsDto
            .filter(filterOperatorGroupsSelected)
            .map(mapOperatorDtoToFrontWithChecked),
        );
      } catch (err) {
        console.error('Get operator groups for OperatorSettings error', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  React.useEffect(() => {
    handleOperatorGroupsChange(selectedOperatorGroups.map((operatorGroup) => operatorGroup.id));
  }, [handleOperatorGroupsChange, selectedOperatorGroups]);

  return (
    <OverlayLoader
      loading={loading}
      wrapperClassName={clsx(utils.dGrid, utils.h100, styles.operatorGroupsWrapper)}
    >
      <OperatorGroupsTable
        header={getLocaleMessageById('operatorGroups.modal.form.availableOperatorGroups')}
        data={availableOperatorGroups}
        onChange={(newOperators) => setAvailableOperatorGroups(newOperators)}
      />
      <div
        className={clsx(
          utils.dFlex,
          utils.flexColumn,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
          utils.gap2,
          utils.pX4,
        )}
      >
        <Button
          className={clsx(utils.pX0, styles.moveButton)}
          variant={ButtonVariant.Secondary}
          onClick={() => {
            const operatorsToTransfer = availableOperatorGroups
              .filter((operator) => operator.checked)
              .map((operator) => ({ ...operator, checked: false }));
            setSelectedOperatorGroups([...selectedOperatorGroups, ...operatorsToTransfer]);
            setAvailableOperatorGroups(
              availableOperatorGroups.filter((operator) => !operator.checked),
            );
          }}
          disabled={!availableOperatorGroups.some((operator) => operator.checked)}
        >
          <IconDropRight />
        </Button>
        <Button
          className={clsx(utils.pX0, styles.moveButton)}
          variant={ButtonVariant.Secondary}
          onClick={() => {
            const operatorsToTransfer = selectedOperatorGroups
              .filter((operator) => operator.checked)
              .map((operator) => ({ ...operator, checked: false }));
            setAvailableOperatorGroups([...availableOperatorGroups, ...operatorsToTransfer]);
            setSelectedOperatorGroups(
              selectedOperatorGroups.filter((operator) => !operator.checked),
            );
          }}
          disabled={!selectedOperatorGroups.some((operator) => operator.checked)}
        >
          <IconDropLeft />
        </Button>
      </div>
      <OperatorGroupsTable
        header={getLocaleMessageById('operatorGroups.modal.form.selectedOperatorGroups')}
        data={selectedOperatorGroups}
        onChange={(newOperatorGroups) => setSelectedOperatorGroups(newOperatorGroups)}
      />
      {error && (
        <AlertError
          className={utils.flexGrow1}
          header={getLocaleMessageById('operators.get.error')}
          error={error}
        />
      )}
    </OverlayLoader>
  );
};

export default OperatorGroupsSettings;
