import React from 'react';

import clsx from 'clsx';

import { Text, TextVariant, utils } from '@product.front/ui-kit';

interface IFieldsGroupHeader {
  children: React.ReactNode;
}

const FieldsGroupHeader: React.FC<IFieldsGroupHeader> = ({ children }) => (
  <Text variant={TextVariant.HeadlineSemibold} as="h2" className={clsx(utils.mT0, utils.mB3)}>
    {children}
  </Text>
);

export default FieldsGroupHeader;
