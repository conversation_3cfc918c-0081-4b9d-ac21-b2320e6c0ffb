import React from 'react';

import clsx from 'clsx';

import {
  ChannelIcon,
  ChannelIconSize,
  Colors,
  grids,
  Jumbotron,
  JumbotronType,
  Progress,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconCalendar from '@product.front/icons/dist/icons11/MainStuff/IconCalendar';
import IconPriority from '@product.front/icons/dist/icons11/MainStuff/IconPriority';
import IconDownArrow from '@product.front/icons/dist/icons17/MainStuff/IconDownArrow';
import IconUpArrow from '@product.front/icons/dist/icons17/MainStuff/IconUpArrow';

import getChanelIconForMessageType from '@monorepo/common/src/mappers/ChannelIconForMessageType';
import { mapUCMMChannelToGraphQLMessageType } from '@monorepo/common/src/mappers/graphQlMappers';

import { IFrontCampaign } from '../../../@types/campaign';
import { CampaignType } from '../../../@types/generated/marketing';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { setSelectedCampaign } from '../../../store/campaigns/campaigns.slice';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../../store/hooks';

import styles from './styles.module.scss';

export interface ISortState {
  field: keyof IFrontCampaign;
  direction: 'asc' | 'desc';
  type: 'string' | 'number' | 'date';
}

interface ICampaignsListProps {
  types: CampaignType[];
  sort: ISortState;
}

const compareFns: Record<
  ISortState['type'],
  (a: any, b: any, direction: ISortState['direction']) => number
> = {
  string: (a: string, b: string, direction) =>
    direction === 'asc' ? a.localeCompare(b) : b.localeCompare(a),
  number: (a: number, b: number, direction) => (direction === 'asc' ? a - b : b - a),
  date: (a: string, b: string, direction) =>
    direction === 'asc'
      ? Number(new Date(a)) - Number(new Date(b))
      : Number(new Date(b)) - Number(new Date(a)),
};

const CampaignsList = ({ types, sort }: ICampaignsListProps) => {
  const dispatch = useMarketingAppDispatch();

  const { campaigns } = useMarketingAppSelector((state) => state.campaigns);
  const { channels } = useMarketingAppSelector((state) => state.settings);

  const campaignsToDisplay = React.useMemo(
    () =>
      campaigns
        .filter((campaign) => types.includes(campaign.type))
        .sort((a, b) => compareFns[sort.type](a[sort.field], b[sort.field], sort.direction)),
    [campaigns, types, sort],
  );

  if (campaignsToDisplay.length === 0) {
    return (
      <div
        className={clsx(
          utils.w100,
          utils.h100,
          utils.dFlex,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
        )}
      >
        <Jumbotron
          type={JumbotronType.Info}
          header={getLocaleMessageById('app.campaigns.notFound')}
        />
      </div>
    );
  }

  return (
    <div className={clsx(grids.row, utils.gap5, utils.p5)}>
      {campaignsToDisplay.map((campaign) => (
        <div
          key={campaign.id}
          className={clsx(
            grids.col3,
            utils.border,
            utils.p5,
            utils.positionRelative,
            utils.dGrid,
            utils.gap3,
            styles.campaignCard,
          )}
          style={{ borderRadius: '6px' }}
          onClick={() => dispatch(setSelectedCampaign(campaign))}
          onKeyDown={() => {}}
          tabIndex={-1}
          role="button"
        >
          <div className={clsx(utils.positionAbsolute)} style={{ top: 10, right: 10 }}>
            {campaign.type === CampaignType.Incoming ? <IconDownArrow /> : <IconUpArrow />}
          </div>
          <Text>{campaign.name}</Text>
          <div className={clsx(utils.dFlex, utils.alignItemsCenter, utils.flexNoWrap, utils.gap2)}>
            {campaign.channels.map((channel) => (
              <React.Fragment key={channel}>
                <ChannelIcon
                  size={ChannelIconSize.Small}
                  channel={getChanelIconForMessageType(
                    mapUCMMChannelToGraphQLMessageType(channels[channel].sourceName ?? ''),
                  )}
                />
              </React.Fragment>
            ))}
          </div>
          <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap1)}>
            <Text
              color={Colors.OnyxBlack60}
              className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}
            >
              <IconCalendar />
              {campaign.dateFrom && new Date(campaign.dateFrom).toLocaleDateString()}
              {' - '}
              {campaign.dateTo && new Date(campaign.dateTo).toLocaleDateString()}
            </Text>
            <Text
              color={Colors.OnyxBlack60}
              className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}
            >
              <IconPriority />
              {campaign.priority}
            </Text>
          </div>
          <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap1)}>
            <Text variant={TextVariant.SmallMedium}>
              {getLocaleMessageById('app.campaigns.card.accomplishment')}
            </Text>
            <Text variant={TextVariant.HeadlineSemibold}>{campaign.accomplishmentPercent}%</Text>
            <Progress value={campaign.accomplishmentPercent} color="var(--palette-grassios-80)" />
          </div>
          <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap1)}>
            <Text variant={TextVariant.SmallMedium}>
              {getLocaleMessageById('app.campaigns.card.feedback')}
            </Text>
            <Text variant={TextVariant.HeadlineSemibold}>{campaign.feedbackPercent}%</Text>
            <Progress value={campaign.feedbackPercent} />
          </div>
        </div>
      ))}
    </div>
  );
};

export default CampaignsList;
