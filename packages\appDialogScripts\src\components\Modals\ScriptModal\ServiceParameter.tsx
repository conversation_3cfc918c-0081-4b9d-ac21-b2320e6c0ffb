import React from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import { grids, Input, InputSize, Select, Text, utils } from '@product.front/ui-kit';

import { IFrontStep, IFrontServiceParameter } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import InputErrorMessage from '../../InputErrorMessage';

interface IServiceParameterProps {
  parameter: IFrontServiceParameter;
  onChange: (updatedParameter: IFrontServiceParameter) => void;
  disabled?: boolean;
  variables: Record<string, IFrontStep['variableName']>;
  isInvalid?: boolean;
  message?: string;
}

const ServiceParameter = ({
  parameter,
  onChange,
  disabled,
  variables,
  isInvalid,
  message,
}: IServiceParameterProps) => {
  const isInvalidSelect = isInvalid && !(parameter.variableCode || parameter.variableCode === '');
  const isInvalidInput = isInvalid && !parameter.value && !parameter.variableCode?.length;

  return (
    <div className={clsx(grids.row)}>
      <Text
        className={clsx(grids.col4)}
        title={[parameter.name, parameter.description].join('\n')}
        ellipsis
        noWrap
      >
        {parameter.name}
      </Text>
      <div className={grids.col4}>
        <Select
          placeholder={getLocaleMessageById('app.editor.routerRuleVariable')}
          size={InputSize.Small}
          wrapperClassName={utils.flexGrow6}
          data={[
            { text: getLocaleMessageById('app.editor.serviceInputsManual'), value: '' },
            ...(
              Object.entries(variables).map(([code, name]) => ({
                value: code,
                text: name || code,
              })) as ISelectDataItem[]
            ).sort((a, b) => a.text.localeCompare(b.text) ?? 0),
          ]}
          value={parameter.variableCode || ''}
          onChange={({ value }) => onChange({ ...parameter, variableCode: value ?? '' })}
          disabled={disabled}
          withDebounce
          isInvalid={isInvalidSelect}
          message={isInvalidSelect && <InputErrorMessage>{message}</InputErrorMessage>}
        />
      </div>
      <div className={grids.col4}>
        <Input
          placeholder={
            !parameter.variableCode?.length
              ? getLocaleMessageById('app.editor.routerRuleValue')
              : getLocaleMessageById('app.editor.routerRuleVariable')
          }
          size={InputSize.Small}
          wrapperClassName={utils.flexGrow6}
          value={!parameter.variableCode?.length ? parameter.value : ''}
          onChange={({ value }) => onChange({ ...parameter, value })}
          disabled={disabled || !!parameter.variableCode?.length}
          withDebounce
          isInvalid={isInvalidInput}
          message={isInvalidInput && <InputErrorMessage>{message}</InputErrorMessage>}
        />
      </div>
    </div>
  );
};

export default ServiceParameter;
