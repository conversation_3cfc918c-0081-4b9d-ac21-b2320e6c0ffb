import React from 'react';

import { showModal, Table } from '@product.front/ui-kit';

import IconCopy from '@product.front/icons/dist/icons17/MainStuff/IconCopy';
import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import InfoMessage from '@monorepo/common/src/components/Table/InfoMessage';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { IAdmTabComponent } from '../../../../@types/components';
import { IFrontQueue, IFrontQueueBase } from '../../../../@types/queue';
import { getSettings } from '../../../../helpers/appSettings';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { mapFullQueueDtoToFront } from '../../../../mappers/queues';
import { deleteQueue, getQueue } from '../../../../services/queues';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { getAllQueues } from '../../../../store/queues/queues.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import columns from './columns';
import QueueEditor from './QueueEditor';

const QueuesTab: React.FC<IAdmTabComponent> = ({ name }) => {
  const dispatch = useAdministratorAppDispatch();

  const { queues, loading, error } = useAdministratorAppSelector((state) => state.queues);

  const [selectedQueue, setSelectedQueue] = React.useState<IFrontQueueBase | null>(null);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);
  const [isCopying, setIsCopying] = React.useState(false);

  const openQueueEditor = async (
    queue: IFrontQueueBase | null,
    fullQueuePatch?: Partial<IFrontQueue>,
  ) => {
    let fullQueue: IFrontQueue | null = null;
    if (queue) {
      try {
        fullQueue = { ...mapFullQueueDtoToFront(await getQueue(queue.id)), ...fullQueuePatch };
      } catch (e) {
        console.error('Error getting queue', e);

        const errText = getLocaleMessageById('queues.getFull.error');
        const errArgs = getNotificationArgsByError(errText, e);
        getPlatformPopupNotificationManager().notifyError(...errArgs);
      }

      if (!fullQueue) return;
    }

    showModal({
      header: getLocaleMessageById(
        queue ? 'queues.editor.header.edit' : 'queues.editor.header.create',
        {
          queueName: fullQueue?.name,
        },
      ),
      children: (onClose) => (
        <QueueEditor fullQueue={fullQueue} onSubmit={refresh} onClose={onClose} />
      ),
      flushBody: true,
      canClose: false,
    });
  };

  const handleDeleteClick = async () => {
    if (!selectedQueue) return;

    setIsDeleting(true);
    showConfirmModal({
      header: getLocaleMessageById('queues.delete.confirm.title'),
      text: getLocaleMessageById('queues.delete.confirm.text', {
        queueName: selectedQueue.name,
      }),
      onConfirm: async () => {
        try {
          const url = `${getSettings().dataPresentationServiceUrl}/DataPresentation/Requests/$count?$filter=(TimeClosed eq null or TimeClosed eq 0001-01-01T00:00:00Z) and QueueId in (${selectedQueue.id})`;

          const activeRequestsResponse = await commonFetch(url, { credentials: 'include' });

          const activeRequestsNumber = await activeRequestsResponse.json();
          if (activeRequestsNumber !== 0) {
            getPlatformPopupNotificationManager().notifyError(
              getLocaleMessageById('queues.delete.error.hasActiveRequests.text'),
              getLocaleMessageById('queues.delete.error.hasActiveRequests.header'),
            );
            return;
          }

          await deleteQueue(selectedQueue.id);
          refresh();
        } catch (e) {
          console.error('Error deleting queue', e);

          const errText = getLocaleMessageById('queues.delete.error');
          const errArgs = getNotificationArgsByError(errText, e);
          getPlatformPopupNotificationManager().notifyError(...errArgs);
        } finally {
          setIsDeleting(false);
        }
      },
      onCancel: () => setIsDeleting(false),
    });
  };

  const handleCopyClick = async () => {
    if (!selectedQueue) return;

    setIsCopying(true);
    await openQueueEditor(selectedQueue, {
      id: 0,
      name: `COPY ${selectedQueue.name}`,
    });
    setIsCopying(false);
  };

  const refresh = React.useCallback(() => {
    dispatch(getAllQueues());
  }, [dispatch]);

  React.useEffect(() => {
    refresh();
  }, [refresh]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <AdmToolbarIconButton
          onClick={refresh}
          disabled={loading}
          tooltip={getLocaleMessageById('queues.button.refresh')}
        >
          <IconRefresh />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          onClick={handleCopyClick}
          disabled={!selectedQueue || isCopying}
          loading={isCopying}
          tooltip={getLocaleMessageById('queues.button.copy')}
        >
          <IconCopy />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          onClick={handleDeleteClick}
          disabled={!selectedQueue || isDeleting}
          loading={isDeleting}
          tooltip={getLocaleMessageById('queues.button.delete')}
        >
          <IconTrash />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          onClick={async () => {
            setIsEditing(true);
            await openQueueEditor(selectedQueue);
            setIsEditing(false);
          }}
          disabled={!selectedQueue || isEditing}
          loading={isEditing}
          tooltip={getLocaleMessageById('queues.button.edit')}
        >
          <IconEdit />
        </AdmToolbarIconButton>

        <AdmToolbarCtaButton onClick={() => openQueueEditor(null)}>
          {getLocaleMessageById('queues.button.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody loading={loading}>
        <Table<IFrontQueueBase>
          data={queues.map((row) => ({ ...row, meta: { active: selectedQueue?.id === row.id } }))}
          columns={columns(refresh)}
          onRowClick={(row) => setSelectedQueue(row.data)}
          rendererAfter={() => (
            <>
              {!error && queues.length === 0 && (
                <InfoMessage header={getLocaleMessageById('queues.empty')} />
              )}
              {error && <JumbotronError error={error} sticky />}
            </>
          )}
        />
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default QueuesTab;
