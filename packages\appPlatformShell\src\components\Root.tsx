import React from 'react';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import Shell from './Shell';

const Root = () => {
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const initBranding = async () => {
      try {
        const raw = await commonFetch('branding.json');
        try {
          const brandingData = (await raw.json()) as Record<string, string>;
          if (!brandingData) throw new Error('empty config');
          Object.entries(brandingData).forEach(([k, v]) => {
            document.documentElement.style.setProperty(k, v);
          });
        } catch (e) {
          console.warn('Branding config fetching error', e.message);
        }
      } catch (e) {
        // проглатываем ошибку. Нет - так нет
        console.info('Branding file getting error', e.message);
      }
      setIsLoading(false);
    };

    initBranding();
  }, []);

  return isLoading ? null : <Shell />;
};

export default Root;
