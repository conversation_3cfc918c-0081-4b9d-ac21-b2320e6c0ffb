import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { ITemplatesStore } from './templates.slice';
import { getAllFolderTemplates, selectTemplate } from './templates.thunk';

const getFolderTemplatesReducers = (builder: ActionReducerMapBuilder<ITemplatesStore>) =>
  builder
    .addCase(getAllFolderTemplates.pending, (state) => {
      state.loading = true;
      state.error = undefined;
      state.selectedTemplate = null;
    })
    .addCase(getAllFolderTemplates.fulfilled, (state, action) => {
      state.folders = action.payload;
      state.loading = false;
    })
    .addCase(getAllFolderTemplates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

const getTemplatesReducers = (builder: ActionReducerMapBuilder<ITemplatesStore>) =>
  builder.addCase(selectTemplate.fulfilled, (state, action) => {
    state.selectedTemplate = action.payload;
  });

export default (builder: ActionReducerMapBuilder<ITemplatesStore>) => {
  getFolderTemplatesReducers(builder);
  getTemplatesReducers(builder);

  return builder;
};
