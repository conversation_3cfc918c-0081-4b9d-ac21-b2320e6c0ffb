import React from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import {
  CanClearBehavior,
  Checkbox,
  Colors,
  grids,
  Input,
  Select,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import { IFrontAttachmentWithRealFile } from '@monorepo/common/src/@types/frontendChat';
import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';
import { mapFileToIFrontAttachmentWithRealFile } from '@monorepo/dialog-scripts/src/mappers/dialogScriptsEditorMappersToFront';

import { AppConfigContext } from '../../../context/appConfig';
import { getVariablesFromString } from '../../../helpers/bracesVariableHelper';
import { createEmptyChoiceStepAnswer } from '../../../helpers/createObjectFactory';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getVariableRegExpPatternByScriptSteps } from '../../../helpers/scriptHelpers';
import { getAllVariablesFromScriptSteps } from '../../../helpers/scriptVariablesHelpers';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import { useDialogScriptsAppSelector } from '../../../store/hooks';
import AttachmentInput from '../../AttachmentInput';
import AttachmentsList from '../../AttachmentsList';
import InputErrorMessage from '../../InputErrorMessage';

import AnswerList, { getStepWithNewEmptyAnswer } from './components/AnswerList';
import AttachmentVariableList from './components/AttachmentVariableList';
import AttachmentVariablesInput from './components/AttachmentVariablesInput';
import ChoiceStepDetails from './components/ChoiceStepDetails';
import StepFormDescription from './components/StepFormDescription';
import DeleteStepButton from './DeleteStepButton';
import FirstStepButton from './FirstStepButton';
import TransferSelect from './TransferSelect';

import styles from './styles.module.scss';

interface IStepProps {
  step: IFrontStep;
  steps: IFrontStep[];
  number: number;
  onlyStep: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
  disabled: boolean;
  requiredForActive: boolean;
}

const Step = ({
  step,
  steps,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: IStepProps) => {
  const variableInputRef = React.useRef<HTMLInputElement>(null);

  const { sources } = useDialogScriptsAppSelector((state) => state.externalData);

  const [name, setName] = React.useState(step.name);
  const [stepTransfer, setStepTransfer] = React.useState(step.stepTransfer ?? 'default');
  const [limitStepTransfer, setLimitStepTransfer] = React.useState(
    step.limitStepTransfer ?? 'default',
  );

  const [timeoutStepTransfer, setTimeoutStepTransfer] = React.useState(
    step.timeoutStepTransfer ?? 'default',
  );
  const [answerDisplayType, setAnswerDisplayType] = React.useState(
    step.answerDisplayType ?? AnswerTypes.None,
  );
  const [attachments, setAttachments] = React.useState<IFrontAttachmentWithRealFile[]>(
    step.attachments || [],
  );
  const [variable, setVariable] = React.useState(step.variable ?? '');
  const [variableName, setVariableName] = React.useState(step.variableName ?? '');

  const [limit, setLimit] = React.useState(step.limit);
  const [timeout, setTimeout] = React.useState(step.timeout);
  const { answerTypes, allowStepPromptUrl } = React.useContext(AppConfigContext);
  const defaultAnswerType = !answerTypes?.length ? AnswerTypes.Radio : answerTypes[0];
  const variableRegexp = React.useMemo(() => {
    return getVariableRegExpPatternByScriptSteps(
      steps.filter((currentStep) => {
        return currentStep.code !== step.code && step.answerDisplayType !== AnswerTypes.None;
      }),
      sources,
    );
  }, [steps, step, sources]);

  const shouldDisplayTimeout = AnswerTypes.File === answerDisplayType;

  const shouldDisplayTransfer = [AnswerTypes.None, AnswerTypes.File].includes(answerDisplayType);
  const shouldDisplayAnswersFunc = (answerType: AnswerTypes) =>
    answerType && ![AnswerTypes.None, AnswerTypes.TextInput, AnswerTypes.File].includes(answerType);
  const shouldDisplayAnswers = shouldDisplayAnswersFunc(answerDisplayType);
  const shouldDisplayLimit = [
    AnswerTypes.Button,
    AnswerTypes.File,
    AnswerTypes.TextTemplate,
    AnswerTypes.Choice,
  ].includes(answerDisplayType);
  const shouldDisplayBackCheckbox = !step.isFirstStep && AnswerTypes.File !== answerDisplayType;

  React.useEffect(() => {
    variableInputRef.current?.checkValidity();
  }, [variableInputRef]);

  const promptVariables = React.useMemo<string[]>(() => {
    if (!step.promptUrl?.length) {
      return [];
    }
    return getVariablesFromString(step.promptUrl);
  }, [step.promptUrl]);

  const handleChangeAttachments = ({ files }: { files: FileList }) => {
    setAttachments((cur) => {
      const attachmentsUpdate = [
        ...cur,
        ...Array.from(files).map(mapFileToIFrontAttachmentWithRealFile),
      ];

      onChange({
        ...step,
        attachments: attachmentsUpdate,
      });

      return attachmentsUpdate;
    });
  };

  const hasSources = sources.length > 0;

  const availableAnswerTypes: ISelectDataItem[] = [
    {
      value: AnswerTypes.Radio,
      text: getLocaleMessageById('app.answerDisplayType.radio'),
    },
    {
      value: AnswerTypes.Select,
      text: getLocaleMessageById('app.answerDisplayType.select'),
    },
    {
      value: AnswerTypes.TextInput,
      text: getLocaleMessageById('app.answerDisplayType.free'),
    },
    {
      value: AnswerTypes.TextTemplate,
      text: getLocaleMessageById('app.answerDisplayType.template'),
    },
    {
      value: AnswerTypes.Button,
      text: getLocaleMessageById('app.answerDisplayType.button'),
    },
    {
      value: AnswerTypes.File,
      text: getLocaleMessageById('app.answerDisplayType.file'),
    },
    {
      value: AnswerTypes.Choice,
      text: getLocaleMessageById('app.answerDisplayType.choice'),
    },
  ].filter((item) => !answerTypes || answerTypes.includes(item.value));

  const variables: Record<string, IFrontStep['variableName']> =
    getAllVariablesFromScriptSteps(steps);

  const appModalsFormTransfer = getLocaleMessageById('app.modals.form.transfer');

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,
        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {step.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
          <FirstStepButton stepCode={step.code} isFirstStep={step.isFirstStep} />
          {!onlyStep && (
            <DeleteStepButton
              needConfirm={Boolean(
                name ||
                  step.description ||
                  step.stepTransfer !== 'default' ||
                  answerDisplayType !== AnswerTypes.None ||
                  variable ||
                  variableName ||
                  step?.answers?.length,
              )}
              disabled={disabled}
              onDelete={onDelete}
            />
          )}
        </aside>
      </div>
      <Input
        label={getLocaleMessageById('app.modals.form.stepName')}
        placeholder={getLocaleMessageById('app.editor.noName')}
        value={name}
        autoFocus={!name.length}
        onChange={({ value }) => setName(value || '')}
        onBlur={() => onChange({ ...step, name })}
        canClearBehavior={CanClearBehavior.Value}
        required={requiredForActive}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.name}
        message={<InputErrorMessage>{step.invalidReasons?.name}</InputErrorMessage>}
      />
      <StepFormDescription
        step={step}
        onChange={onChange}
        required={requiredForActive}
        disabled={disabled}
      />

      <div>
        <AttachmentInput onChange={handleChangeAttachments} multiple disabled={disabled} />
        <AttachmentVariablesInput
          disabled={disabled}
          variables={variables}
          step={step}
          onChange={onChange}
        />
      </div>

      {allowStepPromptUrl !== false && (
        <Input
          type="url"
          label={getLocaleMessageById('app.modals.form.stepPromptUrl')}
          value={step.promptUrl}
          onChange={({ value }) => onChange({ ...step, promptUrl: value })}
          required={!!step.promptUrl?.length}
          disabled={disabled}
          message={
            promptVariables.length > 0 ? (
              <Text variant={TextVariant.TinySemibold} color={Colors.OnyxBlack60}>
                {getLocaleMessageById('app.editor.step.promptVariables', {
                  variables: promptVariables.join(', '),
                })}
              </Text>
            ) : null
          }
        />
      )}
      {(!answerTypes || answerTypes.includes(AnswerTypes.None)) && (
        <Checkbox
          label={getLocaleMessageById('app.modals.form.answerDisplayTypeNotNone')}
          checked={answerDisplayType !== AnswerTypes.None}
          onChange={({ checked }) => {
            const newAnswerDisplayType = checked ? defaultAnswerType : AnswerTypes.None;
            setAnswerDisplayType(newAnswerDisplayType);
            let patch: IFrontStep = {
              ...step,
              answerDisplayType: newAnswerDisplayType,
            };
            if (
              checked &&
              !step.answers?.length &&
              shouldDisplayAnswersFunc(newAnswerDisplayType)
            ) {
              patch = getStepWithNewEmptyAnswer(patch);
            }
            onChange(patch);
          }}
          disabled={disabled}
        />
      )}
      {answerDisplayType !== AnswerTypes.None && (
        <>
          {availableAnswerTypes.length > 1 && (
            <div className={clsx(grids.row)}>
              <div className={grids.col12}>
                <Select
                  wrapperClassName={utils.w100}
                  label={getLocaleMessageById('app.modals.form.answerDisplayType')}
                  value={answerDisplayType.toString()}
                  data={availableAnswerTypes}
                  onChange={({ value }) => {
                    const newDisplayType = (value || AnswerTypes.None) as AnswerTypes;
                    setAnswerDisplayType(newDisplayType);
                    let patch: IFrontStep = {
                      ...step,
                      answerDisplayType: newDisplayType,
                    };
                    if (!step.answers?.length && shouldDisplayAnswersFunc(newDisplayType)) {
                      patch = getStepWithNewEmptyAnswer(patch);
                    }
                    onChange(patch);
                  }}
                  required
                  disabled={disabled}
                />
              </div>
            </div>
          )}
          <div className={clsx(grids.row, utils.gap4)}>
            <Input
              wrapperClassName={hasSources ? grids.col4 : grids.col6}
              label={getLocaleMessageById('app.modals.form.variableName')}
              value={variableName}
              onChange={({ value }) => setVariableName(value || '')}
              onBlur={() => onChange({ ...step, variableName })}
              canClearBehavior={CanClearBehavior.Value}
              disabled={disabled}
              isInvalid={!!step.invalidReasons?.variableName}
              message={<InputErrorMessage>{step.invalidReasons?.variableName}</InputErrorMessage>}
            />
            <Input
              ref={variableInputRef}
              wrapperClassName={hasSources ? grids.col4 : grids.col6}
              label={getLocaleMessageById('app.modals.form.variableCode')}
              pattern={variableRegexp}
              value={variable}
              onChange={({ value }, event) => {
                event?.target.checkValidity();
                setVariable(value || '');
                if (!event?.target || !value?.match(variableRegexp)) return;

                event.target.setCustomValidity('');
                event.target.setAttribute('title', '');
              }}
              onBlur={() => onChange({ ...step, variable })}
              canClearBehavior={CanClearBehavior.Value}
              disabled={disabled}
              onInvalid={(event) => {
                const target = event.target as HTMLInputElement;
                const validityState = target.validity;
                let errorMessage = '';
                if (validityState.patternMismatch) {
                  errorMessage = getLocaleMessageById('app.error.notUniqueVariable');
                }
                if (!errorMessage) return;

                target.setCustomValidity(errorMessage);
                target.title = errorMessage;
              }}
              isInvalid={!!step.invalidReasons?.variable}
              message={<InputErrorMessage>{step.invalidReasons?.variable}</InputErrorMessage>}
            />
            {hasSources && (
              <Select
                label={getLocaleMessageById('app.editor.step.variableSource')}
                wrapperClassName={grids.col4}
                data={sources.map(({ key, label }, i) => ({
                  text: label ?? '—',
                  value: key ?? `unknown-${i}`,
                }))}
                value={step.variableSource}
                onChange={({ value }) => {
                  onChange({
                    ...step,
                    variableSource: value ?? undefined,
                    isSkippable: value ? step.isSkippable : false,
                  });
                }}
                canClearBehavior={CanClearBehavior.Value}
                disabled={disabled}
              />
            )}
          </div>
        </>
      )}
      {shouldDisplayTransfer && (
        <TransferSelect
          label={appModalsFormTransfer}
          value={stepTransfer}
          steps={steps.filter((s) => s.code !== step.code)}
          onChange={({ value }) => {
            const newTransfer = value ?? 'default';
            setStepTransfer(newTransfer);
            onChange({ ...step, stepTransfer: newTransfer });
          }}
          disabled={disabled}
          isInvalid={!!step.invalidReasons?.transferRequired}
          message={<InputErrorMessage>{step.invalidReasons?.transferRequired}</InputErrorMessage>}
        />
      )}
      {answerDisplayType === AnswerTypes.Choice && (
        <ChoiceStepDetails
          details={step.choiceStepAnswer ?? createEmptyChoiceStepAnswer()}
          invalidReasons={step.invalidReasons?.choiceStepDetailsInvalidReason ?? {}}
          steps={steps.filter((s) => s.code !== step.code)}
          onChange={(newDetails) => onChange({ ...step, choiceStepAnswer: { ...newDetails } })}
          currentStepCode={step.code}
        />
      )}
      {shouldDisplayLimit && (
        <div className={clsx(grids.row, utils.gap4)}>
          <Input
            label={getLocaleMessageById('app.modals.limit')}
            canClearBehavior={CanClearBehavior.Value}
            onChange={(value) => {
              const intVal = value?.value ? parseInt(value.value as string, 10) : null;
              if (intVal == null || !isNaN(intVal)) {
                setLimit(intVal);
              }
            }}
            type="number"
            min={1}
            max={100}
            onBlur={() => onChange({ ...step, limit })}
            value={limit?.toString()}
            disabled={disabled}
            wrapperClassName={grids.col6}
            isInvalid={!!step.invalidReasons?.limitInvalidReason}
            message={
              <InputErrorMessage>{step.invalidReasons?.limitInvalidReason}</InputErrorMessage>
            }
          />
          <div className={grids.col6}>
            <TransferSelect
              label={appModalsFormTransfer}
              value={limitStepTransfer}
              steps={steps.filter((s) => s.code !== step.code)}
              onChange={({ value }) => {
                const newTransfer = value ?? 'default';
                setLimitStepTransfer(newTransfer);
                onChange({ ...step, limitStepTransfer: newTransfer });
              }}
              disabled={disabled || !limit || limit < 1}
              isInvalid={!!step.invalidReasons?.limitTransferRequired}
              message={
                <InputErrorMessage>{step.invalidReasons?.limitTransferRequired}</InputErrorMessage>
              }
            />
          </div>
        </div>
      )}
      {shouldDisplayTimeout && (
        <div className={clsx(grids.row, utils.gap4)}>
          <Input
            label={`${getLocaleMessageById('app.modals.timeout')} ${getLocaleMessageById('app.modals.timeoutUnit')}`}
            canClearBehavior={CanClearBehavior.Value}
            onChange={(value) => {
              const intVal = value?.value ? parseInt(value.value as string, 10) : null;
              if (intVal == null || !isNaN(intVal)) {
                setTimeout(intVal);
              }
            }}
            type="number"
            min={1}
            max={100}
            onBlur={() => onChange({ ...step, timeout })}
            value={timeout?.toString()}
            disabled={disabled}
            wrapperClassName={grids.col6}
            isInvalid={!!step.invalidReasons?.timeoutInvalidReason}
            message={
              <InputErrorMessage>{step.invalidReasons?.timeoutInvalidReason}</InputErrorMessage>
            }
          />
          <div className={grids.col6}>
            <TransferSelect
              label={appModalsFormTransfer}
              value={timeoutStepTransfer}
              steps={steps.filter((s) => s.code !== step.code)}
              onChange={({ value }) => {
                const newTransfer = value ?? 'default';
                setTimeoutStepTransfer(newTransfer);
                onChange({ ...step, timeoutStepTransfer: newTransfer });
              }}
              disabled={disabled || !timeout || timeout < 1}
              isInvalid={!!step.invalidReasons?.timeoutTransferRequired}
              message={
                <InputErrorMessage>
                  {step.invalidReasons?.timeoutTransferRequired}
                </InputErrorMessage>
              }
            />
          </div>
        </div>
      )}
      {answerDisplayType === AnswerTypes.TextInput && (
        <div className={clsx(grids.row, utils.gap4, utils.alignItemsCenter)}>
          <Text className={grids.col6}>{getLocaleMessageById('app.modals.form.freeAnswer')}</Text>
          <div className={grids.col6}>
            <TransferSelect
              label={appModalsFormTransfer}
              value={step.stepTransfer}
              steps={steps.filter((s) => s.code !== step.code)}
              onChange={({ value }) => {
                const newTransfer = value ?? 'default';
                onChange({ ...step, stepTransfer: newTransfer });
              }}
              disabled={disabled}
            />
          </div>
        </div>
      )}
      {shouldDisplayAnswers && (
        <AnswerList
          step={step}
          steps={steps}
          disabled={disabled}
          requiredForActive={
            answerDisplayType !== AnswerTypes.TextTemplate ? requiredForActive : false
          }
          onChange={onChange}
          answerType={answerDisplayType}
        />
      )}

      {!!step.variableSource && (
        <Checkbox
          label={getLocaleMessageById('app.editor.step.labelIsSkippable')}
          checked={step.isSkippable}
          onChange={({ checked }) => {
            onChange({ ...step, isSkippable: checked });
          }}
          disabled={disabled}
        />
      )}

      {shouldDisplayBackCheckbox && (
        <Checkbox
          label={getLocaleMessageById('app.editor.step.labelIsBackButtonAvailable')}
          checked={step.isBackButtonAvailable}
          onChange={({ checked }) => {
            onChange({ ...step, isBackButtonAvailable: checked });
          }}
          disabled={disabled}
        />
      )}

      <AttachmentsList
        attachments={attachments}
        onChange={(atta) => {
          setAttachments(atta);
          onChange({ ...step, attachments: atta });
        }}
      />

      <AttachmentVariableList
        step={step}
        disabled={disabled}
        onChange={onChange}
        variables={variables}
      />
    </div>
  );
};

export default Step;
