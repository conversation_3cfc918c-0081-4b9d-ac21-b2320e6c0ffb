import React from 'react';

import clsx from 'clsx';
import { Handle, Position } from 'reactflow';

import { Colors, colors, Text, utils } from '@product.front/ui-kit';

import { fictionNodeHeight, fictionNodeWidth } from '../../../const/nodeSizes';
import { getNodeTargetId } from '../../../helpers/idsHelper';

import styles from './styles.module.scss';

function NodeFinish({ id, preview }: any) {
  return (
    <div
      className={clsx(
        styles.nodeFinish,
        utils.p2,
        utils.dFlex,
        utils.alignItemsCenter,
        utils.justifyContentCenter,
        colors.bgAmenaza60,
      )}
      style={{ width: fictionNodeWidth, height: fictionNodeHeight }}
    >
      <Text color={Colors.HollywoodSmile}>Стоп</Text>
      {!preview && <Handle type="target" position={Position.Left} id={getNodeTargetId(id)} />}
    </div>
  );
}

export default React.memo(NodeFinish);
