/* stylelint-disable */
.app-buttons {
  height      : 100%;
  white-space : nowrap;
  width       : 100vw;
  display     : flex;
  flex-wrap   : nowrap;
}
.app-buttons::-webkit-scrollbar {
  height     : 0;
  width      : 0;
  background : transparent;
}
.app-button {
  cursor           : pointer;
  margin           : 0;
  height           : 100%;
  border           : 0;
  padding          : 0px 16px 0 16px;
  font-style       : normal;
  font-weight      : 500;
  font-size        : 13px;
  line-height      : 19px;
  position         : relative;
  flex             : 1;
  overflow         : hidden;
  --button-bg      : var(--palette-onyxBlack-100);
  background-color : var(--button-bg);
  max-width        : 220px;
  min-width        : 82px;
}

.app-button.active {
  --button-bg : var(--palette-moodBlue-70);
  color       : var(--palette-hollywoodSmile);
}

.app-button.inactive {
  --button-bg : var(--palette-onyxBlack-100);
  color       : var(--palette-hollywoodSmile);
}

.app-button:hover {
  --button-bg: var(--palette-onyxBlack-80);
}

.app-button .app-state,
.app-button .app-notification-count {
  position: absolute;
  top     : 3px;
  right   : 3px;
  z-index : 1;
}

.app-name {
  overflow      : hidden;
  text-overflow : clip;
}

.app-button:after {
  content    : '';
  position   : absolute;
  top        : 0;
  right      : 0;
  height     : 100%;
  width      : 24px;
  background : linear-gradient(90deg, transparent, var(--button-bg), var(--button-bg) 12px);
}

.app-button:before {
  content     : '';
  position    : absolute;
  top         : 6px;
  height      : calc(100% - 12px);
  left        : 0;
  border-left : 1px solid var(--palette-onyxBlack-70);
}

.app-button:first-child:before {
  border-left-color: transparent;
}
/* stylelint-enable */
