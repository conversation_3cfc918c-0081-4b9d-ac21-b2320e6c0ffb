import React from 'react';

import clsx from 'clsx';

import { Loader, Radio, Textarea, utils } from '@product.front/ui-kit';

import { getProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';

import { getLocaleMessageById } from '../../../helpers/localeHelper';

import ErrorMessage from './ErrorMessage';

const Wysiwyg = React.lazy(
  () => import(/* webpackChunkName: "wysiwyg" */ '@monorepo/common/src/components/Wysiwyg'),
);

interface IDefaultTypeFields {
  text: string;
  validationResult: { text: string };
  onTextChange: (value: string) => void;
}

enum TextDisplayType {
  TextareaInput = 'textarea',
  WysiwygInput = 'wysiwyg',
}

const DefaultTypeFields = ({ text, validationResult, onTextChange }: IDefaultTypeFields) => {
  const [textDisplayType, setTextDisplayType] = React.useState<TextDisplayType>(
    TextDisplayType.TextareaInput,
  );

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4)}>
      <div className={clsx(utils.dFlex, utils.gap6)}>
        <Radio
          label={getLocaleMessageById('app.offer.form.input.textType.textarea')}
          value={TextDisplayType.TextareaInput}
          checked={textDisplayType === TextDisplayType.TextareaInput}
          onChange={({ value }) => setTextDisplayType(value)}
        />
        <Radio
          label={getLocaleMessageById('app.offer.form.input.textType.wysiwyg')}
          value={TextDisplayType.WysiwygInput}
          checked={textDisplayType === TextDisplayType.WysiwygInput}
          onChange={({ value }) => setTextDisplayType(value)}
        />
      </div>
      {textDisplayType === TextDisplayType.TextareaInput && (
        <Textarea
          label={getLocaleMessageById('app.offer.form.input.text')}
          value={text}
          onChange={({ value }) => onTextChange(value ?? '')}
          rows={14}
          required
          message={<ErrorMessage>{validationResult.text}</ErrorMessage>}
          isInvalid={!!validationResult.text}
        />
      )}
      {textDisplayType === TextDisplayType.WysiwygInput && (
        <div
          className={clsx(
            utils.border,
            utils.w100,
            utils.dFlex,
            utils.alignItemsCenter,
            utils.justifyContentCenter,
          )}
          style={{ height: '300px', borderRadius: '4px' }}
        >
          <React.Suspense fallback={<Loader />}>
            <Wysiwyg
              value={text}
              onChange={({ value }) => onTextChange(value)}
              uploadImageAndReturnUrlAsync={getProductServicesManager().files.upload}
            />
          </React.Suspense>
        </div>
      )}
    </div>
  );
};

export default DefaultTypeFields;
