import {
  AddRequestSubject,
  RequestSubject,
  UpdateRequestSubject,
} from '../@types/generated/administration';
import { IFrontRequestSubject } from '../@types/requestSubject';

export const mapRequestSubjectDtoToFront = (subjectDto: RequestSubject): IFrontRequestSubject => ({
  id: subjectDto.id,
  name: subjectDto.name,
  code: subjectDto.code,
  description: subjectDto.description ?? '',
  parentId: subjectDto.parentId ?? null,
  knowledgeBaseContext: subjectDto.knowledgeBaseContext ?? '',
  chatBotContext: subjectDto.chatBotContext ?? '',
  dialogScriptCode: subjectDto.dialogScriptCode ?? '',
});

export const mapRequestSubjectFrontToDto = (
  subject: IFrontRequestSubject,
): AddRequestSubject | UpdateRequestSubject => ({
  name: subject.name,
  code: subject.code,
  description: subject.description || null,
  parentId: subject.parentId,
  knowledgeBaseContext: subject.knowledgeBaseContext || null,
  chatBotContext: subject.chatBotContext || null,
  dialogScriptCode: subject.dialogScriptCode || null,
});
