export const getDateTimeFromTime = (time?: string) => {
  let newValue = null;
  if (time) {
    const date = new Date();
    const times = time.split(':');
    date.setHours(Number(times[0]));
    date.setMinutes(Number(times[1]));
    newValue = date.toISOString();
  }

  return newValue;
};

export const getTimeFromDateTime = (dateTime: string) => {
  if (!dateTime) return '';

  return `${new Date(dateTime)
    .getHours()
    .toLocaleString(undefined, { minimumIntegerDigits: 2 })}:${new Date(dateTime)
    .getMinutes()
    .toLocaleString(undefined, { minimumIntegerDigits: 2 })}`;
};
