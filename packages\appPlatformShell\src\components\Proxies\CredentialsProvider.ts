import { ICredentialsProvider, UserCredentials } from '@monorepo/common/src/platform/cm-interfaces';
import { ProxyBase } from '@monorepo/common/src/platform/utils';

export class CredentialsProvider extends ProxyBase implements ICredentialsProvider {
  constructor(baseUrl: string) {
    super(baseUrl, 'CredentialsProvider/');
  }

  async getCredentials(applicationName: string): Promise<UserCredentials> {
    const params = new URLSearchParams();
    params.append('applicationName', applicationName);
    return await this.getDataFromAction('GetCredentials', params);
  }
}
