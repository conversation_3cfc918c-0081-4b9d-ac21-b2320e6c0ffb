import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { IFrontRequestSubject } from '../../@types/requestSubject';

import extraReducers from './subjects.extraReducers';

export interface ISubjectsStore {
  loading: boolean;
  error?: SerializedError;
  subjects: Record<number, IFrontRequestSubject>;
  selectedSubject: IFrontRequestSubject | null;
}

const initialState: ISubjectsStore = {
  loading: false,
  subjects: [],
  selectedSubject: null,
};

const subjectsSlice = createSlice({
  name: 'subjects',
  initialState,
  reducers: {
    setSelectedRequest(state, action: PayloadAction<IFrontRequestSubject | null>) {
      state.selectedSubject = action.payload;
    },
  },
  extraReducers,
});

export const { setSelectedRequest } = subjectsSlice.actions;

export default subjectsSlice.reducer;
