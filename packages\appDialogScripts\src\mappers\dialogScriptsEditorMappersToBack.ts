import { IFrontAttachmentWithRealFile } from '@monorepo/common/src/@types/frontendChat';
import {
  AddAnswerDto,
  AddProductActionParameterDto,
  AddRouterConditionDto,
  AddRouterRuleDto,
  AddScriptAutomationServiceDto,
  AddScriptDto,
  AddScriptRouterDto,
  AddStepDto,
  AddSubScriptDto,
  AddUpdateAutomationServiceParamDto,
  AnswerDto,
  AnswerTypes,
  BaseStepDesignerPropsDto,
  ProductActionParameterDto,
  ConditionOperations,
  RouterRuleDto,
  ScriptVariableDto,
  StepType,
  UpdateProductActionDto,
  UpdateScriptAutomationServiceDto,
  UpdateScriptDto,
  UpdateStepDto,
  UpdateSubScriptDto,
  AddScriptStepCommon,
  UpdateScriptStepCommon,
  AddProductActionDto,
  UpdateScriptRouterDto,
  FileVariable,
  AddScenarioStepDto,
  UpdateScenarioStepDto,
  ChoiceStepAnswerDto,
} from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import {
  FrontConditionOperator,
  FrontStepType,
  IChoiceStepAnswer,
  IFileVariable,
  IFrontAnswer,
  IFrontRule,
  IFrontRuleCondition,
  IFrontScript,
  IFrontServiceParameter,
  IFrontStep,
  SystemOwner,
} from '@monorepo/dialog-scripts/src/@types/script';

const shouldStepHasAnswers = (step: IFrontStep) =>
  ![AnswerTypes.None, AnswerTypes.TextInput, AnswerTypes.File].includes(
    step.answerDisplayType as AnswerTypes,
  );

const getTransfer = (nextValue: string | number | undefined, nextStep?: IFrontStep) => {
  if (nextValue === 'default') return nextStep?.code ?? null;

  if (!nextValue) return null;

  return nextValue?.toString() || null;
};

const mapFrontStepTypeToStepType = (frontStepType?: FrontStepType): StepType => {
  if (frontStepType === FrontStepType.Router) {
    return StepType.Router;
  }

  if (frontStepType === FrontStepType.Service) {
    return StepType.Service;
  }
  if (frontStepType === FrontStepType.Subscript) {
    return StepType.SubScript;
  }
  if (frontStepType === FrontStepType.Scenario) {
    return StepType.Scenario;
  }
  if (frontStepType === FrontStepType.Terminal) {
    return StepType.ProductAction;
  }
  if (frontStepType === FrontStepType.Rating) {
    return StepType.Rating;
  }
  return StepType.Step;
};

const mapConditionOperationToBack = (operator: FrontConditionOperator): ConditionOperations => {
  const map = {
    [FrontConditionOperator.Eq]: ConditionOperations.Eq,
    [FrontConditionOperator.NotEq]: ConditionOperations.NotEq,
    [FrontConditionOperator.Lt]: ConditionOperations.Lt,
    [FrontConditionOperator.Gt]: ConditionOperations.Gt,
    [FrontConditionOperator.LtEq]: ConditionOperations.LtEq,
    [FrontConditionOperator.GtEq]: ConditionOperations.GtEq,
    [FrontConditionOperator.Contain]: ConditionOperations.Contain,
    [FrontConditionOperator.NotContain]: ConditionOperations.NotContain,
  };

  return map[operator];
};

const mapRouterAddConditionFrontToDto = (
  frontRouterCondition: IFrontRuleCondition,
): AddRouterConditionDto => ({
  operation: mapConditionOperationToBack(
    frontRouterCondition?.operator || FrontConditionOperator.Eq,
  ),
  rightPartValue: frontRouterCondition?.value ?? null,
  leftPartVariableCode: frontRouterCondition?.variable,
});

// * Front to AddDto models

const stepToBaseStepDesignerPropsDto = (step: IFrontStep): BaseStepDesignerPropsDto | undefined => {
  if (!step.positionX || !step.positionY) {
    return;
  }
  const res = {};

  step.positionX &&
    Object.assign<BaseStepDesignerPropsDto, BaseStepDesignerPropsDto>(res, {
      coordX: Math.floor(step.positionX),
    });
  step.positionY &&
    Object.assign<BaseStepDesignerPropsDto, BaseStepDesignerPropsDto>(res, {
      coordY: Math.floor(step.positionY),
    });

  return res;
};

const mapFrontAnswerToAddDto = (answerFront: IFrontAnswer, nextStep: IFrontStep): AddAnswerDto => ({
  nextStepCode: getTransfer(answerFront.transferTo, nextStep) || null,
  valueText: answerFront.text,
  order: answerFront.order,
});

const mapFrontRulesToAddDto = (ruleFront: IFrontRule, nextStep: IFrontStep): AddRouterRuleDto => ({
  nextStepCode: getTransfer(ruleFront.transferTo || '', nextStep) || null,
  name: ruleFront.name,
  conditions: ruleFront.conditions.map(mapRouterAddConditionFrontToDto),
  priority: ruleFront.priority,
});

const mapFrontAttachmentToBackAttachmentId = (
  frontAttachment: IFrontAttachmentWithRealFile,
): string => {
  return frontAttachment.externalId || frontAttachment.id;
};

const mapServiceParametersToBack = (
  frontParameter: IFrontServiceParameter,
): AddUpdateAutomationServiceParamDto => {
  return {
    serviceParamCode: frontParameter.key ?? null,
    scriptVariableCode: frontParameter.variableCode ?? null,
    manualValue: !frontParameter.variableCode ? frontParameter.value : null,
  };
};

const mapFileVariableToBack = (fileVariable: IFileVariable): FileVariable => {
  return {
    fileName: fileVariable.fileName ?? undefined,
    variableCode: fileVariable.variableCode ?? null,
  };
};

const mapFrontStepToAddDto = (
  stepsFront: IFrontStep[],
): Record<
  string,
  | AddStepDto
  | AddScriptRouterDto
  | AddSubScriptDto
  | AddScriptAutomationServiceDto
  | AddProductActionDto
  | AddScenarioStepDto
> => {
  const stepsDto: Record<
    string,
    | AddStepDto
    | AddScriptRouterDto
    | AddSubScriptDto
    | AddScriptAutomationServiceDto
    | AddProductActionDto
    | AddScenarioStepDto
  > = {};
  // eslint-disable-next-line sonarjs/cognitive-complexity
  stepsFront.forEach((stepFront, index) => {
    let commonParts: AddScriptStepCommon = {
      stepType: mapFrontStepTypeToStepType(stepFront.type),
      name: stepFront.name,
      description: stepFront.description,
      designerProps: stepToBaseStepDesignerPropsDto(stepFront),
      nextStepCode: getTransfer(stepFront.stepTransfer, stepsFront[index + 1]),
    };
    if (stepFront.type === FrontStepType.Step) {
      Object.assign<AddScriptStepCommon, Partial<AddStepDto>>(commonParts, {
        choiceStepAnswer:
          stepFront.answerDisplayType === AnswerTypes.Choice
            ? mapFrontChoiceStepAnswerToUpdateDto(stepFront.choiceStepAnswer)
            : null,
        answersType: stepFront.answerDisplayType,
        variableCode: stepFront.answerDisplayType === AnswerTypes.None ? null : stepFront?.variable,
        promptUrl: stepFront.promptUrl,
        isSkippable: stepFront.isSkippable,
        answers: !shouldStepHasAnswers(stepFront)
          ? []
          : (stepFront.answers?.map((answer) =>
              mapFrontAnswerToAddDto(answer, stepsFront[index + 1]),
            ) ?? []),
        attachments: stepFront.attachments?.map(mapFrontAttachmentToBackAttachmentId),
        limitStepCode: stepFront.limitStepTransfer,
        timeoutStepCode: stepFront.timeoutStepTransfer,
        isBackButtonAvailable: stepFront.isFirstStep
          ? false
          : (stepFront.isBackButtonAvailable ?? false),
        attachmentVariableCodes: stepFront.attachmentVariableCodes?.map((rv) => rv.variableCode),
      });

      if ((stepFront.answers?.length ?? 0) > 0 && shouldStepHasAnswers(stepFront)) {
        Object.assign(commonParts, { nextStepCode: null });
      }
    }
    if (stepFront.type === FrontStepType.Rating) {
      Object.assign<AddScriptStepCommon, Partial<AddStepDto>>(commonParts, {
        answersType: AnswerTypes.Button,
        variableCode: stepFront?.variable,
        isSkippable: stepFront.isSkippable,
        answers:
          stepFront.answers?.map((answer) =>
            mapFrontAnswerToAddDto(answer, stepsFront[index + 1]),
          ) ?? [],
        isBackButtonAvailable: stepFront.isFirstStep
          ? false
          : (stepFront.isBackButtonAvailable ?? false),
      });

      if ((stepFront.answers?.length ?? 0) > 0) {
        Object.assign(commonParts, { nextStepCode: null });
      }
    }
    if (stepFront.type === FrontStepType.Router) {
      commonParts = {
        ...commonParts,
        rules:
          stepFront.rules?.map((rule) => mapFrontRulesToAddDto(rule, stepsFront[index + 1])) || [],
      } as AddScriptRouterDto;
    }

    if (stepFront.type === FrontStepType.Service) {
      commonParts = {
        ...commonParts,
        serviceId: stepFront.automationServiceId,
        nextFailStepCode: stepFront.stepFallbackTransfer
          ? getTransfer(stepFront.stepFallbackTransfer, stepsFront[index + 1])
          : null,
        serviceParameters:
          [
            ...(stepFront.serviceParameters || []),
            ...(stepFront.serviceOutputParameters || []),
          ]?.map(mapServiceParametersToBack) || [],
        fileVariables: stepFront.files?.map(mapFileVariableToBack),
      } as AddScriptAutomationServiceDto;
    }

    if (stepFront.type === FrontStepType.Subscript) {
      commonParts = {
        ...commonParts,
        scriptId: stepFront.subscript?.id ?? null,
      } as AddSubScriptDto;
    }

    if (stepFront.type === FrontStepType.Scenario) {
      commonParts = {
        ...commonParts,
        scriptId: stepFront.scenario?.id ?? null,
      } as AddScenarioStepDto;
    }

    if (stepFront.type === FrontStepType.Terminal) {
      Object.assign<AddScriptStepCommon, AddProductActionDto>(commonParts, {
        stepType: mapFrontStepTypeToStepType(stepFront.type),
        productActionParameters: Object.entries(stepFront.terminalUpdatesMap ?? {}).reduce<
          AddProductActionParameterDto[]
        >((acc, [key, value]) => {
          // Игнор добавленных, но незаполненных
          if (!key || key === '_' || !value) {
            return acc;
          }

          return acc.concat({
            source: key,
            scriptVariableCode: value,
          });
        }, []),
        requestThemeCode: stepFront.terminalSubject,
        requestAction: stepFront.terminalAction,
      });
    }

    stepsDto[stepFront.code] = commonParts;
  });

  return stepsDto;
};

const mapFrontStepsToScriptVariableDtoMap = (
  stepsFront: IFrontStep[],
): Record<string, ScriptVariableDto> => {
  const vars: Record<string, ScriptVariableDto> = {};
  stepsFront.forEach((stepFront) => {
    if (
      (stepFront.type === FrontStepType.Step || stepFront.type === FrontStepType.Rating) &&
      stepFront.variable
    ) {
      vars[stepFront.variable] = {
        name: stepFront.variableName || stepFront.variable,
        source: stepFront.variableSource,
      };
    }

    if (stepFront.type === FrontStepType.Service && stepFront.serviceOutputParameters?.length) {
      stepFront.serviceOutputParameters.forEach((outParam) => {
        if (!outParam?.variableCode) return;
        vars[outParam.variableCode] = {
          name: outParam?.variableName || outParam.variableCode,
        };
      });
    }

    if (stepFront.type === FrontStepType.Router && stepFront.rules?.length) {
      stepFront.rules
        .filter((rule) =>
          rule.conditions.some((condition) => condition.variable && !vars[condition.variable]),
        )
        .forEach((rule) => {
          if (!rule.conditions) return;

          rule.conditions.forEach((condition) => {
            if (!condition.variable || !!vars[condition.variable]) return;

            vars[condition.variable] = {
              name: condition.variableName || condition.variable,
              source: condition.variable,
            };
          });
        });
    }
  });

  return vars;
};

export const mapFrontScriptToAddDto = (
  scriptFront: IFrontScript,
  ownerSystemCode: SystemOwner | null,
): AddScriptDto => ({
  code: scriptFront.code.trim(),
  scriptKeywords: scriptFront.tags,
  name: scriptFront.name.trim(),
  description: scriptFront.description.trim(),
  activateFrom: scriptFront.activeFrom || null,
  activateTo: scriptFront.activeTo || null,
  canBeAutomated: scriptFront.canBeAutomated ?? false,
  stepsMap: mapFrontStepToAddDto(scriptFront.steps),
  scriptVariables: mapFrontStepsToScriptVariableDtoMap(scriptFront.steps),
  startingStepCode: scriptFront.steps.find((step) => step.isFirstStep)?.code ?? null,
  ownerSystemCode,
  priority: scriptFront.priority,
});

// * Front to UpdateDto models

const mapFrontAnswerToUpdateDto = (answerFront: IFrontAnswer, nextStep: IFrontStep): AnswerDto => ({
  id: typeof answerFront.id === 'number' ? answerFront.id : undefined,
  nextStepCode: getTransfer(answerFront.transferTo, nextStep),
  valueText: answerFront.text,
  order: answerFront.order,
});

const mapFrontRulesToUpdateDto = (ruleFront: IFrontRule, nextStep: IFrontStep): RouterRuleDto => ({
  id: typeof ruleFront.id === 'number' ? ruleFront.id : undefined,
  nextStepCode: getTransfer(ruleFront.transferTo || '', nextStep) || null,
  name: ruleFront.name,
  conditions: ruleFront.conditions.map(mapRouterAddConditionFrontToDto),
  priority: ruleFront.priority,
});

const mapFrontChoiceStepAnswerToUpdateDto = (
  choiceStepAnswer?: IChoiceStepAnswer,
): ChoiceStepAnswerDto | null | undefined => {
  if (!choiceStepAnswer) {
    return null;
  }

  return {
    nextStepCode: getTransfer(choiceStepAnswer.nextStepCode) ?? '',
    variableArrayCode: choiceStepAnswer.variableArrayCode,
    displayTextVariant: choiceStepAnswer.displayTextVariant,
    keyValueButton: choiceStepAnswer.keyValueButton,
  };
};

const mapFrontStepToUpdateDto = (
  stepsFront: IFrontStep[],
): Record<
  string,
  | UpdateStepDto
  | UpdateScriptRouterDto
  | UpdateSubScriptDto
  | UpdateScriptAutomationServiceDto
  | UpdateProductActionDto
  | UpdateScenarioStepDto
> => {
  const stepsDto: Record<
    string,
    | UpdateStepDto
    | UpdateScriptRouterDto
    | UpdateSubScriptDto
    | UpdateScriptAutomationServiceDto
    | UpdateProductActionDto
    | UpdateScenarioStepDto
  > = {};
  /* eslint-disable sonarjs/cognitive-complexity */
  stepsFront.forEach((stepFront, index) => {
    const commonParts: UpdateScriptStepCommon = {
      stepType: mapFrontStepTypeToStepType(stepFront.type),
      name: stepFront.name,
      description: stepFront.description,
      nextStepCode: getTransfer(stepFront.stepTransfer, stepsFront[index + 1]),
      designerProps: stepToBaseStepDesignerPropsDto(stepFront),
    };
    if (stepFront.type === FrontStepType.Step) {
      Object.assign<UpdateScriptStepCommon, Partial<UpdateStepDto>>(commonParts, {
        choiceStepAnswer:
          stepFront.answerDisplayType === AnswerTypes.Choice
            ? mapFrontChoiceStepAnswerToUpdateDto(stepFront.choiceStepAnswer)
            : null,
        answersType: stepFront.answerDisplayType,
        variableCode: stepFront.answerDisplayType === AnswerTypes.None ? null : stepFront?.variable,
        limit: stepFront.limit,
        timeout: stepFront.timeout,
        answers: !shouldStepHasAnswers(stepFront)
          ? []
          : (stepFront.answers?.map((answer) =>
              mapFrontAnswerToUpdateDto(answer, stepsFront[index + 1]),
            ) ?? []),
        isSkippable: stepFront.isSkippable,
        attachments: stepFront.attachments?.map(mapFrontAttachmentToBackAttachmentId),
        promptUrl: stepFront.promptUrl,
        limitStepCode: stepFront.limitStepTransfer,
        timeoutStepCode: stepFront.timeoutStepTransfer,
        isBackButtonAvailable: stepFront.isFirstStep
          ? false
          : (stepFront.isBackButtonAvailable ?? false),
        attachmentVariableCodes: stepFront.attachmentVariableCodes?.map((rv) => rv.variableCode),
      });

      if ((stepFront.answers?.length ?? 0) > 0 && shouldStepHasAnswers(stepFront)) {
        Object.assign(commonParts, { nextStepCode: null });
      }
    }

    if (stepFront.type === FrontStepType.Rating) {
      Object.assign<UpdateScriptStepCommon, Partial<UpdateStepDto>>(commonParts, {
        answersType: AnswerTypes.Button,
        variableCode: stepFront?.variable,
        answers:
          stepFront.answers?.map((answer) =>
            mapFrontAnswerToUpdateDto(answer, stepsFront[index + 1]),
          ) ?? [],
        isSkippable: stepFront.isSkippable,

        isBackButtonAvailable: stepFront.isFirstStep
          ? false
          : (stepFront.isBackButtonAvailable ?? false),
      });

      if ((stepFront.answers?.length ?? 0) > 0) {
        Object.assign(commonParts, { nextStepCode: null });
      }
    }

    if (stepFront.type === FrontStepType.Router) {
      Object.assign<UpdateScriptStepCommon, Partial<UpdateScriptRouterDto>>(commonParts, {
        rules:
          stepFront.rules?.map((rule) => mapFrontRulesToUpdateDto(rule, stepsFront[index + 1])) ??
          [],
      });
    }

    if (stepFront.type === FrontStepType.Service) {
      Object.assign<UpdateScriptStepCommon, UpdateScriptAutomationServiceDto>(commonParts, {
        stepType: mapFrontStepTypeToStepType(stepFront.type),
        serviceId: stepFront.automationServiceId!,
        nextFailStepCode: stepFront.stepFallbackTransfer
          ? getTransfer(stepFront.stepFallbackTransfer, stepsFront[index + 1])
          : null,
        serviceParameters:
          [
            ...(stepFront.serviceParameters || []),
            ...(stepFront.serviceOutputParameters || []),
          ]?.map(mapServiceParametersToBack) || [],
        fileVariables: stepFront.files?.map(mapFileVariableToBack),
      });
    }

    if (stepFront.type === FrontStepType.Subscript) {
      Object.assign<UpdateScriptStepCommon, UpdateSubScriptDto>(commonParts, {
        stepType: mapFrontStepTypeToStepType(stepFront.type),
        scriptId: stepFront.subscript?.id ?? null,
      });
    }

    if (stepFront.type === FrontStepType.Scenario) {
      Object.assign<UpdateScriptStepCommon, UpdateScenarioStepDto>(commonParts, {
        stepType: mapFrontStepTypeToStepType(stepFront.type),
        scriptId: stepFront.scenario?.id ?? null,
      });
    }

    if (stepFront.type === FrontStepType.Terminal) {
      Object.assign<UpdateScriptStepCommon, UpdateProductActionDto>(commonParts, {
        stepType: mapFrontStepTypeToStepType(stepFront.type),
        productActionParameters: Object.entries(stepFront.terminalUpdatesMap ?? {}).reduce<
          ProductActionParameterDto[]
        >((acc, [key, value]) => {
          // Игнор добавленных, но незаполненных
          if (!key || key === '_' || !value) {
            return acc;
          }

          return acc.concat({
            source: key,
            scriptVariableCode: value,
          });
        }, []),
        requestThemeCode: stepFront.terminalSubject,
        requestAction: stepFront.terminalAction,
        nextStepCode: null, // У терминального шага не должно быть следующего - он конечный
      });
    }

    stepsDto[stepFront.code] = commonParts;
  });
  /* eslint-enable sonarjs/cognitive-complexity */

  return stepsDto;
};

export const mapFrontScriptToUpdateDto = (
  scriptFront: IFrontScript,
  ownerSystemCode: SystemOwner | null,
): UpdateScriptDto => ({
  code: scriptFront.code.trim(),
  scriptKeywords: scriptFront.tags,
  name: scriptFront.name.trim(),
  description: scriptFront.description.trim(),
  activateFrom: scriptFront.activeFrom || null,
  activateTo: scriptFront.activeTo || null,
  canBeAutomated: scriptFront.canBeAutomated ?? false,
  stepsMap: mapFrontStepToUpdateDto(scriptFront.steps),
  scriptVariables: mapFrontStepsToScriptVariableDtoMap(scriptFront.steps),
  startingStepCode: scriptFront.steps.find((step) => step.isFirstStep)?.code ?? null,
  ownerSystemCode,
  priority: scriptFront.priority,
});
