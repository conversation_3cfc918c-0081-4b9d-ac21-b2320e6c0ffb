import { createAsyncThunk } from '@reduxjs/toolkit';
import { Guid } from 'guid-typescript';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import { TimePeriod, timePeriods } from '@monorepo/common/src/helpers/timePeriods';

import { IOdataRequest } from '../../@types/Requests';
import { getSettings } from '../../config/appSettings';
import mapOdataRequestToFront from '../../mappers/mapOdataRequestToFront';

const requestsNumberByPage = 100;

interface IGetRequestsParams {
  pageNumber: number;
  operatorId: Guid;
  timePeriod: TimePeriod;
  additionalFilter?: string;
  sortString?: string;
}

const odataCount = '@odata.count';

export const getRequests = createAsyncThunk(
  'requests/getAll',
  async ({
    pageNumber,
    operatorId,
    timePeriod,
    additionalFilter,
    sortString,
  }: IGetRequestsParams) => {
    const paginationString = `$top=${requestsNumberByPage}&$skip=${
      (pageNumber - 1) * requestsNumberByPage
    }&$count=true`;

    const timeData = timePeriods[timePeriod];

    const timeFrom = timeData.from ? ` and TimeRegistered gt ${timeData.from}` : '';
    const timeTo = timeData.to ? ` and TimeRegistered lt ${timeData.to}` : '';

    const appSettings = getSettings();

    const data = await commonFetch(
      `${
        appSettings.dataPresentationServiceUrl
      }DataPresentation/Requests?$filter=ExecutorId eq ${operatorId}${timeFrom}${timeTo}${
        additionalFilter ?? ''
      }&$orderBy=${sortString}&${paginationString}`,
      { credentials: 'include' },
    );
    const json = (await data.json()) as {
      error?: { message: string };
      value: IOdataRequest[];
      [odataCount]: number;
    };

    if (json.error) {
      throw new Error(json.error.message);
    }

    return {
      requests: json.value.map(mapOdataRequestToFront),
      requestsNumber: json[odataCount],
      lastPageNumber: Math.ceil(json[odataCount] / requestsNumberByPage),
    };
  },
);
