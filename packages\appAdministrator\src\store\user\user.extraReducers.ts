import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';
import { MessageType } from '@monorepo/services/src/@types/generated/productApi';

import { IUserStore } from './user.slice';
import { getAvailableAddressTypesData } from './user.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<IUserStore>) =>
  builder.addCase(getAvailableAddressTypesData.fulfilled, (state, action) => {
    action.payload.forEach((value) => {
      state.addressData[value.code as UcmmChannel] = {
        type: value.type ?? MessageType.Undefined,
        displayName: value.displayName ?? '',
      };
    });
  });

export default extraReducers;
