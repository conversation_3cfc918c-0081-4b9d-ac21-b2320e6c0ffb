import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IAutomationServicesStore } from './automationServices.slice';
import {
  archiveAutomationService,
  executeAutomationService,
  getAutomationServices,
  publishAutomationService,
} from './automationServices.thunk';

export default (builder: ActionReducerMapBuilder<IAutomationServicesStore>) => {
  // getAutomationServices
  builder
    .addCase(getAutomationServices.pending, (state) => {
      state.selectedAutomationService = null;
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAutomationServices.fulfilled, (state, action) => {
      state.loading = false;
      state.automationServices = action.payload;
    })
    .addCase(getAutomationServices.rejected, (state, action) => {
      state.loading = false;
      state.automationServices = [];
      state.error = action.error;
      console.error(action.error);
    })
    // publishAutomationService
    .addCase(publishAutomationService.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(publishAutomationService.fulfilled, (state) => {
      state.loading = false;
    })
    .addCase(publishAutomationService.rejected, (state, action) => {
      state.loading = false;
      console.error(action.error);
    })
    // archiveAutomationService
    .addCase(archiveAutomationService.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(archiveAutomationService.fulfilled, (state) => {
      state.loading = false;
    })
    .addCase(archiveAutomationService.rejected, (state, action) => {
      state.loading = false;
      console.error(action.error);
    })
    // executeAutomationService
    .addCase(executeAutomationService.pending, (state) => {
      state.executionResult = null;
    })
    .addCase(executeAutomationService.fulfilled, (state, action) => {
      state.executionResult = action.payload;
    });
  return builder;
};
