import { Guid } from 'guid-typescript';

import { NewOperator, OperatorView, OperatorViewBase } from '../@types/generated/administration';
import { IFrontOperator, IFrontOperatorBase } from '../@types/operator';
import { getCuratorFio } from '../helpers/operatorHelper';

import {
  mapFrontSessionSettingsToNewDto,
  mapFrontStatusSettingsToNewDto,
  mapSessionSettingsDtoToFront,
  mapStatusSettingsDtoToFront,
} from './operatorGroups';

export const mapOperatorDtoToFront = (operator: OperatorViewBase): IFrontOperatorBase => ({
  id: operator.id,
  login: operator.login,
  lastName: operator.lastName ?? '',
  firstName: operator.firstName ?? '',
  middleName: operator.middleName ?? '',
  groups: operator.groups?.map((group) => ({ id: group.id, name: group.name })) ?? [],
  curatorId: operator.curator?.id ?? Guid.EMPTY,
  curatorName: operator.curator ? getCuratorFio(operator.curator) : undefined,
});

export const mapFullOperatorDtoToFront = (operator: OperatorView): IFrontOperator => ({
  id: operator.id,
  login: operator.login,
  lastName: operator.lastName ?? '',
  firstName: operator.firstName ?? '',
  middleName: operator.middleName ?? '',
  email: operator.email ?? '',
  groups: operator.groups?.map((group) => ({ id: group.id, name: group.name })) ?? [],
  sessionSettings: mapSessionSettingsDtoToFront(operator.sessionSettings),
  statusSettings: mapStatusSettingsDtoToFront(operator.statusSettings),
  kpiSettings:
    operator.kpiSettings?.map((kpi) => ({ code: kpi.code, target: kpi.target ?? null })) ?? [],
  curatorId: operator.curator?.id ?? Guid.EMPTY,
  curatorName: operator.curator ? getCuratorFio(operator.curator) : undefined,
});

export const mapFrontOperatorToNewDto = (operator: IFrontOperator): NewOperator => ({
  login: operator.login,
  lastName: operator.lastName || null,
  firstName: operator.firstName || null,
  middleName: operator.middleName || null,
  email: operator.email || null,
  sessionSettings: mapFrontSessionSettingsToNewDto(operator.sessionSettings),
  statusSettings: mapFrontStatusSettingsToNewDto(operator.statusSettings),
  kpiSettings: operator.kpiSettings?.map((kpi) => ({ code: kpi.code, target: kpi.target })) ?? [],
  curatorId: operator.curatorId === Guid.EMPTY ? undefined : operator.curatorId,
});
