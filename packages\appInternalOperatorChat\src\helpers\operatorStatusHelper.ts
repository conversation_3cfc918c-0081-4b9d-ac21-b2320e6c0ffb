import { IndicationType } from '@product.front/ui-kit';

import { OperatorStatus } from '../@types/generated/signalr';

export function getIndicationTypeForOperatorStatus(status: OperatorStatus): IndicationType {
  //Offline = Offline (серый на фронте)
  if (status === OperatorStatus.Offline) {
    return IndicationType.Offline;
  }
  //InWork = Busy (красный на фронте)
  if (status === OperatorStatus.Busy) {
    return IndicationType.Busy;
  }
  //Not available (оранжевый на фронте)
  if (status === OperatorStatus.NotAvailable) {
    return IndicationType.Away;
  }
  //Остальное = Free (зеленый на фронте)
  if (status === OperatorStatus.Available) {
    return IndicationType.Online;
  }
  //Остальное = Free (зеленый на фронте)
  return IndicationType.Offline;
}
export function getTextForOperatorStatus(status: OperatorStatus): string {
  //Offline = Offline (серый на фронте)
  if (status === OperatorStatus.Offline) {
    return 'Не в сети';
  }
  //InWork = Busy (красный на фронте)
  if (status === OperatorStatus.Busy) {
    return 'Занят';
  }
  //Остальное = Free (зеленый на фронте)
  if (status === OperatorStatus.Available) {
    return 'Онлайн';
  }

  //Not available (оранжевый на фронте)
  if (status === OperatorStatus.NotAvailable) {
    return 'Нет на месте';
  }
  return 'Статус неизвестен';
}
