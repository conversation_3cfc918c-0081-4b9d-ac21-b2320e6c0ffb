import React from 'react';

import clsx from 'clsx';

import {
  Badge,
  BadgeType,
  CanClearBehavior,
  grids,
  Input,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getVariableRegExpPatternByScriptSteps } from '../../../helpers/scriptHelpers';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import InputErrorMessage from '../../InputErrorMessage';

import StepFormDescription from './components/StepFormDescription';
import DeleteStepButton from './DeleteStepButton';
import FirstStepButton from './FirstStepButton';
import TransferSelect from './TransferSelect';

import styles from './styles.module.scss';

interface IRatingStepProps {
  step: IFrontStep;
  steps: IFrontStep[];
  number: number;
  onlyStep: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
  disabled: boolean;
  requiredForActive: boolean;
}

const RatingStep = ({
  step,
  steps,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: IRatingStepProps) => {
  const variableInputRef = React.useRef<HTMLInputElement>(null);

  const [name, setName] = React.useState(step.name);

  const [variable, setVariable] = React.useState(step.variable ?? '');
  const [variableName, setVariableName] = React.useState(step.variableName ?? '');

  const variableRegexp = React.useMemo(() => {
    return getVariableRegExpPatternByScriptSteps(
      steps.filter((currentStep) => {
        return currentStep.code !== step.code;
      }),
    );
  }, [steps, step]);

  React.useEffect(() => {
    variableInputRef.current?.checkValidity();
  }, [variableInputRef]);

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,

        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {step.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
          <FirstStepButton stepCode={step.code} isFirstStep={step.isFirstStep} />
          {!onlyStep && (
            <DeleteStepButton
              needConfirm={Boolean(
                name ||
                  step.description ||
                  step.stepTransfer !== 'default' ||
                  variable ||
                  variableName,
              )}
              disabled={disabled}
              onDelete={onDelete}
            />
          )}
        </aside>
      </div>
      <Input
        label={getLocaleMessageById('app.modals.form.stepName')}
        placeholder={getLocaleMessageById('app.editor.noName')}
        value={name}
        autoFocus={!name.length}
        onChange={({ value }) => setName(value || '')}
        onBlur={() => onChange({ ...step, name })}
        canClearBehavior={CanClearBehavior.Value}
        required={requiredForActive}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.name}
        message={<InputErrorMessage>{step.invalidReasons?.name}</InputErrorMessage>}
      />

      <StepFormDescription
        step={step}
        onChange={onChange}
        required={requiredForActive}
        disabled={disabled}
      />

      <TransferSelect
        withEnd={false}
        label={getLocaleMessageById('app.modals.form.transfer')}
        value={step.stepTransfer}
        steps={steps.filter((s) => s.code !== step.code)}
        onChange={({ value }) => {
          const newTransfer = value ?? 'default';
          onChange({
            ...step,
            stepTransfer: newTransfer,
            answers: step.answers?.map((answer) => ({ ...answer, transferTo: newTransfer })),
          });
        }}
        required={requiredForActive}
        disabled={disabled}
      />

      <div className={clsx(grids.row, utils.gap4)}>
        <Input
          wrapperClassName={grids.col6}
          label={getLocaleMessageById('app.modals.form.variableName')}
          value={variableName}
          onChange={({ value }) => setVariableName(value || '')}
          onBlur={() => onChange({ ...step, variableName })}
          canClearBehavior={CanClearBehavior.Value}
          disabled={disabled}
          isInvalid={!!step.invalidReasons?.variableName}
          message={<InputErrorMessage>{step.invalidReasons?.variableName}</InputErrorMessage>}
        />
        <Input
          ref={variableInputRef}
          wrapperClassName={grids.col6}
          label={getLocaleMessageById('app.modals.form.variableCode')}
          pattern={variableRegexp}
          value={variable}
          onChange={({ value }, event) => {
            event?.target.checkValidity();
            setVariable(value || '');
            if (!event?.target || !value?.match(variableRegexp)) return;

            event.target.setCustomValidity('');
            event.target.setAttribute('title', '');
          }}
          onBlur={() => onChange({ ...step, variable })}
          canClearBehavior={CanClearBehavior.Value}
          disabled={disabled}
          onInvalid={(event) => {
            const target = event.target as HTMLInputElement;
            const validityState = target.validity;
            let errorMessage = '';
            if (validityState.patternMismatch) {
              errorMessage = getLocaleMessageById('app.error.notUniqueVariable');
            }
            if (!errorMessage) return;

            target.setCustomValidity(errorMessage);
            target.title = errorMessage;
          }}
          isInvalid={!!step.invalidReasons?.variable}
          message={<InputErrorMessage>{step.invalidReasons?.variable}</InputErrorMessage>}
        />
      </div>

      {step.answers?.map((answer, answerIndex) => {
        return (
          <div key={answer.id} className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            <Badge type={BadgeType.Attention}>{answer.text}</Badge>
            <TransferSelect
              wrapperClassName={utils.w100}
              steps={steps}
              label={getLocaleMessageById('app.modals.form.transfer')}
              required={requiredForActive}
              value={answer.transferTo.toString()}
              onChange={({ value }) => {
                const stepPatch: IFrontStep = { ...step };
                const transferTo = value ?? 'default';

                if (transferTo !== 'default' && transferTo !== step.stepTransfer) {
                  stepPatch.stepTransfer = undefined;
                }

                stepPatch.answers = step.answers?.map((a, i) => {
                  if (i === answerIndex) return { ...a, transferTo };
                  return a;
                });

                onChange(stepPatch);
              }}
              disabled={disabled}
            />
          </div>
        );
      })}
    </div>
  );
};

export default RatingStep;
