import { createAsyncThunk } from '@reduxjs/toolkit';

import { IFrontFolder } from '@monorepo/common/src/@types/templates';

import { PersonalTemplate, TemplateCategory } from '../../@types/generated/administration';
import { mapPersonalFolderToFrontFolder } from '../../mappers/folders';
import { mapPersonalTemplateToFrontTemplate } from '../../mappers/templates';
import {
  getFolderTemplates,
  getTemplate,
  ISearchFolderTemplatesOptions,
} from '../../services/template';

export const getAllPersonalFolderTemplates = createAsyncThunk(
  'personalTemplates/allFolderTemplates',
  async (searchOptions: Omit<ISearchFolderTemplatesOptions, 'statuses'>) => {
    const data = await getFolderTemplates(TemplateCategory.Personal, searchOptions);

    const foldersMap: Record<string, IFrontFolder> = {};

    foldersMap['root'] = {
      id: 'personalRoot',
      ownerId: null,
      parentId: null,
      title: '',
      childrenFolders: data.personal?.map((view) => view.folder.id) ?? [],
      templates: [],
    };
    data.personal?.forEach((view) => {
      mapPersonalFolderToFrontFolder(view.folder, foldersMap);
    });

    return foldersMap;
  },
);

export const selectPersonalTemplate = createAsyncThunk(
  'personalTemplates/select',
  async (templateId: string) => {
    const data = (await getTemplate(templateId)) as PersonalTemplate;

    return mapPersonalTemplateToFrontTemplate(data);
  },
);
