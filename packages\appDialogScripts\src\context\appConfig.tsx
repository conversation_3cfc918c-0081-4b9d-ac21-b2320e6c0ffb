import React from 'react';

import { FrontStepType, SystemOwner } from '@monorepo/dialog-scripts/src/@types/script';

import { AppConfig, ExternalAppConfig } from '../@types/context';
import { ColumnNames } from '../components/Body/columns';
import { getLocaleMessageById } from '../helpers/localeHelper';

export const defaultDialogScriptsConfig: AppConfig = {
  title: getLocaleMessageById('app.header.title'),
  columns: [
    'name',
    'code',
    'status',
    'createdBy',
    'createdDate',
    'changedBy',
    'changedDate',
    'activeFrom',
    'activeTo',
    'rating',
    'runs',
    'aborted',
  ] satisfies ColumnNames[],
  editorTitlePlaceholder: getLocaleMessageById('app.editor.scriptHeader'),
  canAutostart: true,
  canSetPriority: false,
  stepTypes: [
    FrontStepType.Step,
    FrontStepType.Router,
    FrontStepType.Service,
    FrontStepType.Subscript,
  ],
  disableAutoRelation: false,
  fileExtension: 'csd',
  canChangeAvailableStatus: false,
  allowRichStepDescription: false,
  systemOwner: SystemOwner.DialogScripts,
};

export const AppConfigContext = React.createContext<AppConfig>(defaultDialogScriptsConfig);

export const AppConfigProvider = ({
  children,
  externalConfig,
}: {
  children: React.ReactNode;
  externalConfig?: ExternalAppConfig;
}) => (
  <AppConfigContext.Provider
    value={{
      ...defaultDialogScriptsConfig,
      ...externalConfig,
    }}
  >
    {children}
  </AppConfigContext.Provider>
);
