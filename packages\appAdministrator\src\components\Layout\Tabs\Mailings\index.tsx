import React from 'react';

import clsx from 'clsx';

import { utils, Radio } from '@product.front/ui-kit';

import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';

import { IAdmTabComponent } from '../../../../@types/components';
import { MailingType } from '../../../../@types/mailings';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { setMailingType } from '../../../../store/mailings/mailings.slice';
import {
  getAllMailings,
  getAvailableChannels,
  getMailingsMetrics,
  getQueuesForMailings,
  getSenders,
} from '../../../../store/mailings/mailings.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import MailingsForm from './components/MailingsForm';

const Mailings: React.FC<IAdmTabComponent> = ({ name }) => {
  const [isStarted, setIsStarted] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasNew, setHasNew] = React.useState(false);
  const [error, setError] = React.useState<Error>();

  const dispatch = useAdministratorAppDispatch();

  const { periodicMailings, thresholdMailings, selectedMailingType, loading } =
    useAdministratorAppSelector((store) => store.mailings);

  React.useEffect(() => {
    if (isStarted) return;
    const start = async () => {
      try {
        setError(undefined);
        setIsLoading(true);
        await dispatch(getQueuesForMailings()).unwrap();
        await dispatch(getMailingsMetrics()).unwrap();
        await dispatch(getAllMailings()).unwrap();
        await dispatch(getAvailableChannels()).unwrap();
        await dispatch(getSenders()).unwrap();
        setIsStarted(true);
      } catch (e) {
        setError(e);
      } finally {
        setIsLoading(false);
      }
    };

    start();
  }, [dispatch, isStarted]);

  const list =
    selectedMailingType === MailingType.PeriodicMailing ? periodicMailings : thresholdMailings;

  if (isLoading) {
    return (
      <AdmTabWrapper>
        <AdmTabHeader header={name} />
        <AdmTabBody loading />
      </AdmTabWrapper>
    );
  }

  if (error) {
    return (
      <AdmTabWrapper>
        <AdmTabHeader header={name} />
        <AdmTabBody loading={false}>
          <div className={clsx(utils.h100, utils.w100, utils.flexCentredBlock)}>
            <JumbotronError header={getLocaleMessageById('mailings.init.error')} error={error} />
          </div>
        </AdmTabBody>
      </AdmTabWrapper>
    );
  }

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <div className={clsx(utils.dFlex, utils.gap4, utils.mR6)}>
          <Radio
            value={MailingType.PeriodicMailing}
            checked={selectedMailingType === MailingType.PeriodicMailing}
            onChange={() => dispatch(setMailingType(MailingType.PeriodicMailing))}
            name="mailingType"
            label={getLocaleMessageById('mailings.subTab.periodicMailing')}
          />
          <Radio
            value={MailingType.ThresholdMailing}
            checked={selectedMailingType === MailingType.ThresholdMailing}
            onChange={() => dispatch(setMailingType(MailingType.ThresholdMailing))}
            name="mailingType"
            label={getLocaleMessageById('mailings.subTab.thresholdMailing')}
          />
        </div>
        <AdmToolbarIconButton
          onClick={() => {
            dispatch(getAllMailings());
          }}
          className={utils.mL6}
          tooltip={getLocaleMessageById('app.common.refresh')}
        >
          <IconRefresh />
        </AdmToolbarIconButton>

        <AdmToolbarCtaButton
          disabled={hasNew}
          onClick={() => setHasNew(true)}
          title={getLocaleMessageById('mailings.rules.add')}
        >
          {getLocaleMessageById('app.common.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody className={utils.gap4} loading={loading}>
        {hasNew && (
          <MailingsForm
            mailing={undefined}
            onSave={(needRefresh) => {
              setHasNew(false);
              needRefresh && dispatch(getAllMailings());
            }}
          />
        )}
        {list.map((mailing) => (
          <MailingsForm
            key={mailing.id}
            mailing={mailing}
            onSave={(needRefresh) => {
              needRefresh && dispatch(getAllMailings());
            }}
          />
        ))}
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default Mailings;
