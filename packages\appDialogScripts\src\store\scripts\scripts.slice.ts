import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { IFrontBffScript } from '@monorepo/dialog-scripts/src/@types/script';

import extraReducers from './scripts.extraReducers';

export interface IScriptsStore {
  selectedScript: IFrontBffScript | null;
  scripts: IFrontBffScript[];
  loading: boolean;
  saving: boolean;
  error?: SerializedError;
  savingError?: SerializedError;
}

const initialState: IScriptsStore = {
  selectedScript: null,
  scripts: [],
  loading: false,
  saving: false,
};

const scriptsSlice = createSlice({
  name: 'scripts',
  initialState,
  reducers: {
    setSelected(state, action: PayloadAction<IFrontBffScript | null>) {
      const { payload } = action;
      state.selectedScript = payload;
      state.saving = false;
      state.savingError = undefined;
    },
    clearSavingError(state) {
      state.savingError = undefined;
    },
  },
  extraReducers,
});

export const { setSelected, clearSavingError } = scriptsSlice.actions;

export default scriptsSlice.reducer;
