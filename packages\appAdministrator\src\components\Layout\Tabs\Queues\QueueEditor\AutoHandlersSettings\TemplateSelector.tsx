import React from 'react';

import clsx from 'clsx';

import {
  Input,
  Jumbotron,
  JumbotronType,
  Select,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import { IFrontTemplateBase } from '@monorepo/common/src/@types/templates';

import { IAutoHandlerType, IFrontQueueAutoHandler } from '../../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';

interface ITemplateSelectorProps {
  templates: IFrontTemplateBase[];
  queueAutoHandler: IFrontQueueAutoHandler;
  onChange: (autoHandler: IFrontQueueAutoHandler) => void;
}

const TemplateSelector = ({ templates, queueAutoHandler, onChange }: ITemplateSelectorProps) => {
  if (queueAutoHandler.type === IAutoHandlerType.EmptyAutoHandler) {
    return (
      <div
        className={clsx(utils.h50, utils.dFlex, utils.alignItemsCenter, utils.justifyContentCenter)}
      >
        <Jumbotron
          type={JumbotronType.Info}
          header={getLocaleMessageById('queues.editor.emptyAutoHandler')}
        />
      </div>
    );
  }
  return (
    <div className={clsx(utils.h50, utils.scrollbar, utils.overflowAuto, utils.pT2)}>
      {queueAutoHandler.type === IAutoHandlerType.PredictedWaitTimeAutoHandler && (
        <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
          <Text variant={TextVariant.BodySemibold}>
            {getLocaleMessageById('queues.editor.autoHandlers.values')}
          </Text>
          <Input
            label={getLocaleMessageById('queues.editor.autoHandlers.pvoMinValue')}
            type="number"
            min={0}
            value={(queueAutoHandler.min || '').toString()}
            onChange={({ value }) => onChange({ ...queueAutoHandler, min: Number(value) })}
          />
          <Input
            label={getLocaleMessageById('queues.editor.autoHandlers.pvoMaxValue')}
            type="number"
            min={0}
            value={(queueAutoHandler.max || '').toString()}
            onChange={({ value }) => onChange({ ...queueAutoHandler, max: Number(value) })}
          />
          <Text variant={TextVariant.BodySemibold}>
            {getLocaleMessageById('queues.editor.autoHandlers.templates')}
          </Text>
          <Select
            label={getLocaleMessageById('queues.editor.autoHandlers.pvoMin')}
            data={templates.map((template) => ({ value: template.id, text: template.title }))}
            value={queueAutoHandler.templateBelowMin}
            onChange={({ value }) => onChange({ ...queueAutoHandler, templateBelowMin: value })}
          />
          <Select
            label={getLocaleMessageById('queues.editor.autoHandlers.pvoOk')}
            data={templates.map((template) => ({ value: template.id, text: template.title }))}
            value={queueAutoHandler.templateWithinRange}
            onChange={({ value }) => onChange({ ...queueAutoHandler, templateWithinRange: value })}
          />
          <Select
            label={getLocaleMessageById('queues.editor.autoHandlers.pvoMax')}
            data={templates.map((template) => ({ value: template.id, text: template.title }))}
            value={queueAutoHandler.templateAboveMax}
            onChange={({ value }) => onChange({ ...queueAutoHandler, templateAboveMax: value })}
          />
        </div>
      )}
      {queueAutoHandler.type === IAutoHandlerType.TemplateAutoHandler && (
        <Select
          label={getLocaleMessageById('queues.editor.autoHandlers.forAll')}
          data={templates.map((template) => ({ value: template.id, text: template.title }))}
          value={queueAutoHandler.templateId}
          onChange={({ value }) => onChange({ ...queueAutoHandler, templateId: value ?? '' })}
        />
      )}
    </div>
  );
};

export default TemplateSelector;
