import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { Colors, Text, TextVariant, utils } from '@product.front/ui-kit';

import { Operator, OperatorStatus } from '../../../../@types/generated/signalr';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorName } from '../../../../helpers/operatorHelper';
import {
  getIndicationTypeForOperatorStatus,
  getTextForOperatorStatus,
} from '../../../../helpers/operatorStatusHelper';
import ChatUserAvatar from '../../../ChatUserAvatar';

interface IInternalChatOperatorProps extends HTMLAttributes<HTMLDivElement> {
  operator: Operator;
  status: OperatorStatus;
  me?: boolean;
}

const InternalChatOperator: React.FC<IInternalChatOperatorProps> = ({
  operator,
  status,
  className,
  me = false,
  children,
}) => {
  return (
    <div className={clsx(utils.dFlex, className)}>
      <ChatUserAvatar
        id={operator.id || ''}
        name={getOperatorName(operator)}
        status={getIndicationTypeForOperatorStatus(status)}
        className={utils.mR3}
      />
      <div style={{ flexGrow: 1 }}>
        <Text variant={TextVariant.BodySemibold}>
          {me ? getLocaleMessageById('app.chat.me') : getOperatorName(operator)}
        </Text>
        <Text
          variant={TextVariant.CaptionMedium}
          color={status === OperatorStatus.Available ? Colors.MoodBlue70 : Colors.OnyxBlack70}
          as="div"
        >
          {getTextForOperatorStatus(status)}
        </Text>
      </div>
      {children}
    </div>
  );
};

export default InternalChatOperator;
