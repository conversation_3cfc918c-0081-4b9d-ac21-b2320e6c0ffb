import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { OverlayLoader, utils } from '@product.front/ui-kit';

interface IAdmTabWrapperProps extends HTMLAttributes<HTMLDivElement> {
  loading?: boolean;
}

const AdmTabWrapper: React.FC<IAdmTabWrapperProps> = ({
  children,
  className,
  loading,
  ...rest
}) => {
  const cn = clsx(
    'qa-admin-tab-wrapper',
    utils.dFlex,
    utils.flexColumn,
    utils.h100,
    utils.w100,
    className,
  );

  if (typeof loading === 'undefined') {
    return (
      <div className={cn} {...rest}>
        {children}
      </div>
    );
  }

  return (
    <OverlayLoader loading={loading} wrapperClassName={cn} {...rest}>
      {children}
    </OverlayLoader>
  );
};

export default AdmTabWrapper;
