import { getCurrentOperatorUsernameString } from '@monorepo/common/src/managers/currentOperatorManager';
import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontScript } from '@monorepo/dialog-scripts/src/@types/script';

export const emptyScript: IFrontScript = {
  scriptVariables: {},
  name: '',
  code: '',
  description: '',
  status: ScriptStatus.Template,
  createdBy: getCurrentOperatorUsernameString({ fallback: 'front' }),
  changedBy: getCurrentOperatorUsernameString({ fallback: 'front' }),
  changedDate: '',
  createdDate: '',
  activeFrom: '',
  activeTo: '',
  steps: [],
  tags: [],
  canBeAutomated: true,
  dependentScripts: [],
};
