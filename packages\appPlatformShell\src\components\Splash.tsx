﻿import * as React from 'react';

import { Guid } from 'guid-typescript';

import { Button, ButtonSize } from '@product.front/ui-kit';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

import { getLocaleMessageById } from '../helpers/localeHelper';
import { getLastSelectedItem, saveLastSelectedItem } from '../helpers/splash.helper';

import FormattedMessage from './FormattedMessage';
import { AwpConfigurationManager } from './Managers/AwpConfigurationManager';
import * as Essentials from './Managers/EssentialsManager';

type SplashProps = {
  serviceResolver: AWP.IServiceResolver;
};

type SplashState = {
  progressDescription: string;
  progressPercentage: number;
  profileDataReceived: boolean;

  profilesAndEssentialsData: AWP.ProfilesAndEssentialsData | null;
};

class Splash extends React.PureComponent<SplashProps, SplashState> {
  serviceResolver: AWP.IServiceResolver;
  notificator: AWP.IPopupNotificationManager;
  eventManager: AWP.IEventManager;

  configurationManager: AWP.IAwpConfigurationManager;

  appSettings: any;

  selectedRoleId: string | null = getLastSelectedItem('userRole');
  selectedServiceAreaId: string | null = getLastSelectedItem('serviceArea');
  selectedWorkPlaceId: string | null = getLastSelectedItem('workPlace');

  userRolesEssentialsManager: Essentials.EssentialsManager | null = null;
  serviceAreasEssentialsManager: Essentials.EssentialsManager | null = null;
  workplacesEssentialsManager: Essentials.EssentialsManager | null = null;
  specifiedProfileIds: string[] | null = null;

  constructor(props: SplashProps) {
    super(props);

    this.start = this.start.bind(this);
    this.fireStartError = this.fireStartError.bind(this);

    this.serviceResolver = props.serviceResolver;
    this.appSettings = this.serviceResolver.resolve<any>('AppSettings');
    this.notificator = this.serviceResolver.resolve<AWP.IPopupNotificationManager>(
      'IPopupNotificationManager',
    );
    this.eventManager = this.serviceResolver.resolve<AWP.IEventManager>('IEventManager');

    this.configurationManager = new AwpConfigurationManager(
      this.appSettings.infrastructureServicesUrl,
    );
    this.serviceResolver.register<AWP.IAwpConfigurationManager>(
      'IAwpConfigurationManager',
      this.configurationManager,
    );

    this.onUserRolesEssentialsManagerRendered =
      this.onUserRolesEssentialsManagerRendered.bind(this);
    this.onServiceAreasEssentialsManagerRendered =
      this.onServiceAreasEssentialsManagerRendered.bind(this);
    this.onWorkplacesEssentialsManagerRendered =
      this.onWorkplacesEssentialsManagerRendered.bind(this);

    this.onUserRolesEssentialsManagerSelectionChanged =
      this.onUserRolesEssentialsManagerSelectionChanged.bind(this);
    this.onServiceAreasEssentialsManagerSelectionChanged =
      this.onServiceAreasEssentialsManagerSelectionChanged.bind(this);
    this.onWorkplacesEssentialsManagerSelectionChanged =
      this.onWorkplacesEssentialsManagerSelectionChanged.bind(this);

    this.state = {
      profileDataReceived: false,
      progressDescription: '',
      progressPercentage: -1,

      profilesAndEssentialsData: null,
    };
  }

  initializeEssentialManagers(profilesAndEssentialsData: AWP.ProfilesAndEssentialsData): void {
    this.initializeUserRolesEssentialManager(profilesAndEssentialsData);
    this.initializeServiceAreasEssentialManager(profilesAndEssentialsData);
    this.initializeWorkplacesEssentialManager(profilesAndEssentialsData);
  }
  initializeUserRolesEssentialManager(
    profilesAndEssentialsData: AWP.ProfilesAndEssentialsData,
  ): void {
    const allConfiguredUserRoles = profilesAndEssentialsData.allUserRoles.map((x) =>
      Essentials.EssentialItemHolder.createForUserRole(x),
    );
    const userRolesProfileMap: Essentials.ProfileItemsMap[] =
      new Array<Essentials.ProfileItemsMap>();

    profilesAndEssentialsData.profilesData.forEach((profileData) => {
      userRolesProfileMap.push({
        profileId: profileData.id.toString(),
        itemIds: profileData.userRoles.map((x) => x.id.toString()),
      });
    });

    this.userRolesEssentialsManager?.initialize(allConfiguredUserRoles, userRolesProfileMap, null);
  }
  initializeServiceAreasEssentialManager(
    profilesAndEssentialsData: AWP.ProfilesAndEssentialsData,
  ): void {
    const allConfiguredServiceAreas = profilesAndEssentialsData.allServiceAreas.map((x) =>
      Essentials.EssentialItemHolder.createForServiceArea(x),
    );
    const serviceAreasProfileMap: Essentials.ProfileItemsMap[] =
      new Array<Essentials.ProfileItemsMap>();

    profilesAndEssentialsData.profilesData.forEach((profileData) => {
      serviceAreasProfileMap.push({
        profileId: profileData.id.toString(),
        itemIds: profileData.serviceAreas.map((x) => x.id.toString()),
      });
    });

    this.serviceAreasEssentialsManager?.initialize(
      allConfiguredServiceAreas,
      serviceAreasProfileMap,
      this.specifiedProfileIds,
    );
  }
  initializeWorkplacesEssentialManager(
    profilesAndEssentialsData: AWP.ProfilesAndEssentialsData,
  ): void {
    const allConfiguredWorkplaces = profilesAndEssentialsData.allWorkplaces.map((x) =>
      Essentials.EssentialItemHolder.createForWorkplace(x),
    );
    const workplacesProfileMap: Essentials.ProfileItemsMap[] =
      new Array<Essentials.ProfileItemsMap>();

    profilesAndEssentialsData.profilesData.forEach((profileData) => {
      workplacesProfileMap.push({
        profileId: profileData.id.toString(),
        itemIds: profileData.workplaces.map((x) => x.id.toString()),
      });
    });

    this.workplacesEssentialsManager?.initialize(
      allConfiguredWorkplaces,
      workplacesProfileMap,
      this.specifiedProfileIds,
    );
  }

  private inactivityTimer: ReturnType<typeof setTimeout> | undefined;

  private startInactivityLogoutTimer() {
    const HARDCODE_INACTIVITY_TIMEOUT = 10 * 60 * 1000; // 10 минут
    const HARDCODE_INACTIVITY_REDIRECT_URL = '/auth-info/'; // подразумевает наличие развернутого /auth-info/ в корне того же домена, что и вебарм
    const HARDCODE_LOGOUT_URL = './logout';

    const logout = () => {
      const fetchOptions: RequestInit = { credentials: 'include', mode: 'no-cors' };
      return commonFetch(HARDCODE_LOGOUT_URL, fetchOptions);
    };

    /**
     * Логика:
     * Если за HARDCODE_INACTIVITY_TIMEOUT пользователь не вошел в арм
     * редиректим на HARDCODE_INACTIVITY_REDIRECT_URL
     */

    this.inactivityTimer = setTimeout(async () => {
      await logout();

      const queryParams = new URLSearchParams({
        reason: 'inactive',
        url: self.location.href,
      });

      const fullRedirectUrl = [HARDCODE_INACTIVITY_REDIRECT_URL, queryParams].join('?');
      self.location.href = fullRedirectUrl;
    }, HARDCODE_INACTIVITY_TIMEOUT);
  }

  private stopInactivityLogoutTimer() {
    this.inactivityTimer && clearTimeout(this.inactivityTimer);
  }

  componentDidMount() {
    this.startInactivityLogoutTimer();
    this.configurationManager
      .getAvailableProfilesWithAllEssentials()
      .then(
        (data) => {
          this.setState(() => ({ profilesAndEssentialsData: data }));
          this.initializeEssentialManagers(data);
          this.setState(() => ({ profileDataReceived: true }));
        },
        (fetchError) => {
          this.fireStartError([fetchError as Error]);
        },
      )
      .catch((error) => {
        this.fireStartError([error as Error]);
      });
  }

  componentWillUnmount() {
    this.stopInactivityLogoutTimer();
  }

  fireStartError(error: Error[]) {
    console.error('Start error', error);
    this.eventManager.fire('StartError', error);
  }

  start() {
    if (this.userRolesEssentialsManager == null) {
      throw new Error('userRolesEssentialsManager undefined!');
    }
    if (this.serviceAreasEssentialsManager == null) {
      throw new Error('serviceAreasEssentialsManager undefined!');
    }
    if (this.workplacesEssentialsManager == null) {
      throw new Error('workplacesEssentialsManager undefined!');
    }

    const selectedRoleId = Guid.parse(this.userRolesEssentialsManager.selectedItemId ?? '');
    const selectedServiceAreaId = Guid.parse(
      this.serviceAreasEssentialsManager.selectedItemId ?? '',
    );
    const selectedWorkPlaceId = Guid.parse(this.workplacesEssentialsManager.selectedItemId ?? '');

    const selectedRole: AWP.Role = this.userRolesEssentialsManager.selectedItem?.item;
    const selectedServiceArea: AWP.ServiceArea =
      this.serviceAreasEssentialsManager.selectedItem?.item;
    const selectedWorkPlace: AWP.Workplace = this.workplacesEssentialsManager.selectedItem?.item;

    console.info(`start: userRole=[${selectedRole?.name} (${selectedRole.id}, ${selectedRoleId})]`);
    console.info(
      `start: serviceArea=[${selectedServiceArea?.name} (${selectedServiceArea.id}, ${selectedServiceAreaId})]`,
    );
    console.info(
      `start: workPlace=[${selectedWorkPlace?.name} (${selectedWorkPlace.id}, ${selectedWorkPlaceId})]`,
    );

    this.eventManager.fire('SplashSelectionMade', {
      selectedRoleId,
      selectedRole,
      selectedServiceAreaId,
      selectedServiceArea,
      selectedWorkPlaceId,
      selectedWorkPlace,
    });
  }

  render() {
    return (
      <div className="st-splash-outerDiv">
        <div className="st-splash-innerDiv">{this.renderSplash()}</div>
      </div>
    );
  }

  onUserRolesEssentialsManagerRendered(element: any) {
    console.info(`onUserRolesEssentialsManagerRendered!`);
    this.userRolesEssentialsManager = element;
    if (this.state.profilesAndEssentialsData != null) {
      this.initializeUserRolesEssentialManager(this.state.profilesAndEssentialsData);
    }
  }
  onServiceAreasEssentialsManagerRendered(element: any) {
    console.info(`onServiceAreasEssentialsManagerRendered!`);
    this.serviceAreasEssentialsManager = element;
    if (this.state.profilesAndEssentialsData != null) {
      this.initializeServiceAreasEssentialManager(this.state.profilesAndEssentialsData);
    }
  }
  onWorkplacesEssentialsManagerRendered(element: any) {
    console.info(`onWorkplacesEssentialsManagerRendered!`);
    this.workplacesEssentialsManager = element;
    if (this.state.profilesAndEssentialsData != null) {
      this.initializeWorkplacesEssentialManager(this.state.profilesAndEssentialsData);
    }
  }

  onUserRolesEssentialsManagerSelectionChanged() {
    console.info(
      `onUserRolesEssentialsManagerSelectionChanged! (${this.userRolesEssentialsManager?.selectedItemId})`,
    );

    if (this.userRolesEssentialsManager == null) return;

    this.selectedRoleId = this.userRolesEssentialsManager.selectedItemId;
    this.specifiedProfileIds = this.userRolesEssentialsManager.getSelectionProfileIds();
    this.serviceAreasEssentialsManager?.applyProfileIds(this.specifiedProfileIds);
    this.workplacesEssentialsManager?.applyProfileIds(this.specifiedProfileIds);

    saveLastSelectedItem('userRole', this.selectedRoleId ?? null);
  }
  onServiceAreasEssentialsManagerSelectionChanged() {
    console.info(
      `onServiceAreasEssentialsManagerSelectionChanged! (${this.serviceAreasEssentialsManager?.selectedItemId})`,
    );

    if (this.serviceAreasEssentialsManager != null) {
      this.selectedServiceAreaId = this.serviceAreasEssentialsManager.selectedItemId;
      saveLastSelectedItem('serviceArea', this.selectedServiceAreaId ?? null);
    }
  }
  onWorkplacesEssentialsManagerSelectionChanged() {
    console.info(
      `onWorkplacesEssentialsManagerSelectionChanged! (${this.workplacesEssentialsManager?.selectedItemId})`,
    );

    if (this.workplacesEssentialsManager != null) {
      this.selectedWorkPlaceId = this.workplacesEssentialsManager.selectedItemId;
      saveLastSelectedItem('workPlace', this.selectedWorkPlaceId ?? null);
    }
  }

  renderSplash() {
    return (
      <div className="st-splash-content">
        {!this.state.profileDataReceived && (
          <div className="loading-white">
            <div className="loading-content small"></div>
          </div>
        )}

        <span className="st-splash-logo" />

        <span className="st-splash-action-name">
          <FormattedMessage id="app.shell.splash.header" />
        </span>
        <span className="st-splash-action-description">
          <FormattedMessage id="app.shell.splash.subheader" />
        </span>

        <Essentials.EssentialsManager
          id="userRoles"
          label={getLocaleMessageById('app.shell.splash.userRoles')}
          alowOnlyLeafs={false}
          selectedItemId={this.selectedRoleId}
          onSelectionChanged={this.onUserRolesEssentialsManagerSelectionChanged}
          ref={(element) => {
            this.onUserRolesEssentialsManagerRendered(element);
          }}
        />
        <Essentials.EssentialsManager
          id="serviceAreas"
          label={getLocaleMessageById('app.shell.splash.serviceAreas')}
          alowOnlyLeafs={true}
          selectedItemId={this.selectedServiceAreaId}
          onSelectionChanged={this.onServiceAreasEssentialsManagerSelectionChanged}
          ref={(element) => {
            this.onServiceAreasEssentialsManagerRendered(element);
          }}
        />
        <Essentials.EssentialsManager
          id="workplaces"
          label={getLocaleMessageById('app.shell.splash.workplaces')}
          alowOnlyLeafs={true}
          selectedItemId={this.selectedWorkPlaceId}
          onSelectionChanged={this.onWorkplacesEssentialsManagerSelectionChanged}
          ref={(element) => {
            this.onWorkplacesEssentialsManagerRendered(element);
          }}
        />

        <div className="st-splash-actions st-splash-mt32">
          {/* Пока что скроем это сообщение для нужд УЭС. Нужно переделать чтобы это конфигурировалось и
          только потом возвращать вывод. */}
          {/* <span className="st-splash-actions-notes">
            <FormattedMessage id="app.shell.splash.supportText" />{' '}
            <a href="mailto:<EMAIL>">
              <FormattedMessage id="app.shell.splash.supportLinkText" />
            </a>
            .
          </span> */}
          <Button
            onClick={this.start}
            size={ButtonSize.Big}
            className="st-splash btn btn-primary st-splash-mt32"
            autoFocus
          >
            <FormattedMessage id="app.shell.splash.startButton" />
          </Button>
        </div>
      </div>
    );
  }
}

export default Splash;
