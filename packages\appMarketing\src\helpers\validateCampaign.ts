import { IFrontCampaign } from '../@types/campaign';
import { CampaignType } from '../@types/generated/marketing';

import { getLocaleMessageById } from './localeHelper';

export const defaultValidationResult = {
  name: '',
  priority: '',
  channels: '',
  queueId: '',
  startTime: '',
  stopTime: '',
  dateFrom: '',
  dateTo: '',
  offerId: '',
  clients: '',
};

const requiredMessage = getLocaleMessageById('app.common.required');

// eslint-disable-next-line sonarjs/cognitive-complexity
export const validateCampaignSettings = (campaign: IFrontCampaign) => {
  const newValidationResult = { ...defaultValidationResult };
  let isErrored = false;

  const {
    name,
    type,
    priority,
    channels,
    queueId,
    startTime,
    stopTime,
    dateFrom,
    dateTo,
    offerId,
    filter,
    filterDescriptors,
    fileUploadingId,
  } = campaign;

  if (!name) {
    isErrored = true;
    newValidationResult.name = requiredMessage;
  }

  if (type === CampaignType.Incoming && !priority) {
    isErrored = true;
    newValidationResult.priority = requiredMessage;
  }

  if (channels.length === 0) {
    isErrored = true;
    newValidationResult.channels = requiredMessage;
  }

  if (type === CampaignType.Outgoing) {
    if (!queueId) {
      isErrored = true;
      newValidationResult.queueId = requiredMessage;
    }

    if (!startTime) {
      isErrored = true;
      newValidationResult.startTime = requiredMessage;
    }

    if (!stopTime) {
      isErrored = true;
      newValidationResult.stopTime = requiredMessage;
    }

    if (!filter && filterDescriptors === '{}' && !fileUploadingId) {
      isErrored = true;
      newValidationResult.clients = getLocaleMessageById('app.campaign.filter.required');
    }
  }

  if (!dateFrom) {
    isErrored = true;
    newValidationResult.dateFrom = requiredMessage;
  }

  if (!dateTo) {
    isErrored = true;
    newValidationResult.dateTo = requiredMessage;
  }

  if (!offerId) {
    isErrored = true;
    newValidationResult.offerId = requiredMessage;
  }

  return { validationResult: newValidationResult, isErrored };
};
