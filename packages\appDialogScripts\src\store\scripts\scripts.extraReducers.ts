import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IScriptsStore } from './scripts.slice';
import { addScript, getAllScripts, updateScript } from './scripts.thunk';

export default (builder: ActionReducerMapBuilder<IScriptsStore>) =>
  builder
    .addCase(getAllScripts.pending, (state) => {
      state.selectedScript = null;
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllScripts.fulfilled, (state, action) => {
      state.loading = false;
      state.scripts = action.payload;
    })
    .addCase(getAllScripts.rejected, (state, action) => {
      state.loading = false;
      state.scripts = [];
      state.error = action.error;
    })
    .addCase(addScript.pending, (state) => {
      state.saving = true;
      state.savingError = undefined;
    })
    .addCase(addScript.fulfilled, (state) => {
      state.saving = false;
    })
    .addCase(addScript.rejected, (state, action) => {
      state.saving = false;
      state.savingError = action.error;
    })
    .addCase(updateScript.pending, (state) => {
      state.saving = true;
      state.savingError = undefined;
    })
    .addCase(updateScript.fulfilled, (state) => {
      state.saving = false;
    })
    .addCase(updateScript.rejected, (state, action) => {
      state.saving = false;
      state.savingError = action.error;
    });
