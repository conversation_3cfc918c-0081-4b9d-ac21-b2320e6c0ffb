import React from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  Checkbox,
  FloatingDropdown,
  grids,
  Input,
  Menu,
  MenuItem,
  Tag,
  TagColor,
  Text,
  utils,
} from '@product.front/ui-kit';

import { getInputDate } from '@monorepo/common/src/helpers/dateHelper';

import { DisplayCampaignStatus } from '../../../../../@types/generated/marketing';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { updateCampaignField } from '../../../../../store/campaigns/campaigns.slice';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../../../../store/hooks';
import ErrorMessage from '../../../../TabOffers/OfferForm/ErrorMessage';

import styles from './styles.module.scss';

const IncomingFields = () => {
  const dispatch = useMarketingAppDispatch();

  const { selectedCampaign, campaignInEdit, validationResult } = useMarketingAppSelector(
    (state) => state.campaigns,
  );
  const { channels } = useMarketingAppSelector((state) => state.settings);

  const notSelectedChannels = React.useMemo(
    () =>
      Object.keys(channels)
        .filter((channel) => !(campaignInEdit?.channels.includes(Number(channel)) ?? false))
        .map(Number),
    [channels, campaignInEdit?.channels],
  );

  if (!campaignInEdit) return null;

  const {
    priority,
    availableForBot,
    dateFrom,
    dateTo,
    channels: campaignChannels,
  } = campaignInEdit;

  return (
    <>
      <div
        className={clsx(
          utils.dFlex,
          utils.alignItemsStart,
          utils.justifyContentBetween,
          styles.content,
        )}
      >
        <Input
          label={getLocaleMessageById('app.campaigns.form.label.priority')}
          type="number"
          value={priority.toString()}
          onChange={({ value }) =>
            dispatch(updateCampaignField({ fieldName: 'priority', fieldValue: Number(value || 0) }))
          }
          required
          min={1}
          isInvalid={!!validationResult.priority}
          message={<ErrorMessage>{validationResult.priority}</ErrorMessage>}
          readOnly={selectedCampaign?.displayStatus !== DisplayCampaignStatus.Completed}
        />

        <Checkbox
          label={getLocaleMessageById('app.campaigns.form.label.availableForBot')}
          checked={availableForBot}
          onChange={({ checked }) =>
            dispatch(updateCampaignField({ fieldName: 'availableForBot', fieldValue: checked }))
          }
          disabled={
            ![DisplayCampaignStatus.New, DisplayCampaignStatus.Planned].includes(
              selectedCampaign?.displayStatus ?? DisplayCampaignStatus.New,
            )
          }
        />
      </div>
      <div className={grids.row}>
        <Input
          wrapperClassName={grids.col6}
          label={getLocaleMessageById('app.campaigns.form.label.dateFrom')}
          type="datetime-local"
          value={dateFrom ? getInputDate(new Date(dateFrom)) : ''}
          onBlur={(event) =>
            dispatch(updateCampaignField({ fieldName: 'dateFrom', fieldValue: event.target.value }))
          }
          required
          canClearBehavior={CanClearBehavior.Always}
          min={selectedCampaign?.dateFrom ? getInputDate(new Date(selectedCampaign.dateFrom)) : ''}
          max={
            selectedCampaign?.dateTo || dateTo
              ? getInputDate(new Date(selectedCampaign?.dateTo || dateTo!))
              : '9999-12-31T23:59:59'
          }
          isInvalid={!!validationResult.dateFrom}
          message={<ErrorMessage>{validationResult.dateFrom}</ErrorMessage>}
          readOnly={selectedCampaign?.displayStatus === DisplayCampaignStatus.Completed}
        />
        <Input
          wrapperClassName={grids.col6}
          label={getLocaleMessageById('app.campaigns.form.label.dateTo')}
          type="datetime-local"
          value={dateTo ? getInputDate(new Date(dateTo)) : ''}
          onBlur={(event) =>
            dispatch(updateCampaignField({ fieldName: 'dateTo', fieldValue: event.target.value }))
          }
          required
          canClearBehavior={CanClearBehavior.Always}
          min={
            selectedCampaign?.dateFrom || dateFrom
              ? getInputDate(new Date(selectedCampaign?.dateFrom || dateFrom!))
              : ''
          }
          max={
            selectedCampaign?.dateTo
              ? getInputDate(new Date(selectedCampaign.dateTo))
              : '9999-12-31T23:59:59'
          }
          isInvalid={!!validationResult.dateTo}
          message={<ErrorMessage>{validationResult.dateTo}</ErrorMessage>}
          readOnly={selectedCampaign?.displayStatus === DisplayCampaignStatus.Completed}
        />
      </div>
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap2, styles.fullWidth)}>
        <Text>{getLocaleMessageById('app.campaigns.form.label.channels')}</Text>
        <div className={clsx(utils.dFlex, utils.gap2, utils.flexWrap)}>
          {campaignChannels.map((channel) => (
            <Tag
              key={channel}
              color={TagColor.MoodBlue}
              canClose={!campaignInEdit.id}
              onClose={() =>
                dispatch(
                  updateCampaignField({
                    fieldName: 'channels',
                    fieldValue: campaignChannels.filter((value) => value !== channel),
                  }),
                )
              }
            >
              {channels[channel].displayName}
            </Tag>
          ))}
          {!campaignInEdit.id && notSelectedChannels.length > 0 && (
            <FloatingDropdown
              menu={
                <Menu>
                  {notSelectedChannels.map((channel) => (
                    <MenuItem
                      key={channel}
                      onClick={() =>
                        dispatch(
                          updateCampaignField({
                            fieldName: 'channels',
                            fieldValue: [...campaignChannels, channel],
                          }),
                        )
                      }
                      disabled={
                        ![DisplayCampaignStatus.New, DisplayCampaignStatus.Planned].includes(
                          selectedCampaign?.displayStatus ?? DisplayCampaignStatus.New,
                        )
                      }
                    >
                      {channels[channel].displayName}
                    </MenuItem>
                  ))}
                  <MenuItem
                    onClick={() =>
                      dispatch(
                        updateCampaignField({
                          fieldName: 'channels',
                          fieldValue: [...campaignChannels, ...notSelectedChannels],
                        }),
                      )
                    }
                    disabled={
                      ![DisplayCampaignStatus.New, DisplayCampaignStatus.Planned].includes(
                        selectedCampaign?.displayStatus ?? DisplayCampaignStatus.New,
                      )
                    }
                  >
                    {getLocaleMessageById('app.campaigns.form.label.channelsSelectAll')}
                  </MenuItem>
                </Menu>
              }
            >
              <Tag color={TagColor.MoodBlue}>+</Tag>
            </FloatingDropdown>
          )}
        </div>
        <ErrorMessage>{validationResult.channels}</ErrorMessage>
      </div>
    </>
  );
};

export default IncomingFields;
