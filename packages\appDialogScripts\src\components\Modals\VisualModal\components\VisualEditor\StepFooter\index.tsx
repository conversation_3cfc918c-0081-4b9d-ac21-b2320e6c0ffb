import React from 'react';

import clsx from 'clsx';

import { Badge, BadgeType, utils } from '@product.front/ui-kit';

import IconClip from '@product.front/icons/dist/icons17/Chat&Mail/IconClip';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

interface IStepFooterProps {
  step: IFrontStep;
}

const StepFooter: React.FC<IStepFooterProps> = ({ step }) => {
  if (!step.attachments?.length && !step.attachmentVariableCodes?.length) {
    return null;
  }

  return (
    <footer
      className={clsx(utils.p2, utils.borderTop, utils.dFlex, utils.alignItemsCenter, utils.gap1)}
    >
      <IconClip />
      <Badge style={{ padding: 0 }} type={BadgeType.Attention}>
        {(step.attachments?.length ?? 0) + (step.attachmentVariableCodes?.length ?? 0)}
      </Badge>
    </footer>
  );
};

export default StepFooter;
