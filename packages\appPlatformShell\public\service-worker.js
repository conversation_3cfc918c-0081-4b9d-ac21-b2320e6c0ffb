const broadcast = new BroadcastChannel('product-fetch-channel');

self.addEventListener('fetch', (event) => {
  event.respondWith(
    fetch(event.request).then(function (response) {
      if (!response.ok) {
        const data = {
          type: 'PRODUCT_FETCH_ERROR',
          status: response.status,
          statusText: response.statusText,
          url: response.url,
        };

        broadcast.postMessage(data);
      }

      // If we got back a non-error HTTP response, return it to the page.
      return response;
    }),
  );
});
