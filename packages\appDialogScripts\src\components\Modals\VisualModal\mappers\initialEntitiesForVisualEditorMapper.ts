import { Edge, MarkerType } from 'reactflow';

import { AnswerTypes, ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { FrontStepType, IFrontScript } from '@monorepo/dialog-scripts/src/@types/script';

import { getEmptyStep } from '../../../../helpers/scriptHelpers';
import { fictionNodeOffset, fictionNodeWidth, nodeMinWidth } from '../const/nodeSizes';
import {
  getAnswerSourceId,
  getEdgeId,
  getNodeId,
  getNodeSourceId,
  getRuleSourceId,
  getServiceFailSourceId,
  getServiceSuccessSourceId,
  getSubscriptSourceId,
  getLimitSourceId,
  getTimeoutSourceId,
  getChoiceStepAnswerSourceId,
} from '../helpers/idsHelper';
import { getSameAnswersTransfer } from '../helpers/ifrontAsnwesHelper';
import {
  getNextXPositionForNode,
  getNextYPositionForNode,
  resetNextXPositionForNode,
  resetNextYPositionForNode,
} from '../helpers/nodePositionHelper';
import { NodeType, ScripDialogStepNode } from '../types/scriptDialogsVisualEditorTypes';

import { mapFrontStepTypeToNodeType, mapScriptStepToNode } from './visualEditorMappers';

const autoEdgeStyle = { strokeDasharray: 4 };
const finishEdgeStyle = { stroke: '#FFA3B6', strokeDasharray: 1 };
const manualEdgeStyle = {};

interface IVisualEditorInitialEntities {
  initialNodes: ScripDialogStepNode[];
  initialEdges: Edge[];
}

export const finishNodeId = getNodeId();

interface IInitialEntitiesByStepsOptions {
  drawAutoRelations: boolean;
  disableVirtualFinishStep: boolean;
}

export const getInitialEntitiesBySteps = (
  script: IFrontScript,
  options: IInitialEntitiesByStepsOptions,
): IVisualEditorInitialEntities => {
  resetNextXPositionForNode();
  resetNextYPositionForNode();

  const steps = script.steps;

  const defaultEdges: Edge[] = [];

  const startNode: ScripDialogStepNode = {
    id: getNodeId(),
    type: NodeType.Start,
    data: {
      step: getEmptyStep(),
    },
    position: { x: getNextXPositionForNode(), y: getNextYPositionForNode() },
    draggable: false,
    selectable: false,
  };

  const defineTarget = (code: string, nextStepCode?: string): string | undefined => {
    if (!['', 'default'].includes(code) || !options.drawAutoRelations) {
      return code;
    }

    if (code === 'default' && nextStepCode) {
      return nextStepCode;
    }

    return undefined;
  };

  const getEdgeStylesByNextStepCode = (code: string) => {
    if (code === finishNodeId || code === '') {
      return finishEdgeStyle;
    }

    if (['default'].includes(code)) {
      return autoEdgeStyle;
    }

    return manualEdgeStyle;
  };

  const firstStep = steps.find((step) => step.isFirstStep);

  // eslint-disable-next-line sonarjs/cognitive-complexity
  steps.forEach((step, i, allSteps) => {
    firstStep?.code === step.code &&
      defaultEdges.push({
        id: getEdgeId(),
        source: startNode.id,
        target: step.code,
        markerEnd: {
          type: MarkerType.ArrowClosed,
        },
      });
    const nextStep = allSteps[i + 1];

    const createEdge = (
      targetCode: string | undefined,
      defaultTargetCode: string,
      sourceCode: string,
    ): Edge<any> => {
      const targetCodeOrDefault = targetCode?.toString() ?? 'default';
      const target = defineTarget(targetCodeOrDefault, nextStep?.code) || defaultTargetCode;

      return {
        id: getEdgeId(),
        source: step.code,
        sourceHandle: sourceCode,
        target,
        style: getEdgeStylesByNextStepCode(targetCodeOrDefault),
        markerEnd: {
          type: MarkerType.ArrowClosed,
        },
      };
    };

    if (step.type === FrontStepType.Step || step.type === FrontStepType.Rating) {
      /**
       * Типы шагов у которых могут быть ответы (стрелка рисуется не из шапки шага, а из ответа)
       * */
      const answerDisplayTypeWithAnswers = [
        AnswerTypes.TextTemplate,
        AnswerTypes.Button,
        AnswerTypes.Choice,
        AnswerTypes.Radio,
        AnswerTypes.Select,
      ];

      if (step.answerDisplayType === AnswerTypes.File) {
        defaultEdges.push(createEdge(step.stepTransfer, finishNodeId, getNodeSourceId(step.code)));

        if ((step.limit ?? 0) > 0) {
          defaultEdges.push(
            createEdge(step.limitStepTransfer, finishNodeId, getLimitSourceId(step.code)),
          );
        }

        if ((step.timeout ?? 0) > 0) {
          defaultEdges.push(
            createEdge(step.timeoutStepTransfer, finishNodeId, getTimeoutSourceId(step.code)),
          );
        }
      } else if (
        step.answerDisplayType &&
        answerDisplayTypeWithAnswers.includes(step.answerDisplayType)
      ) {
        if (step.answers) {
          const stepTransfer = getSameAnswersTransfer(step.answers);
          if (stepTransfer) {
            defaultEdges.push(createEdge(stepTransfer, finishNodeId, getNodeSourceId(step.code)));
          } else {
            step.answers?.forEach((answer) => {
              defaultEdges.push(
                createEdge(
                  answer.transferTo,
                  finishNodeId,
                  getAnswerSourceId(answer.id.toString()),
                ),
              );
            });
          }
        }

        if ((step.limit ?? 0) > 0) {
          defaultEdges.push(
            createEdge(step.limitStepTransfer, finishNodeId, getLimitSourceId(step.code)),
          );
        }
      } else {
        defaultEdges.push(createEdge(step.stepTransfer, finishNodeId, getNodeSourceId(step.code)));
      }
      if (step.choiceStepAnswer?.nextStepCode) {
        defaultEdges.push(
          createEdge(
            step.choiceStepAnswer?.nextStepCode,
            finishNodeId,
            getChoiceStepAnswerSourceId(step.code),
          ),
        );
      }
    }

    if (step.type === FrontStepType.Router) {
      defaultEdges.push(createEdge(step.stepTransfer, 'default', getNodeSourceId(step.code)));
      step.rules?.forEach((rule) => {
        defaultEdges.push(createEdge(rule.transferTo, '', getRuleSourceId(rule.id.toString())));
      });
    }

    if (step.type === FrontStepType.Service) {
      defaultEdges.push(
        createEdge(step.stepTransfer, 'default', getServiceSuccessSourceId(step.code)),
      );
      defaultEdges.push(
        createEdge(step.stepFallbackTransfer, 'default', getServiceFailSourceId(step.code)),
      );
    }

    if (step.type === FrontStepType.Subscript) {
      defaultEdges.push(
        createEdge(step.stepTransfer, finishNodeId, getSubscriptSourceId(step.code)),
      );
    }
  });

  const intermediateNodes = steps.map((step) =>
    mapScriptStepToNode(step, {
      type: mapFrontStepTypeToNodeType(step.type),
      draggable: script.status !== ScriptStatus.Archive,
      selectable: script.status !== ScriptStatus.Archive,
    }),
  );

  const minX = steps.reduce(
    (acc, s) => (s.positionX && s.positionX < acc ? s.positionX : acc),
    startNode.position.x,
  );

  const maxX = steps.reduce(
    (acc, s) => (s.positionX && s.positionX > acc ? s.positionX : acc),
    getNextXPositionForNode(),
  );

  const finishNode: ScripDialogStepNode = {
    id: finishNodeId,
    type: NodeType.Finish,
    data: {
      step: getEmptyStep(),
    },
    position: { x: maxX + (nodeMinWidth + 70) + fictionNodeOffset, y: getNextYPositionForNode() },
    draggable: false,
    selectable: false,
  };

  const initialNodes = [
    {
      ...startNode,
      position: { ...startNode.position, x: minX - (fictionNodeOffset + fictionNodeWidth) },
    },
    ...intermediateNodes,
  ];

  if (!options.disableVirtualFinishStep) {
    initialNodes.push(finishNode);
  }

  return {
    initialNodes,
    initialEdges: defaultEdges,
  };
};
