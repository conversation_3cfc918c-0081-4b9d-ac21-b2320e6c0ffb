import React from 'react';

import clsx from 'clsx';

import { Colors, Select, Text, TextVariant, utils } from '@product.front/ui-kit';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { IFrontDistributionRule } from '../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

import { IValidationResult } from './validator';

interface IDistributionSettingsProps {
  value: string;
  onChange: (newValue: string) => void;
  distributionRules: IFrontDistributionRule[];
  validationResult: IValidationResult['distribution'];
}

const DistributionSettings = ({
  value,
  onChange,
  distributionRules,
  validationResult,
}: IDistributionSettingsProps) => {
  const [selectedDistribution, setSelectedDistribution] =
    React.useState<IFrontDistributionRule | null>(
      distributionRules.find((rule) => rule.ruleType === value) ?? null,
    );

  React.useEffect(() => {
    if (selectedDistribution) return;

    let newDistributionRule: IFrontDistributionRule | null = null;
    if (value) {
      newDistributionRule = distributionRules.find((rule) => rule.ruleType === value) ?? null;
    } else {
      newDistributionRule = distributionRules.find((rule) => rule.isDefault) ?? null;
    }
    setSelectedDistribution(newDistributionRule);
    newDistributionRule && onChange(newDistributionRule.ruleType);
  }, [distributionRules, onChange, selectedDistribution, value]);

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
      <Select
        label={getLocaleMessageById('queues.editor.distributionRule')}
        data={distributionRules.map((rule) => ({
          value: rule.ruleType,
          text: rule.name,
          data: rule,
        }))}
        value={selectedDistribution?.ruleType}
        onChange={(newValue) => {
          setSelectedDistribution(
            distributionRules.find((rule) => rule.ruleType === newValue.value)!,
          );
          onChange(newValue.value ?? '');
        }}
        isInvalid={!!validationResult.distributionRule}
        message={<InputErrorMessage>{validationResult.distributionRule}</InputErrorMessage>}
      />
      <Text variant={TextVariant.CaptionMedium} color={Colors.OnyxBlack80}>
        {selectedDistribution?.description}
      </Text>
    </div>
  );
};

export default DistributionSettings;
