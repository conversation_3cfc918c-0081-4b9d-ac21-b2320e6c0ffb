import React, { useCallback } from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  grids,
  helpers,
  Text,
  TextVariant,
  utils,
  Textarea,
  IconButton,
  Input,
  CanClearBehavior,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import { IRequestFile } from '../../../@types/automationService.types';
import { getLocaleMessageById } from '../../../helpers/localeHelper';

interface IFilesBlockProps {
  files: Record<string, IRequestFile>;
  onChange: (value: Record<string, IRequestFile>) => void;
  disabled: boolean;
}

const FilesBlock: React.FC<IFilesBlockProps> = ({ files, disabled, onChange }) => {
  const handleFileDelete = useCallback(
    (key: string) => {
      // eslint-disable-next-line sonarjs/no-unused-vars
      const { [key]: _, ...newFiles } = files;
      onChange(newFiles);
    },
    [onChange, files],
  );

  const changeItems = useCallback(
    (key: string, changeAction: (file: IRequestFile) => void) => {
      const newFile = { ...files[key] };

      changeAction(newFile);

      onChange({ ...files, [key]: newFile });
    },
    [onChange, files],
  );

  return (
    <section>
      <Text as="h3" variant={TextVariant.SubheadSemibold} className={clsx(utils.m0, utils.mB3)}>
        {getLocaleMessageById('app.automationService.requestFiles')}
      </Text>
      {Object.keys(files).map((key) => {
        return (
          <div className={clsx(grids.row, utils.mT1, utils.gap1, utils.alignItemsCenter)} key={key}>
            <Textarea
              key={key}
              rows={2}
              wrapperClassName={grids.col7}
              label={getLocaleMessageById('app.automationService.requestFileUrl')}
              disabled={disabled}
              value={files[key].url}
              onChange={(value) => {
                changeItems(key, (f) => (f.url = value.value ?? ''));
              }}
            />
            <Input
              label={getLocaleMessageById('app.automationService.requestFileName')}
              placeholder={getLocaleMessageById('app.automationService.requestFileName')}
              value={files[key].fileName ?? ''}
              onChange={({ value }) => {
                changeItems(key, (f) => (f.fileName = value ?? ''));
              }}
              canClearBehavior={CanClearBehavior.Value}
              wrapperClassName={grids.col4}
            />
            <div className={clsx(grids.col1, utils.alignItemsCenter)}>
              <IconButton
                onClick={() => {
                  handleFileDelete(key);
                }}
                disabled={disabled}
              >
                <IconTrash />
              </IconButton>
            </div>
          </div>
        );
      })}
      <div className={clsx(grids.row, utils.mY2)}>
        <Button
          variant={ButtonVariant.Transparent}
          className={utils.mLn4}
          disabled={disabled}
          onClick={() => {
            onChange({
              ...files,
              [helpers.getUniqueId('fileBlock-')]: { url: '' },
            });
          }}
        >
          <IconAdd className={utils.mR2} />
          {getLocaleMessageById('app.automationService.requestFileUrlAdd')}
        </Button>
      </div>
    </section>
  );
};

export default FilesBlock;
