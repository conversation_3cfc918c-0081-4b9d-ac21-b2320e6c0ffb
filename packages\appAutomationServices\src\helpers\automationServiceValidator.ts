import {
  AutomationServiceRequestBodyType,
  AutomationServiceRequestType,
  AutomationServiceResponseBodyType,
  IFrontAutomationService,
  IFrontAutomationServiceInvalidReasons,
} from '../@types/automationService.types';

import { getJSONStringInvalidReason, getXMLStringInvalidReason } from './dataValidators';
import { getLocaleMessageById } from './localeHelper';
import {
  validateJSScriptCodeEnd,
  validateJSScriptCodeStart,
} from './scriptCodeJS/scriptCodeJSHelper';
import { isRest, isScript } from './serviceTypeHelper';

export const getAutomationServiceInvalidReasons = (
  automationService: IFrontAutomationService,
): Partial<IFrontAutomationServiceInvalidReasons> => {
  const reasons: Partial<IFrontAutomationServiceInvalidReasons> = {};

  if (isRest(automationService.type)) {
    if (automationService.requestType !== AutomationServiceRequestType.GET) {
      switch (automationService.requestBodyType) {
        case AutomationServiceRequestBodyType.JSON: {
          const invalidReason = getJSONStringInvalidReason(automationService.requestBody);
          reasons.requestBody = invalidReason;
          break;
        }

        case AutomationServiceRequestBodyType.XML: {
          const invalidReason = getXMLStringInvalidReason(automationService.requestBody);
          reasons.requestBody = invalidReason;
          break;
        }

        default:
          reasons.requestBody = undefined;
      }
    }

    switch (automationService.responseBodyType) {
      case AutomationServiceResponseBodyType.JSON: {
        const invalidReason = getJSONStringInvalidReason(automationService.responseBody);
        reasons.responseBody = invalidReason;
        break;
      }

      case AutomationServiceResponseBodyType.XML: {
        const invalidReason = getXMLStringInvalidReason(automationService.responseBody);
        reasons.responseBody = invalidReason;
        break;
      }

      default:
        reasons.responseBody = undefined;
    }
  }

  if (isScript(automationService.type)) {
    if (!validateJSScriptCodeStart(automationService.requestBody)) {
      reasons.scriptCodeBody = getLocaleMessageById('app.automationService.scriptSyntaxErrorStart');
    } else if (!validateJSScriptCodeEnd(automationService.requestBody)) {
      reasons.scriptCodeBody = getLocaleMessageById('app.automationService.scriptSyntaxErrorEnd');
    }
  }

  return reasons;
};
