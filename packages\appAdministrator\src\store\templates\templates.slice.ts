import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontFolder, IFrontTemplate } from '@monorepo/common/src/@types/templates';

import extraReducers from './templates.extraReducers';

export interface ITemplatesStore {
  loading: boolean;
  error?: SerializedError;
  folders: Record<string, IFrontFolder>;
  selectedTemplate: IFrontTemplate | null;
}

const initialState: ITemplatesStore = {
  loading: false,
  folders: {},
  selectedTemplate: null,
};

const templatesSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {},
  extraReducers,
});

export default templatesSlice.reducer;
