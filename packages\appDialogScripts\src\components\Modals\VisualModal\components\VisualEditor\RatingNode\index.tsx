import React from 'react';

import clsx from 'clsx';
import { Handle, NodeProps, Position } from 'reactflow';

import { Colors, Menu, MenuItem, Text, TextVariant, utils } from '@product.front/ui-kit';

import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getNoNameNameForStep } from '../../../../../../helpers/stepListHelper';
import { nodeMinHeight, nodeMinWidth } from '../../../const/nodeSizes';
import { getAnswerSourceId, getNodeSourceId, getNodeTargetId } from '../../../helpers/idsHelper';
import { NodeWithStepData } from '../../../types/scriptDialogsVisualEditorTypes';
import IconStep from '../IconStep';
import StepDescription from '../StepDescription';
import StepFooter from '../StepFooter';

import styles from './styles.module.scss';

const Answers = ({
  step,
  classNameForEachHandle,
}: {
  step: IFrontStep;
  classNameForEachHandle?: string;
}) => {
  return (
    <>
      <Menu>
        {step.answers?.map(({ id: answerId, text }: any) => (
          <MenuItem key={answerId} className={clsx(utils.positionRelative)}>
            <Text ellipsis noWrap>
              {text}
            </Text>
            <Handle
              type="source"
              position={Position.Right}
              id={getAnswerSourceId(answerId)}
              className={classNameForEachHandle}
            />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

type IRatingNodeProps = NodeProps<NodeWithStepData>;

const RatingNode: React.FC<IRatingNodeProps> = ({ id, data }) => {
  const hasVariants =
    data.step.answerDisplayType &&
    ![AnswerTypes.None, AnswerTypes.TextInput].includes(data.step.answerDisplayType);
  const isInvalid = data.step.invalidReasons && Object.entries(data.step.invalidReasons).length > 0;
  const isActive = data.isSelected;
  return (
    <div
      className={clsx(styles.ratingNode, {
        [utils.border]: !isInvalid,
        [styles.invalid]: isInvalid,
        [styles.active]: isActive,
      })}
      style={{ minWidth: nodeMinWidth, minHeight: nodeMinHeight }}
    >
      <header className={clsx(styles.ratingNodeHeader, !hasVariants && styles.onlyHeader)}>
        <Handle type="target" position={Position.Left} id={getNodeTargetId(id)} />
        <div className={clsx(utils.dFlex, utils.p2, utils.mR2)}>
          <div className={clsx(utils.pR2)}>
            <IconStep />
          </div>
          <Text
            color={Colors.HollywoodSmile}
            variant={TextVariant.BodyMedium}
            title={[data.step.name, data.step.description].join('\n')}
            className={styles.headerText}
          >
            {data.step.name || getNoNameNameForStep(data.step)}
          </Text>
        </div>
        <Handle
          type="source"
          position={Position.Right}
          id={getNodeSourceId(id)}
          className={clsx(styles.handle)}
        />
      </header>
      <section>
        <StepDescription text={data?.step?.description} className={styles.template} />
        {hasVariants && (
          <Answers
            step={data.step}
            classNameForEachHandle={clsx(
              styles.handle,
              data.step.stepTransfer && styles.handleHole,
            )}
          />
        )}
        <StepFooter step={data.step} />
      </section>
    </div>
  );
};

export default React.memo(RatingNode);
