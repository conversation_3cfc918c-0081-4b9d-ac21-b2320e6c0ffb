import {
  IFrontPrioritizationAttribute,
  IFrontPrioritizationRule,
  RightPartType,
} from '../../../../../@types/prioritization';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

export interface IValidationResult {
  isErrored: boolean;
  title: string;
  attributeId: string;
  comparisonRule: string;
  values: Record<string, { value?: string; priority?: string }>;
}

export const defaultValidationResult: IValidationResult = {
  isErrored: false,
  title: '',
  attributeId: '',
  comparisonRule: '',
  values: {},
};

const requiredMessage = getLocaleMessageById('app.common.validation.required');

export const validatePrioritizationRule = (
  rule: IFrontPrioritizationRule,
  selectedAttribute?: IFrontPrioritizationAttribute,
) => {
  const validationResult: IValidationResult = {
    ...defaultValidationResult,
    values: {},
  };

  if (!rule.title) {
    validationResult.isErrored = true;
    validationResult.title = requiredMessage;
  }

  if (!rule.attributeId) {
    validationResult.isErrored = true;
    validationResult.attributeId = requiredMessage;
  }

  if (selectedAttribute?.rightPartType !== RightPartType.None && !rule.comparisonRule) {
    validationResult.isErrored = true;
    validationResult.comparisonRule = requiredMessage;
  }

  rule.values.forEach((value) => {
    if (selectedAttribute?.rightPartType !== RightPartType.None && !value.value) {
      validationResult.isErrored = true;
      validationResult.values[value.key] = {
        ...validationResult.values[value.key],
        value: requiredMessage,
      };
    }

    if (value.priority > 32767 || value.priority < 1) {
      validationResult.isErrored = true;
      validationResult.values[value.key] = {
        ...validationResult.values[value.key],
        priority: getLocaleMessageById('app.common.validation.range', { min: 1, max: 32767 }),
      };
    }
  });

  return validationResult;
};
