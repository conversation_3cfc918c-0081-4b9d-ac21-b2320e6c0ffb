.membersList {
  overflow-y: auto;
  flex-grow: 3;
}

.member {
  position: relative;

  .toolbar {
    position: absolute;
    top: 0;
    right: 8px;

    display: none;
    align-items: center;
    justify-content: center;

    height: 100%;
    padding-right: 8px;
    padding-left: 40px;

    background: linear-gradient(-90deg, var(--palette-onyxBlack-20), var(--palette-onyxBlack-20), transparent);
  }

  &:hover {
    background-color: var(--palette-onyxBlack-20);

    .toolbar {
      display: flex;
    }
  }
}
