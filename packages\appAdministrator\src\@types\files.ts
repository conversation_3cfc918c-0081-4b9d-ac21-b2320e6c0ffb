export const rootFilesFolder = 'trusted';

export enum FolderContentType {
  File = 'file',
  Folder = 'folder',
}

export interface FolderContentDTO {
  contentType: 'folder' | 'file';
  contentName: string;
  parentFolder: string;
}

export interface IFrontFolderContent {
  parent: string;
  path: string;
  type: FolderContentType;
  name: string;
}

export interface IFrontFile extends IFrontFolderContent {
  type: FolderContentType.File;
  extension: string;
}

export interface IFrontFolder extends IFrontFolderContent {
  type: FolderContentType.Folder;
  items: FrontFileOrFolder[];
}

export type FrontFileOrFolder = IFrontFile | IFrontFolder;

export interface IFoldersTreeItem {
  path: string;
  name: string;
  parent: string;
  subfolders: IFoldersTreeItem[];
}
