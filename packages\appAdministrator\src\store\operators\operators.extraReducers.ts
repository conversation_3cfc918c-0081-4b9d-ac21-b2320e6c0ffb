import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IOperatorsStore } from './operators.slice';
import { getAllOperators } from './operators.thunk';

const getAllOperatorsReducers = (builder: ActionReducerMapBuilder<IOperatorsStore>) =>
  builder
    .addCase(getAllOperators.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllOperators.fulfilled, (state, action) => {
      state.loading = false;
      state.operators = action.payload;
    })
    .addCase(getAllOperators.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<IOperatorsStore>) => {
  getAllOperatorsReducers(builder);

  return builder;
};
