.ratingNode {
  max-width: 240px;
  border-radius: 5px;
  background: var(--palette-hollywoodSmile);

  .ratingNodeHeader {
    position: relative;
    border-radius: 4px 4px 0 0;
    color: var(--palette-hollywoodSmile);
    background-color: var(--palette-gatito-90);

    &.onlyHeader {
      border-radius: 4px;
    }
  }

  .headerText {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
}

.selectContainer {
  position: relative;
  padding: 8px 0;
}

.toolbarNodeInput {
  background-color: var(--palette-grassios-30);
}

.toolbarNodeOutput {
  background-color: var(--palette-amenaza-30);
}

.toolbarNodeDefault {
  background-color: var(--palette-moodBlue-30);
}

.handleHole {
  border-color: var(--palette-onyxBlack-50);
  background: var(--palette-hollywoodSmile) !important;
}

.invalid {
  border: 2px solid var(--palette-amenaza-70) !important;

  .handle {
    margin-right: -1px;
  }
}

.active {
  border: 2px solid var(--palette-moodBlue-60) !important;

  .handle {
    margin-right: -1px;
  }

  .handleHole {
    border-color: var(--palette-moodBlue-60);
  }
}
