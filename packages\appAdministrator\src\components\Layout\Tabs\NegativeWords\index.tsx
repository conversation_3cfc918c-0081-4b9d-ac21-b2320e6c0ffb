import React from 'react';

import clsx from 'clsx';

import {
  Input,
  Jumbotron,
  JumbotronType,
  showModal,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconSearch from '@product.front/icons/dist/icons17/MainStuff/IconSearch';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IAdmTabComponent } from '../../../../@types/components';
import { NegativeWord } from '../../../../@types/generated/administration';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import {
  createNegativeWord,
  deleteNegativeWord,
  getAllNegativeWords,
  updateNegativeWord,
} from '../../../../store/negativeWords/negativeWords.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';
import Editor from '../SpamWords/Editor';
import QuickFilterButton from '../SpamWords/QuickFilterButton';

import styles from './styles.module.scss';

const NegativeWordsTab: React.FC<IAdmTabComponent> = ({ name }) => {
  const dispatch = useAdministratorAppDispatch();

  const { loading, error, negativeWords } = useAdministratorAppSelector(
    (store) => store.negativeWords,
  );

  const [selectedWord, setSelectedWord] = React.useState<NegativeWord | null>(null);
  const [selectedFilter, setSelectedFilter] = React.useState<string>('all');
  const [searchString, setSearchString] = React.useState('');
  const [isDeleting, setIsDeleting] = React.useState(false);

  React.useEffect(() => {
    dispatch(getAllNegativeWords());
  }, [dispatch]);

  React.useEffect(() => {
    setSelectedWord(null);
  }, [selectedFilter, searchString, negativeWords]);

  const negativeGroups = React.useMemo(() => {
    const negativeWordMap: Record<string, NegativeWord[]> = {};
    let wordsToUse = negativeWords;

    if (searchString) {
      wordsToUse = wordsToUse.filter((word) =>
        word.name.toLowerCase().includes(searchString.toLowerCase()),
      );
    }

    wordsToUse
      .filter((word) => (selectedFilter === 'deleted' ? !!word.dateDeleted : !word.dateDeleted))
      .forEach((word) => {
        negativeWordMap[word.name[0].toLowerCase()] = [
          ...(negativeWordMap[word.name[0].toLowerCase()] ?? []),
          word,
        ];
      });

    return negativeWordMap;
  }, [negativeWords, selectedFilter, searchString]);

  const openEditor = (word: NegativeWord | null) => {
    showModal({
      header: getLocaleMessageById(
        word ? 'negativeWords.modals.edit.header' : 'negativeWords.modals.add.header',
      ),
      children: (onClose) => (
        <Editor
          spamWord={word}
          onSubmit={async (negativeWord) =>
            dispatch(
              word
                ? updateNegativeWord({ name: negativeWord, id: word.id })
                : createNegativeWord({ name: negativeWord }),
            ).unwrap()
          }
          onClose={onClose}
        />
      ),
      flushBody: true,
    });
  };

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name} className={clsx(utils.dFlex, utils.gap2)}>
        <Input
          placeholder={getLocaleMessageById('negativeWords.searchPlaceholder')}
          value={searchString}
          onChange={({ value }) => setSearchString(value || '')}
          wrapperClassName={utils.mR6}
          style={{ width: 280 }}
          preContent={<IconSearch className={clsx(utils.mL2)} />}
        />

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.refresh')}
          disabled={loading}
          loading={loading}
          onClick={() => {
            dispatch(getAllNegativeWords());
          }}
        >
          <IconRefresh />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.delete')}
          loading={isDeleting}
          disabled={!selectedWord || loading}
          onClick={() =>
            showConfirmModal({
              header: getLocaleMessageById('negativeWords.modals.delete.header'),
              text: getLocaleMessageById('negativeWords.modals.delete.text', {
                word: selectedWord?.name,
              }),
              confirmText: getLocaleMessageById('app.common.delete'),
              onConfirm: async () => {
                setIsDeleting(true);
                selectedWord && (await dispatch(deleteNegativeWord(selectedWord.id)).unwrap());
                setIsDeleting(false);
              },
            })
          }
        >
          <IconTrash />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.edit')}
          disabled={!selectedWord || loading}
          onClick={() => openEditor(selectedWord)}
        >
          <IconEdit />
        </AdmToolbarIconButton>
        <AdmToolbarCtaButton onClick={() => openEditor(null)} disabled={loading}>
          {getLocaleMessageById('app.common.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody loading={loading || isDeleting}>
        <div className={clsx(utils.dFlex, utils.gap2, utils.flexWrap)}>
          <QuickFilterButton
            text={getLocaleMessageById('negativeWords.all')}
            isActive={selectedFilter === 'all'}
            onClick={() => setSelectedFilter('all')}
          />
          {Object.keys(negativeGroups).map((symbol) => (
            <QuickFilterButton
              key={symbol}
              text={symbol}
              isActive={selectedFilter === symbol}
              onClick={() => setSelectedFilter(symbol)}
            />
          ))}
          <QuickFilterButton
            text={getLocaleMessageById('negativeWords.deleted')}
            isActive={selectedFilter === 'deleted'}
            onClick={() => setSelectedFilter('deleted')}
          />
        </div>

        <div
          className={clsx(
            utils.flexBasis0,
            utils.flexGrow1,
            utils.scrollbar,
            utils.overflowAuto,
            utils.w100,
            utils.dFlex,
            utils.flexColumn,
            utils.flexWrap,
            utils.gap2,
            utils.mT4,
          )}
          onClick={() => {
            setSelectedWord(null);
          }}
          onKeyDown={() => {}}
          role="button"
          tabIndex={-1}
        >
          {error && (
            <JumbotronError header={getLocaleMessageById('negativeWords.error')} error={error} />
          )}
          {((selectedFilter.length > 1 && Object.keys(negativeGroups).length === 0) ||
            (selectedFilter.length === 1 && (negativeGroups[selectedFilter]?.length ?? 0) === 0)) &&
            !error && (
              <Jumbotron
                type={JumbotronType.Info}
                header={getLocaleMessageById('negativeWords.empty')}
                className={clsx(
                  utils.dFlex,
                  utils.flexColumn,
                  utils.justifyContentCenter,
                  utils.alignItemsCenter,
                  utils.h100,
                  utils.w100,
                )}
              />
            )}
          {(selectedFilter.length > 1
            ? Object.keys(negativeGroups).sort((a, b) => a.localeCompare(b))
            : [selectedFilter]
          ).map((group) => (
            <div key={group} className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
              <Text variant={TextVariant.SubheadSemibold} style={{ textTransform: 'uppercase' }}>
                {group}
              </Text>
              {negativeGroups[group]?.map((word) => (
                <Text
                  key={word.id}
                  className={clsx(styles.word)}
                  title={`${getLocaleMessageById('negativeWords.wordTooltip.added', {
                    addedBy: word.addedByFio,
                    addedAt: word.dateCreated ? new Date(word.dateCreated).toLocaleString() : '',
                  })}\n${getLocaleMessageById('negativeWords.wordTooltip.deleted', {
                    deletedBy: word.deletedBy ?? '-',
                    deletedAt: word.dateDeleted ? new Date(word.dateDeleted).toLocaleString() : '',
                  })}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedWord(word);
                  }}
                  data-active={selectedWord?.id === word.id}
                >
                  {word.name}
                </Text>
              ))}
            </div>
          ))}
        </div>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default NegativeWordsTab;
