import WebarmAppSettingsResolver from '@monorepo/common/src/common/helpers/appSettings/WebarmAppSettingsResolver';
import { IShell } from '@monorepo/common/src/platform/awp-web-interfaces';

export interface IAppSettings {
  productMarketingService: string;
  productFileServer: string;
  productFileServerApiUrl: string;
}

const appSettingsResolver = new WebarmAppSettingsResolver<IAppSettings>('appMarketing');

export async function loadSettings(shell?: IShell): Promise<IAppSettings> {
  appSettingsResolver.setRequiredFields([
    'productMarketingService',
    'productFileServer',
    'productFileServerApiUrl',
  ]);

  appSettingsResolver.tryApplyShell(shell);
  await appSettingsResolver.tryApplyUnderfoot('config/appSettings.json');

  return appSettingsResolver.getAppSettings();
}

export function getSettings() {
  return appSettingsResolver.getAppSettings();
}

export function getSettingsSafe() {
  try {
    return appSettingsResolver.getAppSettings();
  } catch (error) {
    console.error(error);
    return undefined;
  }
}
