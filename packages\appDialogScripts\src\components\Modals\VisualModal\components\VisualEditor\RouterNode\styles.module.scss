.routerNode {
  max-width: 240px;
  border-radius: 5px;
  background: var(--palette-hollywoodSmile);

  .customNodeHeader {
    position: relative;
    border-radius: 4px 4px 0 0;
    color: var(--palette-hollywoodSmile);
    background-color: var(--palette-orangelle-60);

    &.onlyHeader {
      border-radius: 4px;
    }
  }

  .headerText {
    overflow: hidden;
    display: -webkit-box; /* stylelint-disable-line */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
}

.selectContainer {
  position: relative;
  padding: 8px 0;
}

.toolbarNodeInput {
  background-color: var(--palette-grassios-30);
}

.toolbarNodeOutput {
  background-color: var(--palette-amenaza-30);
}

.toolbarNodeDefault {
  background-color: var(--palette-moodBlue-30);
}

.invalid {
  border: 2px solid var(--palette-amenaza-70) !important;
}

.active {
  border: 2px solid var(--palette-moodBlue-60) !important;
}
