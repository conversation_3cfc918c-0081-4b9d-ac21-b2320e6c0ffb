import React from 'react';

import { AppComponentProps } from '@monorepo/common/src/platform/awp-web-interfaces';
import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { SystemOwner } from '@monorepo/dialog-scripts/src/@types/script';

import { defaultDialogScriptsConfig } from '../../context/appConfig';
import DialogScriptsApp from '../DialogScriptsApp';

export default function Root({ shell, initString }: AppComponentProps) {
  const initObj = {};

  if (initString?.length && initString.startsWith('{')) {
    try {
      const obj = JSON.parse(initString);
      Object.assign(initObj, obj);
    } catch (error) {
      console.error('AppDialogScripts. Error parsing init string:', error);
    }
  }

  return (
    <DialogScriptsApp
      shell={shell}
      externalConfig={{
        ...defaultDialogScriptsConfig,
        allowRichStepDescription: true,
        answerTypes: [
          AnswerTypes.Radio,
          AnswerTypes.Select,
          AnswerTypes.Button,
          AnswerTypes.TextInput,
          AnswerTypes.None,
        ],
        ...initObj,
        systemOwner: SystemOwner.DialogScripts,
      }}
      systemOwner={SystemOwner.DialogScripts}
    />
  );
}
