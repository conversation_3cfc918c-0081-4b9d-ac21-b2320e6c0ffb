import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { ISubjectsStore } from './subjects.slice';
import { getAllSubjects } from './subjects.thunk';

const getSubjectsReducers = (builder: ActionReducerMapBuilder<ISubjectsStore>) =>
  builder
    .addCase(getAllSubjects.pending, (state) => {
      state.loading = true;
      state.error = undefined;
      state.selectedSubject = null;
    })
    .addCase(getAllSubjects.fulfilled, (state, action) => {
      state.subjects = action.payload;
      state.loading = false;
    })
    .addCase(getAllSubjects.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<ISubjectsStore>) => {
  getSubjectsReducers(builder);

  return builder;
};
