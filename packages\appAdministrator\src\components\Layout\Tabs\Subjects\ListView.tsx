import React from 'react';

import { MenuItem, Text } from '@product.front/ui-kit';

import { highlightText } from '@monorepo/common/src/helpers/textHighlight';

import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { setSelectedRequest } from '../../../../store/subjects/subjects.slice';

import ContextMenu from './ContextMenu';

import styles from './styles.module.scss';

interface ISubjectsListViewProps {
  searchText: string;
  shouldSearchByName: boolean;
  shouldSearchByText: boolean;
  isDescSorting: boolean;
}
const ListView = ({
  searchText,
  shouldSearchByName,
  shouldSearchByText,
  isDescSorting,
}: ISubjectsListViewProps) => {
  const dispatch = useAdministratorAppDispatch();

  const { subjects, selectedSubject } = useAdministratorAppSelector((store) => store.subjects);

  const filteredSubjects = React.useMemo(() => {
    let toReturn = Object.values(subjects);

    if (searchText && (shouldSearchByName || shouldSearchByText)) {
      toReturn = toReturn.filter(
        (subject) =>
          (!shouldSearchByName || subject.name.toLowerCase().includes(searchText.toLowerCase())) &&
          (!shouldSearchByText ||
            subject.description?.toLowerCase().includes(searchText.toLowerCase())),
      );
    }

    return toReturn.toSorted((a, b) =>
      isDescSorting ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name),
    );
  }, [searchText, shouldSearchByName, shouldSearchByText, isDescSorting, subjects]);

  return (
    <>
      {filteredSubjects.map((subject) => (
        <MenuItem
          key={subject.id}
          className={styles.menuItem}
          active={selectedSubject?.id === subject.id}
          onClick={() => dispatch(setSelectedRequest(subject))}
        >
          <Text ellipsis>
            {shouldSearchByName ? highlightText(subject.name, searchText) : subject.name}
          </Text>
          <ContextMenu subject={subject} />
        </MenuItem>
      ))}
    </>
  );
};

export default ListView;
