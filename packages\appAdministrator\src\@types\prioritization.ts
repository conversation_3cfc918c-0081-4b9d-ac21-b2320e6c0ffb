export interface IFrontPrioritizationRule {
  id: number;
  title: string;
  isEnabled: boolean;
  attributeId: number;
  comparisonRule: string;
  values: { key: string; value: string; priority: number }[];
}

export enum RightPartType {
  Text = 'Text',
  List = 'List',
  None = 'None',
  Unknown = 'Unknown',
}

export interface IFrontPrioritizationAttribute {
  id: number;
  displayName: string;
  comparisonRules: {
    name: string;
    code: string;
  }[];
  rightPartType: RightPartType;
  options: { name: string; value: string }[];
}
