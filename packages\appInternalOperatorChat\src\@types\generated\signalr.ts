/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/**
 * Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.AddedToChat
 */
export interface AddedToChatNotificationPayload {
  /** Представляет модель чата */
  chat?: Chat;

  /** Модель добавленных пользователей */
  addedUsers?: PartialOperator[] | null;
}

/**
 * Представляет модель уведомления
 */
export interface AddedToChatNotificationPayloadNotification {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  guid?: string;

  /** Представляет тип уведомления */
  type?: NotificationType;

  /**
   * Уникальный идентификатор пользователя вызвавшего уведомление
   * @format uuid
   */
  initiatorId?: string | null;

  /** Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.AddedToChat */
  payload?: AddedToChatNotificationPayload;
}

/**
 * Представляет запрос на добавление пользователей в чат
 */
export interface AddUsersToChatRequest {
  /** Уникальные идентификаторы пользователей */
  userIds?: string[] | null;

  /**
   * Уникальный идентификатор чата
   * @format uuid
   */
  chatId?: string;
}

export interface Assembly {
  definedTypes?: TypeInfo[] | null;
  exportedTypes?: Type[] | null;
  codeBase?: string | null;
  entryPoint?: MethodInfo;
  fullName?: string | null;
  imageRuntimeVersion?: string | null;
  isDynamic?: boolean;
  location?: string | null;
  reflectionOnly?: boolean;
  isCollectible?: boolean;
  isFullyTrusted?: boolean;
  customAttributes?: CustomAttributeData[] | null;
  escapedCodeBase?: string | null;
  manifestModule?: Module;
  modules?: Module[] | null;
  globalAssemblyCache?: boolean;

  /** @format int64 */
  hostContext?: number;
  securityRuleSet?: SecurityRuleSet;
}

/**
 * Представляет модель вложения в сообщении
 */
export interface Attachment {
  /**
   * Униальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Название файла вложения */
  fileName: string;

  /** MIME-тип */
  mimetype: string;

  /**
   * URL адрес для скачивания
   * @format uri
   */
  url: string;
}

export enum CallingConventions {
  Standard = "Standard",
  VarArgs = "VarArgs",
  Any = "Any",
  HasThis = "HasThis",
  ExplicitThis = "ExplicitThis",
}

/**
 * Представляет модель чата
 */
export interface Chat {
  /**
   * Униальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Название */
  name?: string | null;

  /** Представляет тип чата */
  type?: ChatType;

  /**
   * Уникальный идентификатор создателя
   * @format uuid
   */
  ownerId?: string | null;

  /**
   * Дата создания чата в UTC
   * @format date-time
   */
  createdAt?: string | null;

  /**
   * Дата удаления чата в UTC
   * @format date-time
   */
  deletedAt?: string | null;

  /**
   * Количество непрочитанных сообщений
   * @format int64
   */
  unreadedMessagesCount?: number;

  /** Представляет модель текстового сообщения */
  lastMessage?: Message;

  /** Участники */
  participants?: PartialOperator[] | null;
}

/**
 * Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.ChatCreated
 */
export interface ChatCreatedNotificationPayload {
  /** Представляет частичную модель чата */
  createdPartialChat?: PartialChat;
}

/**
 * Представляет модель уведомления
 */
export interface ChatCreatedNotificationPayloadNotification {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  guid?: string;

  /** Представляет тип уведомления */
  type?: NotificationType;

  /**
   * Уникальный идентификатор пользователя вызвавшего уведомление
   * @format uuid
   */
  initiatorId?: string | null;

  /** Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.ChatCreated */
  payload?: ChatCreatedNotificationPayload;
}

/**
 * Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.ChatDeleted
 */
export interface ChatDeletedNotificationPayload {
  /**
   * Уникальный идентификатор удалённого чата
   * @format uuid
   */
  chatId?: string;
}

/**
 * Представляет модель уведомления
 */
export interface ChatDeletedNotificationPayloadNotification {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  guid?: string;

  /** Представляет тип уведомления */
  type?: NotificationType;

  /**
   * Уникальный идентификатор пользователя вызвавшего уведомление
   * @format uuid
   */
  initiatorId?: string | null;

  /** Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.ChatDeleted */
  payload?: ChatDeletedNotificationPayload;
}

/**
 * Представляет тип чата
 */
export enum ChatType {
  Unknown = "Unknown",
  Global = "Global",
  Personal = "Personal",
  Public = "Public",
  Closed = "Closed",
}

/**
 * Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.ChatUpdated
 */
export interface ChatUpdatedNotificationPayload {
  /** Представляет частичную модель чата */
  partialChat?: PartialChat;
}

/**
 * Представляет модель уведомления
 */
export interface ChatUpdatedNotificationPayloadNotification {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  guid?: string;

  /** Представляет тип уведомления */
  type?: NotificationType;

  /**
   * Уникальный идентификатор пользователя вызвавшего уведомление
   * @format uuid
   */
  initiatorId?: string | null;

  /** Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.ChatUpdated */
  payload?: ChatUpdatedNotificationPayload;
}

export interface ConstructorInfo {
  name?: string | null;
  declaringType?: Type;
  reflectedType?: Type;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  attributes?: MethodAttributes;
  methodImplementationFlags?: MethodImplAttributes;
  callingConvention?: CallingConventions;
  isAbstract?: boolean;
  isConstructor?: boolean;
  isFinal?: boolean;
  isHideBySig?: boolean;
  isSpecialName?: boolean;
  isStatic?: boolean;
  isVirtual?: boolean;
  isAssembly?: boolean;
  isFamily?: boolean;
  isFamilyAndAssembly?: boolean;
  isFamilyOrAssembly?: boolean;
  isPrivate?: boolean;
  isPublic?: boolean;
  isConstructedGenericMethod?: boolean;
  isGenericMethod?: boolean;
  isGenericMethodDefinition?: boolean;
  containsGenericParameters?: boolean;
  methodHandle?: RuntimeMethodHandle;
  isSecurityCritical?: boolean;
  isSecuritySafeCritical?: boolean;
  isSecurityTransparent?: boolean;
  memberType?: MemberTypes;
}

/**
 * Представляет запрос на создание чата
 */
export interface CreateChatRequest {
  /** Название */
  name?: string | null;

  /** Участники чата */
  participants: string[];
}

export interface CustomAttributeData {
  attributeType?: Type;
  constructor?: ConstructorInfo;
  constructorArguments?: CustomAttributeTypedArgument[] | null;
  namedArguments?: CustomAttributeNamedArgument[] | null;
}

export interface CustomAttributeNamedArgument {
  memberInfo?: MemberInfo;
  typedValue?: CustomAttributeTypedArgument;
  memberName?: string | null;
  isField?: boolean;
}

export interface CustomAttributeTypedArgument {
  argumentType?: Type;
  value?: any;
}

/**
 * Представляет данные об ошибке
 */
export interface ErrorData {
  /** Сообщения */
  messages?: string[] | null;

  /** Статусы ошибок */
  status?: ErrorStatus;

  /** Текстовое представлние статуса ошибки (InternalOperatorChat.Contracts.Models.ErrorData.Status) */
  statusText?: string | null;
}

/**
 * Представляет ошибку
 */
export interface ErrorMessage {
  /** Представляет данные об ошибке */
  error?: ErrorData;
}

/**
 * Статусы ошибок
 */
export enum ErrorStatus {
  Unknown = "Unknown",
  ApiError = "ApiError",
}

export enum EventAttributes {
  None = "None",
  SpecialName = "SpecialName",
  RTSpecialName = "RTSpecialName",
}

export interface EventInfo {
  name?: string | null;
  declaringType?: Type;
  reflectedType?: Type;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  memberType?: MemberTypes;
  attributes?: EventAttributes;
  isSpecialName?: boolean;
  addMethod?: MethodInfo;
  removeMethod?: MethodInfo;
  raiseMethod?: MethodInfo;
  isMulticast?: boolean;
  eventHandlerType?: Type;
}

export interface Exception {
  targetSite?: MethodBase;
  message?: string | null;
  data?: Record<string, any>;
  innerException?: Exception;
  helpLink?: string | null;
  source?: string | null;

  /** @format int32 */
  hResult?: number;
  stackTrace?: string | null;
}

export enum FieldAttributes {
  PrivateScope = "PrivateScope",
  Private = "Private",
  FamANDAssem = "FamANDAssem",
  Assembly = "Assembly",
  Family = "Family",
  FamORAssem = "FamORAssem",
  Public = "Public",
  FieldAccessMask = "FieldAccessMask",
  Static = "Static",
  InitOnly = "InitOnly",
  Literal = "Literal",
  NotSerialized = "NotSerialized",
  HasFieldRVA = "HasFieldRVA",
  SpecialName = "SpecialName",
  RTSpecialName = "RTSpecialName",
  HasFieldMarshal = "HasFieldMarshal",
  PinvokeImpl = "PinvokeImpl",
  HasDefault = "HasDefault",
  ReservedMask = "ReservedMask",
}

export interface FieldInfo {
  name?: string | null;
  declaringType?: Type;
  reflectedType?: Type;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  memberType?: MemberTypes;
  attributes?: FieldAttributes;
  fieldType?: Type;
  isInitOnly?: boolean;
  isLiteral?: boolean;
  isNotSerialized?: boolean;
  isPinvokeImpl?: boolean;
  isSpecialName?: boolean;
  isStatic?: boolean;
  isAssembly?: boolean;
  isFamily?: boolean;
  isFamilyAndAssembly?: boolean;
  isFamilyOrAssembly?: boolean;
  isPrivate?: boolean;
  isPublic?: boolean;
  isSecurityCritical?: boolean;
  isSecuritySafeCritical?: boolean;
  isSecurityTransparent?: boolean;
  fieldHandle?: RuntimeFieldHandle;
}

export enum GenericParameterAttributes {
  None = "None",
  Covariant = "Covariant",
  Contravariant = "Contravariant",
  VarianceMask = "VarianceMask",
  ReferenceTypeConstraint = "ReferenceTypeConstraint",
  NotNullableValueTypeConstraint = "NotNullableValueTypeConstraint",
  DefaultConstructorConstraint = "DefaultConstructorConstraint",
  SpecialConstraintMask = "SpecialConstraintMask",
}

/**
 * Представляет запрос на получение истории сообщений
 */
export interface GetMessagesRequest {
  /**
   * Уникальный идентификатор чата
   * @format uuid
   */
  chatId?: string;

  /**
   * Сколько сообщений пропустить
   * @format int64
   */
  offset?: number;

  /**
   * Сколько сообщений взять
   * @format int64
   */
  limit?: number;
}

/**
 * Представляет модель ответа на запрос получения списка операторов и групп продукта
 */
export interface GetOperatorsAndGroupsResponse {
  /** Операторы */
  operators?: InternalOperator[] | null;

  /** Группы */
  groups?: OperatorGroup[] | null;
}

export type ICustomAttributeProvider = object;

/**
 * Представлят расширенную модель оператора
 */
export interface InternalOperator {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Логин */
  login?: string | null;

  /** Имя */
  firstName?: string | null;

  /** Фамилия */
  lastName?: string | null;

  /** Отчество */
  middleName?: string | null;

  /** Представляет статус оператора */
  status?: OperatorStatus;
}

export type IntPtr = object;

export enum LayoutKind {
  Sequential = "Sequential",
  Explicit = "Explicit",
  Auto = "Auto",
}

export interface MemberInfo {
  memberType?: MemberTypes;
  declaringType?: Type;
  reflectedType?: Type;
  name?: string | null;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
}

export enum MemberTypes {
  Constructor = "Constructor",
  Event = "Event",
  Field = "Field",
  Method = "Method",
  Property = "Property",
  TypeInfo = "TypeInfo",
  Custom = "Custom",
  NestedType = "NestedType",
  All = "All",
}

/**
 * Представляет модель текстового сообщения
 */
export interface Message {
  /**
   * Униальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Представляет тип сообщения */
  type?: MessageType;

  /** Текст сообщения */
  text?: string | null;

  /**
   * Дата создания в UTC
   * @format date-time
   */
  createDate?: string;

  /**
   * Идентификатор чата отправителя
   * @format uuid
   */
  chatId?: string;

  /** Представляет частичную модель оператора */
  author?: PartialOperator;

  /** Список вложений */
  attachments?: Attachment[] | null;

  /** Представляет модель текстового сообщения */
  replyMessage?: Message;

  /** Представляет модель полезной нагрузки для систменого сообщения */
  systemPayload?: SystemPayload;
}

/**
 * Представляет тип сообщения
 */
export enum MessageType {
  Normal = "Normal",
  System = "System",
}

export enum MethodAttributes {
  ReuseSlot = "ReuseSlot",
  Private = "Private",
  FamANDAssem = "FamANDAssem",
  Assembly = "Assembly",
  Family = "Family",
  FamORAssem = "FamORAssem",
  Public = "Public",
  MemberAccessMask = "MemberAccessMask",
  UnmanagedExport = "UnmanagedExport",
  Static = "Static",
  Final = "Final",
  Virtual = "Virtual",
  HideBySig = "HideBySig",
  NewSlot = "NewSlot",
  CheckAccessOnOverride = "CheckAccessOnOverride",
  Abstract = "Abstract",
  SpecialName = "SpecialName",
  RTSpecialName = "RTSpecialName",
  PinvokeImpl = "PinvokeImpl",
  HasSecurity = "HasSecurity",
  RequireSecObject = "RequireSecObject",
  ReservedMask = "ReservedMask",
}

export interface MethodBase {
  memberType?: MemberTypes;
  name?: string | null;
  declaringType?: Type;
  reflectedType?: Type;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  attributes?: MethodAttributes;
  methodImplementationFlags?: MethodImplAttributes;
  callingConvention?: CallingConventions;
  isAbstract?: boolean;
  isConstructor?: boolean;
  isFinal?: boolean;
  isHideBySig?: boolean;
  isSpecialName?: boolean;
  isStatic?: boolean;
  isVirtual?: boolean;
  isAssembly?: boolean;
  isFamily?: boolean;
  isFamilyAndAssembly?: boolean;
  isFamilyOrAssembly?: boolean;
  isPrivate?: boolean;
  isPublic?: boolean;
  isConstructedGenericMethod?: boolean;
  isGenericMethod?: boolean;
  isGenericMethodDefinition?: boolean;
  containsGenericParameters?: boolean;
  methodHandle?: RuntimeMethodHandle;
  isSecurityCritical?: boolean;
  isSecuritySafeCritical?: boolean;
  isSecurityTransparent?: boolean;
}

export enum MethodImplAttributes {
  IL = "IL",
  Native = "Native",
  OPTIL = "OPTIL",
  Runtime = "Runtime",
  Unmanaged = "Unmanaged",
  NoInlining = "NoInlining",
  ForwardRef = "ForwardRef",
  Synchronized = "Synchronized",
  NoOptimization = "NoOptimization",
  PreserveSig = "PreserveSig",
  AggressiveInlining = "AggressiveInlining",
  AggressiveOptimization = "AggressiveOptimization",
  InternalCall = "InternalCall",
  MaxMethodImplVal = "MaxMethodImplVal",
}

export interface MethodInfo {
  name?: string | null;
  declaringType?: Type;
  reflectedType?: Type;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  attributes?: MethodAttributes;
  methodImplementationFlags?: MethodImplAttributes;
  callingConvention?: CallingConventions;
  isAbstract?: boolean;
  isConstructor?: boolean;
  isFinal?: boolean;
  isHideBySig?: boolean;
  isSpecialName?: boolean;
  isStatic?: boolean;
  isVirtual?: boolean;
  isAssembly?: boolean;
  isFamily?: boolean;
  isFamilyAndAssembly?: boolean;
  isFamilyOrAssembly?: boolean;
  isPrivate?: boolean;
  isPublic?: boolean;
  isConstructedGenericMethod?: boolean;
  isGenericMethod?: boolean;
  isGenericMethodDefinition?: boolean;
  containsGenericParameters?: boolean;
  methodHandle?: RuntimeMethodHandle;
  isSecurityCritical?: boolean;
  isSecuritySafeCritical?: boolean;
  isSecurityTransparent?: boolean;
  memberType?: MemberTypes;
  returnParameter?: ParameterInfo;
  returnType?: Type;
  returnTypeCustomAttributes?: ICustomAttributeProvider;
}

export interface Module {
  assembly?: Assembly;
  fullyQualifiedName?: string | null;
  name?: string | null;

  /** @format int32 */
  mdStreamVersion?: number;

  /** @format uuid */
  moduleVersionId?: string;
  scopeName?: string | null;
  moduleHandle?: ModuleHandle;
  customAttributes?: CustomAttributeData[] | null;

  /** @format int32 */
  metadataToken?: number;
}

export interface ModuleHandle {
  /** @format int32 */
  mdStreamVersion?: number;
}

/**
 * Представляет тип уведомления
 */
export enum NotificationType {
  Unknown = "Unknown",
  AddedToChat = "AddedToChat",
  RemovedFromChat = "RemovedFromChat",
  ChatUpdated = "ChatUpdated",
  ChatCreated = "ChatCreated",
  OperatorStatusChanged = "OperatorStatusChanged",
  ChatDeleted = "ChatDeleted",
}

/**
 * Представляет модель оператора
 */
export interface Operator {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Логин */
  login?: string | null;

  /** Имя */
  firstName?: string | null;

  /** Фамилия */
  lastName?: string | null;

  /** Отчество */
  middleName?: string | null;

  /** Представляет роль оператора */
  role?: OperatorRole;
}

/**
 * Представляет группу платформы
 */
export interface OperatorGroup {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Название */
  name?: string | null;

  /**
   * Уникальный идентификатор родительской группы
   * @format uuid
   */
  parentId?: string | null;

  /** Уникальные идентификаторы операторов */
  operatorIds?: string[] | null;
}

/**
 * Представляет роль оператора
 */
export enum OperatorRole {
  User = "User",
  Admin = "Admin",
  SuperAdmin = "SuperAdmin",
}

/**
 * Представляет статус оператора
 */
export enum OperatorStatus {
  NotAvailable = "NotAvailable",
  Offline = "Offline",
  Available = "Available",
  Busy = "Busy",
}

/**
 * Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.OperatorStatusChanged
 */
export interface OperatorStatusChangedNotificationPayload {
  /**
   * Уникальный идентификатор оператора
   * @format uuid
   */
  operatorId?: string;

  /** Представляет статус оператора */
  status?: OperatorStatus;
}

/**
 * Представляет модель уведомления
 */
export interface OperatorStatusChangedNotificationPayloadNotification {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  guid?: string;

  /** Представляет тип уведомления */
  type?: NotificationType;

  /**
   * Уникальный идентификатор пользователя вызвавшего уведомление
   * @format uuid
   */
  initiatorId?: string | null;

  /** Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.OperatorStatusChanged */
  payload?: OperatorStatusChangedNotificationPayload;
}

export enum ParameterAttributes {
  None = "None",
  In = "In",
  Out = "Out",
  Lcid = "Lcid",
  Retval = "Retval",
  Optional = "Optional",
  HasDefault = "HasDefault",
  HasFieldMarshal = "HasFieldMarshal",
  Reserved3 = "Reserved3",
  Reserved4 = "Reserved4",
  ReservedMask = "ReservedMask",
}

export interface ParameterInfo {
  attributes?: ParameterAttributes;
  member?: MemberInfo;
  name?: string | null;
  parameterType?: Type;

  /** @format int32 */
  position?: number;
  isIn?: boolean;
  isLcid?: boolean;
  isOptional?: boolean;
  isOut?: boolean;
  isRetval?: boolean;
  defaultValue?: any;
  rawDefaultValue?: any;
  hasDefaultValue?: boolean;
  customAttributes?: CustomAttributeData[] | null;

  /** @format int32 */
  metadataToken?: number;
}

/**
 * Представляет частичную модель чата
 */
export interface PartialChat {
  /**
   * Униальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Название */
  name?: string | null;

  /** Представляет тип чата */
  type?: ChatType;

  /**
   * Уникальный идентификатор создателя
   * @format uuid
   */
  ownerId?: string | null;

  /**
   * Дата создания чата в UTC
   * @format date-time
   */
  createdAt?: string | null;

  /**
   * Дата удаления чата в UTC
   * @format date-time
   */
  deletedAt?: string | null;
}

/**
 * Представляет частичную модель оператора
 */
export interface PartialOperator {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  id?: string;

  /** Логин */
  login?: string | null;

  /** Имя */
  firstName?: string | null;

  /** Фамилия */
  lastName?: string | null;

  /** Отчество */
  middleName?: string | null;
}

export enum PropertyAttributes {
  None = "None",
  SpecialName = "SpecialName",
  RTSpecialName = "RTSpecialName",
  HasDefault = "HasDefault",
  Reserved2 = "Reserved2",
  Reserved3 = "Reserved3",
  Reserved4 = "Reserved4",
  ReservedMask = "ReservedMask",
}

export interface PropertyInfo {
  name?: string | null;
  declaringType?: Type;
  reflectedType?: Type;
  module?: Module;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  memberType?: MemberTypes;
  propertyType?: Type;
  attributes?: PropertyAttributes;
  isSpecialName?: boolean;
  canRead?: boolean;
  canWrite?: boolean;
  getMethod?: MethodInfo;
  setMethod?: MethodInfo;
}

/**
 * Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.RemovedFromChat
 */
export interface RemovedFromChatNotificationPayload {
  /**
   * Уникальный идентификатор чата из которого удалили пользователя
   * @format uuid
   */
  chatId?: string;

  /** Удалённые пользователи */
  removedUsers?: PartialOperator[] | null;
}

/**
 * Представляет модель уведомления
 */
export interface RemovedFromChatNotificationPayloadNotification {
  /**
   * Уникальный идентификатор
   * @format uuid
   */
  guid?: string;

  /** Представляет тип уведомления */
  type?: NotificationType;

  /**
   * Уникальный идентификатор пользователя вызвавшего уведомление
   * @format uuid
   */
  initiatorId?: string | null;

  /** Представляет модель полезной нагрузки уведомления InternalOperatorChat.Contracts.Models.NotificationType.RemovedFromChat */
  payload?: RemovedFromChatNotificationPayload;
}

/**
 * Представляет запрос на удаление пользователей из чата
 */
export interface RemoveUsersFromChatRequest {
  /** Уникальные идентификаторы пользователей */
  userIds?: string[] | null;

  /**
   * Уникальный идентификатор чата
   * @format uuid
   */
  chatId?: string;
}

export interface RuntimeFieldHandle {
  value?: IntPtr;
}

export interface RuntimeMethodHandle {
  value?: IntPtr;
}

export interface RuntimeTypeHandle {
  value?: IntPtr;
}

export enum SecurityRuleSet {
  None = "None",
  Level1 = "Level1",
  Level2 = "Level2",
}

/**
 * Представляет запрос на отправку сообщения в чат
 */
export interface SendMessageRequest {
  /**
   * Уникальный идентификатор чата
   * @format uuid
   */
  chatId?: string;

  /** Текст сообщения */
  text: string;

  /** Список вложений */
  attachments?: Attachment[] | null;

  /** Представляет модель текстового сообщения */
  replyMessage?: Message;
}

export interface StructLayoutAttribute {
  typeId?: any;
  value?: LayoutKind;
}

/**
 * Представляет тип системного сообщения
 */
export enum SystemMessageType {
  UserAdded = "UserAdded",
  UserRemoved = "UserRemoved",
}

/**
 * Представляет модель полезной нагрузки для систменого сообщения
 */
export interface SystemPayload {
  /** Представляет тип системного сообщения */
  type?: SystemMessageType;

  /**
   * Уникальный идентификатор оператора
   * @format uuid
   */
  operatorId?: string | null;

  /** Имя оператора */
  firstName?: string | null;

  /** Фамилия оператора */
  lastName?: string | null;

  /** Отчество оператора */
  middleName?: string | null;
}

export interface Type {
  name?: string | null;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  isInterface?: boolean;
  memberType?: MemberTypes;
  namespace?: string | null;
  assemblyQualifiedName?: string | null;
  fullName?: string | null;
  assembly?: Assembly;
  module?: Module;
  isNested?: boolean;
  declaringType?: Type;
  declaringMethod?: MethodBase;
  reflectedType?: Type;
  underlyingSystemType?: Type;
  isTypeDefinition?: boolean;
  isArray?: boolean;
  isByRef?: boolean;
  isPointer?: boolean;
  isConstructedGenericType?: boolean;
  isGenericParameter?: boolean;
  isGenericTypeParameter?: boolean;
  isGenericMethodParameter?: boolean;
  isGenericType?: boolean;
  isGenericTypeDefinition?: boolean;
  isSZArray?: boolean;
  isVariableBoundArray?: boolean;
  isByRefLike?: boolean;
  hasElementType?: boolean;
  genericTypeArguments?: Type[] | null;

  /** @format int32 */
  genericParameterPosition?: number;
  genericParameterAttributes?: GenericParameterAttributes;
  attributes?: TypeAttributes;
  isAbstract?: boolean;
  isImport?: boolean;
  isSealed?: boolean;
  isSpecialName?: boolean;
  isClass?: boolean;
  isNestedAssembly?: boolean;
  isNestedFamANDAssem?: boolean;
  isNestedFamily?: boolean;
  isNestedFamORAssem?: boolean;
  isNestedPrivate?: boolean;
  isNestedPublic?: boolean;
  isNotPublic?: boolean;
  isPublic?: boolean;
  isAutoLayout?: boolean;
  isExplicitLayout?: boolean;
  isLayoutSequential?: boolean;
  isAnsiClass?: boolean;
  isAutoClass?: boolean;
  isUnicodeClass?: boolean;
  isCOMObject?: boolean;
  isContextful?: boolean;
  isEnum?: boolean;
  isMarshalByRef?: boolean;
  isPrimitive?: boolean;
  isValueType?: boolean;
  isSignatureType?: boolean;
  isSecurityCritical?: boolean;
  isSecuritySafeCritical?: boolean;
  isSecurityTransparent?: boolean;
  structLayoutAttribute?: StructLayoutAttribute;
  typeInitializer?: ConstructorInfo;
  typeHandle?: RuntimeTypeHandle;

  /** @format uuid */
  guid?: string;
  baseType?: Type;
  isSerializable?: boolean;
  containsGenericParameters?: boolean;
  isVisible?: boolean;
}

export enum TypeAttributes {
  NotPublic = "NotPublic",
  Public = "Public",
  NestedPublic = "NestedPublic",
  NestedPrivate = "NestedPrivate",
  NestedFamily = "NestedFamily",
  NestedAssembly = "NestedAssembly",
  NestedFamANDAssem = "NestedFamANDAssem",
  NestedFamORAssem = "NestedFamORAssem",
  SequentialLayout = "SequentialLayout",
  ExplicitLayout = "ExplicitLayout",
  LayoutMask = "LayoutMask",
  Interface = "Interface",
  Abstract = "Abstract",
  Sealed = "Sealed",
  SpecialName = "SpecialName",
  RTSpecialName = "RTSpecialName",
  Import = "Import",
  Serializable = "Serializable",
  WindowsRuntime = "WindowsRuntime",
  UnicodeClass = "UnicodeClass",
  AutoClass = "AutoClass",
  CustomFormatClass = "CustomFormatClass",
  HasSecurity = "HasSecurity",
  ReservedMask = "ReservedMask",
  BeforeFieldInit = "BeforeFieldInit",
  CustomFormatMask = "CustomFormatMask",
}

export interface TypeInfo {
  name?: string | null;
  customAttributes?: CustomAttributeData[] | null;
  isCollectible?: boolean;

  /** @format int32 */
  metadataToken?: number;
  isInterface?: boolean;
  memberType?: MemberTypes;
  namespace?: string | null;
  assemblyQualifiedName?: string | null;
  fullName?: string | null;
  assembly?: Assembly;
  module?: Module;
  isNested?: boolean;
  declaringType?: Type;
  declaringMethod?: MethodBase;
  reflectedType?: Type;
  underlyingSystemType?: Type;
  isTypeDefinition?: boolean;
  isArray?: boolean;
  isByRef?: boolean;
  isPointer?: boolean;
  isConstructedGenericType?: boolean;
  isGenericParameter?: boolean;
  isGenericTypeParameter?: boolean;
  isGenericMethodParameter?: boolean;
  isGenericType?: boolean;
  isGenericTypeDefinition?: boolean;
  isSZArray?: boolean;
  isVariableBoundArray?: boolean;
  isByRefLike?: boolean;
  hasElementType?: boolean;
  genericTypeArguments?: Type[] | null;

  /** @format int32 */
  genericParameterPosition?: number;
  genericParameterAttributes?: GenericParameterAttributes;
  attributes?: TypeAttributes;
  isAbstract?: boolean;
  isImport?: boolean;
  isSealed?: boolean;
  isSpecialName?: boolean;
  isClass?: boolean;
  isNestedAssembly?: boolean;
  isNestedFamANDAssem?: boolean;
  isNestedFamily?: boolean;
  isNestedFamORAssem?: boolean;
  isNestedPrivate?: boolean;
  isNestedPublic?: boolean;
  isNotPublic?: boolean;
  isPublic?: boolean;
  isAutoLayout?: boolean;
  isExplicitLayout?: boolean;
  isLayoutSequential?: boolean;
  isAnsiClass?: boolean;
  isAutoClass?: boolean;
  isUnicodeClass?: boolean;
  isCOMObject?: boolean;
  isContextful?: boolean;
  isEnum?: boolean;
  isMarshalByRef?: boolean;
  isPrimitive?: boolean;
  isValueType?: boolean;
  isSignatureType?: boolean;
  isSecurityCritical?: boolean;
  isSecuritySafeCritical?: boolean;
  isSecurityTransparent?: boolean;
  structLayoutAttribute?: StructLayoutAttribute;
  typeInitializer?: ConstructorInfo;
  typeHandle?: RuntimeTypeHandle;

  /** @format uuid */
  guid?: string;
  baseType?: Type;
  isSerializable?: boolean;
  containsGenericParameters?: boolean;
  isVisible?: boolean;
  genericTypeParameters?: Type[] | null;
  declaredConstructors?: ConstructorInfo[] | null;
  declaredEvents?: EventInfo[] | null;
  declaredFields?: FieldInfo[] | null;
  declaredMembers?: MemberInfo[] | null;
  declaredMethods?: MethodInfo[] | null;
  declaredNestedTypes?: TypeInfo[] | null;
  declaredProperties?: PropertyInfo[] | null;
  implementedInterfaces?: Type[] | null;
}

/**
 * Представлят запрос на обновление данных чата
 */
export interface UpdateChatRequest {
  /**
   * Уникальный идентификатор чата
   * @format uuid
   */
  id?: string;

  /** Название чата */
  name?: string | null;
}

/**
 * Представляет ошибку пользователя, которую нужно отобразить
 */
export interface UserException {
  targetSite?: MethodBase;
  message?: string | null;
  data?: Record<string, any>;
  innerException?: Exception;
  helpLink?: string | null;
  source?: string | null;

  /** @format int32 */
  hResult?: number;
  stackTrace?: string | null;
}
