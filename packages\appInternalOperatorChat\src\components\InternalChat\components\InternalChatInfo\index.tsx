import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import {
  Avatar,
  AvatarSize,
  AvatarVariant,
  Button,
  ButtonSize,
  ButtonVariant,
  Colors,
  IconButton,
  Input,
  showModal,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconClose from '@product.front/icons/dist/icons17/MainStuff/IconClose';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';
import IconUsers from '@product.front/icons/dist/icons17/Person&Doc/IconUsers';

import { getPluralGroup } from '@monorepo/common/src/helpers/localeHelper';

import { ChatType, Operator } from '../../../../@types/generated/signalr';
import { getAbbr } from '../../../../helpers/abbrHelper';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { injectGroupsToOperators } from '../../../../helpers/operatorsGroupsHelper';
import {
  addUsersToChatAsync,
  removeUsersFromChatAsync,
  updateChatAsync,
} from '../../../../services/internalChat.service';
import { useInternalChatDispatch, useInternalChatSelector } from '../../../../store/hooks';
import { toggleChatInfo, updateChat } from '../../../../store/internalChat.slice';
import { selectUserRights } from '../../../../store/user/user.selectors';
import InternalChatHeader from '../InternalChatHeader';
import InternalChatMembersSelectorModal from '../InternalChatMembersSelectorModal';
import InternalChatOperator from '../InternalChatOperator';

import { getChatInfo } from './chatInfoHelper';
import DeleteModal from './DeleteModal';

import styles from './styles.module.scss';

const InternalChatInfo: React.FC<HTMLAttributes<HTMLDivElement>> = () => {
  const dispatch = useInternalChatDispatch();
  const { chats, allOperators, allOperatorsGroups } = useInternalChatSelector(
    (state) => state.internalChat,
  );

  const { user } = useInternalChatSelector((state) => state.user);
  const rights = useInternalChatSelector(selectUserRights);
  const selectedChat = chats.find((chat) => chat.isSelected);

  const isOwner = user?.id === selectedChat?.ownerId;

  const canRename = selectedChat?.type !== ChatType.Global && (isOwner || rights.canRenameAll);
  const canChangeMembers =
    selectedChat?.type !== ChatType.Global && (isOwner || rights.canChangeMembersAll);

  const canRemove = selectedChat?.type !== ChatType.Global && (isOwner || rights.canRemoveAll);

  const handleSave = async (selectedOperators: Operator[], close?: () => void) => {
    const chatId = selectedChat?.id;
    const currentOperatorsIds = selectedChat?.participants?.map(({ id }) => id);
    const selectedOperatorsIds = selectedOperators.map(({ id }) => id);

    const needToRemoveUserIds = currentOperatorsIds?.filter(
      (id) => !selectedOperatorsIds.includes(id),
    );

    const needToAddUserIds = selectedOperatorsIds.filter(
      (id) => !currentOperatorsIds?.includes(id),
    );

    if (chatId && needToAddUserIds?.length) {
      await addUsersToChatAsync({
        chatId,
        userIds: needToAddUserIds as string[],
      });
    }

    if (needToRemoveUserIds?.length && chatId) {
      await removeUsersFromChatAsync({
        chatId,
        userIds: needToRemoveUserIds as string[],
      });
    }

    close?.();
  };

  const handleEdit = () => {
    if (!selectedChat || !selectedChat?.participants || !user) {
      return;
    }
    showModal({
      header: getLocaleMessageById('app.header.changeMembers'),
      children: (onClose) => (
        <InternalChatMembersSelectorModal
          selected={selectedChat.participants!}
          allOperators={injectGroupsToOperators(allOperators, allOperatorsGroups)}
          user={user}
          onCancel={onClose!}
          onSave={(selectedOperators) => handleSave(selectedOperators, onClose)}
        />
      ),
    });
  };

  const handleDelete = () => {
    if (!selectedChat?.id) return;
    showModal({
      children: (closeCallback) => (
        <DeleteModal chatId={selectedChat.id!} closeCallback={closeCallback!} />
      ),
    });
  };

  if (!selectedChat) return null;

  return (
    <aside className={clsx(utils.h100, utils.borderLeft, styles.chatsPanel)}>
      <InternalChatHeader className={utils.borderBottom}>
        <Text
          variant={TextVariant.SubheadMedium}
          title={selectedChat.participants?.map(({ id }) => id).join('\n')}
        >
          {getLocaleMessageById('app.chat.info')}
        </Text>
        <IconButton onClick={() => dispatch(toggleChatInfo())}>
          <IconClose />
        </IconButton>
      </InternalChatHeader>
      <div className={clsx(styles.content)}>
        <header className={clsx(utils.pX6, utils.pT6)}>
          <div className={clsx(utils.dFlex, utils.justifyContentCenter, utils.pB4)}>
            <Avatar
              alt={getAbbr(selectedChat.name || '')}
              colorize={selectedChat.id || ''}
              className={clsx(utils.mR4)}
              size={AvatarSize.Big}
              variant={AvatarVariant.Dark}
            />
            <div
              style={{ flexGrow: 2 }}
              className={clsx(utils.dFlex, utils.flexColumn, utils.justifyContentCenter)}
            >
              {canRename ? (
                <Input
                  value={selectedChat.name || ''}
                  className={utils.mB1}
                  onChange={({ value }) => {
                    console.log(value);
                    updateChatAsync({ name: value, id: selectedChat.id }).then((res) => {
                      dispatch(updateChat(res));
                    });
                  }}
                  withDebounce={600}
                />
              ) : (
                <Text variant={TextVariant.SubheadMedium}>{selectedChat?.name}</Text>
              )}
              <Text variant={TextVariant.CaptionMedium} color={Colors.OnyxBlack70}>
                {getChatInfo(selectedChat)}
              </Text>
            </div>
          </div>
        </header>
        <div
          className={clsx(
            utils.dFlex,
            utils.justifyContentBetween,
            utils.alignItemsCenter,
            utils.borderBottom,
            utils.pL6,
            utils.pR2,
          )}
        >
          <Text variant={TextVariant.BodyMedium}>
            {getLocaleMessageById('app.chatInfo.membersNumber', {
              count: selectedChat.participants?.length || 0,
              countGroup: getPluralGroup(selectedChat.participants?.length || 0),
            })}
          </Text>
          {canChangeMembers && (
            <Button
              className={clsx(utils.dFlex, utils.flexNoWrap, utils.gap1)}
              variant={ButtonVariant.Transparent}
              onClick={handleEdit}
            >
              <IconUsers />
              {getLocaleMessageById('app.button.changeMembers')}
            </Button>
          )}
        </div>
        <section className={clsx(styles.list, utils.scrollbar, utils.mT2)} style={{ flexGrow: 2 }}>
          {selectedChat.participants?.map((pp) => {
            const userStatus = allOperators.find((op) => op.id === pp.id)?.status;

            return (
              <InternalChatOperator
                key={pp.id}
                operator={pp}
                status={userStatus!}
                className={clsx(styles.item, utils.dFlex, utils.pY3, utils.pX6)}
              >
                {pp.id === selectedChat.ownerId && (
                  <Text variant={TextVariant.CaptionMedium} color={Colors.Grassios80}>
                    {getLocaleMessageById('app.chatInfo.creator')}
                  </Text>
                )}
              </InternalChatOperator>
            );
          })}
        </section>
        {canRemove && (
          <footer className={clsx(utils.pX6, utils.pY4)}>
            <Button
              size={ButtonSize.Big}
              variant={ButtonVariant.Secondary}
              className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter, utils.w100)}
              onClick={handleDelete}
            >
              <IconTrash />
              {getLocaleMessageById('app.button.deleteChat')}
            </Button>
          </footer>
        )}
      </div>
    </aside>
  );
};

export default InternalChatInfo;
