import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import styles from './styles.module.scss';

const IconStep: React.FC<HTMLAttributes<HTMLDivElement>> = ({ className, style = {} }) => {
  return (
    <div className={clsx(styles.stepIconWrapper, className)}>
      <div className={styles.stepIcon} style={style} role="img" aria-label="step icon" />
    </div>
  );
};

export default IconStep;
