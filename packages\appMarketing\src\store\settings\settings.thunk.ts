import { createAsyncThunk } from '@reduxjs/toolkit';

import {
  getChannelsAsync,
  getClientTableViewAsync,
  getOfferTypesAsync,
  getQueuesAsync,
  getResultsReasonsAsync,
} from '../../helpers/api';

export const getOfferTypes = createAsyncThunk('settings/getOfferTypes', async () => {
  return await getOfferTypesAsync();
});

export const getChannels = createAsyncThunk('settings/getChannels', async () => {
  return await getChannelsAsync();
});

export const getQueues = createAsyncThunk('settings/getQueues', async () => {
  return await getQueuesAsync();
});

export const getClientTableView = createAsyncThunk('settings/getClientTableView', async () => {
  return await getClientTableViewAsync();
});

export const getResultReasons = createAsyncThunk('settings/resultReasons', async () => {
  return await getResultsReasonsAsync();
});
