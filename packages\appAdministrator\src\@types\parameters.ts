import { IFrontKpiUnit } from './queue';

export interface IFrontBot {
  name: string;
  code: string;
}

export interface IFrontKpiThresholdValue {
  code: string;
  displayName: string;
  isInteger: boolean;
  minValue: number;
  defaultValue?: number;
  maxValue?: number;
}

export interface IFrontQueueKpiParameter {
  displayName: string;
  code: string;
  isInteger: boolean;
  alarmExceedsWarning: boolean;
  defaultAlarmValue: number | null;
  alarmMinValue: number | null;
  alarmMaxValue: number | null;
  defaultWarningValue: number | null;
  warningMinValue: number | null;
  warningMaxValue: number | null;
  units: IFrontKpiUnit[];
  isWarningAvailable: boolean;
  canExceed: boolean;
}

export enum IFrontAttributeModel {
  Request = 'Request',
  Client = 'Client',
}

export enum IFrontAttributeType {
  Request = 'Request',
  Client = 'Client',
}

export enum IFrontDataType {
  String = 'String',
  Number = 'Number',
  Date = 'Date',
}

export enum IFrontRightPartType {
  Text = 'Text',
  List = 'List',
  MiltiselectList = 'MiltiselectList',
}

export interface IFrontRoutingAttribute {
  id: number;
  displayName: string;
  model: IFrontAttributeModel;
  attributeType: IFrontAttributeType;
  code: string;
  dataType: IFrontDataType;
  rightPartType: IFrontRightPartType;
  options: {
    name: string;
    value: string;
  }[];
  comparisonRules: {
    name: string;
    code: string;
  }[];
}

export interface IFrontAutoHandler {
  name: string;
  code: string;
  isPvoo: boolean;
}
