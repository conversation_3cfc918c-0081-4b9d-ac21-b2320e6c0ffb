import {
  FrontStepType,
  IFrontScript,
  IFrontStep,
} from '@monorepo/dialog-scripts/src/@types/script';

type VariablesKV = Record<string, IFrontStep['variableName']>;

export const getAllVariablesFromScriptSteps = (steps: IFrontScript['steps']): VariablesKV => {
  const stepVariables: VariablesKV = {};
  const servicesVariables: VariablesKV = {};

  // Переменные из шагов (когда в результате выбора/ввода на шаге сетится переменная)
  steps
    .filter((step) => !!step.variable)
    .forEach((step) => {
      stepVariables[step.variable!] = step.variableName || step.variable;
    });

  // Переменные из сервисов (по результату выполнения запроса к внешнему сервису сетится переменная)
  steps
    .filter((step) => step.type === FrontStepType.Service && step.serviceOutputParameters?.length)
    .forEach((step) => {
      step.serviceOutputParameters?.forEach((parameter) => {
        if (!parameter.variableCode) return;
        servicesVariables[parameter.variableCode] =
          parameter.variableName || parameter.variableCode;
      });
    });

  return {
    ...stepVariables,
    ...servicesVariables,
  };
};
