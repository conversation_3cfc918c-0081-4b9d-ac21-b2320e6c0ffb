import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  Collapsible,
  FloatingTooltip,
  grids,
  IconButton,
  Loader,
  LoaderSize,
  MenuItem,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconArchive from '@product.front/icons/dist/icons17/MainStuff/IconArchive';
import IconDropDown from '@product.front/icons/dist/icons17/MainStuff/IconDropDown';
import IconDropUp from '@product.front/icons/dist/icons17/MainStuff/IconDropUp';
import IconInformation from '@product.front/icons/dist/icons17/MainStuff/IconInformation';
import IconPlay from '@product.front/icons/dist/icons17/MainStuff/IconPlay';
import IconSave from '@product.front/icons/dist/icons17/MainStuff/IconSave';
import IconShare from '@product.front/icons/dist/icons17/MainStuff/IconShare1';
import IconEyeOff from '@product.front/icons/dist/icons17/Sorting/IconEyeOff';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { AppConfigContext } from '../../../../../../context/appConfig';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { getDependentScriptNamesTooltip } from '../../../../../../helpers/scriptHelpers';
import { showChangeSriptStatusConfirmModal } from '../../../../../../helpers/showChangeSriptStatusConfirmModal/showChangeSriptStatusConfirmModal';
import { showDialogScriptPlayer } from '../../../../../../helpers/showDialogScriptPlayerModal';
import {
  useDialogScriptsAppDispatch,
  useDialogScriptsAppSelector,
} from '../../../../../../store/hooks';
import { updateScriptStep } from '../../../../../../store/oneScript/oneScript.slice';
import {
  addScript,
  archiveAndSaveScript,
  archiveScript,
  makeScriptUnavailable,
  makeUnavailableAndSaveScript,
  publishAndSaveScript,
  publishScript,
  updateScript,
} from '../../../../../../store/scripts/scripts.thunk';
import Status from '../../../../../Body/components/Status';
import { getStepInvalidReasons } from '../../../validators/stepValidator';
import BaseInfoForm from '../BaseInfoForm';

import styles from './styles.module.scss';

const Header = () => {
  const dispatch = useDialogScriptsAppDispatch();

  const { script, initialScript } = useDialogScriptsAppSelector((state) => state.oneScript);
  const { saving } = useDialogScriptsAppSelector((state) => state.scripts);

  const [isFormOpen, setIsFormOpen] = React.useState(!script?.id);
  const [isPublishing, setIsPublishing] = React.useState(false);
  const [isArchiving, setIsArchiving] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);
  const [isSettingNotAvailable, setSettingsUnavailable] = React.useState(false);
  const [pendingStatus, setPendingStatus] = React.useState<null | ScriptStatus>(null);

  const { canChangeAvailableStatus } = React.useContext(AppConfigContext);

  const formRef = React.useRef<HTMLFormElement>(null);

  const checkIsValid = React.useCallback(
    (opts = { isActiveScriptValidation: false }): boolean => {
      if (!formRef.current) {
        return false;
      }

      const isBaseInfoFormValid = formRef.current.checkValidity();

      if (!isBaseInfoFormValid) {
        setIsFormOpen(true);
        formRef.current.requestSubmit();
        return false;
      }

      const invalidSteps =
        script?.steps?.reduce<IFrontStep[]>((acc, step, index) => {
          const reasons = getStepInvalidReasons(
            step,
            index,
            script.steps,
            opts.isActiveScriptValidation,
          );
          if (Object.entries(reasons).length) {
            acc.push({ ...step, invalidReasons: reasons });
          }
          return acc;
        }, []) || [];

      if (invalidSteps.length) {
        invalidSteps.forEach((stepWithInvalidReasons) => {
          dispatch(updateScriptStep(stepWithInvalidReasons));
        });

        showConfirmModal({
          header: getLocaleMessageById('app.editor.errorStepsInvalid'),
          text: (
            <details
              className={clsx(utils.textLeft, utils.scrollbar, utils.overflowYAuto)}
              style={{ maxHeight: '50vh' }}
            >
              {invalidSteps.map((st) => (
                <MenuItem key={st.code}>
                  <div className={clsx(utils.w100, utils.textLeft)}>
                    <Text variant={TextVariant.BodySemibold}>{st.name || st.code}</Text>

                    <ul>
                      {Object.entries(st?.invalidReasons || {}).map(([k, v]) => {
                        const value = typeof v === 'object' ? JSON.stringify(v) : v;
                        return (
                          <Text
                            as="li"
                            style={{ display: 'block' }}
                            title={value}
                            ellipsis
                            noWrap
                            key={k}
                          >
                            {k} - {value}
                          </Text>
                        );
                      })}
                    </ul>
                  </div>
                </MenuItem>
              ))}
            </details>
          ),
          onConfirm: () => {},
          confirmText: getLocaleMessageById('app.common.OK'),
          canCancel: false,
        });

        return false;
      }

      return true;
    },
    [dispatch, script?.steps],
  );

  // сохранение
  React.useEffect(() => {
    if (!isSaving || !!pendingStatus) {
      return;
    }

    const isValidForSave = checkIsValid({
      isActiveScriptValidation: script?.status === ScriptStatus.Active,
    });

    if (isValidForSave) {
      dispatch(script?.id ? updateScript(script) : addScript(script!));
    }
    setIsSaving(false);
  }, [checkIsValid, dispatch, isSaving, pendingStatus, script]);

  // публикация
  React.useEffect(() => {
    if (!isPublishing || !script?.id || pendingStatus !== ScriptStatus.Active) {
      return;
    }
    const isValidForPublish = checkIsValid({
      isActiveScriptValidation: true,
    });

    const publishingFallback = () => {
      setIsPublishing(false);
    };

    if (isValidForPublish) {
      if (script.steps.some((step) => step.subscript?.status === ScriptStatus.Template)) {
        showConfirmModal({
          header: getLocaleMessageById('app.modals.subscriptProblem.header', {
            scriptName: script?.name,
            subscriptState: getLocaleMessageById('app.scriptStatus.draft'),
          }),
          text: getLocaleMessageById('app.modals.subscriptProblem.body'),
          onConfirm: () => {
            setIsPublishing(false);
          },
          canCancel: false,
        });
        setIsPublishing(false);
        return;
      }

      showChangeSriptStatusConfirmModal(
        script,
        () => {
          showConfirmModal({
            header: getLocaleMessageById('app.modals.publish.header', { scriptName: script?.name }),
            onConfirm: () => {
              if (!script?.id) return;

              if (JSON.stringify(initialScript) !== JSON.stringify(script)) {
                dispatch(publishAndSaveScript(script));
              } else {
                dispatch(publishScript(script.id));
              }
            },
            onCancel: publishingFallback,
          });
        },
        publishingFallback,
      );
    } else {
      publishingFallback();
    }
  }, [checkIsValid, dispatch, initialScript, isPublishing, pendingStatus, script]);

  // архивация
  React.useEffect(() => {
    if (!isArchiving || !script?.id || pendingStatus !== ScriptStatus.Archive) {
      return;
    }
    const isValidForArchiving = checkIsValid({
      isActiveScriptValidation: false,
    });

    const archivingFallback = () => {
      setIsArchiving(false);
    };

    if (isValidForArchiving) {
      showChangeSriptStatusConfirmModal(
        script,
        () => {
          showConfirmModal({
            header: getLocaleMessageById('app.modals.archive.header', { scriptName: script.name }),
            text: getLocaleMessageById('app.modals.archive.text'),
            onConfirm: () => {
              if (!script.id) return;

              if (JSON.stringify(initialScript) !== JSON.stringify(script)) {
                dispatch(archiveAndSaveScript(script));
              } else {
                dispatch(archiveScript(script.id));
              }
            },
            onCancel: archivingFallback,
          });
        },
        archivingFallback,
      );
    } else {
      archivingFallback();
    }
  }, [checkIsValid, dispatch, initialScript, isArchiving, pendingStatus, script]);

  // перевод в "не доступен"
  React.useEffect(() => {
    if (!isSettingNotAvailable || !script?.id || pendingStatus !== ScriptStatus.NotAvailable) {
      return;
    }
    const isValidForNotAvailable = checkIsValid({
      isActiveScriptValidation: false,
    });

    const notAvailableFallback = () => {
      setSettingsUnavailable(false);
    };

    if (isValidForNotAvailable) {
      showChangeSriptStatusConfirmModal(
        script,
        () => {
          showConfirmModal({
            header: getLocaleMessageById('app.modals.notAvailableHeader', {
              scriptName: script.name,
            }),
            text: getLocaleMessageById('app.modals.notAvailableText'),
            onConfirm: async () => {
              if (!script.id) return;

              if (JSON.stringify(initialScript) !== JSON.stringify(script)) {
                dispatch(makeUnavailableAndSaveScript(script));
              } else {
                dispatch(makeScriptUnavailable(script.id));
              }
            },
            onCancel: notAvailableFallback,
          });
        },
        notAvailableFallback,
      );
    } else {
      notAvailableFallback();
    }
  }, [checkIsValid, dispatch, initialScript, isSettingNotAvailable, pendingStatus, script]);

  const handlePlay = () => {
    if (!script?.id) return;

    showDialogScriptPlayer(script.id, script.name);
  };

  const handleSave = () => {
    setPendingStatus(null);
    setIsSaving(true);
  };

  const handlePublish = () => {
    setIsPublishing(true);
    setPendingStatus(ScriptStatus.Active);
  };

  const handleNotAvailable = () => {
    setSettingsUnavailable(true);
    setPendingStatus(ScriptStatus.NotAvailable);
  };

  const handleArchive = () => {
    setIsArchiving(true);
    setPendingStatus(ScriptStatus.Archive);
  };

  const readonly = script?.status === ScriptStatus.Archive;

  const infoScriptTooltip = getDependentScriptNamesTooltip(script?.dependentScripts ?? []);

  return (
    <header className={clsx(utils.borderBottom, utils.p3, styles.Header)}>
      <div className={clsx(grids.row)}>
        <div
          className={clsx(
            grids.col6,
            utils.dFlex,
            utils.justifyContentStart,
            utils.alignItemsCenter,
            utils.gap2,
          )}
        >
          <Status status={script?.status ?? ScriptStatus.Template} />
          <div title={infoScriptTooltip} className={utils.dFlex}>
            <IconInformation />
          </div>
        </div>
        <div
          className={clsx(
            grids.col5,
            utils.dFlex,
            utils.justifyContentEnd,
            utils.alignItemsCenter,
            utils.gap2,
          )}
          onClick={(e) => e.stopPropagation()}
          onKeyDown={() => {}}
          role="button"
          tabIndex={-1}
        >
          <FloatingTooltip tooltip={getLocaleMessageById('app.common.save')}>
            <IconButton onClick={handleSave} disabled={readonly || saving}>
              {saving ? <Loader size={LoaderSize.Small} /> : <IconSave />}
            </IconButton>
          </FloatingTooltip>
          <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.play')}>
            <IconButton disabled={!script?.id || readonly} onClick={handlePlay}>
              <IconPlay />
            </IconButton>
          </FloatingTooltip>
          <FloatingTooltip tooltip={getLocaleMessageById('app.script.publish')}>
            <IconButton
              disabled={
                isPublishing ||
                !script?.id ||
                readonly ||
                ![ScriptStatus.Template, ScriptStatus.NotAvailable].includes(script?.status) ||
                !script.steps?.length
              }
              onClick={handlePublish}
            >
              {isPublishing ? <Loader size={LoaderSize.Small} /> : <IconShare />}
            </IconButton>
          </FloatingTooltip>
          {canChangeAvailableStatus && (
            <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.notAvailableAction')}>
              <IconButton
                disabled={
                  isSettingNotAvailable ||
                  !script?.id ||
                  readonly ||
                  script?.status !== ScriptStatus.Active
                }
                onClick={handleNotAvailable}
              >
                {isSettingNotAvailable ? <Loader size={LoaderSize.Small} /> : <IconEyeOff />}
              </IconButton>
            </FloatingTooltip>
          )}
          <FloatingTooltip tooltip={getLocaleMessageById('app.tooltip.archive')}>
            <IconButton
              disabled={
                isArchiving || !script?.id || readonly || script?.status === ScriptStatus.Archive
              }
              onClick={handleArchive}
            >
              {isArchiving ? <Loader size={LoaderSize.Small} /> : <IconArchive />}
            </IconButton>
          </FloatingTooltip>
        </div>
        <div className={clsx(grids.col1, utils.textRight)}>
          <Button variant={ButtonVariant.Transparent} onClick={() => setIsFormOpen((c) => !c)}>
            {isFormOpen
              ? getLocaleMessageById('app.common.collapse')
              : getLocaleMessageById('app.common.expand')}
            &nbsp;
            {isFormOpen ? <IconDropUp /> : <IconDropDown />}
          </Button>
        </div>
      </div>
      <Collapsible open={isFormOpen} onToggle={setIsFormOpen} activatorComponent={<div />}>
        <div className={utils.pT4}>
          <BaseInfoForm
            ref={formRef}
            requiredForActive={[script?.status, pendingStatus].includes(ScriptStatus.Active)}
          />
        </div>
      </Collapsible>
    </header>
  );
};
export default Header;
