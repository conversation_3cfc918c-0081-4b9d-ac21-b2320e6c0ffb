import { Guid } from 'guid-typescript';

import { SignalRClientBase } from '@monorepo/common/src/platform/utils';

export type WorkSession = {
  startedWorkSessionId: string | null;
  clientType: string;
  description: string;
  userRoleId: string;
  serviceAreaId: string;
  workplaceId: string;
  utcOffset: number;
};

export class WorkSessionClientOverSignalR extends SignalRClientBase {
  constructor(baseUrl: string, reconnectionDelayInSeconds: number) {
    super(baseUrl + '/hubs/communication', reconnectionDelayInSeconds);

    this.startWorkSession = this.startWorkSession.bind(this);
    this.endWorkSession = this.endWorkSession.bind(this);
  }

  //#region Hub methods (client handlers and server calls)

  public async startWorkSession(
    operatorId: Guid,
    workSession: WorkSession,
  ): Promise<Guid | undefined> {
    return await this.hubConnection?.invoke<Guid>('startWorkSession', operatorId, workSession);
  }
  public async endWorkSession(): Promise<void> {
    await this.hubConnection?.invoke('endWorkSession');
  }
  //#endregion
}
