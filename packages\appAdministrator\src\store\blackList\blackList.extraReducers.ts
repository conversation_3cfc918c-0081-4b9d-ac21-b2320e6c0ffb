import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IBlackListStore } from './blackList.slice';
import { getAllBlackListAddresses } from './blackList.thunk';

const getAllOperatorsReducers = (builder: ActionReducerMapBuilder<IBlackListStore>) =>
  builder
    .addCase(getAllBlackListAddresses.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllBlackListAddresses.fulfilled, (state, action) => {
      state.loading = false;
      state.blackListAddresses = action.payload;
    })
    .addCase(getAllBlackListAddresses.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<IBlackListStore>) => {
  getAllOperatorsReducers(builder);

  return builder;
};
