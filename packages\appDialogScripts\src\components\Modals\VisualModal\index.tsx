import React from 'react';

import clsx from 'clsx';

import {
  colors,
  helpers,
  IconButton,
  Loader,
  Text,
  utils,
  ZIndexElementType,
} from '@product.front/ui-kit';

import IconClose from '@product.front/icons/dist/icons17/MainStuff/IconClose';

import ErrorBoundaryContainer from '@monorepo/common/src/common/components/Errors/ErrorBoundary/Container';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { AppConfigContext } from '../../../context/appConfig';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { initStepNames } from '../../../helpers/stepListHelper';
import { useDialogScriptsAppSelector } from '../../../store/hooks';

import styles from './styles.module.scss';

const VisualEditor = React.lazy<React.FC>(
  () => import(/* webpackChunkName: "scripts-visual-editor" */ './components/VisualEditor'),
); // Lazy-loaded

interface IVisualModalProps {
  onClose: () => void;
}

const VisualModal = ({ onClose }: IVisualModalProps) => {
  const { script, initialScript } = useDialogScriptsAppSelector((state) => state.oneScript);

  const [zIndex] = React.useState(helpers.addZIndexElement(ZIndexElementType.Modal));

  const { editorTitlePlaceholder } = React.useContext(AppConfigContext);

  const handleClose = () => {
    if (JSON.stringify(script) !== JSON.stringify(initialScript)) {
      showConfirmModal({
        header: getLocaleMessageById('app.modals.closeScript.header'),
        text: getLocaleMessageById('app.modals.closeScript.text'),
        onConfirm: onClose,
      });
    } else {
      onClose();
    }
  };

  React.useEffect(() => {
    initStepNames();
  }, [initialScript]);

  React.useEffect(() => () => helpers.removeZIndexElement(ZIndexElementType.Modal), []);

  return (
    <section className={styles.modal} style={{ zIndex }}>
      <ErrorBoundaryContainer canRetry>
        <div className={clsx(utils.dFlex, utils.flexColumn, styles.modalBody)}>
          <header
            className={clsx(
              colors.bgOnyxBlack90,
              colors.colorOnyxBlack40,
              utils.p4,
              utils.dFlex,
              utils.justifyContentBetween,
              utils.alignItemsCenter,
            )}
          >
            <Text ellipsis noWrap title={script?.name || ''} style={{ color: 'inherit' }}>
              {script?.name || editorTitlePlaceholder}
            </Text>
            <div className={clsx(utils.dFlex, utils.gap4)}>
              <IconButton onClick={handleClose} variant="Dark">
                <IconClose />
              </IconButton>
            </div>
          </header>
          <section className={clsx(utils.flexGrow1, utils.scrollbar, styles.content)}>
            <React.Suspense
              fallback={
                <div
                  className={clsx(
                    utils.dFlex,
                    utils.alignItemsCenter,
                    utils.justifyContentCenter,
                    utils.h100,
                  )}
                >
                  <Loader />
                </div>
              }
            >
              <VisualEditor key={JSON.stringify(initialScript)} />
            </React.Suspense>
          </section>
        </div>
      </ErrorBoundaryContainer>
    </section>
  );
};

export default VisualModal;
