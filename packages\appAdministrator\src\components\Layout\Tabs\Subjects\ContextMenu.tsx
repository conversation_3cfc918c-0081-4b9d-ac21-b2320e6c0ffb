import React from 'react';

import {
  AttentionIconType,
  FloatingDropdown,
  IconButton,
  Menu,
  MenuItem,
  showModal,
} from '@product.front/ui-kit';

import IconMore from '@product.front/icons/dist/icons17/Sorting/IconMore';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IFrontRequestSubject } from '../../../../@types/requestSubject';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import {
  createSubject,
  deleteSubject,
  updateSubject,
} from '../../../../store/subjects/subjects.thunk';

import SubjectForm from './SubjectForm';

import styles from './styles.module.scss';

export const deleteSubjectAction = (onConfirm: () => void) =>
  showConfirmModal({
    header: getLocaleMessageById('subject.action.delete.header'),
    type: AttentionIconType.Attention,
    text: getLocaleMessageById('subject.action.delete.text'),
    onConfirm: onConfirm,
  });

export const createUpdateSubjectAction = (
  subject: IFrontRequestSubject | null,
  onConfirm: (subject: IFrontRequestSubject) => Promise<void>,
  parentId: number | null,
  subjects: IFrontRequestSubject[],
) => {
  const subjectToEdit = {
    id: subject?.id ?? 0,
    name: subject?.name ?? '',
    code: subject?.code ?? '',
    description: subject?.description ?? '',
    parentId: subject?.parentId ?? parentId,
    knowledgeBaseContext: subject?.knowledgeBaseContext ?? '',
    chatBotContext: subject?.chatBotContext ?? '',
    dialogScriptCode: subject?.dialogScriptCode ?? '',
  } satisfies IFrontRequestSubject;

  showModal({
    header: getLocaleMessageById(
      subject ? 'subject.action.update.header' : 'subject.action.create.header',
      {
        subjectName: subjectToEdit.name,
      },
    ),
    children: (close) => (
      <SubjectForm
        subject={subjectToEdit}
        subjects={subjects}
        onConfirm={onConfirm}
        onClose={close}
      />
    ),
    flushBody: true,
    canClose: false,
  });
};

interface IContextMenuProps {
  subject: IFrontRequestSubject;
}

const ContextMenu = ({ subject }: IContextMenuProps) => {
  const dispatch = useAdministratorAppDispatch();

  const { subjects } = useAdministratorAppSelector((store) => store.subjects);

  return (
    <FloatingDropdown
      className={styles.contextMenu}
      menu={
        <Menu>
          <MenuItem
            onClick={() =>
              createUpdateSubjectAction(
                subject,
                async (updatedSubject) => await dispatch(updateSubject(updatedSubject)).unwrap(),
                subject.parentId,
                Object.values(subjects),
              )
            }
          >
            {getLocaleMessageById('subject.edit')}
          </MenuItem>
          <MenuItem
            onClick={() =>
              createUpdateSubjectAction(
                null,
                async (createdSubject) => dispatch(createSubject(createdSubject)).unwrap(),
                subject.id,
                Object.values(subjects),
              )
            }
          >
            {getLocaleMessageById('subject.addChild')}
          </MenuItem>
          <MenuItem onClick={() => deleteSubjectAction(() => dispatch(deleteSubject(subject.id)))}>
            {getLocaleMessageById('subject.delete')}
          </MenuItem>
        </Menu>
      }
      onClick={(event) => event.stopPropagation()}
      style={{ marginLeft: 'auto' }}
    >
      <IconButton>
        <IconMore />
      </IconButton>
    </FloatingDropdown>
  );
};

export default ContextMenu;
