import React from 'react';

import clsx from 'clsx';

import {
  Avatar,
  AvatarVariant,
  Badge,
  BadgeType,
  Colors,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import { getPluralGroup } from '@monorepo/common/src/helpers/localeHelper';

import {
  MessageType,
  SystemMessageType,
  SystemPayload,
} from '../../../../../../@types/generated/signalr';
import { IChat } from '../../../../../../@types/signalRTypes';
import { getAbbr } from '../../../../../../helpers/abbrHelper';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';

import styles from './styles.module.scss';

function getShortMsg(text: string): React.ReactNode {
  const len = 23;
  return text?.length > len ? text.substr(0, len) + '…' : text;
}

const getShortSystemMsg = (systemMessage: SystemPayload) => {
  const parts = [];
  systemMessage.lastName && parts.push(systemMessage.lastName);
  systemMessage.firstName && parts.push(systemMessage.firstName.substr(0, 1) + '.');
  systemMessage.middleName && parts.push(systemMessage.middleName.substr(0, 1) + '.');

  systemMessage.type === SystemMessageType.UserAdded &&
    parts.push(getLocaleMessageById('app.systemMessage.userAdded'));
  systemMessage.type === SystemMessageType.UserRemoved &&
    parts.push(getLocaleMessageById('app.systemMessage.userRemoved'));

  return parts.join('\u00a0'); //&nbsp;
};

const ChatListItem = ({
  chat,
  className,
  onClick,
  active,
}: {
  chat: IChat;
  className?: string;
  onClick: () => void;
  active: boolean;
}) => {
  const count = chat?.unreadedMessagesCount || 0;

  const countTitle = getLocaleMessageById('app.chatList.newMessagesNumber', {
    count,
    countGroup: getPluralGroup(count),
  });

  const membersCount = chat?.participants?.length || 0;

  const membersCountTitle = getLocaleMessageById('app.chatList.membersNumber', {
    count: membersCount,
    countGroup: getPluralGroup(membersCount),
  });

  return (
    <div
      className={clsx(
        utils.borderBottom,
        utils.pY3,
        utils.pX5,
        styles.chatListItem,
        active && styles.chatListItemActive,
        className,
      )}
      role="presentation"
      onClick={onClick}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(styles.chatItemAvatar, utils.mR4)}>
          <Avatar
            alt={getAbbr(chat?.name || '-')}
            colorize={chat.id}
            variant={AvatarVariant.Dark}
          />
          {count > 0 && (
            <Badge
              rounded
              type={BadgeType.Warning}
              className={styles.chatItemAvatarBadge}
              maxLength={2}
              title={countTitle}
            >
              {count}
            </Badge>
          )}
        </div>
        <div className={styles.chatItemBody}>
          <header
            className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}
          >
            <Text
              as="div"
              variant={TextVariant.BodySemibold}
              style={{ minWidth: 0 }}
              ellipsis
              noWrap
              title={chat?.name || '-'}
            >
              {chat.name}
            </Text>
            <Text variant={TextVariant.SmallMedium} noWrap>
              {membersCountTitle}
            </Text>
          </header>
          {chat.lastMessage && (
            <>
              <div className={clsx(utils.dFlex, utils.justifyContentBetween)}>
                <Text
                  as="div"
                  variant={TextVariant.CaptionMedium}
                  ellipsis
                  noWrap
                  className={utils.mR2}
                >
                  {chat.lastMessage.author && (
                    <>
                      {chat.lastMessage.author.lastName}{' '}
                      {chat.lastMessage.author.firstName?.slice(0, 1)}.
                    </>
                  )}
                  {!chat.lastMessage.author && getLocaleMessageById('app.author.system')}
                </Text>
                <Text variant={TextVariant.CaptionMedium} noWrap color={Colors.OnyxBlack70}>
                  {chat.lastMessage &&
                    new Date(Date.parse(chat.lastMessage.createDate!))
                      .toLocaleString()
                      .replace(',', getLocaleMessageById('app.time.at'))}
                </Text>
              </div>
              <Text
                as="div"
                noWrap
                ellipsis
                variant={TextVariant.CaptionMedium}
                color={Colors.OnyxBlack70}
              >
                {chat.lastMessage.type === MessageType.System && chat.lastMessage.systemPayload
                  ? getShortSystemMsg(chat.lastMessage.systemPayload)
                  : getShortMsg(chat?.lastMessage?.text || '')}
              </Text>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatListItem;
