import React, { FormEvent } from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  Input,
  showModal,
  utils,
  helpers,
  Select,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import { FolderContentType, FrontFileOrFolder, IFrontFile } from '../../../../../../@types/files';
import { needConfirmWhenCompareFalse } from '../../../../../../helpers/confirmSave.helper';
import { getPathsForSelect, normalizePathForApi } from '../../../../../../helpers/files';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { addTrustedFileAsync, updateTrustedFileAsync } from '../../../../../../services/files';

interface IFileAddOrEditModalProps {
  file?: IFrontFile;
  contextFolderPath: string;
  filesAndFolders: FrontFileOrFolder[];
  onSuccess(newFolder: IFrontFile): void;
  onClose?: () => void;
}

interface FormElements extends HTMLFormControlsCollection {
  name: HTMLInputElement;
  path: HTMLInputElement;
  file: HTMLInputElement;
}

const FileAddOrEditModal: React.FC<IFileAddOrEditModalProps> = ({
  file,
  onClose,
  onSuccess,
  contextFolderPath,
  filesAndFolders,
}) => {
  const [isSaving, setIsSaving] = React.useState(false);
  const [err, setErr] = React.useState<Error>();
  const [name, setName] = React.useState(file?.name ?? '');

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const elements = e.currentTarget.elements as FormElements;

    try {
      setErr(undefined);
      setIsSaving(true);

      const path = elements.path.value.trim();

      if (file) {
        const ext = helpers.getExtensionByFileName(file.name);
        const nameWithExt = [name.trim(), ext].join('.');

        const newPath = [path, name].join('/');

        await updateTrustedFileAsync(normalizePathForApi(file.path), normalizePathForApi(newPath));

        onSuccess({
          ...file,
          name: nameWithExt,
          path: newPath,
        });
      } else {
        if (!elements.file.files?.length) throw new Error('Has no file');
        const fileFromInput = elements.file.files[0];
        const ext = helpers.getExtensionByFileName(fileFromInput.name);
        const newPath = [path, name].join('/');
        const nameWithExt = [name.trim(), ext].join('.');
        await addTrustedFileAsync(fileFromInput, nameWithExt, normalizePathForApi(path));

        onSuccess({
          name: nameWithExt,
          path: newPath,
          parent: path,
          type: FolderContentType.File,
          extension: helpers.getExtensionByFileName(name) ?? 'unknown',
        });
      }

      onClose?.();
    } catch (error) {
      setErr(error);
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = (e: FormEvent<HTMLFormElement>) => {
    const elements = e.currentTarget.elements as FormElements;
    e.preventDefault();
    const hasNoChanges =
      !file ||
      (file.name === elements.name.value &&
        normalizePathForApi(file.parent) === elements.path.value);

    needConfirmWhenCompareFalse(hasNoChanges, onClose);
  };

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn)}>
      <form
        id="FileEditorForm"
        className={clsx(utils.dFlex, utils.flexColumn, utils.gap5, utils.p6)}
        onSubmit={handleSubmit}
        onReset={handleReset}
      >
        {!file && (
          <Input
            name="file"
            type="file"
            autoFocus
            placeholder={getLocaleMessageById('files.modal.addFile.file')}
            disabled={isSaving}
            required
            onChange={(_, e) => {
              if (!e?.target.files) return;
              const ext = helpers.getExtensionByFileName(e.target.files[0].name);
              setName(e.target.files[0].name.replace(`.${ext}`, ''));
            }}
          />
        )}
        <Input
          name="name"
          value={name}
          onChange={({ value }) => {
            setName(value ?? '');
          }}
          label={getLocaleMessageById('files.modal.addFile.name')}
          disabled={isSaving}
          required
        />
        <Select
          data={getPathsForSelect(filesAndFolders)}
          name="path"
          label={getLocaleMessageById('files.modal.addFolder.folder')}
          value={normalizePathForApi(file?.parent ?? contextFolderPath ?? '/')}
          disabled={isSaving}
          required
        />
      </form>
      <footer className={clsx(utils.borderTop, utils.pX6, utils.pY4, utils.dFlex, utils.gap2)}>
        {err && (
          <AlertError
            header={
              file
                ? getLocaleMessageById('files.modal.editFile.error')
                : getLocaleMessageById('files.modal.addFile.error')
            }
            error={err}
          />
        )}
        <Button
          className={utils.mLauto}
          variant={ButtonVariant.Secondary}
          type="reset"
          form="FileEditorForm"
          disabled={isSaving}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button type="submit" form="FileEditorForm" disabled={isSaving}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </footer>
    </div>
  );
};

export default FileAddOrEditModal;

export const addOrEditFileModal = ({
  contextFolderPath,
  file,
  onSuccess,
  onClose,
  filesAndFolders,
}: IFileAddOrEditModalProps) => {
  showModal({
    style: { width: 480 },
    header: getLocaleMessageById(file ? 'files.modal.editFile.title' : 'files.modal.addFile.title'),
    children: (closeModal) => (
      <FileAddOrEditModal
        filesAndFolders={filesAndFolders}
        file={file}
        contextFolderPath={contextFolderPath}
        onSuccess={onSuccess}
        onClose={closeModal}
      />
    ),
    flushBody: true,
    canClose: false,
    onClose: onClose,
  });
};
