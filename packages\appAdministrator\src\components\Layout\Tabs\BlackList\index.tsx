import React from 'react';

import { Checkbox, showModal, Table, utils } from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import InfoMessage from '@monorepo/common/src/components/Table/InfoMessage';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IFrontBlackListAddress } from '../../../../@types/blackList';
import { IAdmTabComponent } from '../../../../@types/components';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { toggleIncludeDeleted } from '../../../../store/blackList/blackList.slice';
import {
  deleteBlackListAddresses,
  getAllBlackListAddresses,
} from '../../../../store/blackList/blackList.thunk';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import BlackListEditor from './BlackListEditor';
import columns from './columns';

const BlackListTab: React.FC<IAdmTabComponent> = ({ name }) => {
  const dispatch = useAdministratorAppDispatch();

  const { blackListAddresses, loading, error } = useAdministratorAppSelector(
    (store) => store.blackList,
  );

  const [selectedAddress, setSelectedAddress] = React.useState<IFrontBlackListAddress | null>(null);
  const [isEditOpening, setIsEditOpening] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const openBlackListAddressEditor = async (blackListAddress: IFrontBlackListAddress | null) => {
    showModal({
      header: getLocaleMessageById(
        blackListAddress ? 'blackList.modal.header.edit' : 'blackList.modal.header.create',
      ),
      children: (onClose) => (
        <BlackListEditor
          blackListAddress={blackListAddress}
          onSubmit={() => dispatch(getAllBlackListAddresses())}
          onClose={onClose}
        />
      ),
      flushBody: true,
      canClose: false,
    });
  };

  const updateBlackListAddressesList = React.useCallback(
    () => dispatch(getAllBlackListAddresses()),
    [dispatch],
  );

  React.useEffect(() => {
    updateBlackListAddressesList();
  }, [updateBlackListAddressesList]);

  React.useEffect(() => {
    setSelectedAddress(null);
  }, [blackListAddresses]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <Checkbox
          label={getLocaleMessageById('blackList.showDeleted')}
          className={utils.mR2}
          onChange={() => {
            dispatch(toggleIncludeDeleted());
            updateBlackListAddressesList();
          }}
        />
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('blackList.button.refresh')}
          onClick={updateBlackListAddressesList}
        >
          <IconRefresh />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          loading={isDeleting}
          tooltip={getLocaleMessageById('blackList.button.delete')}
          disabled={!selectedAddress || !!selectedAddress.dateDeleted || isDeleting}
          onClick={async () => {
            if (!selectedAddress) return;
            setIsDeleting(true);

            showConfirmModal({
              header: getLocaleMessageById('blackList.delete.confirm', {
                address: selectedAddress.address,
                date: new Date(selectedAddress.dueDate).toLocaleString(),
              }),
              onConfirm: async () => {
                await dispatch(deleteBlackListAddresses(selectedAddress.id)).unwrap();
                setIsDeleting(false);
              },
              onCancel: () => setIsDeleting(false),
            });
          }}
        >
          <IconTrash />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('blackList.button.edit')}
          loading={isEditOpening}
          onClick={async () => {
            setIsEditOpening(true);
            await openBlackListAddressEditor(selectedAddress);
            setIsEditOpening(false);
          }}
          disabled={!selectedAddress || !!selectedAddress.dateDeleted}
        >
          <IconEdit />
        </AdmToolbarIconButton>
        <AdmToolbarCtaButton onClick={() => openBlackListAddressEditor(null)}>
          {getLocaleMessageById('blackList.button.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody loading={loading}>
        <Table<IFrontBlackListAddress>
          data={blackListAddresses.map((row) => ({
            ...row,
            meta: {
              active: row.id === selectedAddress?.id,
            },
          }))}
          columns={columns}
          onRowClick={(row) => setSelectedAddress(row.data)}
          rendererAfter={() => (
            <>
              {!loading && !error && blackListAddresses.length === 0 && (
                <InfoMessage header={getLocaleMessageById('blackList.empty')} />
              )}
              {error && <JumbotronError error={error} sticky />}
            </>
          )}
          cellProps={(cell) => ({
            style: {
              color: cell.row.original.dateDeleted
                ? 'var(--palette-onyxBlack-60)'
                : 'var(--palette-onyxBlack-100)',
            },
          })}
        />
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default BlackListTab;
