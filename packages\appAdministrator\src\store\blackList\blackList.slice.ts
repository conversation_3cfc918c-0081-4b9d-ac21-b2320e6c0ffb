import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontBlackListAddress } from '../../@types/blackList';

import extraReducers from './blackList.extraReducers';

export interface IBlackListStore {
  loading: boolean;
  error?: SerializedError;
  blackListAddresses: IFrontBlackListAddress[];
  includeDeleted: boolean;
}

const initialState: IBlackListStore = {
  loading: false,
  blackListAddresses: [],
  includeDeleted: false,
};

const blackListSlice = createSlice({
  name: 'blackList',
  initialState,
  reducers: {
    toggleIncludeDeleted: (state) => {
      state.includeDeleted = !state.includeDeleted;
    },
  },
  extraReducers,
});
export const { toggleIncludeDeleted } = blackListSlice.actions;
export default blackListSlice.reducer;
