import React from 'react';

import styles from './styles.module.scss';

interface IQuickFilterButtonProps {
  text: string;
  isActive: boolean;
  onClick: () => void;
}

const QuickFilterButton = ({ text, isActive, onClick }: IQuickFilterButtonProps) => {
  return (
    <button className={styles.quickFilter} onClick={onClick} data-active={isActive}>
      {text}
    </button>
  );
};

export default QuickFilterButton;
