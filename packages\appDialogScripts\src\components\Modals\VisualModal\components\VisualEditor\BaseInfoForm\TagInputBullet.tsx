import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  EditableText,
  FloatingTooltip,
  FloatingTooltipVariant,
  Tag,
  TagColor,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';

import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';

import styles from './styles.module.scss';

interface ITagInputBulletProps {
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
  maxCount?: number;
}

const tagColor = TagColor.MoodBlue;

const TagInputBullet = ({ value, disabled, maxCount = 9999, onChange }: ITagInputBulletProps) => {
  const [isAddMode, setIsAddMode] = React.useState(false);
  const [newTag, setNewTag] = React.useState('');

  const inputRef = React.useRef<HTMLInputElement>(null);

  const tagsToDisplay = React.useMemo(
    () => (value.length > maxCount ? value.slice(0, maxCount) : value),
    [value, maxCount],
  );

  const hiddenTags = React.useMemo(
    () => (value.length > maxCount ? value.slice(maxCount) : []),
    [value, maxCount],
  );

  const addTag = () => {
    if (newTag && !value.includes(newTag)) {
      onChange([...value, newTag]);
    }

    setNewTag('');
  };

  const removeTag = (tag: string) => {
    if (!tag) return;

    onChange([...value].filter((t) => t !== tag));
  };

  const renderTag = (tag: string, index: number, className?: string) => {
    return (
      <Tag
        key={`${tag}-${index}`}
        className={className}
        style={{ maxWidth: '200px' }}
        canClose={!disabled}
        color={tagColor}
        onClose={() => removeTag(tag)}
      >
        {tag}
      </Tag>
    );
  };

  const handleInputKeyPress = (e: React.KeyboardEvent) => {
    if (!['Enter', ' '].includes(e.key)) return;

    e.preventDefault();
    addTag();
  };

  return (
    <div
      className={clsx(
        utils.w100,
        utils.dFlex,
        utils.alignItemsCenter,
        utils.flexWrap,
        styles.tagContainer,
      )}
      style={{ gap: '8px 4px' }}
    >
      {tagsToDisplay.map((tag, index) => renderTag(tag, index, styles.tag))}
      {hiddenTags.length > 0 && (
        <FloatingTooltip
          tooltip={
            <div className={clsx(utils.dFlex, utils.gap1, utils.flexWrap)}>
              {hiddenTags.map((tag, index) => renderTag(tag, index))}
            </div>
          }
          variant={FloatingTooltipVariant.Light}
          hideDuration={200}
        >
          <Tag className={styles.tag} color={tagColor}>
            +{hiddenTags.length}
          </Tag>
        </FloatingTooltip>
      )}
      <EditableText
        wrapperClassName={clsx(utils.dFlex, utils.alignItemsCenter, utils.flexShrink0)}
        style={{ width: isAddMode ? '150px' : '0', transition: 'width 0.2s ease-in-out' }}
        ref={inputRef}
        placeholder=""
        value={newTag}
        onChange={({ value: inputValue }) => setNewTag(inputValue ?? '')}
        onKeyPress={handleInputKeyPress}
        onBlur={() => {
          setIsAddMode(false);
          addTag();
        }}
      />
      <Button
        className={clsx(
          utils.dFlex,
          utils.alignItemsCenter,
          utils.flexShrink0,
          utils.gap2,
          utils.p0,
        )}
        variant={ButtonVariant.Transparent}
        disabled={disabled}
        onClick={() => {
          setIsAddMode(true);
          inputRef.current?.focus();
        }}
      >
        <IconAdd />
        {getLocaleMessageById('app.modals.forms.addTag')}
      </Button>
    </div>
  );
};

export default TagInputBullet;
