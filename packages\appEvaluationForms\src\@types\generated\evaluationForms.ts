/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

type UtilRequiredKeys<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

export interface AddUpdateAutomaticParameterDto {
  /**
   * Код сервиса автоматизации, который будет производить расчет
   * @minLength 1
   */
  serviceCode: string;
  /**
   * Отображаемого название параметра (сервиса)
   * @minLength 1
   */
  caption: string;
  /** Варианты типов сравнения параметра с оценкой */
  conditionType: ConditionType;
}

/** Состояние поддтеврждения оценки оператора */
export enum AgreementStatus {
  Agree = "Agree",
  Disagree = "Disagree",
}

/** Список полей автоматического критерия */
export type AutomaticCriterionDto = UtilRequiredKeys<CriterionDto, "kind"> & {
  /** Набор оценок для автоматического критерия */
  evaluations: AutomaticCriterionEvaluationDto[];
  /** Поля параметра на основе которого будет произведен автоматический расчет оценки */
  parameter: AutomaticParameterDto;
  /** @default "AutomaticCriterionDto" */
  kind: string;
};

/** Модель, содержащая поля автоматической оценки */
export interface AutomaticCriterionEvaluationDto {
  /**
   * Число на основе которого может быть рассчитана оценка
   * @format float
   */
  conditionValue: number;
  /**
   * ID оценки
   * @format int64
   */
  id: number;
  /**
   * Название
   * @maxLength 61
   */
  caption?: string | null;
  /**
   * Оценка
   * @format int32
   * @min 1
   * @max 5
   */
  score: number;
}

/** Поля параметра на основе которого будет произведен автоматический расчет оценки */
export interface AutomaticParameterDto {
  /**
   * Идентификатор параметра для сервиса автоматизации, который будет производить расчет
   * @format int64
   */
  id: number;
  /**
   * Код сервиса автоматизации, который будет производить расчет
   * @minLength 1
   */
  serviceCode: string;
  /**
   * Отображаемого название параметра (сервиса)
   * @minLength 1
   */
  caption: string;
  /** Варианты типов сравнения параметра с оценкой */
  conditionType: ConditionType;
}

export interface BffForm {
  /** @format int64 */
  runId?: number;
  title?: string | null;
  code?: string | null;
  operatorFullName?: string | null;
  /** @format int64 */
  requestId?: number | null;
  /** @format float */
  score?: number;
  createdBy?: string | null;
  /** @format date-time */
  createdAt?: string | null;
  lastSavedBy?: string | null;
  /** @format date-time */
  lastSavedAt?: string | null;
  operatorLogin?: string | null;
  /** @format date-time */
  viewedAt?: string | null;
  /** Состояние поддтеврждения оценки оператора */
  agreementStatus?: AgreementStatus;
  /** @format date-time */
  agreementAt?: string | null;
}

/** Правило по которому будет рассчитываться итоговая оценка по форме оценки */
export enum CalculationRule {
  Average = "Average",
  WeightedAverage = "WeightedAverage",
}

/** Варианты типов сравнения параметра с оценкой */
export enum ConditionType {
  LessIsBetter = "LessIsBetter",
  MoreIsBetter = "MoreIsBetter",
}

/** Список полей для создания автоматического критерия */
export type CreateAutomaticCriterionDto = UtilRequiredKeys<CreateCriterionDto, "kind"> & {
  /** Набор оценок для автоматического критерия */
  evaluations: CreateAutomaticCriterionEvaluationDto[];
  /**
   * Идентификатор параметра на основе которого будет произведен автоматический расчет оценки
   * @format int64
   */
  parameterId: number;
  /** @default "CreateAutomaticCriterionDto" */
  kind: string;
};

/** Модель, содержащая поля создания автоматической оценки */
export interface CreateAutomaticCriterionEvaluationDto {
  /**
   * Число на основе которого может быть рассчитана оценка
   * @format float
   */
  conditionValue: number;
  /**
   * Название
   * @maxLength 61
   */
  caption?: string | null;
  /**
   * Оценка
   * @format int32
   * @min 1
   * @max 5
   */
  score: number;
}

/** Модель, содержащая общие поля для создания критерия */
export interface CreateCriterionDto {
  kind: "CreateAutomaticCriterionDto" | "CreateDefaultCriterionDto" | "CreateGroupCriterionDto";
  /**
   * Заголовок критерия
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Вес критерия
   * @format float
   */
  weight?: number;
  /**
   * Порядковый номер
   * @format int32
   */
  order: number;
}

/** Модель, содержащая поля создания дефолтной оценки */
export interface CreateCriterionEvaluationDto {
  /**
   * Описание
   * @maxLength 365
   */
  annotation?: string | null;
  /**
   * Название
   * @maxLength 61
   */
  caption?: string | null;
  /**
   * Оценка
   * @format int32
   * @min 1
   * @max 5
   */
  score: number;
}

/** Список полей для создания дефолтного критерия */
export type CreateDefaultCriterionDto = UtilRequiredKeys<CreateCriterionDto, "kind"> & {
  /** Набор оценок для дефолтного критерия */
  evaluations: CreateCriterionEvaluationDto[];
  /** @default "CreateDefaultCriterionDto" */
  kind: string;
};

/**
 * Модель, содержащая поля для создания формы оценок
 * @example {"Title":"Оценка оператора","Description":"Для оценки оператора","FormCriteria":[{"kind":"CreateDefaultCriterionDto","Title":"Обычный критерий","Order":1,"Evaluations":[{"kind":"CreateCriterionEvaluationDto","Annotation":"Аннотация к плохой оценке","Caption":"Плохо","Score":1},{"kind":"CreateCriterionEvaluationDto","Annotation":"Аннотация к хорошей оценке","Caption":"Хорошо","Score":5}]}],"Code":"CODE","CalculationRule":"Average","ValidFrom":"2023-09-19T09:01:25.1231853+00:00"}
 */
export interface CreateFormDto {
  /** Список критериев */
  formCriteria?: (CreateGroupCriterionDto | CreateDefaultCriterionDto | CreateAutomaticCriterionDto)[] | null;
  /**
   * Название
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Описание
   * @maxLength 365
   */
  description?: string | null;
  /**
   * Уникальный строковый код формы
   * @minLength 3
   * @maxLength 72
   */
  code: string;
  /**
   * Дата, с которой форма будет доступна для оценки
   * @format date-time
   */
  validFrom?: string | null;
  /**
   * Дата, с которой форма перестанет быть доступной для оценки
   * @format date-time
   */
  validUntil?: string | null;
  /** Правило по которому будет рассчитываться итоговая оценка по форме оценки */
  calculationRule: CalculationRule;
  /** Ключевые слова */
  formKeywords?: string[] | null;
}

/** Список полей для создания группового критерия */
export type CreateGroupCriterionDto = UtilRequiredKeys<CreateCriterionDto, "kind"> & {
  /**
   * Список дочерних критериев
   * @minItems 1
   */
  childCriteria: (CreateGroupCriterionDto | CreateDefaultCriterionDto | CreateAutomaticCriterionDto)[];
  /** @default "CreateGroupCriterionDto" */
  kind: string;
};

/** Модель создания объекта запуска оценочной формы */
export interface CreateRunDto {
  /**
   * Идентификатор оценочной формы, по которой будет производиться оценка
   * @format int64
   */
  formId: number;
  /**
   * Пользователь, для которого запущена оценочная форма оценка
   * @minLength 1
   */
  runFor: string;
  context?: Record<string, string>;
}

/** Модель, содержащая общие поля для критерия */
export interface CriterionDto {
  kind:
    | "AutomaticCriterionDto"
    | "DefaultCriterionDto"
    | "GroupCriterionDto"
    | "RunAutomaticCriterionDto"
    | "RunDefaultCriterionDto"
    | "RunGroupCriterionDto";
  /**
   * ID критерия
   * @format int64
   */
  id: number;
  /**
   * Заголовок критерия
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Вес критерия
   * @format float
   */
  weight?: number;
  /**
   * Порядковый номер
   * @format int32
   */
  order: number;
}

/** Модель, содержащая поля дефолтной оценки */
export interface CriterionEvaluationDto {
  /**
   * Описание
   * @maxLength 365
   */
  annotation?: string | null;
  /**
   * ID оценки
   * @format int64
   */
  id: number;
  /**
   * Название
   * @maxLength 61
   */
  caption?: string | null;
  /**
   * Оценка
   * @format int32
   * @min 1
   * @max 5
   */
  score: number;
}

/** Список полей дефолтного критерия */
export type DefaultCriterionDto = UtilRequiredKeys<CriterionDto, "kind"> & {
  /** Набор оценок для дефолтного критерия */
  evaluations: CriterionEvaluationDto[];
  /** @default "DefaultCriterionDto" */
  kind: string;
};

/** @format int32 */
export enum EdmContainerElementKind {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
}

/** @format int32 */
export enum EdmExpressionKind {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value6 = 6,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value10 = 10,
  Value11 = 11,
  Value12 = 12,
  Value13 = 13,
  Value14 = 14,
  Value15 = 15,
  Value16 = 16,
  Value17 = 17,
  Value18 = 18,
  Value19 = 19,
  Value20 = 20,
  Value21 = 21,
  Value22 = 22,
  Value23 = 23,
  Value24 = 24,
  Value25 = 25,
}

/** @format int32 */
export enum EdmSchemaElementKind {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
}

/** @format int32 */
export enum EdmTypeKind {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value6 = 6,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
}

/** @format int32 */
export enum EvaluationFormStatus {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
}

/** Модель, содержащая поля формы оценок */
export interface FormDto {
  /**
   * Уникальный числовой идентификатор формы
   * @format int64
   */
  id: number;
  /** Текущий статус формы */
  status: FormStatus;
  /**
   * Логин пользователя, создавшего форму
   * @minLength 1
   */
  createdBy: string;
  /**
   * Дата создания
   * @format date-time
   */
  createdAt: string;
  /** Логин пользователя, изменившего форму */
  modifiedBy?: string | null;
  /**
   * Дата изменения
   * @format date-time
   */
  modifiedAt?: string | null;
  /** Логин пользователя, изменившего статус формы */
  statusChangedBy?: string | null;
  /**
   * Дата последнего изменения статуса
   * @format date-time
   */
  statusChangedAt?: string | null;
  /** Список критериев */
  formCriteria?: (GroupCriterionDto | DefaultCriterionDto | AutomaticCriterionDto)[] | null;
  /**
   * Название
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Описание
   * @maxLength 365
   */
  description?: string | null;
  /**
   * Уникальный строковый код формы
   * @minLength 3
   * @maxLength 72
   */
  code: string;
  /**
   * Дата, с которой форма будет доступна для оценки
   * @format date-time
   */
  validFrom?: string | null;
  /**
   * Дата, с которой форма перестанет быть доступной для оценки
   * @format date-time
   */
  validUntil?: string | null;
  /** Правило по которому будет рассчитываться итоговая оценка по форме оценки */
  calculationRule: CalculationRule;
  /** Ключевые слова */
  formKeywords?: string[] | null;
}

/** Текущий статус формы */
export enum FormStatus {
  Draft = "Draft",
  Published = "Published",
  Archived = "Archived",
}

/** Список полей группового критерия */
export type GroupCriterionDto = UtilRequiredKeys<CriterionDto, "kind"> & {
  /**
   * Список дочерних критериев
   * @minItems 1
   */
  childCriteria: (GroupCriterionDto | DefaultCriterionDto | AutomaticCriterionDto)[];
  /** @default "GroupCriterionDto" */
  kind: string;
};

export type IEdmDirectValueAnnotationsManager = object;

export interface IEdmEntityContainer {
  elements?: IEdmEntityContainerElement[] | null;
  schemaElementKind?: EdmSchemaElementKind;
  namespace?: string | null;
  name?: string | null;
}

export interface IEdmEntityContainerElement {
  containerElementKind?: EdmContainerElementKind;
  container?: IEdmEntityContainer;
  name?: string | null;
}

export interface IEdmExpression {
  expressionKind?: EdmExpressionKind;
}

export interface IEdmModel {
  schemaElements?: IEdmSchemaElement[] | null;
  vocabularyAnnotations?: IEdmVocabularyAnnotation[] | null;
  referencedModels?: IEdmModel[] | null;
  declaredNamespaces?: string[] | null;
  directValueAnnotationsManager?: IEdmDirectValueAnnotationsManager;
  entityContainer?: IEdmEntityContainer;
}

export interface IEdmSchemaElement {
  schemaElementKind?: EdmSchemaElementKind;
  namespace?: string | null;
  name?: string | null;
}

export interface IEdmTerm {
  type?: IEdmTypeReference;
  appliesTo?: string | null;
  defaultValue?: string | null;
  schemaElementKind?: EdmSchemaElementKind;
  namespace?: string | null;
  name?: string | null;
}

export interface IEdmType {
  typeKind?: EdmTypeKind;
}

export interface IEdmTypeReference {
  isNullable?: boolean;
  definition?: IEdmType;
}

export type IEdmVocabularyAnnotatable = object;

export interface IEdmVocabularyAnnotation {
  qualifier?: string | null;
  term?: IEdmTerm;
  target?: IEdmVocabularyAnnotatable;
  value?: IEdmExpression;
}

export interface ODataBffForm {
  /** @format int64 */
  id?: number;
  title?: string | null;
  code?: string | null;
  status?: EvaluationFormStatus;
  createdBy?: string | null;
  /** @format date-time */
  createdAt?: string;
  modifiedBy?: string | null;
  /** @format date-time */
  modifiedAt?: string | null;
  /** @format date-time */
  validFrom?: string | null;
  /** @format date-time */
  validUntil?: string | null;
}

export interface ODataBffRun {
  /** @format int64 */
  runId?: number;
  title?: string | null;
  code?: string | null;
  operatorFullName?: string | null;
  /** @format int64 */
  requestId?: number | null;
  /** @format float */
  score?: number;
  createdBy?: string | null;
  /** @format date-time */
  createdAt?: string | null;
  lastSavedBy?: string | null;
  /** @format date-time */
  lastSavedAt?: string | null;
  operatorLogin?: string | null;
  /** @format date-time */
  viewedAt?: string | null;
  agreementStatus?: RunAgreementStatus;
  /** @format date-time */
  agreementAt?: string | null;
}

export interface ODataEntitySetInfo {
  /** @format uri */
  url?: string | null;
  name?: string | null;
  title?: string | null;
  typeAnnotation?: ODataTypeAnnotation;
}

export interface ODataFunctionImportInfo {
  /** @format uri */
  url?: string | null;
  name?: string | null;
  title?: string | null;
  typeAnnotation?: ODataTypeAnnotation;
}

export interface ODataServiceDocument {
  entitySets?: ODataEntitySetInfo[] | null;
  singletons?: ODataSingletonInfo[] | null;
  functionImports?: ODataFunctionImportInfo[] | null;
  typeAnnotation?: ODataTypeAnnotation;
}

export interface ODataSingletonInfo {
  /** @format uri */
  url?: string | null;
  name?: string | null;
  title?: string | null;
  typeAnnotation?: ODataTypeAnnotation;
}

export interface ODataTypeAnnotation {
  typeName?: string | null;
}

export interface ProblemDetails {
  type?: string | null;
  title?: string | null;
  /** @format int32 */
  status?: number | null;
  detail?: string | null;
  instance?: string | null;
  [key: string]: any;
}

/** @format int32 */
export enum RunAgreementStatus {
  Value0 = 0,
  Value1 = 1,
}

/** Список полей автоматического критерия */
export type RunAutomaticCriterionDto = UtilRequiredKeys<RunCriterionDto, "kind"> & {
  /** Набор оценок для автоматического критерия */
  evaluations: AutomaticCriterionEvaluationDto[];
  /** Поля параметра на основе которого будет произведен автоматический расчет оценки */
  parameter: RunAutomaticCriterionParameterDto;
  /** Комментарий */
  comment?: string | null;
  /**
   * ИД выбранной оценки
   * @format int64
   */
  selectedEvaluationId?: number | null;
  /** Признак, по которому определяется учитывать или не учитывать критерий при расчете оценки */
  isSkipped: boolean;
  /**
   * ИД критерия
   * @format int64
   */
  criterionId: number;
  /** @default "RunAutomaticCriterionDto" */
  kind: string;
};

/** Поля параметра на основе которого будет произведен автоматический расчет оценки */
export interface RunAutomaticCriterionParameterDto {
  /**
   * Рассчитанное значение автоматического критерия
   * @format float
   */
  conditionValue: number;
  /**
   * Идентификатор параметра для сервиса автоматизации, который будет производить расчет
   * @format int64
   */
  id: number;
  /**
   * Код сервиса автоматизации, который будет производить расчет
   * @minLength 1
   */
  serviceCode: string;
  /**
   * Отображаемого название параметра (сервиса)
   * @minLength 1
   */
  caption: string;
  /** Варианты типов сравнения параметра с оценкой */
  conditionType: ConditionType;
}

/** Модель, содержащая общие поля для критерия */
export interface RunCriterionDto {
  kind: "RunAutomaticCriterionDto" | "RunDefaultCriterionDto" | "RunGroupCriterionDto";
  /**
   * ID критерия
   * @format int64
   */
  id: number;
  /**
   * Заголовок критерия
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Вес критерия
   * @format float
   */
  weight?: number;
  /**
   * Порядковый номер
   * @format int32
   */
  order: number;
}

/** Список полей дефолтного критерия в запуске */
export type RunDefaultCriterionDto = UtilRequiredKeys<RunCriterionDto, "kind"> & {
  /** Набор оценок для дефолтного критерия */
  evaluations: CriterionEvaluationDto[];
  /**
   * ИД выбранной оценки
   * @format int64
   */
  selectedEvaluationId?: number | null;
  /** Признак, по которому определяется учитывать или не учитывать критерий при расчете оценки */
  isSkipped: boolean;
  /** Комментарий */
  comment?: string | null;
  /**
   * ИД критерия
   * @format int64
   */
  criterionId?: number;
  /** @default "RunDefaultCriterionDto" */
  kind: string;
};

/** Полная модель текущего состояния запуска формы */
export interface RunDto {
  /**
   * Идентификатор объекта запуска оценки
   * @format int64
   */
  id: number;
  form: RunFormDto;
  /**
   * Логин пользователя, которого выбрали для оценки
   * @minLength 1
   */
  runFor: string;
  /** Дополнительный набор данных для запуска оценки (например номер обращения) */
  runContext: Record<string, string>;
  /**
   * Дата первого запуска оценки
   * @format date-time
   */
  createdAt?: string;
  /** Пользователь, который инициировал оценку */
  createdBy?: string | null;
  /**
   * Дата первого сохранения оценки
   * @format date-time
   */
  firstSavedAt?: string | null;
  /** Пользователь, который первый раз сохранил изменения */
  firstSavedBy?: string | null;
  /**
   * Дата последнего сохранения
   * @format date-time
   */
  lastSavedAt?: string | null;
  /** Пользователь, который последний раз делал изменения */
  lastSavedBy?: string | null;
  /**
   * Дата публикации
   * @format date-time
   */
  publishedAt?: string | null;
  /** Пользователь, который опубликовал оценку */
  publishedBy?: string | null;
  /**
   * Текущая рассчитанная оценка по запуску
   * @format float
   */
  scoreResult?: number | null;
  /**
   * Время просомтра оценки оператором
   * @format date-time
   */
  viewedAt?: string | null;
  /** Состояние поддтеврждения оценки оператора */
  agreementStatus?: AgreementStatus;
  /**
   * Время изменения состояния согласия с оценкой
   * @format date-time
   */
  agreementAt?: string | null;
}

export interface RunFormDto {
  /**
   * Уникальный числовой идентификатор формы
   * @format int64
   */
  id: number;
  /**
   * Название
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Описание
   * @minLength 1
   * @maxLength 365
   */
  description: string;
  /**
   * Уникальный строковый код формы
   * @minLength 3
   * @maxLength 72
   */
  code: string;
  /** Правило по которому будет рассчитываться итоговая оценка по форме оценки */
  calculationRule: CalculationRule;
  /** Ключевые слова */
  formKeywords?: string[] | null;
  /** Список критериев */
  formCriteria?: (RunDefaultCriterionDto | RunAutomaticCriterionDto | RunGroupCriterionDto)[] | null;
  /** Комментарий */
  comment?: string | null;
}

/** Список полей группового критерия */
export type RunGroupCriterionDto = UtilRequiredKeys<RunCriterionDto, "kind"> & {
  /**
   * Список дочерних критериев
   * @minItems 1
   */
  childCriteria: (RunDefaultCriterionDto | RunAutomaticCriterionDto | RunGroupCriterionDto)[];
  /** @default "RunGroupCriterionDto" */
  kind: string;
};

export interface UpdateCriterionPropertyDto {
  /** @format int64 */
  criterionId?: number;
  /**
   * Идентификатор выставленной оценки
   * @format int64
   */
  selectedEvaluationId?: number | null;
  /**
   * Комментарий
   * @maxLength 365
   */
  comment?: string | null;
  /** Признак, по которому определяется учитывать или не учитывать критерий при расчете оценки */
  isSkipped: boolean;
}

/** Модель, содержащая поля для обновления формы оценок */
export interface UpdateFormDto {
  /** Список критериев */
  formCriteria?: (CreateGroupCriterionDto | CreateDefaultCriterionDto | CreateAutomaticCriterionDto)[] | null;
  /**
   * Название
   * @minLength 1
   * @maxLength 365
   */
  title: string;
  /**
   * Описание
   * @maxLength 365
   */
  description?: string | null;
  /**
   * Уникальный строковый код формы
   * @minLength 3
   * @maxLength 72
   */
  code: string;
  /**
   * Дата, с которой форма будет доступна для оценки
   * @format date-time
   */
  validFrom?: string | null;
  /**
   * Дата, с которой форма перестанет быть доступной для оценки
   * @format date-time
   */
  validUntil?: string | null;
  /** Правило по которому будет рассчитываться итоговая оценка по форме оценки */
  calculationRule: CalculationRule;
  /** Ключевые слова */
  formKeywords?: string[] | null;
}

/** Базовые поля относящиеся к запуску формы */
export interface UpdateRunDto {
  /**
   * Рассчитанная оценка по запуску
   * @format float
   */
  scoreResult: number;
  /** Коментарий к оценочной форме */
  evaluationFormComment?: string | null;
  /**
   * Коллекция свойств критерия
   * @minItems 0
   */
  criterionProperties: UpdateCriterionPropertyDto[];
}

export interface ValidationProblemDetails {
  errors?: Record<string, string[]>;
  type?: string | null;
  title?: string | null;
  /** @format int32 */
  status?: number | null;
  detail?: string | null;
  instance?: string | null;
  [key: string]: any;
}
