import React from 'react';

import clsx from 'clsx';

import { LoaderSize, OverlayLoader, OverlayVariant, Radio, utils } from '@product.front/ui-kit';

import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { getLocaleMessageById } from '../../../../helpers/localeHelper';

interface ILoadingRadioProps {
  checked: boolean;
  onChange?: (checked: boolean) => Promise<void>;
}

const LoadingRadio = ({ checked, onChange }: ILoadingRadioProps) => {
  const [loading, setLoading] = React.useState(false);

  const onRadioChange = async () => {
    if (!onChange) return;

    setLoading(true);
    try {
      await onChange(true);
    } catch (error) {
      if (error.message === 'canceled') return;

      const errText = getLocaleMessageById('queues.isDefaultChange.error');
      const errArgs = getNotificationArgsByError(errText, error);
      getPlatformPopupNotificationManager().notifyError(...errArgs);
    } finally {
      setLoading(false);
    }
  };

  return (
    <OverlayLoader
      wrapperClassName={clsx(
        utils.w100,
        utils.dFlex,
        utils.alignItemsCenter,
        utils.justifyContentCenter,
      )}
      loading={loading}
      loaderSize={LoaderSize.Small}
      variant={OverlayVariant.Transparent}
    >
      <Radio value={undefined} checked={checked} onChange={onRadioChange} disabled={!onChange} />
    </OverlayLoader>
  );
};

export default LoadingRadio;
