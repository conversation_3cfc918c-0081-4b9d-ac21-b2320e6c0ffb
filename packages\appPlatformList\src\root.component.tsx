import * as React from 'react';

import { AppProps } from 'single-spa';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

import AppItemComponent from './AppItemComponent';

import './Product-AppsList.css';

export type AppsListComponentProps = AppProps & AWP.AppComponentProps;

export type AppsListComponentState = {
  hostedApps: string[];
  activeAppName: string | null;
};

export default class AppsListComponent
  extends React.PureComponent<AppsListComponentProps, AppsListComponentState>
  implements AWP.IHostedAppAdapter
{
  hostedAppsManager: AWP.IHostedAppsManager;
  eventManager: AWP.IEventManager;

  constructor(props: AppsListComponentProps) {
    super(props);

    this.hostedAppsManager =
      this.props.shell.serviceResolver.resolve<AWP.IHostedAppsManager>('IHostedAppsManager');
    this.eventManager = this.props.shell.eventManager;
    this.state = {
      hostedApps: this.hostedAppsManager.getHostedApps(),
      activeAppName: this.hostedAppsManager.activeAppName,
    };
    this.executeAction = this.executeAction.bind(this);

    this.onAppButtonClicked = this.onAppButtonClicked.bind(this);
    this.onAppActivated = this.onAppActivated.bind(this);
    this.props.shell.eventManager.subscribe('HostedApplicationActivated', this.onAppActivated);

    this.appName = this.props.name;
    this.hostedAppsManager.registerHostedAppAdapter(this);
  }

  appName: string;
  appState: AWP.HostedAppState;

  executeAction(actionName: string, actionParameters: any) {
    throw new Error('Method not implemented.' + actionName + '. ' + actionParameters);
  }

  onAppButtonClicked(appName: string) {
    this.hostedAppsManager.activateApp(appName);
  }

  onAppActivated(eventArgs: { prevActiveApp: string; currentActiveApp: string }) {
    if (this.state.hostedApps.find((s) => s == eventArgs.currentActiveApp) != undefined) {
      this.setState({ activeAppName: eventArgs.currentActiveApp });
    }
  }

  render() {
    return (
      <div className="app-buttons">
        {this.state.hostedApps.map((hostedApp) => {
          return (
            <AppItemComponent
              className={
                this.state.activeAppName == hostedApp ? 'app-button active' : 'app-button inactive'
              }
              name={hostedApp}
              hostedAppsManager={this.hostedAppsManager}
              eventManager={this.eventManager}
              key={hostedApp}
            >
              {hostedApp}
            </AppItemComponent>
          );
        })}
      </div>
    );
  }
}
