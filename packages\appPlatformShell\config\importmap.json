{"imports": {"@product.front/ui-kit": "./CDN/lib-uikit/product-ui-kit.js", "@product.front/ui-kit/dist/product-ui-kit.css": "./CDN/lib-uikit/product-ui-kit.css", "@Product/root-config": "./CDN/pl-root/Product-root-config.js", "AppAdapters/Administrator": "./CDN/app-administrator/PRODUCT-administrator.js", "AppAdapters/AppsList": "./CDN/pl-app-appslist/Product-AppsList.js", "AppAdapters/AutomationServices": "./CDN/app-automationservices/PRODUCT-automation-services.js", "AppAdapters/ButtonBot": "./CDN/app-buttonbot/PRODUCT-button-bot.js", "AppAdapters/RatingBot": "./CDN/app-ratingbot/PRODUCT-rating-bot.js", "AppAdapters/Clients": "./CDN/app-clients/PRODUCT-clients.js", "AppAdapters/DialogScriptPlayer": "./CDN/app-dialogscriptplayer/PRODUCT-dialog-script-player.js", "AppAdapters/DialogScripts": "./CDN/app-dialogscripts/PRODUCT-dialog-scripts.js", "AppAdapters/EvaluationFormPlayer": "./CDN/app-evaluationform-player/PRODUCT-evaluation-form-player.js", "AppAdapters/EvaluationForms": "./CDN/app-evaluationforms/PRODUCT-evaluation-forms.js", "AppAdapters/Info": "./CDN/app-info/PRODUCT-info.js", "AppAdapters/InternalChat": "./CDN/app-internaloperatorchat/PRODUCT-internal-operator-chat.js", "AppAdapters/Marketing": "./CDN/app-marketing/PRODUCT-marketing.js", "AppAdapters/RequestProcessing": "./CDN/app-crpm/PRODUCT-crpm.js", "AppAdapters/RequestsAwaiting": "./CDN/app-requestsawaiting/PRODUCT-requests-awaiting.js", "AppAdapters/Supervisor": "./CDN/app-supervisor/PRODUCT-supervisor.js", "AppAdapters/WebPage": "./CDN/pl-app-webpage/Product-WebPage.js", "AppAdapters/VideoChat": "./CDN/app-videochat/PRODUCT-video-chat.js", "AppAdapters/ServiceObjects": "./CDN/app-serviceobjects/PRODUCT-service-objects.js", "HeaderItems/DialogScriptsPanel": "./CDN/panel-dialogscripts/PRODUCT-dialog-scripts-panel.js", "HeaderItems/OperatorQueuesInfoPanel": "./CDN/panel-operatorqueuesinfo/PRODUCT-operator-queues-info-panel.js", "HeaderItems/OperatorStatusPanelModule": "./CDN/panel-operatorstatus/PRODUCT-operator-status-panel.js", "HeaderItems/SoftPhonePanel": "./CDN/panel-softphone/PRODUCT-softphone-panel.js", "HeaderItems/TemplatePanel": "./CDN/panel-templates/PRODUCT-template-panel.js", "HeaderItems/VersionPanel": "./CDN/panel-version/PRODUCT-version-panel.js", "Modules/ProductAcwModule": "./CDN/module-product-acw/PRODUCT-acw-module.js", "Modules/ProductAuthModule": "./CDN/module-product-auth/PRODUCT-auth-module.js", "Modules/ProductServices": "./CDN/module-product-services/PRODUCT-services.js", "Modules/EventServiceModule": "./CDN/module-eventservice/PRODUCT-EventService.js", "Modules/InactivityModule": "./CDN/module-inactivity/PRODUCT-inactivity-module.js", "Modules/OperatorScreenModule": "./CDN/module-operator-screen/PRODUCT-operator-screen-module.js", "Modules/NotificationsModule": "./CDN/pl-module-notifications/Product-NotificationsModule.js", "Modules/OperatorAuthenticationModule": "./CDN/pl-module-auth/Product-OperatorAuthenticationModule.js", "Modules/OperatorStatusModule": "./CDN/pl-module-operatorstatus/Product-OperatorStatusModule.js", "Modules/PermissionModule": "./CDN/pl-module-permission/Product-PermissionModule.js", "Modules/WorkExecutionModule": "./CDN/pl-module-workexecution/Product-WorkExecutionModule.js", "Modules/WorkflowManagerModule": "./CDN/pl-module-workflowmanager/Product-WorkflowManagerModule.js", "WorkExecutors/Multimedia.AwpWorker": "./CDN/worker-webworker/PRODUCT-WebWorker.js"}}