import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';

import {
  NewOperatorGroup,
  OperatorGroupTreeView,
  OperatorGroupView,
  SessionSettings,
  StatusSettings,
} from '../@types/generated/administration';
import { IFrontSessionSettings, IFrontStatusSettings } from '../@types/operator';
import { IFrontOperatorGroup, IFrontOperatorGroupBase } from '../@types/operatorGroup';

import { mapOperatorDtoToFront } from './operators';

export const mapOperatorGroupDtoToFront = (
  operatorGroup: OperatorGroupTreeView & { childOperatorGroupsIds: string[] },
): IFrontOperatorGroupBase => ({
  id: operatorGroup.id,
  name: operatorGroup.name,
  parentId: operatorGroup.parentId ?? null,
  childOperatorGroupsIds: operatorGroup.childOperatorGroupsIds,
});

export const mapSessionSettingsDtoToFront = (
  sessionSettings?: SessionSettings,
): IFrontSessionSettings => ({
  maxSessions: sessionSettings?.maxSessions ?? null,
  channelSessions:
    sessionSettings?.channelSessions
      ?.filter((channelSession) => channelSession.sessionCount != null)
      .map((channelSession) => ({
        channel: channelSession.channel as UcmmChannel,
        // *sessionCount is not null because of filter above
        maxSessions: channelSession.sessionCount!,
      })) ?? [],
});

export const mapStatusSettingsDtoToFront = (
  statusSettings?: StatusSettings,
): IFrontStatusSettings => ({
  maxBreakTime: statusSettings?.maxBreakTimeMinutes
    ? statusSettings.maxBreakTimeMinutes * 60
    : null,
  maxStatusTime:
    statusSettings?.maxStatusTimes?.map((statusTime) => ({
      status: statusTime.status,
      maxTime: (statusTime.maxTimeMinutes ?? 0) * 60,
    })) ?? [],
});

export const mapOperatorGroupViewDtoToFront = (
  operatorGroup: OperatorGroupView,
): IFrontOperatorGroup => ({
  id: operatorGroup.id,
  name: operatorGroup.name,
  parentId: operatorGroup.parentId ?? null,
  operators: operatorGroup.operators?.map(mapOperatorDtoToFront) ?? [],
  sessionSettings: mapSessionSettingsDtoToFront(operatorGroup.sessionSettings),
  statusSettings: mapStatusSettingsDtoToFront(operatorGroup.statusSettings),
  kpiSettings:
    operatorGroup.kpiSettings?.map((kpi) => ({
      code: kpi.code,
      target: kpi.target ?? null,
    })) ?? [],
});

export const mapFrontSessionSettingsToNewDto = (
  sessionSettings: IFrontSessionSettings,
): SessionSettings => ({
  maxSessions: sessionSettings.maxSessions ?? null,
  channelSessions:
    sessionSettings.channelSessions?.map((channelSession) => ({
      channel: channelSession.channel,
      sessionCount: channelSession.maxSessions,
    })) ?? [],
});

export const mapFrontStatusSettingsToNewDto = (
  statusSettings: IFrontStatusSettings,
): StatusSettings => ({
  maxBreakTimeMinutes: Math.floor((statusSettings.maxBreakTime ?? 0) / 60),
  maxStatusTimes: statusSettings.maxStatusTime?.map((statusTime) => ({
    status: statusTime.status,
    maxTimeMinutes: Math.floor((statusTime.maxTime ?? 0) / 60),
  })),
});

export const mapFrontOperatorGroupToNewDto = (
  operatorGroup: IFrontOperatorGroup,
): NewOperatorGroup => ({
  name: operatorGroup.name,
  parentId: operatorGroup.parentId,
  operatorIds: operatorGroup.operators.map((operator) => operator.id),
  sessionSettings: mapFrontSessionSettingsToNewDto(operatorGroup.sessionSettings),
  statusSettings: mapFrontStatusSettingsToNewDto(operatorGroup.statusSettings),
  kpiSettings: operatorGroup.kpiSettings.map((kpi) => ({ code: kpi.code, target: kpi.target })),
});
