import { createSlice } from '@reduxjs/toolkit';

import { ResultReason } from '../../@types/campaign';
import { Channel, RequestQueue } from '../../@types/generated/marketing';
import { IOfferType } from '../../@types/offer';
import { IClientTableView } from '../../@types/settings';

import extraReducers from './settings.extraReducers';

export interface ISettingsStore {
  offerTypes: IOfferType[];
  channels: Record<number, Channel>;
  queues: RequestQueue[];
  clientTableView: IClientTableView['columnSettings'];
  resultReasons: ResultReason[];
}

const initialState: ISettingsStore = {
  offerTypes: [],
  channels: {},
  queues: [],
  clientTableView: [],
  resultReasons: [],
};

const settingsSlice = createSlice({
  name: 'settingsData',
  initialState,
  reducers: {},
  extraReducers,
});

export default settingsSlice.reducer;
