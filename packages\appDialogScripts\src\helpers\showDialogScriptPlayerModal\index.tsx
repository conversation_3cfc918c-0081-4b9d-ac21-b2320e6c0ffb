import React from 'react';

import clsx from 'clsx';

import { showModal, utils } from '@product.front/ui-kit';

import DialogScriptPlayer from '@monorepo/dialog-script-player/src/components/Root/builtIn';

import { getSettings } from '../../config/appSettings';

import styles from './styles.module.scss';

export const showDialogScriptPlayer = (scriptId: number, scriptName: string) => {
  return showModal({
    overlay: true,
    className: clsx(utils.dFlex, utils.flexColumn, styles.modal),
    flushBody: true,
    header: scriptName,
    children: (close) => (
      <DialogScriptPlayer scriptId={scriptId} onClose={close} settings={getSettings()} />
    ),
  });
};
