import React from 'react';

import clsx from 'clsx';

import { Text, Textarea, TextVariant, utils } from '@product.front/ui-kit';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontStep, IScenarioStepScenario } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { activeScriptCheck } from '../../../helpers/scriptHelpers';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import InputErrorMessage from '../../InputErrorMessage';

import ScriptSelect from './components/ScriptSelect';
import DeleteStepButton from './DeleteStepButton';
import ScriptStatusSelect from './ScriptStatusSelect';

import styles from './styles.module.scss';

interface IScenarioProps {
  step: IFrontStep;
  number: number;
  onlyStep: boolean;
  disabled: boolean;
  requiredForActive: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
}

const Scenario = ({
  step,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: IScenarioProps) => {
  const [selectedScript, setSelectedScript] = React.useState<IScenarioStepScenario | undefined>(
    step?.scenario,
  );

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,

        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {selectedScript?.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
          {!onlyStep && (
            <DeleteStepButton
              needConfirm={Boolean(
                step.description.length || step.name.length || step.rules?.length,
              )}
              disabled={disabled}
              onDelete={onDelete}
            />
          )}
        </aside>
      </div>
      <ScriptSelect
        label={getLocaleMessageById('app.editor.step.scenarioSelect')}
        selectedScriptId={selectedScript?.id?.toString()}
        filter={(script) => script.id === selectedScript?.id || activeScriptCheck(script)}
        enableStatusIcon={false}
        onSelect={(scenario) => {
          setSelectedScript(scenario);
          onChange({
            ...step,
            name: scenario?.name || '',
            description: scenario?.description || '',
            scenario,
          });
        }}
        required={requiredForActive}
        disabled={disabled}
        invalidMessage={step.invalidReasons?.scenario}
        scriptDisabledForSelect={(script) =>
          script.id === selectedScript?.id && !activeScriptCheck(selectedScript!)
        }
      />

      <Textarea
        label={getLocaleMessageById('app.editor.step.scenarioDescription')}
        value={selectedScript?.description}
        style={{ minHeight: '72px', maxHeight: '20vh' }}
        disabled
      />

      <ScriptStatusSelect
        wrapperClassName={utils.w100}
        label={getLocaleMessageById('app.editor.step.scenarioStatus')}
        value={selectedScript?.status || ScriptStatus.Template}
        required
        disabled
      />

      {!!step.invalidReasons?.invalidRelation && (
        <aside className={utils.mY4}>
          <InputErrorMessage>{step.invalidReasons?.invalidRelation}</InputErrorMessage>
        </aside>
      )}
    </div>
  );
};

export default Scenario;
