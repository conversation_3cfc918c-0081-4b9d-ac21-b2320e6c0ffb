import { createAsyncThunk } from '@reduxjs/toolkit';

import { mapOperatorDtoToFront } from '../../mappers/operators';
import * as api from '../../services/operators';

export const getAllOperators = createAsyncThunk('operators/all', async () =>
  (await api.getOperators()).map(mapOperatorDtoToFront),
);

export const deleteOperator = createAsyncThunk(
  'operator/delete',
  async (id: string, { dispatch }) => {
    await api.deleteOperator(id);
    dispatch(getAllOperators());
  },
);
