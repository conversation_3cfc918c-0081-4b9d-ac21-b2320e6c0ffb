import { createAsyncThunk, createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { logError } from '@monorepo/common/src/helpers/logHelper';
import { playSound } from '@monorepo/common/src/helpers/soundHelper';
import { getPersonalSettings } from '@monorepo/common/src/managers/personalSettingsManager';

import {
  GetOperatorsAndGroupsResponse,
  InternalOperator,
  Message,
  OperatorGroup,
  PartialChat,
} from '../@types/generated/signalr';
import { IChat } from '../@types/signalRTypes';
import { getPlatformApp } from '../managers/platformAppManager';
import { getPlatformHostedAppManager } from '../managers/platformHostedAppManager';
import {
  getMessages,
  getOperatorsAndGroups,
  readMessage,
  sendMessage,
} from '../services/internalChat.service';
import newMessageSoundUrl from '../sounds/newMessage.mp3';

import chatNotificationReducer from './notifications/notifications.reducer';
import { handleChatNotification } from './notifications/notifications.thunks';
import { selectUserId } from './user/user.selectors';

import { RootState } from './index';

export interface IInternalChatStore {
  chats: IChat[];
  showChatInfo: boolean;
  messages: Array<Message>;
  isChatLoading: boolean;
  chatLoadingError?: SerializedError;
  isChatHistoryEndReached: boolean;
  isMessageSending: boolean;
  chatSendError?: SerializedError;
  page: number;
  perPageCount: number;
  replyMessage: Message | null;
  allOperators: InternalOperator[];
  allOperatorsGroups: OperatorGroup[];
}

const initialState: IInternalChatStore = {
  chats: [],
  showChatInfo: false,
  messages: [],
  isChatLoading: false,
  isChatHistoryEndReached: false,
  isMessageSending: false,
  page: 0,
  perPageCount: 30,
  replyMessage: null,
  allOperators: [],
  allOperatorsGroups: [],
};

export const getMessagesData = createAsyncThunk('internalChat/getChatHistory', getMessages);
export const sendMessageData = createAsyncThunk('internalChat/sendMessage', sendMessage);
export const addReceiveMessage = createAsyncThunk(
  'internalChat/addReceiveMessage',
  (payload: Message, { getState }) => {
    const currentUserId = selectUserId(getState() as RootState);
    const isMyMessage = payload?.author?.id === currentUserId;
    const canPlaySound = getPersonalSettings().sounds.newMessageGroupChat;

    if (canPlaySound && !isMyMessage) {
      playSound(newMessageSoundUrl);
    }
  },
);

export const markChatMessagesRead = createAsyncThunk(
  'internalChat/markChatMessagesRead',
  readMessage,
);
export const getOperatorsAndGroupsAction = createAsyncThunk(
  'internalChat/operatorsAndGroups',
  getOperatorsAndGroups,
);
const internalChatSlice = createSlice({
  name: 'internalChat',
  initialState,
  reducers: {
    nextPage(state) {
      state.page = state.page + 1;
    },
    toggleChatInfo(state) {
      state.showChatInfo = !state.showChatInfo;
    },
    setOperatorChats(state, action: PayloadAction<IChat[]>) {
      state.chats = action.payload;
    },
    setSelectedChat(state, action: PayloadAction<IChat>) {
      if (action.payload.isSelected) return;

      state.messages = [];
      state.page = 0;
      state.isChatHistoryEndReached = false;
      state.chats = state.chats.map((chat) => {
        if (chat.id === action.payload.id) {
          return { ...chat, isSelected: true };
        } else if (chat.isSelected) {
          return { ...chat, isSelected: false };
        }

        return chat;
      });
    },
    addChat(state, action: PayloadAction<IChat>) {
      state.messages = [];
      state.page = 0;
      state.isChatHistoryEndReached = false;
      state.chats = [
        action.payload,
        ...state.chats.map((chat) => ({ ...chat, isSelected: false })),
      ];
    },
    updateChat(state, action: PayloadAction<PartialChat>) {
      const targetChat = state.chats.find((chat) => chat.id === action.payload.id);
      if (targetChat) {
        targetChat.name = action.payload.name;
      }
    },
    setReplyMessage(state, action: PayloadAction<Message | null>) {
      if (!action.payload) {
        state.replyMessage = null;
      }

      const msg = { ...action.payload };
      msg.replyMessage = undefined; // Не рекурсируем цитаты (только 1 уровень вложенности цитат)
      state.replyMessage = msg;
    },
    dropUnreadCountForChat(state, action: PayloadAction<string>) {
      const targetChat = state.chats.find((chat) => chat.id === action.payload);
      if (targetChat) {
        targetChat.unreadedMessagesCount = 0;
      }
    },
  },
  extraReducers: (builder) => {
    // get Messages
    builder.addCase(getMessagesData.pending, (state) => {
      state.isChatLoading = true;
    });
    builder.addCase(getMessagesData.fulfilled, (state, action: PayloadAction<Message[]>) => {
      state.isChatLoading = false;
      state.isChatHistoryEndReached = action.payload.length < state.perPageCount;
      const msgs = [...action.payload, ...state.messages];
      const targetChat = state.chats.find((chat) => chat.isSelected);
      if (targetChat && targetChat.unreadedMessagesCount && !targetChat.newDividerPrevMessage) {
        const index = targetChat.unreadedMessagesCount + 1;
        targetChat.newDividerPrevMessage = msgs[msgs.length - index]?.id;
      }
      state.messages = msgs;
    });
    builder.addCase(getMessagesData.rejected, (state, { error }) => {
      state.chatLoadingError = error;
      state.isChatLoading = false;
    });

    builder.addCase(addReceiveMessage.pending, (state, action) => {
      const payload = action.meta.arg;

      const isMessageToCurrentChat =
        payload.chatId === state.chats.find((chat) => chat.isSelected)?.id;

      if (
        isMessageToCurrentChat &&
        !state.messages.find((msg) => msg.id === payload.id) &&
        state.messages.length !== 0
      ) {
        state.messages = [...state.messages, payload];
      }

      const targetChat = state.chats.find((chat) => chat.id === payload.chatId);
      if (targetChat) {
        targetChat.lastMessage = payload;
        const isChatWindowActive = getPlatformHostedAppManager()?.activeAppName
          ? getPlatformHostedAppManager()?.activeAppName === getPlatformApp()?.name
          : document.hasFocus();
        if (!targetChat.isSelected || !isChatWindowActive) {
          targetChat.unreadedMessagesCount = (targetChat.unreadedMessagesCount || 0) + 1;
        }
      } else {
        logError('no targetChat for receive msg', payload);
      }
    });

    builder.addCase(handleChatNotification, chatNotificationReducer);

    // get AllOperators
    builder.addCase(getOperatorsAndGroupsAction.pending, (state) => {
      state.allOperators = [];
      state.allOperatorsGroups = [];
    });

    builder.addCase(
      getOperatorsAndGroupsAction.fulfilled,
      (state, action: PayloadAction<GetOperatorsAndGroupsResponse>) => {
        state.allOperators = action.payload.operators ? action.payload.operators : [];
        state.allOperatorsGroups = action.payload.groups ? action.payload.groups : [];
      },
    );
    builder.addCase(getOperatorsAndGroupsAction.rejected, (state, { error }) => {
      console.log(error);
      state.allOperators = [];
      state.allOperatorsGroups = [];
    });
    // send Message
    builder.addCase(sendMessageData.pending, (state) => {
      state.isMessageSending = true;
    });

    builder.addCase(sendMessageData.fulfilled, (state) => {
      state.isMessageSending = false;
      state.replyMessage = null;
    });
    builder.addCase(sendMessageData.rejected, (state, { error }) => {
      state.chatSendError = error;
      state.isMessageSending = false;
    });
  },
});

export const {
  nextPage,
  toggleChatInfo,
  setOperatorChats,
  addChat,
  updateChat,
  setSelectedChat,
  setReplyMessage,
  dropUnreadCountForChat,
} = internalChatSlice.actions;
export default internalChatSlice.reducer;
