import { <PERSON>agEventHand<PERSON> } from 'react';

import { IFrontFolder } from '../../../../@types/files';
import { normalizePathForApi } from '../../../../helpers/files';
import {
  addTrustedFileAsync,
  moveTrustedFolderAsync,
  updateTrustedFileAsync,
} from '../../../../services/files';

export const handleDropOnFolder =
  (currentFolder: IFrontFolder, callback: () => void): DragEventHandler<HTMLElement> =>
  async (e) => {
    e.preventDefault();
    e.stopPropagation();

    const draggedFolder = e.dataTransfer.getData('application/webarm-trusted-folder');

    if (draggedFolder) {
      await moveTrustedFolderAsync(
        normalizePathForApi(draggedFolder),
        normalizePathForApi([currentFolder.path, draggedFolder.split('/').pop()].join('/')),
      );
      callback();
    }

    const draggedFile = e.dataTransfer.getData('application/webarm-trusted-file');

    if (draggedFile) {
      await updateTrustedFileAsync(
        normalizePathFor<PERSON>pi(draggedFile),
        normalizePathForApi([currentFolder.path, draggedFile.split('/').pop()].join('/')),
      );
      callback();
    }

    if (e.dataTransfer.types[0] === 'Files') {
      const files = Array.from(e.dataTransfer.files);
      const path = currentFolder.path ?? '/';

      for (const file of files) {
        await addTrustedFileAsync(file, file.name, normalizePathForApi(path));
      }
      callback();
    }
  };
