import React from 'react';

import clsx from 'clsx';

import {
  Colors,
  IconButton,
  Text,
  TextVariant,
  Tooltip,
  TooltipPosition,
  utils,
} from '@product.front/ui-kit';

import IconOperations from '@product.front/icons/dist/icons17/MainStuff/IconOperations';

import { ChatType } from '../../../../@types/generated/signalr';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { useInternalChatDispatch, useInternalChatSelector } from '../../../../store/hooks';
import { setSelectedChat } from '../../../../store/internalChat.slice';
import { selectUserRights } from '../../../../store/user/user.selectors';
import InternalChatChatAdd from '../InternalChatAdd';
import InternalChatLeftPanel from '../InternalChatLeftPanel';

import ChatListItem from './components/ChatListItem';

const InternalChatChatsList = () => {
  const { chats } = useInternalChatSelector((state) => state.internalChat);
  const dispatch = useInternalChatDispatch();

  const [isAddMode, setIsAddMode] = React.useState<ChatType | null>(null);

  const canAdd = useInternalChatSelector(selectUserRights).canAddGroup;

  if (isAddMode && canAdd) {
    return (
      <InternalChatChatAdd onCancel={() => setIsAddMode(null)} onSave={() => setIsAddMode(null)} />
    );
  }

  /**
   * * Сортировка чатов реализована в internalChat.middleware.ts
   * */

  return (
    <InternalChatLeftPanel
      title={getLocaleMessageById('app.title.chats')}
      buttons={
        canAdd && (
          <Tooltip
            tooltip={getLocaleMessageById('app.tooltip.createChat')}
            position={TooltipPosition.Bottom}
          >
            <IconButton onClick={() => setIsAddMode(ChatType.Closed)}>
              <IconOperations />
            </IconButton>
          </Tooltip>
        )
      }
    >
      {!chats.length && (
        <Text
          variant={TextVariant.SmallMedium}
          className={clsx(utils.dBlock, utils.textCenter, utils.p5)}
          color={Colors.OnyxBlack50}
        >
          {getLocaleMessageById('app.emptyChatList')}
        </Text>
      )}
      {chats.map((chat) => (
        <ChatListItem
          active={chat.isSelected}
          key={chat.id}
          chat={chat}
          onClick={() => {
            dispatch(setSelectedChat(chat));
          }}
        />
      ))}
    </InternalChatLeftPanel>
  );
};

export default InternalChatChatsList;
