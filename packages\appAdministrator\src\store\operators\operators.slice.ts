import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontOperatorBase } from '../../@types/operator';

import extraReducers from './operators.extraReducers';

export interface IOperatorsStore {
  loading: boolean;
  error?: SerializedError;
  operators: IFrontOperatorBase[];
}

const initialState: IOperatorsStore = {
  loading: false,
  operators: [],
};

const operatorsSlice = createSlice({
  name: 'operators',
  initialState,
  reducers: {},
  extraReducers,
});

export default operatorsSlice.reducer;
