import React from 'react';

import clsx from 'clsx';

import { grids, Input, InputSize, Text, utils } from '@product.front/ui-kit';

import { IFrontStep, IFrontServiceParameter } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getRegExpPatternExcludingStrings } from '../../../helpers/scriptHelpers';
import { useDialogScriptsAppSelector } from '../../../store/hooks';

interface IServiceOutputParameterProps {
  parameter: IFrontServiceParameter;
  onChange: (updatedParameter: IFrontServiceParameter) => void;
  disabled?: boolean;
  variables: Record<string, IFrontStep['variableName']>;
}

const ServiceOutputParameter = ({
  parameter,
  onChange,
  disabled,
  variables,
}: IServiceOutputParameterProps) => {
  const { sources } = useDialogScriptsAppSelector((state) => state.externalData);

  const variableInputRef = React.useRef<HTMLInputElement>(null);

  const variableRegexp = React.useMemo(() => {
    return getRegExpPatternExcludingStrings([
      ...Object.keys(variables),
      ...(sources.filter((source) => !!source.key).map((source) => source.key) as string[]),
    ]);
  }, [variables, sources]);

  React.useEffect(() => {
    variableInputRef.current?.checkValidity();
  }, [variableInputRef]);

  return (
    <div className={clsx(grids.row)}>
      <Text
        className={clsx(grids.col4)}
        title={[parameter.name, parameter.description, parameter.key].join('\n')}
        ellipsis
        noWrap
      >
        {parameter.name || parameter.key}
      </Text>
      <div className={grids.col4}>
        <Input
          label={getLocaleMessageById('app.modals.form.variableName')}
          value={parameter.variableName}
          onChange={({ value }) => onChange({ ...parameter, variableName: value ?? '' })}
          size={InputSize.Small}
          wrapperClassName={utils.flexGrow1}
          disabled={disabled}
          withDebounce
        />
      </div>
      <div className={grids.col4}>
        <Input
          ref={variableInputRef}
          pattern={variableRegexp}
          label={getLocaleMessageById('app.modals.form.variableCode')}
          value={parameter.variableCode}
          onChange={({ value }, event) => {
            event?.target.checkValidity();
            onChange({ ...parameter, variableCode: value });
            if (!event?.target || !value?.match(variableRegexp)) return;

            event.target.setCustomValidity('');
            event.target.setAttribute('title', '');
          }}
          onInvalid={(event) => {
            const target = event.target as HTMLInputElement;
            const validityState = target.validity;
            let errorMessage = '';
            if (validityState.patternMismatch) {
              errorMessage = getLocaleMessageById('app.error.notUniqueVariable');
            }

            target.setCustomValidity(errorMessage);
            target.title = errorMessage;
          }}
          required={!!parameter.variableCode?.length}
          size={InputSize.Small}
          wrapperClassName={utils.flexGrow1}
          disabled={disabled}
          withDebounce
        />
      </div>
    </div>
  );
};

export default ServiceOutputParameter;
