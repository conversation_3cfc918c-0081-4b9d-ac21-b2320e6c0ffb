import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { getLocaleMessageById } from '../../helpers/localeHelper';

import { IPrioritizationStore } from './prioritization.slice';
import { getAllPrioritizationRules, savePrioritizationRule } from './prioritization.thunk';

const getAllPrioritizationRulesReducers = (
  builder: ActionReducerMapBuilder<IPrioritizationStore>,
) =>
  builder
    .addCase(getAllPrioritizationRules.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllPrioritizationRules.fulfilled, (state, action) => {
      state.loading = false;
      state.rules = action.payload;
    })
    .addCase(getAllPrioritizationRules.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });
const savePrioritizationRuleReducers = (builder: ActionReducerMapBuilder<IPrioritizationStore>) =>
  builder
    .addCase(savePrioritizationRule.pending, (state, action) => {
      state.rules = [
        ...state.rules.map((rule) =>
          rule.id === action.meta.arg.id ? { ...rule, isEnabled: action.meta.arg.isEnabled } : rule,
        ),
      ];
    })
    .addCase(savePrioritizationRule.rejected, (state, action) => {
      state.rules = [
        ...state.rules.map((rule) =>
          rule.id === action.meta.arg.id
            ? { ...rule, isEnabled: !action.meta.arg.isEnabled }
            : rule,
        ),
      ];
      console.error(
        `Error saving prioritization rule ${action.meta.arg.title} with id ${action.meta.arg.id}`,
        action.error,
      );

      const errText = getLocaleMessageById('prioritization.save.error');
      const errArgs = getNotificationArgsByError(errText, action.error);
      getPlatformPopupNotificationManager().notifyError(...errArgs);
    });

const extraReducers = (builder: ActionReducerMapBuilder<IPrioritizationStore>) => {
  getAllPrioritizationRulesReducers(builder);
  savePrioritizationRuleReducers(builder);

  return builder;
};
export default extraReducers;
