import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonSize,
  ButtonVariant,
  Table,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconDropLeft from '@product.front/icons/dist/icons17/MainStuff/IconDropLeft';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';

import { Operator } from '../../../../@types/generated/signalr';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { IOperatorWithGroups } from '../../../../helpers/operatorsGroupsHelper';

import { useOperatorsTableColumns } from './hooks/useOperatorsTableColumns';
import { mapOperatorToTableData } from './mappers/tabledata.mapper';

import styles from './styles.module.scss';

interface IInternalChatChatAddProps {
  selected?: Operator[];
  allOperators: IOperatorWithGroups[];
  onCancel: () => void;
  onSave: (selectedOperators: Operator[]) => void;
  user: Operator;
}

export type PanelType = 'left' | 'right';
export interface IOperatorFotSelect extends IOperatorWithGroups {
  panel: PanelType;
  isSelected: boolean;
}

const InternalChatMembersSelectorModal: React.FC<IInternalChatChatAddProps> = ({
  onCancel,
  onSave,
  selected: defaultSelected,
  allOperators,
  user,
}) => {
  const [list, setList] = React.useState<IOperatorFotSelect[]>(
    allOperators.map((x) => ({
      ...x,
      panel: defaultSelected?.find((y) => x.id === y.id) ? 'right' : 'left',
      isSelected: false,
    })),
  );

  const handleSave = async () => {
    onSave(list.filter((x) => x.panel === 'right'));
  };

  const handleCheck = (operator: Operator) => {
    const isMe = user.id === operator.id;

    if (isMe) return;

    const updatedList = list.map((x) => {
      if (x.id !== operator.id) {
        return x;
      }

      return { ...x, isSelected: !x.isSelected };
    });

    setList(updatedList);
  };

  const handleMove = (from: 'left' | 'right') => () => {
    const updatedList = list.map((x) => {
      if (x.panel !== from || !x.isSelected) {
        return x;
      }

      const panel = (from === 'left' ? 'right' : 'left') as PanelType;
      return { ...x, panel, isSelected: false };
    });
    setList(updatedList);
  };

  const tableColumns = useOperatorsTableColumns();

  return (
    <section>
      <div
        className={clsx(utils.dFlex, utils.justifyContentCenter, utils.alignItemsStart, utils.pY4)}
        style={{ width: '80vw', minWidth: '1000px', minHeight: '540px' }}
      >
        <div className={styles.panel}>
          <Text variant={TextVariant.BodyMedium} as="div">
            {getLocaleMessageById('app.membersSelector.availableOperators')}
          </Text>

          <div className={styles.tableWrapper}>
            <Table
              fixHeader
              data={mapOperatorToTableData(list.filter((op) => op && op.panel === 'left'))}
              columns={tableColumns}
              onRowClick={(row) => {
                handleCheck(row.data.operator);
              }}
            />
          </div>
        </div>
        <div
          className={clsx(
            utils.dFlex,
            utils.flexColumn,
            utils.justifyContentCenter,
            utils.alignItemsCenter,
            utils.p4,
            utils.mT6,
          )}
          style={{ alignSelf: 'center' }}
        >
          <Button
            variant={ButtonVariant.Secondary}
            className={clsx(styles.moveButton, utils.mB2)}
            onClick={handleMove('left')}
          >
            <IconDropRight />
          </Button>
          <Button
            className={clsx(styles.moveButton)}
            variant={ButtonVariant.Secondary}
            onClick={handleMove('right')}
          >
            <IconDropLeft />
          </Button>
        </div>
        <div className={styles.panel}>
          <Text variant={TextVariant.BodyMedium} as="div">
            {getLocaleMessageById('app.membersSelector.chatMembers')}
          </Text>
          <div className={clsx(utils.scrollbar, styles.tableWrapper)}>
            <Table
              fixHeader
              data={mapOperatorToTableData(
                list.filter((op) => op && op.panel === 'right'),
                user.id,
              )}
              columns={tableColumns}
              onRowClick={(row) => {
                handleCheck(row.data.operator);
              }}
            />
          </div>
        </div>
      </div>
      <footer
        className={clsx(
          utils.dFlex,
          utils.justifyContentEnd,
          utils.borderTop,
          utils.mXn6,
          utils.pX6,
          utils.pT4,
          utils.mBn1,
        )}
      >
        <Button size={ButtonSize.Big} variant={ButtonVariant.Secondary} onClick={onCancel}>
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button className={utils.mL4} size={ButtonSize.Big} onClick={handleSave}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </footer>
    </section>
  );
};

export default InternalChatMembersSelectorModal;
