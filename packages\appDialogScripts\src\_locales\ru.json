{"app.common.apply": "Применить", "app.common.cancel": "Отмена", "app.common.date.from": "С", "app.common.date.to": "По", "app.common.yes": "Да", "app.common.save": "Сохранить", "app.common.create": "Создать", "app.common.close": "Закрыть", "app.common.default": "По умолчанию", "app.common.continue": "Продолжить", "app.header.title": "Скрипты диалогов", "app.header.button.create": "Создать", "app.table.empty": "Записей не найдено", "app.table.header.code": "<PERSON>од", "app.table.header.name": "Название", "app.table.header.priority": "Приоритет", "app.table.header.status": "Статус", "app.table.header.createdBy": "Создал", "app.table.header.changedBy": "Изменил", "app.table.header.changedDate": "Дата изменения", "app.table.header.activeFrom": "Действует с", "app.table.header.activeTo": "Действует по", "app.table.header.createdDate": "Дата создания", "app.table.header.recordsNumber": "Количество найденных записей", "app.table.header.dependentScripts": "Используется в", "app.modals.form.name": "Название*", "app.modals.form.code": "Код*", "app.modals.form.description": "Описание", "app.modals.form.status": "Статус*", "app.modals.form.activeFrom": "Начало действия", "app.modals.form.activeTo": "Окончание действия", "app.modals.form.steps": "<PERSON>а<PERSON>и", "app.modals.form.step": "Шаг №", "app.modals.form.addStep": "Добавить шаг", "app.modals.form.stepName": "Название шага", "app.modals.form.stepDescription": "Отображаемый текст", "app.modals.form.displayTextVariant": "Отображаемый текст варианта", "app.modals.form.keyValueButton": "Значения кнопки", "app.modals.form.choice.addVariant": "Добавить другой вариант", "app.modals.form.stepPromptUrl": "Точка расширения", "app.modals.form.answerText": "Текст ответа", "app.modals.form.answerTemplate": "Шабл<PERSON>н", "app.modals.form.transfer": "Переход", "app.modals.form.answerDisplayType": "Отображение ответов", "app.modals.form.variableName": "Название переменной", "app.modals.form.variableArrayName": "Переменная-массив", "app.modals.form.variableCode": "Код переменной", "app.modals.form.addAnswer": "Добавить ответ", "app.modals.form.addTextTemplate": "Добавить шаблон", "app.modals.closeScript.header": "Закрыть редактор?", "app.modals.closeScript.text": "Все внесенные изменения будут утеряны", "app.modals.deleteStep.header": "Удалить шаг?", "app.modals.deleteStep.text": "При удалении шага цепочка будет прервана", "app.modals.deleteRelation.header": "Удалить связь?", "app.modals.statistics.totalAnswers": "Всего ответов", "app.scriptStatus.draft": "Черновик", "app.scriptStatus.published": "Опубликован", "app.scriptStatus.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.scriptStatus.notAvailable": "Недоступен", "app.answerDisplayType.radio": "Один из списка", "app.answerDisplayType.select": "Раскрывающийся список", "app.answerDisplayType.free": "Текст (строка)", "app.answerDisplayType.button": "Кнопка", "app.answerDisplayType.choice": "Выбор", "app.answerDisplayType.file": "<PERSON>а<PERSON><PERSON>", "app.answerDisplayType.template": "Текст (шаблон)", "app.answerDisplayType.none": "Без вариантов ответов", "app.stepTransfer.default": "Следующий шаг", "app.modals.form.answerDisplayTypeNotNone": "С ответами", "app.modals.timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modals.timeoutUnit": "(мин.)", "app.modals.limit": "<PERSON>и<PERSON><PERSON><PERSON>", "app.modals.form.freeAnswer": "Ответ «Свой вариант»: Свободная форма ответа", "app.stepTransfer.end": "Завершение", "modal.error.message": "Ошибка выполнения запроса", "app.script.saveAndPublish": "Сохранить и опубликовать", "app.script.publish": "Опубликовать", "app.script.saveAndArchive": "Сохранить и архивировать", "app.script.archive": "Архивировать", "app.tooltip.archive": "Архивировать", "app.tooltip.notAvailableAction": "Снять с публикации", "app.modals.notAvailableHeader": "Снять с публикации «{scriptName}»?", "app.modals.notAvailableText": "Его статус будет изменен на «Недоступен»", "app.results.notAvailableError": "Ошибка смены доступности", "app.results.notAvailableSuccess": "Доступность успешно изменена", "app.tooltip.copy": "Создать копию", "app.tooltip.edit": "Редактировать", "app.tooltip.play": "Тестовое воспроизведение", "app.tooltip.refresh": "Обновить", "app.tooltip.current": "Обрабатывает обращения", "app.modals.archive.header": "Архивировать «{scriptName}»?", "app.modals.archive.text": "Его нельзя будет восстановить", "app.modals.publish.header": "Опубликовать «{scriptName}»?", "app.modals.dependentScript.header": "Данный сценарий привязан к другому «{scriptName}»?", "app.modals.dependentScripts.header": "Данный сценарий привязан к другим «{scriptNames}»?", "app.modals.form.canBeAutomated": "Автоматический запуск при распределении", "app.modals.form.priority": "Приоритет", "app.modals.formValidation.invalidRange": "Значение должно быть в диапазоне от {min} до {max}", "app.modals.formValidation.valueRequired": "Значение не должно быть пустым", "app.modals.formValidation.variableRequired": "Необходимо выбрать переменную", "app.modals.formValidation.listItemRequired": "Список не должен быть пустым", "app.modals.formValidation.invalidRelation": "Некорректный переход", "app.modals.formValidation.notActualScenario": "Неактуальный сценарий", "app.modals.subscriptProblem.header": "Скрипт «{scriptName}» не может быть опубликован, т.к. содержит вложенные скрипты в статусе {subscriptState}", "app.modals.subscriptProblem.body": "Опубликуйте вложенные скрипты и попробуйте снова", "app.error.notUniqueVariable": "Переменные должны быть уникальными в рамках сценария", "app.name.copy": "Копия", "app.common.collapse": "Свернуть", "app.common.expand": "Развернуть", "app.editor.noName": "Без названия", "app.editor.blockTypeStep": "<PERSON>аг", "app.editor.blockTypeRouter": "Мар<PERSON>рутизатор", "app.editor.blockTypeService": "Действие", "app.editor.blockTypeSubscript": "Скрипт", "app.editor.blockTypeTerminal": "Терминальный", "app.editor.blockTypeRating": "Оценка CSI", "app.editor.blockTypeScenario": "Сценарий", "app.editor.errorStepsInvalid": "Проверьте корректность заполнения шагов (подсвечены красным)", "app.editor.scriptHeader": "Новый скрипт", "app.editor.routerHeader": "Новый маршрутизатор", "app.editor.routerName": "Название", "app.editor.routerDescription": "Описание", "app.editor.routerNewRule": "Новое правило", "app.editor.routerRuleName": "Название правила", "app.editor.routerRuleDefaultTransfer": "Переход по умолчанию", "app.editor.routerRuleVariable": "Переменная", "app.editor.routerRuleOperator": "Оператор", "app.editor.routerRuleValue": "Значение", "app.editor.routerRuleCondition": "Условие", "app.editor.routerRule": "Правило", "app.editor.routerRuleConditionEqual": "Равно", "app.editor.routerRuleConditionNotEqual": "Не равно", "app.editor.routerRuleConditionContains": "Соде<PERSON><PERSON><PERSON>т", "app.editor.routerRuleConditionNotContains": "Не содержит", "app.editor.routerRuleConditionGt": "Больше", "app.editor.routerRuleConditionLt": "Меньше", "app.editor.routerRuleConditionGtEq": "Больше или равно", "app.editor.routerRuleConditionLtEq": "Меньше или равно", "app.editor.routerRuleMoveUp": "Поднять выше", "app.editor.routerRuleMoveDown": "Опустить ниже", "app.editor.serviceTypeHeader": "Тип действия", "app.editor.serviceTypePlaceholder": "Выберите тип действия", "app.editor.serviceInputsHeader": "Параметры запроса", "app.editor.serviceOutputHeader": "Параметры ответа", "app.editor.serviceInputsValue": "Значение", "app.editor.serviceInputsManual": "Ручной ввод", "app.editor.serviceTransferHeader": "Переход", "app.editor.serviceTransferSuccess": "Успех", "app.editor.serviceTransferError": "Ошибка", "app.editor.scripinformation.noDependentScripts": "Не используется в других сценариях", "app.editor.scripinformation.dependentScripts": "Используется в сценариях", "app.results.saveSuccess": "Успешно сохранено", "app.results.saveError": "Ошибка сохранения", "app.results.publishSuccess": "Успешно опубликовано", "app.results.publishError": "Ошибка публикации", "app.results.archiveSuccess": "Успешно заархивировано", "app.results.archiveError": "Ошибка архивации", "app.editor.toggleAutoRelations": "Связывать автоматически", "app.export.errorDescription": "Попробуйте повторить выгрузку через некоторое время", "app.import.error": "Ошибка загрузки", "app.import.errorDescription": "Попробуйте повторить загрузку через некоторое время", "app.common.OK": "OK", "app.tooltip.export": "Экспортировать", "app.tooltip.import": "Импортировать", "app.editor.step.addAttachment": "Добавить вложение", "app.editor.step.addAttachmentFromVariable": "Добавить вложение из переменной", "app.editor.step.deleteAll": "Удалить всё", "app.editor.step.attachmentsFromVariables": "Вложения из переменных", "app.editor.step.subscriptTitle": "Скрипт", "app.editor.step.subscriptSelect": "Выберите скрипт", "app.editor.step.scenarioSelect": "Выберите сценарий", "app.editor.step.subscriptDescription": "Описание скрипта", "app.editor.step.scenarioDescription": "Описание сценария", "app.editor.step.subscriptStatus": "Статус", "app.editor.step.scenarioStatus": "Статус сценария", "app.error.notUniqueAnswer": "Ответы должны быть уникальными в рамках шага", "app.table.header.rating": "<PERSON>ей<PERSON>инг", "app.table.header.runsNumber": "Запусков", "app.table.header.abortNumber": "Прерываний", "app.editor.step.variableSource": "Источник", "app.editor.step.labelIsSkippable": "Пропускать шаг для предзаполненных значений", "app.editor.step.promptVariables": "Переменные: {variables}", "app.modals.form.firstStep": "Сделать стартовым", "app.modals.form.answer.actions.delete": "Удалить", "app.editor.step.terminalSubject": "Тематика", "app.editor.step.terminalAction": "Действие", "app.editor.step.terminalUpdateDataHeader": "Обновить данные обращения", "app.editor.step.terminalUpdateDataVariable": "Переменная", "app.editor.step.terminalUpdateDataAttribute": "Атрибут", "app.editor.step.terminalUpdateDataVariableAdd": "Добавить атрибут", "app.editor.step.terminalSubjectHeader": "Установить тематику обращения", "app.editor.step.terminalActionHeader": "Действие с обращением", "app.table.footer.count": "Количество записей", "app.step.service.delete": "Удалить", "app.step.service.serviceUnavailable": "Сервис автоматизации с ID {automationServiceId} не существует или не активен", "app.modals.forms.addTag": "Добавить тег", "app.editor.step.labelIsBackButtonAvailable": "Шаг назад доступен", "app.editor.terminalSubjectOrActionRequired": "Необходимо указать действие с обращением или тематику", "app.common.from": "С", "app.common.to": "По", "app.common.onlyEmpty": "Только пустые", "app.common.onlyNotEmpty": "Только заполненные", "app.editor.serviceFilesHeader": "Файлы запроса", "app.automationService.variableFileAdd": "Добавить переменную", "app.modals.form.variableFileName": "Переменная с файлами", "app.automationService.requestFileName": "Имя файла", "app.automationService.variableAdd": "Добавить переменную", "app.error.notUniqueFileVariable": "Переменная не уникальна в рамках шага"}