import React from 'react';

import clsx from 'clsx';

import { Collapsible, grids, IconButton, Input, Radio, utils } from '@product.front/ui-kit';

import IconEye from '@product.front/icons/dist/icons17/Sorting/IconEye';
import IconEyeOff from '@product.front/icons/dist/icons17/Sorting/IconEyeOff';

import {
  AutomationServiceAuthType,
  IFrontAutomationService,
} from '../../../@types/automationService.types';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { formFieldConstraintsResource } from '../../../resources/automationService.resources';

import FieldsGroupHeader from './FieldsGroupHeader';
import FieldsGroupWrapper from './FieldsGroupWrapper';

interface IBlockAuthenticationProps {
  automationService: IFrontAutomationService;
  onFieldValueChange: (fieldName: string) => ({ value }: { value?: any }) => void;
  disabled: boolean;
  readonly: boolean;
}

const BlockAuthentication: React.FC<IBlockAuthenticationProps> = ({
  automationService,
  onFieldValueChange,
  disabled,
  readonly,
}) => {
  const [showPassword, setShowPassword] = React.useState(false);

  return (
    <FieldsGroupWrapper>
      <FieldsGroupHeader>{getLocaleMessageById('app.automationService.auth')}</FieldsGroupHeader>

      <div className={clsx(utils.dFlex, utils.gap4)}>
        <Radio
          value={AutomationServiceAuthType.NoAuth}
          checked={automationService.authType === AutomationServiceAuthType.NoAuth}
          onChange={onFieldValueChange('authType')}
          disabled={disabled}
          name="authType"
          label={getLocaleMessageById('app.automationService.authNoAuth')}
          readOnly={readonly}
        />
        <Radio
          value={AutomationServiceAuthType.Basic}
          checked={automationService.authType === AutomationServiceAuthType.Basic}
          onChange={onFieldValueChange('authType')}
          disabled={disabled}
          name="authType"
          label={getLocaleMessageById('app.automationService.authBasic')}
          readOnly={readonly}
        />
        <Radio
          value={AutomationServiceAuthType.Bearer}
          checked={automationService.authType === AutomationServiceAuthType.Bearer}
          onChange={onFieldValueChange('authType')}
          disabled={disabled}
          name="authType"
          label={getLocaleMessageById('app.automationService.authBearer')}
          readOnly={readonly}
        />
        <Radio
          value={AutomationServiceAuthType.ApiKey}
          checked={automationService.authType === AutomationServiceAuthType.ApiKey}
          onChange={onFieldValueChange('authType')}
          disabled={disabled}
          name="authType"
          label={getLocaleMessageById('app.automationService.authKey')}
          readOnly={readonly}
        />
      </div>
      <Collapsible
        activatorComponent={<></>}
        open={automationService.authType !== AutomationServiceAuthType.NoAuth}
      >
        {automationService.authType === AutomationServiceAuthType.Basic && (
          <div className={clsx(utils.mT4, grids.row)}>
            <Input
              wrapperClassName={grids.col5}
              label={getLocaleMessageById('app.automationService.authBasicLogin')}
              disabled={disabled}
              readOnly={readonly}
              value={automationService.authLogin}
              onChange={onFieldValueChange('authLogin')}
              {...(formFieldConstraintsResource.authLogin || {})}
            />
            <Input
              type={showPassword ? 'text' : 'password'}
              wrapperClassName={grids.col5}
              label={getLocaleMessageById('app.automationService.authBasicPassword')}
              disabled={disabled}
              readOnly={readonly}
              value={automationService.authPassword}
              onChange={onFieldValueChange('authPassword')}
              postContent={
                <IconButton className={utils.mRn2} onClick={() => setShowPassword((c) => !c)}>
                  {showPassword ? <IconEye /> : <IconEyeOff />}
                </IconButton>
              }
              {...(formFieldConstraintsResource.authPassword || {})}
            />
          </div>
        )}
        {automationService.authType === AutomationServiceAuthType.Bearer && (
          <div className={clsx(utils.mT4, grids.row)}>
            <Input
              wrapperClassName={grids.col7}
              label={`${getLocaleMessageById('app.automationService.authBearerToken')}*`}
              disabled={disabled}
              readOnly={readonly}
              value={automationService.authToken}
              onChange={onFieldValueChange('authToken')}
              {...(formFieldConstraintsResource.authToken || {})}
            />
          </div>
        )}
        {automationService.authType === AutomationServiceAuthType.ApiKey && (
          <div className={clsx(utils.mT4, grids.row)}>
            <Input
              wrapperClassName={grids.col5}
              label={`${getLocaleMessageById('app.automationService.authKeyHeader')}*`}
              disabled={disabled}
              readOnly={readonly}
              value={automationService.authHeaderName}
              onChange={onFieldValueChange('authHeaderName')}
              {...(formFieldConstraintsResource.authHeaderName || {})}
            />
            <Input
              required
              wrapperClassName={grids.col5}
              label={`${getLocaleMessageById('app.automationService.authKeyHeaderValue')}*`}
              disabled={disabled}
              readOnly={readonly}
              value={automationService.authHeaderValue}
              onChange={onFieldValueChange('authHeaderValue')}
              {...(formFieldConstraintsResource.authHeaderValue || {})}
            />
          </div>
        )}
      </Collapsible>
    </FieldsGroupWrapper>
  );
};

export default BlockAuthentication;
