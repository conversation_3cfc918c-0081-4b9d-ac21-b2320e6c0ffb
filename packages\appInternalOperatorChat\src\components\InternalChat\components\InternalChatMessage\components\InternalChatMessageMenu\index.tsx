import React from 'react';

import { Menu, MenuItem, utils } from '@product.front/ui-kit';

import IconReply from '@product.front/icons/dist/icons17/MainStuff/IconReply';

import { Message } from '../../../../../../@types/generated/signalr';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { useInternalChatDispatch } from '../../../../../../store/hooks';
import { setReplyMessage } from '../../../../../../store/internalChat.slice';

interface IMessageStatusProps {
  message: Message;
}

const InternalChatMessageMenu = ({ message }: IMessageStatusProps): JSX.Element => {
  const dispatch = useInternalChatDispatch();
  return (
    <Menu style={{ width: '200px' }}>
      <MenuItem
        onClick={() => {
          dispatch(setReplyMessage(message));
          document.querySelector<HTMLTextAreaElement>('.qa-chat-input')?.focus();
        }}
      >
        <IconReply className={utils.mR2} />
        {getLocaleMessageById('app.chat.reply')}
      </MenuItem>
    </Menu>
  );
};

export default InternalChatMessageMenu;
