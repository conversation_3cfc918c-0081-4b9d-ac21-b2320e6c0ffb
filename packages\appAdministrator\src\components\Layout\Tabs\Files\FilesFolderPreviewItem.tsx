import React from 'react';

import { IButtonProps } from '@product.front/ui-kit/dist/types/components/Button/Button';
import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  FileIcon,
  FileIconSize,
  FolderIcon,
  FolderIconSize,
  FolderIconState,
  getColorForExtension,
  Text,
  utils,
} from '@product.front/ui-kit';

import { FrontFileOrFolder } from '../../../../@types/files';
import { isFolder } from '../../../../helpers/files';
import { getFoldersAndFiles } from '../../../../store/files/files.thunk';
import { useAdministratorAppDispatch } from '../../../../store/hooks';

import { handleDropOnFolder } from './dnd.helper';

import styles from './styles.module.scss';

interface IFilesFolderPreviewItemProps extends IButtonProps {
  folderOrFile: FrontFileOrFolder;
}

const FilesFolderPreviewItem: React.FC<IFilesFolderPreviewItemProps> = ({
  folderOrFile,
  ...rest
}) => {
  const dispatch = useAdministratorAppDispatch();

  return (
    <Button
      variant={ButtonVariant.Secondary}
      style={{ width: 100, height: 124, maxHeight: 'unset' }}
      className={clsx(
        utils.pX3,
        utils.pY4,
        utils.borderNone,
        utils.dInlineFlex,
        utils.flexColumn,
        utils.justifyContentStart,
      )}
      // TODO: FIX this problems if they are
      // eslint-disable-next-line jsx-a11y/tabindex-no-positive
      tabIndex={1}
      // eslint-disable-next-line jsx-a11y/no-autofocus
      autoFocus={rest.active}
      draggable
      onDragStart={(e) => {
        e.dataTransfer.setData(
          isFolder(folderOrFile)
            ? 'application/webarm-trusted-folder'
            : 'application/webarm-trusted-file',
          folderOrFile.path,
        );
        e.dataTransfer.effectAllowed = 'move';
      }}
      onDragOver={(e) => {
        e.preventDefault();
        if (!isFolder(folderOrFile)) return;
        e.dataTransfer.dropEffect = 'move';
        (e.currentTarget as HTMLButtonElement).classList.add(styles.dragHover);
      }}
      onDragLeave={(e) => {
        e.preventDefault();
        if (!isFolder(folderOrFile)) return;
        e.dataTransfer.dropEffect = 'move';
        (e.currentTarget as HTMLButtonElement).classList.remove(styles.dragHover);
      }}
      onDrop={
        !isFolder(folderOrFile)
          ? () => {}
          : handleDropOnFolder(folderOrFile, () => {
              dispatch(getFoldersAndFiles());
            })
      }
      {...rest}
    >
      <div className={utils.flexShrink0}>
        {isFolder(folderOrFile) ? (
          <FolderIcon
            size={FolderIconSize.Large}
            state={folderOrFile.items.length ? FolderIconState.WithFile : FolderIconState.Empty}
          />
        ) : (
          <FileIcon
            size={FileIconSize.Large}
            iconColor={getColorForExtension(folderOrFile.extension)}
          >
            {folderOrFile.extension.toUpperCase()}
          </FileIcon>
        )}
      </div>

      <Text
        as="div"
        className={clsx(utils.textCenter, utils.w100, styles.twoLine)}
        title={folderOrFile.name}
      >
        {folderOrFile.name}
      </Text>
    </Button>
  );
};

export default FilesFolderPreviewItem;
