import React from 'react';

import { useAdministratorAppSelector } from '../../../../../store/hooks';

import TreeItem, { ITreeItem } from './TreeItem';

interface ISubjectsTreeViewProps {
  searchText: string;
  shouldSearchByName: boolean;
  shouldSearchByText: boolean;
}

const SubjectsTreeView = ({
  searchText,
  shouldSearchByName,
  shouldSearchByText,
}: ISubjectsTreeViewProps) => {
  const { subjects } = useAdministratorAppSelector((store) => store.subjects);

  const treeSubjects = React.useMemo(() => {
    const subjectsArray = Object.values(subjects);
    const rootSubjectsMap: typeof subjects = {};
    subjectsArray.forEach((subject) => {
      if (subject.parentId) return;

      rootSubjectsMap[subject.id] = subject;
    });

    const addItemsToSubject = (subject: (typeof rootSubjectsMap)[number]): ITreeItem => ({
      ...subject,
      items: subjectsArray.filter((child) => child.parentId === subject.id).map(addItemsToSubject),
    });

    return Object.values(rootSubjectsMap).map(addItemsToSubject);
  }, [subjects]);

  return (
    <>
      {treeSubjects
        .toSorted((a, b) => a.name.localeCompare(b.name))
        .map((subject) => (
          <TreeItem
            key={subject.id}
            subject={subject}
            level={0}
            searchText={searchText}
            shouldSearchByName={shouldSearchByName}
            shouldSearchByText={shouldSearchByText}
          />
        ))}
    </>
  );
};

export default SubjectsTreeView;
