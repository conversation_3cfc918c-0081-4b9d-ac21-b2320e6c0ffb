import { Draft, PayloadAction } from '@reduxjs/toolkit';

import {
  AddedToChatNotificationPayload,
  ChatCreatedNotificationPayload,
  ChatDeletedNotificationPayload,
  ChatUpdatedNotificationPayload,
  NotificationType,
  OperatorStatusChangedNotificationPayload,
  RemovedFromChatNotificationPayload,
} from '../../@types/generated/signalr';
import { IChat } from '../../@types/signalRTypes';
import { IInternalChatStore } from '../internalChat.slice';

import { NotificationActionPayload } from './notifications.types';

const chatNotificationReducer = (
  state: Draft<IInternalChatStore>,
  action: PayloadAction<NotificationActionPayload & { userId?: string }>,
) => {
  switch (action.payload.type) {
    case NotificationType.AddedToChat: {
      const pl = action.payload.payload as AddedToChatNotificationPayload;
      const alreadyChat = state.chats.find((chat) => pl.chat?.id && chat.id === pl.chat.id);

      if (!alreadyChat) {
        state.chats = [
          { ...pl.chat, participants: pl.addedUsers, isSelected: false },
          ...state.chats,
        ];
      } else {
        alreadyChat.participants = [
          ...(state.chats.find((chat) => pl.chat?.id && chat.id === pl.chat.id)?.participants ||
            []),
          ...(pl.addedUsers || []),
        ];
        alreadyChat.lastMessage = pl.chat?.lastMessage;
        alreadyChat.isSelected = true;
      }
      break;
    }
    case NotificationType.ChatUpdated: {
      const pl = action.payload.payload as ChatUpdatedNotificationPayload;
      state.chats = state.chats.map((chat) => {
        if (chat.id === pl.partialChat?.id) {
          return { ...chat, ...pl.partialChat };
        }

        return chat;
      });
      break;
    }
    case NotificationType.RemovedFromChat: {
      const pl = action.payload.payload as RemovedFromChatNotificationPayload;
      const isMeKickedOut = !!pl.removedUsers?.find((u) => action.payload.userId === u.id);

      if (isMeKickedOut) {
        state.chats = state.chats.filter((chat) => chat.id !== pl.chatId);
        return;
      }

      const targetChat = state.chats.find((chat) => chat.id === pl.chatId);

      if (targetChat) {
        const currentParticipants = targetChat?.participants || [];
        const removedParticipantsIds = pl.removedUsers?.map((p) => p.id) || [];
        const filteredParticipants = currentParticipants.filter(
          (participant) => !removedParticipantsIds.includes(participant.id),
        );

        targetChat.participants = filteredParticipants;
      }

      break;
    }
    case NotificationType.OperatorStatusChanged: {
      const pl = action.payload.payload as OperatorStatusChangedNotificationPayload;
      const targetOperator = state.allOperators.find((op) => op.id === pl.operatorId);
      if (targetOperator) {
        targetOperator.status = pl.status;
      }
      break;
    }
    case NotificationType.ChatCreated: {
      const pl = action.payload.payload as ChatCreatedNotificationPayload;
      const newChats = [
        {
          ...pl.createdPartialChat,
          participants: [],
          isSelected: pl.createdPartialChat?.ownerId === action.payload.initiatorId,
          lastMessage: null,
        } as unknown as IChat,
        ...state.chats.map((ch) => ({ ...ch, isSelected: false })),
      ];
      state.chats = newChats;
      state.messages = [];
      state.page = 0;
      state.isChatHistoryEndReached = false;
      break;
    }
    case NotificationType.ChatDeleted: {
      const pl = action.payload.payload as ChatDeletedNotificationPayload;
      const isCurrentChat = pl.chatId === state.chats.find((chat) => chat.isSelected)?.id;

      state.chats = state.chats
        .filter((chat) => chat.id !== pl.chatId)
        .map((chat, index) => ({ ...chat, isSelected: index === 0 }));
      if (isCurrentChat) {
        state.messages = [];
        state.page = 0;
        state.isChatHistoryEndReached = false;
      }

      break;
    }
    default:
      throw new Error('Not implemented notification handler for ' + action.payload.type);
  }
};

export default chatNotificationReducer;
