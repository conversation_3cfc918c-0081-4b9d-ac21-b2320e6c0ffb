import { Viewport } from 'reactflow';

export const getXYByClientEvent = (
  event: any,
  { bounds, viewport }: { bounds?: DOMRect; viewport?: Viewport },
): { x: number; y: number } => {
  if (!viewport) {
    throw new Error(`Can't define viewport`);
  }
  if (!bounds) {
    throw new Error(`Can't define bounds`);
  }

  const zoom = viewport.zoom || 1;
  const zoomFactor = 1 / zoom;

  const positionX = (event.clientX - bounds.left - viewport.x) * zoomFactor;
  const positionY = (event.clientY - bounds.top - viewport.y) * zoomFactor;

  return {
    x: positionX,
    y: positionY,
  };
};
