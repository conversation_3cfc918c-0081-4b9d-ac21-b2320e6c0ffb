import React from 'react';

import clsx from 'clsx';

import { FloatingTooltip, grids, IconButton, OverlayLoader, utils } from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconCopy from '@product.front/icons/dist/icons17/MainStuff/IconCopy';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import { getCurrentOperatorFullNameString } from '@monorepo/common/src/managers/currentOperatorManager';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { ConnectionType, IOffer } from '../../@types/offer';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../store/hooks';
import { setSelectedOffer } from '../../store/offers/offers.slice';
import {
  createOrUpdateOffer,
  deleteOffer,
  getNeuroNetTemplates,
  getOffers,
  getWhatsAppTemplates,
} from '../../store/offers/offers.thunk';
import { getOfferTypes } from '../../store/settings/settings.thunk';

import OfferForm from './OfferForm';
import TableBody from './TableBody';

export const emptyOffer: IOffer = {
  id: '',
  name: '',
  description: '',
  externalReference: '',
  text: '',
  script: '',
  parameters: '',
  availableForOperator: true,
  connectionType: ConnectionType.Auto,
  updateTime: new Date().toISOString(),
  dateFrom: new Date().toISOString().split('T')[0],
  dateTo: new Date().toISOString().split('T')[0],
  author: getCurrentOperatorFullNameString(),
  type: null,
  automaticType: null,
  automaticOfferValue: null,
};

const TabOffers = () => {
  const dispatch = useMarketingAppDispatch();

  const { selectedOffer, loading, saving, whatsappTemplates, neuronetTemplates } =
    useMarketingAppSelector((state) => state.offers);
  const { offerTypes } = useMarketingAppSelector((state) => state.settings);

  React.useEffect(() => {
    dispatch(setSelectedOffer(null));
    dispatch(getOffers());
    dispatch(getOfferTypes());
    dispatch(getWhatsAppTemplates());
    dispatch(getNeuroNetTemplates());
  }, [dispatch]);

  const disabledButtons = !selectedOffer?.id;

  return (
    <div className={clsx(grids.row, utils.h100, utils.w100, utils.gap0)}>
      <div className={clsx(grids.col7, utils.h100, utils.dFlex, utils.flexColumn)}>
        <div className={clsx(utils.pY2, utils.pX4, utils.borderBottom)}>
          <div
            className={clsx(utils.dFlex, utils.gap2, utils.mLauto)}
            style={{ width: 'min-content' }}
          >
            <FloatingTooltip tooltip={getLocaleMessageById('app.offers.buttons.add.tooltip')}>
              <IconButton onClick={() => dispatch(setSelectedOffer(emptyOffer))}>
                <IconAdd />
              </IconButton>
            </FloatingTooltip>
            <FloatingTooltip tooltip={getLocaleMessageById('app.offers.buttons.copy.tooltip')}>
              <IconButton
                onClick={() =>
                  selectedOffer &&
                  showConfirmModal({
                    header: getLocaleMessageById('app.offer.copyHeader'),
                    text: getLocaleMessageById('app.offer.copyText', {
                      name: selectedOffer.name,
                    }),
                    onConfirm: () => dispatch(setSelectedOffer({ ...selectedOffer, id: '' })),
                    canCancel: true,
                  })
                }
                disabled={disabledButtons}
              >
                <IconCopy />
              </IconButton>
            </FloatingTooltip>
            <FloatingTooltip tooltip={getLocaleMessageById('app.offers.buttons.delete.tooltip')}>
              <IconButton
                onClick={() => {
                  if (!selectedOffer) return;
                  const isCampaignLinkedToOffer = !!selectedOffer.campaigns?.length;
                  if (isCampaignLinkedToOffer) {
                    const err = getLocaleMessageById('app.offers.error.noCascade');
                    return getPlatformPopupNotificationManager().notifyError(err);
                  }

                  showConfirmModal({
                    header: getLocaleMessageById('app.offer.deleteHeader'),
                    text: getLocaleMessageById('app.offer.deleteText', {
                      name: selectedOffer.name,
                    }),
                    onConfirm: () => dispatch(deleteOffer(selectedOffer.id)),
                    canCancel: true,
                  });
                }}
                disabled={disabledButtons}
              >
                <IconTrash />
              </IconButton>
            </FloatingTooltip>
          </div>
        </div>
        <OverlayLoader
          wrapperClassName={clsx(
            utils.flexGrow1,
            utils.flexBasis0,
            utils.overflowAuto,
            utils.scrollbar,
            utils.w100,
          )}
          loading={loading}
        >
          <TableBody />
        </OverlayLoader>
      </div>
      <div className={clsx(grids.col5, utils.h100, utils.borderLeft)}>
        <OfferForm
          selectedOffer={selectedOffer}
          saving={saving}
          offerTypes={offerTypes}
          onCancel={() => dispatch(setSelectedOffer(null))}
          onSubmit={(offer) => dispatch(createOrUpdateOffer(offer))}
          whatsAppTemplates={whatsappTemplates}
          neuroNetTemplates={neuronetTemplates}
        />
      </div>
    </div>
  );
};

export default TabOffers;
