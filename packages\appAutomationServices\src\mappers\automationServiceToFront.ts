import {
  AutomationServiceAuthType,
  AutomationServiceHeader,
  AutomationServiceParameterDescription,
  AutomationServiceRequestBodyType,
  AutomationServiceRequestType,
  AutomationServiceResponseBodyType,
  AutomationServiceStatus,
  AutomationServiceType,
  IFrontAutomationService,
} from '../@types/automationService.types';
import {
  AuthTypes,
  ContentTypes,
  HttpMethods,
  RestServiceDto,
  ServiceParamDto,
  ServiceParamTypes,
  ServiceStatuses,
  ServiceType,
  StringStringKeyValuePair,
} from '../@types/generated/automationServices';

const mapBackServiceTypeToFront = (backType: ServiceType): AutomationServiceType => {
  const map = {
    [ServiceType.Rest]: AutomationServiceType.REST,
    [ServiceType.Soap]: AutomationServiceType.SOAP,
    [ServiceType.Plugin]: AutomationServiceType.Plugin,
    [ServiceType.Script]: AutomationServiceType.Code,
  };

  return map[backType];
};

const mapBackServiceAuthTypeToFront = (backAuthType: AuthTypes): AutomationServiceAuthType => {
  const map = {
    [AuthTypes.None]: AutomationServiceAuthType.NoAuth,
    [AuthTypes.Basic]: AutomationServiceAuthType.Basic,
    [AuthTypes.Bearer]: AutomationServiceAuthType.Bearer,
    [AuthTypes.ApiKey]: AutomationServiceAuthType.ApiKey,
  };

  return map[backAuthType];
};

const mapBackServiceRequestTypeToFront = (
  backRequestType: HttpMethods,
): AutomationServiceRequestType => {
  const map = {
    [HttpMethods.GET]: AutomationServiceRequestType.GET,
    [HttpMethods.POST]: AutomationServiceRequestType.POST,
  };

  return map[backRequestType];
};

const mapBackServiceRequestBodyTypeToFront = (
  backRequestBodyType: ContentTypes,
): AutomationServiceRequestBodyType => {
  const map = {
    [ContentTypes.JSON]: AutomationServiceRequestBodyType.JSON,
    [ContentTypes.XML]: AutomationServiceRequestBodyType.XML,
    [ContentTypes.Text]: AutomationServiceRequestBodyType.Text,
    [ContentTypes.File]: AutomationServiceRequestBodyType.JSON,
  };

  return map[backRequestBodyType];
};

const mapBackServiceResponseBodyTypeToFront = (
  backResponseBodyType: ContentTypes,
): AutomationServiceResponseBodyType => {
  const map = {
    [ContentTypes.JSON]: AutomationServiceResponseBodyType.JSON,
    [ContentTypes.XML]: AutomationServiceResponseBodyType.XML,
    [ContentTypes.Text]: AutomationServiceResponseBodyType.Text,
    [ContentTypes.File]: AutomationServiceResponseBodyType.File,
  };

  return map[backResponseBodyType];
};

const mapBackServiceStatusToFront = (backStatus: ServiceStatuses): AutomationServiceStatus => {
  const map = {
    [ServiceStatuses.Active]: AutomationServiceStatus.Active,
    [ServiceStatuses.Template]: AutomationServiceStatus.Draft,
    [ServiceStatuses.Deleted]: AutomationServiceStatus.Archive,
  };

  return map[backStatus];
};

const mapBackServiceParameterToFront = (
  requestParameters: ServiceParamDto,
): AutomationServiceParameterDescription => {
  return {
    key: requestParameters.key ?? performance.now().toString(),
    description: requestParameters.name ?? '',
    reachDescription: requestParameters.description ?? '',
  };
};
const mapBackServiceRequestHeadersToFront = (
  requestHeaders: StringStringKeyValuePair,
): AutomationServiceHeader => {
  return {
    _key: performance.now().toString(),
    name: requestHeaders.key ?? '',
    value: requestHeaders.value ?? '',
  };
};

export const mapBackServiceToFront = (backService: RestServiceDto): IFrontAutomationService => {
  return {
    id: backService.id,
    status: mapBackServiceStatusToFront(backService.status ?? ServiceStatuses.Template),
    name: backService.name ?? '',
    description: backService.description ?? '',
    code: backService.code ?? '',
    system: backService.system ?? null,
    type: mapBackServiceTypeToFront(backService.serviceType),
    requestType: mapBackServiceRequestTypeToFront(backService.method ?? HttpMethods.GET),
    requestBodyType: mapBackServiceRequestBodyTypeToFront(
      backService.contentType ?? ContentTypes.JSON,
    ),
    requestParameters:
      backService.parameters
        ?.filter((p) => p.type === ServiceParamTypes.Input)
        ?.map(mapBackServiceParameterToFront) ?? [],
    requestHeaders: backService.headers?.map(mapBackServiceRequestHeadersToFront) ?? [],
    canUploadFiles: backService.canUploadFiles ?? false,
    requestUrl: backService.url ?? '',
    requestBody: backService.body ?? '',
    authType: mapBackServiceAuthTypeToFront(backService.authProps?.authType ?? AuthTypes.None),
    authLogin: backService.authProps?.login ?? '',
    authPassword:
      backService.authProps?.authType == AuthTypes.Basic
        ? (backService.authProps?.password ?? '')
        : '',
    authToken:
      backService.authProps?.authType == AuthTypes.Bearer
        ? (backService.authProps?.password ?? '')
        : '',
    authHeaderValue:
      backService.authProps?.authType == AuthTypes.ApiKey
        ? (backService.authProps?.password ?? '')
        : '',
    authHeaderName: backService.authProps?.header ?? '',
    responseBody: backService.response?.body ?? '',
    responseBodyType: mapBackServiceResponseBodyTypeToFront(
      backService.response?.contentType || ContentTypes.JSON,
    ),
    responseParameters:
      backService.parameters
        ?.filter((p) => p.type === ServiceParamTypes.Output)
        ?.map(mapBackServiceParameterToFront) ?? [],
    createdBy: backService.createdBy ?? '-',
    createdDate: backService.timeCreated ?? '',
    changedBy: backService.lastUpdateBy ?? '-',
    changedDate: backService.timeLastUpdate ?? '',
    publishedBy: backService.activatedBy ?? '-',
    publishedDate: backService.timeActivated ?? '',
  };
};
