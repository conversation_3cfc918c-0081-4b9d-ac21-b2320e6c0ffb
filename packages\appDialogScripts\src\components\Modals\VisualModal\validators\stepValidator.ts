import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import {
  FrontStepType,
  IFrontAnswer,
  IFrontAnswerInvalidReasons,
  IFrontStep,
  IChoiceStepAnswer,
  IChoiceStepDetailsInvalidReason,
  IFrontStepInvalidReasons,
  IVariableCode,
} from '@monorepo/dialog-scripts/src/@types/script';

import { createEmptyChoiceStepAnswer } from '../../../../helpers/createObjectFactory';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  activeScriptCheck,
  getVariableRegExpPatternByScriptSteps,
} from '../../../../helpers/scriptHelpers';

import { getAnswerInvalidReasons, getAnswerTransformInvalidReasons } from './answerValidator';
import { getRuleDeepInvalidReasons, getRuleInvalidReasons } from './ruleValidator';

const valueRequired = getLocaleMessageById('app.modals.formValidation.valueRequired');
const limitInvalidRange = getLocaleMessageById('app.modals.formValidation.invalidRange', {
  min: 1,
  max: 100,
});
const timeoutInvalidRange = getLocaleMessageById('app.modals.formValidation.invalidRange', {
  min: 1,
  max: 100,
});
const invalidRelationMessage = getLocaleMessageById('app.modals.formValidation.invalidRelation');

const validateChoiceStepDetails = (
  choiceStepAnswer?: IChoiceStepAnswer,
): IChoiceStepDetailsInvalidReason => {
  const reasons: IChoiceStepDetailsInvalidReason = {};

  const verifiableAnswer = choiceStepAnswer ?? createEmptyChoiceStepAnswer();

  if (!verifiableAnswer.displayTextVariant) {
    reasons.displayTextVariant = valueRequired;
  }
  if (!verifiableAnswer.keyValueButton) {
    reasons.keyValueButton = valueRequired;
  }

  return reasons;
};

const deepValidateChoiceStepDetails = (
  choiceStepAnswer?: IChoiceStepAnswer,
): IChoiceStepDetailsInvalidReason => {
  const reasons: IChoiceStepDetailsInvalidReason = {};

  const verifiableAnswer = choiceStepAnswer ?? createEmptyChoiceStepAnswer();

  if (!verifiableAnswer.displayTextVariant) {
    reasons.displayTextVariant = valueRequired;
  }
  if (!verifiableAnswer.keyValueButton) {
    reasons.keyValueButton = valueRequired;
  }
  if (!verifiableAnswer.variableArrayCode) {
    reasons.variableArrayCode = getLocaleMessageById('app.modals.formValidation.variableRequired');
  }
  if (!verifiableAnswer.nextStepCode) {
    reasons.nextStepCode = invalidRelationMessage;
  }

  return reasons;
};

const validateVariableCodes = (
  variableCodes: IVariableCode[] | undefined,
): Record<string, string> => {
  const variableInvalidReasons: Record<string, string> = {};

  if (!variableCodes) {
    return variableInvalidReasons;
  }

  variableCodes.forEach((varCode, index, allVariables) => {
    if (!varCode.variableCode) {
      variableInvalidReasons[varCode._key] = valueRequired;
    }

    if (
      allVariables.some(
        (otherVar, otherIndex) =>
          index !== otherIndex && otherVar.variableCode === varCode.variableCode,
      )
    ) {
      variableInvalidReasons[varCode._key] = getLocaleMessageById(
        'app.error.notUniqueFileVariable',
      );
    }
  });

  return variableInvalidReasons;
};

const validateStep = (step: IFrontStep, steps: IFrontStep[]) => {
  if (step.type !== FrontStepType.Step && step.type !== FrontStepType.Rating) return;

  let reasons: IFrontStepInvalidReasons = {};

  if (!step.answerDisplayType || [AnswerTypes.None].includes(step.answerDisplayType)) {
    return;
  }

  const regexp = getVariableRegExpPatternByScriptSteps(
    steps.filter((currentStep) => {
      return currentStep.code !== step.code && step.answerDisplayType !== AnswerTypes.None;
    }),
  );
  if (step.variable?.length && !new RegExp(regexp, 'ig').test(step.variable)) {
    reasons.variable = getLocaleMessageById('app.error.notUniqueVariable');
  }

  if (step.answerDisplayType === AnswerTypes.Choice) {
    const choiceStepDetailsInvalidReason = validateChoiceStepDetails(step.choiceStepAnswer);

    if (Object.keys(choiceStepDetailsInvalidReason).length > 0) {
      reasons.choiceStepDetailsInvalidReason = choiceStepDetailsInvalidReason;
    }
  }

  if ([AnswerTypes.TextInput].includes(step.answerDisplayType)) {
    return reasons;
  }

  if (![AnswerTypes.File, AnswerTypes.Choice, AnswerTypes.None].includes(step.answerDisplayType)) {
    const answersInvalidReasons: IFrontStepInvalidReasons['answersInvalidReasons'] = {};
    step.answers?.forEach((answer) => {
      const invalidReasons = getAnswerInvalidReasons(answer, step.answers!);
      if (Object.keys(invalidReasons).length === 0) return;

      answersInvalidReasons[answer.id] = invalidReasons ?? {};
    });

    if (Object.keys(answersInvalidReasons).length > 0) {
      reasons = { ...reasons, answersInvalidReasons };
    }

    return reasons;
  }

  return reasons;
};

const validateRouter = (step: IFrontStep, nextStep?: IFrontStep) => {
  if (step.type !== FrontStepType.Router) return;

  let reasons: IFrontStepInvalidReasons = {};

  const rulesInvalidReasons: IFrontStepInvalidReasons['rulesInvalidReasons'] = {};
  step.rules?.forEach((rule) => {
    const invalidReasons = getRuleInvalidReasons(rule, nextStep);
    if (Object.keys(invalidReasons).length === 0) return;

    rulesInvalidReasons[rule.id] = invalidReasons ?? {};
  });

  if (Object.keys(rulesInvalidReasons).length > 0) {
    reasons = { ...reasons, rulesInvalidReasons };
  }

  if (!step.stepTransfer?.length || (step.stepTransfer === 'default' && !nextStep)) {
    reasons.invalidDefaultRelation = invalidRelationMessage;
  }

  return reasons;
};

const validateService = (step: IFrontStep, nextStep?: IFrontStep) => {
  if (step.type !== FrontStepType.Service) return;

  const reasons: IFrontStepInvalidReasons = {};

  if (!step.stepTransfer?.length || (step.stepTransfer === 'default' && !nextStep)) {
    reasons.invalidDefaultRelation = invalidRelationMessage;
  }

  if (
    !step.stepFallbackTransfer?.length ||
    (step.stepFallbackTransfer === 'default' && !nextStep)
  ) {
    reasons.invalidFallbackRelation = invalidRelationMessage;
  }

  return reasons;
};

const deepValidateCommonStep = (step: IFrontStep) => {
  const reasons: IFrontStepInvalidReasons = {};
  if (!step?.name?.trim?.()?.length) {
    reasons.name = valueRequired;
  }

  return reasons;
};

const checkLimitRequired = (step: IFrontStep, reasons: IFrontStepInvalidReasons) => {
  const shouldDisplayLimit =
    step.answerDisplayType &&
    [AnswerTypes.Button, AnswerTypes.File, AnswerTypes.TextTemplate, AnswerTypes.Choice].includes(
      step.answerDisplayType,
    );

  if (shouldDisplayLimit) {
    if (step.limit != null && (step.limit < 1 || step.limit > 100)) {
      reasons.limitInvalidReason = limitInvalidRange;
    }
    if ((step.limit ?? 0) > 0 && !step.limitStepTransfer) {
      reasons.limitTransferRequired = valueRequired;
    }
  }
};

const checkAnswersRequired = (step: IFrontStep, reasons: IFrontStepInvalidReasons) => {
  if (
    step.answerDisplayType &&
    [AnswerTypes.Radio, AnswerTypes.Select, AnswerTypes.Button].includes(step.answerDisplayType) &&
    !step.answers?.length
  ) {
    reasons.answersRequired = valueRequired;
  }
};

const checkFileAnswerCommonProps = (step: IFrontStep, reasons: IFrontStepInvalidReasons) => {
  if (step.answerDisplayType && [AnswerTypes.File].includes(step.answerDisplayType)) {
    if (step.timeout != null && (step.timeout < 1 || step.timeout > 100)) {
      reasons.timeoutInvalidReason = timeoutInvalidRange;
    }

    if ((step.timeout ?? 0) > 0 && !step.timeoutStepTransfer) {
      reasons.timeoutTransferRequired = valueRequired;
    }

    if (!step.variable) {
      reasons.variable = valueRequired;
    }

    if (!step.variableName) {
      reasons.variableName = valueRequired;
    }

    if (!step.stepTransfer?.length || step.stepTransfer === 'default') {
      reasons.transferRequired = valueRequired;
    }
  }
};

const deepValidateStep = (
  step: IFrontStep,
  answersInvalidReasons?: Record<IFrontAnswer['id'], IFrontAnswerInvalidReasons>,
) => {
  if (step.type !== FrontStepType.Step && step.type !== FrontStepType.Rating) return;

  const reasons: IFrontStepInvalidReasons = {};

  if (!step?.description?.trim?.()?.length) {
    reasons.description = valueRequired;
  }

  checkAnswersRequired(step, reasons);

  checkLimitRequired(step, reasons);

  checkFileAnswerCommonProps(step, reasons);

  if (step.answerDisplayType === AnswerTypes.Choice) {
    const choiceStepDetailsInvalidReason = deepValidateChoiceStepDetails(step.choiceStepAnswer);

    if (Object.keys(choiceStepDetailsInvalidReason).length > 0) {
      reasons.choiceStepDetailsInvalidReason = choiceStepDetailsInvalidReason;
    }
  }

  const transformInvalidReasons = getAnswerTransformInvalidReasons(
    step.answers,
    answersInvalidReasons,
    step.answerDisplayType,
  );

  if (transformInvalidReasons && Object.keys(transformInvalidReasons).length !== 0) {
    reasons.answersInvalidReasons = transformInvalidReasons;
  }

  return reasons;
};

const deepValidateSubscript = (step: IFrontStep) => {
  if (step.type !== FrontStepType.Subscript) return;

  const reasons: IFrontStepInvalidReasons = {};

  if (!step?.description?.trim?.()?.length) {
    reasons.description = valueRequired;
  }

  if (!step.subscript) {
    reasons.subscript = getLocaleMessageById('app.editor.step.subscriptSelect');
  }

  return reasons;
};

const validateScenario = (step: IFrontStep) => {
  if (step.type !== FrontStepType.Scenario) return;

  const reasons: IFrontStepInvalidReasons = {};

  if (!step?.description?.trim?.()?.length) {
    reasons.description = valueRequired;
  }

  if (!step.scenario) {
    reasons.scenario = getLocaleMessageById('app.editor.step.scenarioSelect');

    return reasons;
  }

  if (!activeScriptCheck(step.scenario!)) {
    reasons.scenario = getLocaleMessageById('app.modals.formValidation.notActualScenario');
  }

  return reasons;
};

const deepValidateRouter = (
  step: IFrontStep,
  rulesReasons?: IFrontStepInvalidReasons['rulesInvalidReasons'],
) => {
  if (step.type !== FrontStepType.Router) return;

  let reasons: IFrontStepInvalidReasons = {};

  if (!step?.description?.trim?.()?.length) {
    reasons.description = valueRequired;
  }

  if (!step.rules?.length) {
    reasons.rules = getLocaleMessageById('app.modals.formValidation.listItemRequired');
  }

  const rulesInvalidReasons: IFrontStepInvalidReasons['rulesInvalidReasons'] = {};
  step.rules?.forEach((rule) => {
    const invalidReasons = getRuleDeepInvalidReasons(rule);
    if (Object.keys(invalidReasons).length === 0) return;

    rulesInvalidReasons[rule.id] = { ...rulesReasons?.[rule.id], ...invalidReasons };
  });

  if (Object.keys(rulesInvalidReasons).length > 0) {
    reasons = { ...reasons, rulesInvalidReasons: { ...rulesReasons, ...rulesInvalidReasons } };
  }

  return reasons;
};

const deepValidateService = (step: IFrontStep) => {
  if (step.type !== FrontStepType.Service) return;

  const reasons: IFrontStepInvalidReasons = {};

  if (!step?.description?.trim?.()?.length) {
    reasons.description = valueRequired;
  }

  if (!step.automationServiceId) {
    reasons.invalidAutomationServiceId = getLocaleMessageById('app.editor.serviceTypePlaceholder');
  }

  if (step.serviceParameters?.length) {
    if (step.serviceParameters.findIndex((sp) => !sp.value && !sp.variableCode) > -1) {
      reasons.invalidAutomationInpParams = getLocaleMessageById(
        'app.modals.formValidation.valueRequired',
      );
    }
  }

  const filesInvalidReasons: Record<string, string> = validateVariableCodes(step.files);
  if (Object.keys(filesInvalidReasons).length !== 0) {
    reasons.files = filesInvalidReasons;
  }

  return reasons;
};

const deepValidateTerminal = (step: IFrontStep) => {
  if (step.type !== FrontStepType.Terminal) return;

  const reasons: IFrontStepInvalidReasons = {};

  if (!step.terminalSubject && !step.terminalAction) {
    reasons.terminalSubject = getLocaleMessageById('app.editor.terminalSubjectOrActionRequired');
    reasons.terminalAction = getLocaleMessageById('app.editor.terminalSubjectOrActionRequired');
  }

  return reasons;
};

export const getStepInvalidReasons = (
  step: IFrontStep,
  stepIndex: number,
  steps: IFrontStep[],
  deepValidation = true,
): IFrontStepInvalidReasons => {
  let reasons: IFrontStepInvalidReasons = {};

  reasons = {
    ...reasons,
    ...validateStep(step, steps),
    ...validateRouter(step, steps[stepIndex + 1]),
    ...validateService(step, steps[stepIndex + 1]),
    ...validateScenario(step),
  };

  if (!deepValidation) {
    return reasons;
  }

  reasons = {
    ...reasons,
    ...deepValidateCommonStep(step),
    ...deepValidateStep(step, reasons.answersInvalidReasons),
    ...deepValidateSubscript(step),
    ...validateScenario(step),
    ...deepValidateRouter(step, reasons.rulesInvalidReasons),
    ...deepValidateService(step),
    ...deepValidateTerminal(step),
  };

  return reasons;
};
