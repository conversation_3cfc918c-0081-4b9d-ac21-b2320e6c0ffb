import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontFolder, IFrontTemplate } from '@monorepo/common/src/@types/templates';

import extraReducers from './personalTemplates.extraReducers';

export interface IPersonalTemplatesStore {
  loading: boolean;
  error?: SerializedError;
  folders: Record<string, IFrontFolder>;
  selectedTemplate: IFrontTemplate | null;
}

const initialState: IPersonalTemplatesStore = {
  loading: false,
  folders: {},
  selectedTemplate: null,
};

const personalTemplatesSlice = createSlice({
  name: 'personalTemplates',
  initialState,
  reducers: {},
  extraReducers,
});

export default personalTemplatesSlice.reducer;
