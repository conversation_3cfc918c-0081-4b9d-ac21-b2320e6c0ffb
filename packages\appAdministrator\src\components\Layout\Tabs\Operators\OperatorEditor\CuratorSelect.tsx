import React from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import { Guid } from 'guid-typescript';

import { Select } from '@product.front/ui-kit';

import { getFeatureFlag } from '@monorepo/common/src/helpers/featureFlagsHelper';

import { IFrontOperatorBase } from '../../../../../@types/operator';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { getOperatorFio } from '../../../../../helpers/operatorHelper';

interface ICuratorSelectProps {
  operatorId: string | null;
  curatorId: string | null;
  allOperators: IFrontOperatorBase[];
  onCuratorIdChanged: (value: string | null) => void;
}

const noCuratorItem: ISelectDataItem = {
  text: getLocaleMessageById('operators.modal.form.noCurator'),
  value: Guid.EMPTY,
  data: null,
};

const CuratorSelect = ({
  operatorId,
  curatorId,
  onCuratorIdChanged,
  allOperators,
}: ICuratorSelectProps) => {
  if (!getFeatureFlag('isCuratorOfOperators')) {
    return null;
  }

  const selectItems = allOperators
    .filter((o) => o.id !== operatorId)
    .map((operator) => ({
      text: getOperatorFio(operator),
      value: operator.id,
      data: operator,
    }))
    .sort((item1, item2) => item1.text.localeCompare(item2.text));

  return (
    <Select
      label={getLocaleMessageById('operators.modal.form.curator')}
      value={curatorId ?? noCuratorItem.value}
      data={[noCuratorItem, ...selectItems]}
      onChange={({ value }) => onCuratorIdChanged(value)}
      shouldFixDropDownPosition
    />
  );
};

export default CuratorSelect;
