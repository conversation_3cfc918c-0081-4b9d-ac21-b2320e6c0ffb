import React from 'react';

import { Button } from '@product.front/ui-kit';

import { getLocaleMessageById } from '../../../../../../../../helpers/localeHelper';
import { NodeType } from '../../../../../types/scriptDialogsVisualEditorTypes';

interface IToolbarButtonProps {
  localeMessageId: string;
  icon: React.ReactNode;
  disabled: boolean;
  nodeType: NodeType;
  className: string;
  onDragStart: (event: React.DragEvent<HTMLSpanElement>, nodeType: NodeType) => void;
}

const ToolbarButton: React.FC<IToolbarButtonProps> = ({
  localeMessageId,
  icon,
  disabled,
  nodeType,
  onDragStart,
  className,
}) => {
  return (
    <Button
      className={className}
      onDragStart={(event) => onDragStart(event, nodeType)}
      draggable={!disabled}
      disabled={disabled}
    >
      {icon}
      &nbsp;&nbsp;{getLocaleMessageById(localeMessageId)}
    </Button>
  );
};

export default ToolbarButton;
