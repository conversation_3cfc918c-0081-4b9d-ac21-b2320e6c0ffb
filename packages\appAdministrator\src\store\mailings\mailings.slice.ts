import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import {
  Channel,
  ClientAccountSettingsBase,
} from '@monorepo/services/src/@types/generated/productApi';

import {
  MailingMetric,
  PeriodicMailing,
  QueueInfo,
  ThresholdMailing,
} from '../../@types/generated/administration';
import { MailingType } from '../../@types/mailings';

import extraReducers from './mailings.extraReducers';

export interface IMailingsStore {
  loading: boolean;
  error?: SerializedError;
  periodicMailings: PeriodicMailing[];
  thresholdMailings: ThresholdMailing[];

  metrics: MailingMetric[];
  metricsLoading: boolean;
  metricsError: string;

  queues: QueueInfo[];
  queuesLoading: boolean;
  queuesError?: SerializedError;

  channels: Channel[];
  channelsLoading: boolean;
  channelsError?: SerializedError;

  selectedMailingType: MailingType;

  senders: ClientAccountSettingsBase[];
  sendersLoading: boolean;
  sendersError?: SerializedError;
}

const initialState: IMailingsStore = {
  selectedMailingType: MailingType.PeriodicMailing,
  loading: false,

  periodicMailings: [],
  thresholdMailings: [],

  metrics: [],
  metricsLoading: false,
  metricsError: '',

  queues: [],
  queuesLoading: false,

  channels: [],
  channelsLoading: false,

  senders: [],
  sendersLoading: false,
};

const mailingsSlice = createSlice({
  name: 'mailings',
  initialState,
  reducers: {
    setMailingType(state, action: PayloadAction<MailingType>) {
      state.selectedMailingType = action.payload;
    },
  },
  extraReducers,
});
export const { setMailingType } = mailingsSlice.actions;
export default mailingsSlice.reducer;
