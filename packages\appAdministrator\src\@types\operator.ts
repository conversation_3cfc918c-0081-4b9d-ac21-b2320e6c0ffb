import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';

export interface IFrontSessionSettings {
  maxSessions: number | null;
  channelSessions: {
    channel: UcmmChannel;
    maxSessions: number;
  }[];
}

export interface IFrontKpiSetting {
  code: string;
  target: number | null;
}

export interface IFrontStatusSettings {
  maxBreakTime: number | null;
  maxStatusTime: { status: string; maxTime: number }[];
}

export interface IFrontOperatorBase {
  id: string;
  login: string;
  lastName: string;
  firstName: string;
  middleName: string;
  groups: { id: string; name: string }[];
  curatorId?: string;
  curatorName?: string;
}

export interface IFrontOperator extends IFrontOperatorBase {
  email: string;
  kpiSettings: IFrontKpiSetting[];
  sessionSettings: IFrontSessionSettings;
  statusSettings: IFrontStatusSettings;
}
