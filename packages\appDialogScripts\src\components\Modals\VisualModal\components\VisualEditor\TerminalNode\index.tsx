import React from 'react';

import clsx from 'clsx';
import { Handle, NodeProps, Position } from 'reactflow';

import { Colors, grids, Text, TextVariant, utils } from '@product.front/ui-kit';

import IconFlags from '@product.front/icons/dist/icons17/Smiles/IconFlags';

import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';

import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { getNoNameNameForStep } from '../../../../../../helpers/stepListHelper';
import { useDialogScriptsAppSelector } from '../../../../../../store/hooks';
import { nodeMinHeight, nodeMinWidth } from '../../../const/nodeSizes';
import { getNodeTargetId } from '../../../helpers/idsHelper';
import { NodeWithStepData } from '../../../types/scriptDialogsVisualEditorTypes';
import StepFooter from '../StepFooter';

import styles from './styles.module.scss';

type ITerminalNodeProps = NodeProps<NodeWithStepData>;

const TerminalNode: React.FC<ITerminalNodeProps> = ({ id, data }) => {
  const { terminalSubjects, terminalActions } = useDialogScriptsAppSelector(
    (state) => state.externalData,
  );
  const hasVariants =
    data.step.answerDisplayType &&
    ![AnswerTypes.None, AnswerTypes.TextInput].includes(data.step.answerDisplayType) &&
    !!data.step?.answers?.length;
  const isInvalid = data.step.invalidReasons && Object.entries(data.step.invalidReasons).length > 0;
  const isActive = data.isSelected;
  return (
    <div
      className={clsx(styles.terminalNode, {
        [utils.border]: !isInvalid,
        [styles.invalid]: isInvalid,
        [styles.active]: isActive,
      })}
      style={{ minWidth: nodeMinWidth, minHeight: nodeMinHeight }}
    >
      <header className={clsx(styles.terminalNodeHeader, !hasVariants && styles.onlyHeader)}>
        <Handle type="target" position={Position.Left} id={getNodeTargetId(id)} />
        <div className={clsx(utils.dFlex, utils.alignItemsCenter, utils.p2)}>
          <div className={clsx(utils.pR2, utils.dFlex, utils.alignItemsCenter)}>
            <IconFlags />
          </div>
          <Text
            color={Colors.HollywoodSmile}
            variant={TextVariant.BodyMedium}
            title={[data.step.name, data.step.description].join('\n')}
            className={styles.headerText}
          >
            {data.step.name || getNoNameNameForStep(data.step)}
          </Text>
        </div>
      </header>
      <section>
        <div className={clsx(utils.p4, utils.dFlex, utils.flexColumn, utils.gap2)}>
          {data?.step?.terminalAction && (
            <div className={grids.row}>
              <Text
                variant={TextVariant.SmallMedium}
                color={Colors.OnyxBlack60}
                className={grids.col3}
              >
                {getLocaleMessageById('app.editor.step.terminalAction')}
              </Text>
              <Text variant={TextVariant.SmallMediumItalic} className={grids.col7} noWrap ellipsis>
                {terminalActions.find((s) => s.actionCode === data?.step?.terminalAction)?.label ??
                  '-'}
              </Text>
            </div>
          )}
          {data?.step?.terminalSubject && (
            <div className={grids.row}>
              <Text
                variant={TextVariant.SmallMedium}
                color={Colors.OnyxBlack60}
                className={grids.col3}
              >
                {getLocaleMessageById('app.editor.step.terminalSubject')}
              </Text>
              <Text
                variant={TextVariant.SmallMediumItalic}
                className={clsx(grids.col7)}
                noWrap
                ellipsis
              >
                {terminalSubjects.find((s) => s.code === data?.step?.terminalSubject)?.label ?? '-'}
              </Text>
            </div>
          )}
          {data?.step?.terminalUpdatesMap &&
            Object.entries(data?.step?.terminalUpdatesMap ?? {}).length > 0 && (
              <div className={grids.row}>
                <Text
                  variant={TextVariant.SmallMedium}
                  color={Colors.OnyxBlack60}
                  className={grids.col3}
                >
                  {getLocaleMessageById('app.editor.step.terminalUpdateDataAttribute')}
                </Text>
                <Text variant={TextVariant.SmallMediumItalic} className={grids.col7}>
                  {Object.entries(data?.step?.terminalUpdatesMap ?? {}).length}
                </Text>
              </div>
            )}
        </div>
        <StepFooter step={data.step} />
      </section>
    </div>
  );
};

export default React.memo(TerminalNode);
