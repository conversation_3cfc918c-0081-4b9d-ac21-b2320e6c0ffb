import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { IExecuteResult, IFrontAutomationService } from '../../@types/automationService.types';

import extraReducers from './automationServices.extraReducers';

export interface IAutomationServicesStore {
  automationServices: IFrontAutomationService[];
  selectedAutomationService: IFrontAutomationService | null;
  loading: boolean;
  error?: SerializedError;
  executionResult: IExecuteResult | null;
}

const initialState: IAutomationServicesStore = {
  automationServices: [],
  selectedAutomationService: null,
  loading: false,
  executionResult: null,
};

const scriptsSlice = createSlice({
  name: 'automationServices',
  initialState,
  reducers: {
    setSelected(state, action: PayloadAction<IFrontAutomationService | null>) {
      state.selectedAutomationService = action.payload;
    },
    clearResult(state) {
      state.executionResult = null;
    },
  },
  extraReducers,
});

export const { setSelected, clearResult } = scriptsSlice.actions;

export default scriptsSlice.reducer;
