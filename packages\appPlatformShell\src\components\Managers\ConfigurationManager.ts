import { Guid } from 'guid-typescript';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';
import { ProxyBase } from '@monorepo/common/src/platform/utils';

export class ConfigurationManager extends ProxyBase implements AWP.IConfigurationManager {
  private serviceAreaId: Guid | null = null;

  private cache: Record<string, { stringValue: string }> = {};

  constructor(baseUrl: string, serviceAreaId: Guid) {
    super(baseUrl, 'Configurations/');
    this.serviceAreaId = serviceAreaId;
  }

  async getConfiguration(
    configurationName: Parameters<AWP.IConfigurationManager['getConfiguration']>[0],
    allowCache = true,
  ): ReturnType<AWP.IConfigurationManager['getConfiguration']> {
    if (allowCache && this.cache[configurationName + this.serviceAreaId]) {
      return this.cache[configurationName + this.serviceAreaId];
    }

    const params = new URLSearchParams();
    params.append('configurationName', configurationName);

    if (this.serviceAreaId != null) {
      params.append('serviceAreaId', this.serviceAreaId.toString());
    }

    const res = await this.getDataFromAction('GetConfiguration', params);

    if (allowCache) {
      this.cache[configurationName + this.serviceAreaId] = res;
    }

    return res;
  }

  async prefetchConfigurations(configurationNames: string[]) {
    const params = new URLSearchParams();
    configurationNames.forEach((name) => {
      params.append('configurationNames', name);
    });

    if (this.serviceAreaId != null) {
      params.append('serviceAreaId', this.serviceAreaId.toString());
    }

    const res = await this.getDataFromAction('GetConfigurations', params);
    const configurationsMap = res as Record<string, string>;

    configurationNames.forEach((name) => {
      this.cache[name + this.serviceAreaId] = {
        stringValue: configurationsMap[name] ?? null,
      };
    });
  }
}
