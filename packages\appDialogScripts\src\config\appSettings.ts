import WebarmAppSettingsResolver from '@monorepo/common/src/common/helpers/appSettings/WebarmAppSettingsResolver';
import { IShell } from '@monorepo/common/src/platform/awp-web-interfaces';

export interface IAppSettings {
  dialogScriptsManagementUrl: string;
  productPublicFileServer: string;
}

const appSettingsResolver = new WebarmAppSettingsResolver<IAppSettings>('appDialogScripts');

export async function loadSettings(shell?: IShell): Promise<IAppSettings> {
  appSettingsResolver.setRequiredFields(['dialogScriptsManagementUrl', 'productPublicFileServer']);

  appSettingsResolver.tryApplyShell(shell);
  await appSettingsResolver.tryApplyUnderfoot('config/appSettings.json');

  return appSettingsResolver.getAppSettings();
}

export function getSettings() {
  return appSettingsResolver.getAppSettings();
}
