import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';

import {
  FolderContentType,
  FrontFileOrFolder,
  IFrontFolder,
  rootFilesFolder,
} from '../@types/files';

import { getLocaleMessageById } from './localeHelper';

export const isFolder = (fileOrFolder: FrontFileOrFolder) =>
  fileOrFolder.type === FolderContentType.Folder;

export const normalizePathForDisplay = (path: string) => {
  return path.replace(rootFilesFolder, `/${getLocaleMessageById('files.list.root')}`);
};

export const normalizePathForApi = (path: string) => {
  return path.replace(rootFilesFolder, ``) || '/';
};

/*
Ищет объект папки в дереве по пути
*/
export const getFolderByPath = (
  all: FrontFileOrFolder[],
  path: string,
): IFrontFolder | undefined => {
  const pathParts = path.split('/').reverse();
  let lastFind: IFrontFolder | undefined;

  while (pathParts.length) {
    const lvl = pathParts.pop();
    lastFind = (
      lvl === '' || lvl === rootFilesFolder
        ? all[0]
        : lastFind?.items?.find?.((item) => isFolder(item) && item.name === lvl)
    ) as IFrontFolder;
  }

  return lastFind;
};

export const filterFoldersAndFilesBySearchString = (
  all: FrontFileOrFolder[],
  searchString: string,
): FrontFileOrFolder[] => {
  const lowerCaseSearchString = searchString.toLowerCase();
  return all
    .filter((item) => {
      return (
        item.name.toLowerCase().includes(lowerCaseSearchString) ||
        (item.type === FolderContentType.Folder &&
          filterFoldersAndFilesBySearchString(item.items, lowerCaseSearchString).length > 0)
      );
    })
    .map((item) => {
      if (item.type === FolderContentType.Folder) {
        item.items = filterFoldersAndFilesBySearchString(item.items, lowerCaseSearchString);
      }
      return item;
    });
};

export const getPathsForSelect = (data: FrontFileOrFolder[]): ISelectDataItem[] => {
  const arr: ISelectDataItem[] = [];

  const getForLevel = (filesAndFolders: FrontFileOrFolder[], space = '') => {
    filesAndFolders.filter(isFolder).forEach((f) => {
      arr.push({
        value: normalizePathForApi(f.path),
        text: normalizePathForApi(f.path),
        data: space + f.name,
      });
      getForLevel(f.items, space + '\u00a0\u00a0\u00a0\u00a0');
    });
  };

  getForLevel(data);

  return arr;
};
