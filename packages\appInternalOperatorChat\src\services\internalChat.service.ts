import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  Chat,
  CreateChatRequest,
  GetOperatorsAndGroupsResponse,
  Operator,
  PartialChat,
  UpdateChatRequest,
} from '../@types/generated/signalr';
import {
  IMessageHistoryRequestData,
  IMessageSendRequestData,
  SignalMethods,
} from '../@types/signalRTypes';
import signalRClient from '../clients/signalRClient';

import { getAppSettings } from './appSettings.service';

const signalRConnection = signalRClient.getConnection();

export async function getProfile(): Promise<Operator> {
  return await signalRConnection.invoke(SignalMethods.GetProfile);
}

export async function getMessages(reqData: IMessageHistoryRequestData) {
  return await signalRConnection.invoke(SignalMethods.GetMessageHistory, reqData);
}

export async function sendMessage(reqData: IMessageSendRequestData) {
  return await signalRConnection.invoke(SignalMethods.SendMessage, reqData);
}

export async function createChatAsync(reqData: CreateChatRequest): Promise<Chat> {
  return await signalRConnection.invoke(SignalMethods.CreateChat, reqData);
}
export async function updateChatAsync(reqData: UpdateChatRequest): Promise<PartialChat> {
  return await signalRConnection.invoke(SignalMethods.UpdateChat, reqData);
}
export async function addUsersToChatAsync(reqData: { chatId: string; userIds: string[] }) {
  return await signalRConnection.invoke(SignalMethods.AddUsersToChat, reqData);
}
export async function removeUsersFromChatAsync(reqData: { chatId: string; userIds: string[] }) {
  return await signalRConnection.invoke(SignalMethods.RemoveUsersFromChat, reqData);
}

export async function connectUserToChat(chatId: string) {
  return await signalRConnection.invoke(SignalMethods.ConnectToChat, chatId);
}

export async function deleteChatAsync(chatId: string) {
  return await signalRConnection.invoke(SignalMethods.DeleteChat, chatId);
}

export async function readMessage(chatId: string) {
  return await signalRConnection.invoke(SignalMethods.Read, chatId);
}

export async function getOperatorsAndGroups(): Promise<GetOperatorsAndGroupsResponse> {
  const settingsObj = await getAppSettings();

  const res = await commonFetch(settingsObj.ApiUrl + '/api/User', {
    headers: {
      Accept: 'application/json',
    },
    credentials: 'include',
  });
  return (await res.json()) as GetOperatorsAndGroupsResponse;
}
