import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  IconButton,
  Jumbotron,
  JumbotronType,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { IFrontRoutingAttribute } from '../../../../../../@types/parameters';
import { IFrontRoutingRule, IFrontRoutingRuleSet } from '../../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { IValidationResult } from '../validator';

import RoutingRule from './RoutingRule';

const defaultRule: IFrontRoutingRule = {
  attributeId: 0,
  comparisonRule: '',
  value: '',
};

const defaultRoutingRuleGroup: IFrontRoutingRuleSet = {
  id: 0,
  rules: [defaultRule],
};

interface IRoutingSettingsProps {
  routingAttributes: IFrontRoutingAttribute[];
  routingRuleSets: IFrontRoutingRuleSet[];
  onChange: (routingRuleSets: IFrontRoutingRuleSet[]) => void;
  validationResult: IValidationResult['routing'];
}

const RoutingSettings = ({
  routingAttributes,
  routingRuleSets,
  onChange,
  validationResult,
}: IRoutingSettingsProps) => {
  const [routingRuleGroups, setRoutingRuleGroups] =
    React.useState<IFrontRoutingRuleSet[]>(routingRuleSets);

  const AddRoutingRuleButton = () => (
    <Button
      className={utils.gap2}
      style={{ alignSelf: 'baseline' }}
      variant={ButtonVariant.Transparent}
      onClick={() =>
        setRoutingRuleGroups((current) => [
          ...current,
          {
            ...defaultRoutingRuleGroup,
            rules: [{ ...defaultRule }],
          },
        ])
      }
    >
      <IconAdd />
      {getLocaleMessageById('queues.routing.addRoutingRuleGroup')}
    </Button>
  );

  const updateRoutingRule = (
    groupIndex: number,
    ruleIndex: number,
    patch: Partial<IFrontRoutingRule>,
  ) => {
    const patchRules = (group: IFrontRoutingRuleSet) => ({
      ...group,
      rules: group.rules.map((rule, rIndex) => {
        if (rIndex !== ruleIndex) return rule;

        return { ...rule, ...patch };
      }),
    });

    setRoutingRuleGroups((current) =>
      current.map((group, index) => {
        if (index !== groupIndex) return group;

        return patchRules(group);
      }),
    );
  };

  const deleteRoutingRule = (groupIndex: number, ruleIndex: number) => {
    const filterRules = (group: IFrontRoutingRuleSet) => ({
      ...group,
      rules: group.rules.filter((_, rIndex) => rIndex !== ruleIndex),
    });

    setRoutingRuleGroups((current) =>
      current
        .map((g, index) => {
          if (groupIndex !== index) return g;

          return filterRules(g);
        })
        .filter((g) => g.rules.length > 0),
    );
  };

  const deleteRoutingRuleGroup = (groupIndex: number) => {
    setRoutingRuleGroups((current) => current.filter((_, index) => index !== groupIndex));
  };

  const addRoutingRule = (groupIndex: number) => {
    setRoutingRuleGroups((current) =>
      current.map((value, index) => {
        if (index !== groupIndex) return value;

        return {
          ...value,
          rules: [...value.rules, { ...defaultRule }],
        };
      }),
    );
  };

  React.useEffect(() => {
    onChange(routingRuleGroups);
  }, [routingRuleGroups, onChange]);

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)} style={{ minHeight: '100%' }}>
      {validationResult.routingRules && (
        <InputErrorMessage>{validationResult.routingRules}</InputErrorMessage>
      )}
      {routingRuleGroups.length === 0 ? (
        <Jumbotron
          type={JumbotronType.Info}
          header={getLocaleMessageById('queues.noRoutingRules')}
          subheader={<AddRoutingRuleButton />}
          className={clsx(utils.mTauto, utils.mBauto)}
        />
      ) : (
        <>
          {routingRuleGroups.map((group, groupIndex) => (
            <React.Fragment key={groupIndex}>
              <div
                className={clsx(
                  utils.border,
                  utils.radius2,
                  utils.dFlex,
                  utils.gap2,
                  utils.flexColumn,
                  utils.pX6,
                  utils.pY4,
                )}
              >
                <div className={clsx(utils.dFlex)}>
                  <Text variant={TextVariant.SubheadSemibold}>
                    {getLocaleMessageById('queues.editor.routing.routingRuleGroup')}{' '}
                    {groupIndex + 1}
                  </Text>
                  <IconButton
                    className={utils.mLauto}
                    onClick={() => deleteRoutingRuleGroup(groupIndex)}
                  >
                    <IconTrash />
                  </IconButton>
                </div>
                {group.rules.map((rule, ruleIndex) => {
                  return (
                    <React.Fragment key={`${groupIndex}-${ruleIndex}`}>
                      <RoutingRule
                        rule={rule}
                        routingAttributes={routingAttributes}
                        onChange={(patch) => updateRoutingRule(groupIndex, ruleIndex, patch)}
                        onDelete={() => deleteRoutingRule(groupIndex, ruleIndex)}
                        shouldShowError={validationResult.isErrored}
                      />
                      {group.rules.length > 1 && ruleIndex !== group.rules.length - 1 && (
                        <Text className={clsx(utils.mLauto, utils.mRauto)}>
                          {getLocaleMessageById('app.common.and')}
                        </Text>
                      )}
                    </React.Fragment>
                  );
                })}
                <Button
                  className={utils.gap2}
                  style={{ alignSelf: 'baseline' }}
                  variant={ButtonVariant.Transparent}
                  onClick={() => addRoutingRule(groupIndex)}
                >
                  <IconAdd />
                  {getLocaleMessageById('queues.routing.addRoutingRule')}
                </Button>
              </div>
              {routingRuleGroups.length > 1 && groupIndex !== routingRuleGroups.length - 1 && (
                <Text className={clsx(utils.mLauto, utils.mRauto)}>
                  {getLocaleMessageById('app.common.or')}
                </Text>
              )}
            </React.Fragment>
          ))}
          <AddRoutingRuleButton />
        </>
      )}
    </div>
  );
};

export default RoutingSettings;
