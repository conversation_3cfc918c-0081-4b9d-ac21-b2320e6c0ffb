export enum AutomationServiceStatus {
  Draft = 'Draft',
  Active = 'Active',
  Archive = 'Archive',
}

export enum AutomationServiceType {
  REST = 'REST',
  SOAP = 'SOAP',
  Code = 'Code',
  Plugin = 'Plugin',
}

export enum AutomationServiceAuthType {
  NoAuth = 'NoAuth',
  Basic = 'Basic',
  Bearer = 'Bearer',
  ApiKey = 'ApiKey',
}

export enum AutomationServiceRequestType {
  GET = 'GET',
  POST = 'POST',
}

export enum AutomationServiceRequestBodyType {
  JSON = 'JSON',
  XML = 'XML',
  Text = 'Text',
}

export enum AutomationServiceResponseBodyType {
  JSON = 'JSON',
  XML = 'XML',
  Text = 'Text',
  File = 'File',
}

export interface AutomationServiceParameterDescription {
  key: string;
  description: string;
  reachDescription: string;
}

export interface AutomationServiceHeader {
  _key: string;
  name: string;
  value: string;
}

export interface IFrontAutomationService {
  id?: number;
  name: string;
  code: string;
  status: AutomationServiceStatus;
  description: string;
  system: any;
  type: AutomationServiceType;
  authType: AutomationServiceAuthType;
  authLogin?: string;
  authPassword?: string;
  authToken?: string;
  authHeaderName?: string;
  authHeaderValue?: string;
  requestType: AutomationServiceRequestType;
  requestUrl: string;
  requestHeaders: AutomationServiceHeader[];
  requestBodyType: AutomationServiceRequestBodyType;
  requestBody: string;
  requestParameters: AutomationServiceParameterDescription[];
  responseBodyType: AutomationServiceResponseBodyType;
  responseBody: string; // @todo ?????
  responseParameters: AutomationServiceParameterDescription[];
  createdBy: string;
  createdDate: string;
  changedBy: string;
  changedDate: string;
  publishedBy: string;
  publishedDate: string;
  canUploadFiles: boolean;
}

export enum ExecuteResultStatus {
  Success = 'success',
  Fail = 'fail',
}

export interface IRequestFile {
  url: string;
  fileName?: string;
}

export interface IExecuteFileResult {
  content: ArrayBuffer;
  contentType: string;
  fileName: string;
}

export interface IExecuteResult {
  status: ExecuteResultStatus;
  executingTime: number;
  error: {
    message: string;
    originalText: string;
  } | null;
  body: string;
  contentType: string;
  httpStatusCode: number | null;
  contentLength: number | null;
  outputParameters: Record<string, string>;
  file: IExecuteFileResult | null;
}

export interface IFrontAutomationServiceInvalidReasons {
  requestBody?: string;
  responseBody?: string;
  scriptCodeBody?: string;
}
