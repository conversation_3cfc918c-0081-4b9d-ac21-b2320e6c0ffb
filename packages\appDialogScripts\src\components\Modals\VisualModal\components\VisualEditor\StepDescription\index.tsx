import React from 'react';

import clsx from 'clsx';

import { Text, TextVariant, utils } from '@product.front/ui-kit';

import { AppConfigContext } from '../../../../../../context/appConfig';

import styles from './styles.module.scss';

interface IStepDescriptionProps {
  text?: string;
  className?: string;
}

const StepDescription: React.FC<IStepDescriptionProps> = ({ text = ' ', className }) => {
  const { allowRichStepDescription } = React.useContext(AppConfigContext);

  if (allowRichStepDescription) {
    return (
      <Text
        as="div"
        className={clsx(utils.p4, styles.richText, className)}
        variant={TextVariant.SmallMedium}
        dangerouslySetInnerHTML={{ __html: text }}
      />
    );
  }

  return (
    <Text
      as="div"
      className={clsx(utils.p4, className)}
      variant={TextVariant.SmallMediumItalic}
      style={{ whiteSpace: 'pre-line', lineBreak: 'anywhere' }}
    >
      {text}
    </Text>
  );
};

export default StepDescription;
