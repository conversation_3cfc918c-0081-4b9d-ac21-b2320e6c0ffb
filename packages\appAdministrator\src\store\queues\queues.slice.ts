import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontQueueBase } from '../../@types/queue';

import extraReducers from './queues.extraReducers';

export interface IQueuesStore {
  loading: boolean;
  error?: SerializedError;
  queues: IFrontQueueBase[];
}

const initialState: IQueuesStore = {
  loading: false,
  queues: [],
};

const queuesSlice = createSlice({
  name: 'queues',
  initialState,
  reducers: {},
  extraReducers,
});

export default queuesSlice.reducer;
