import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { FrontStepType, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { finishNodeId } from '../mappers/initialEntitiesForVisualEditorMapper';

import {
  getAnswerSourceId,
  getNodeSourceId,
  getRuleSourceId,
  getLimitSourceId,
  getTimeoutSourceId,
  getServiceFailSourceId,
  getServiceSuccessSourceId,
  getSubscriptSourceId,
  getChoiceStepAnswerSourceId,
} from './idsHelper';

export const getStepPatchForHandle = (
  step: IFrontStep,
  { sourceHandle, target }: { sourceHandle: string | null; target: string | null },
  // eslint-disable-next-line sonarjs/cognitive-complexity
): Partial<IFrontStep> => {
  const realTarget = target === finishNodeId ? '' : target;
  let patch = {};

  if (step.type === FrontStepType.Step || step.type === FrontStepType.Rating) {
    if (getLimitSourceId(step.code) === sourceHandle) {
      patch = {
        ...step,
        limitStepTransfer: realTarget,
      };
    } else if (getTimeoutSourceId(step.code) === sourceHandle) {
      patch = {
        ...step,
        timeoutStepTransfer: realTarget,
      };
    } else if (getNodeSourceId(step.code) === sourceHandle) {
      patch = {
        ...step,
        stepTransfer: realTarget,
        answers: step.answers?.map((answer) => ({ ...answer, transferTo: realTarget })),
      };
    } else if (
      step.answerDisplayType &&
      [AnswerTypes.None, AnswerTypes.File].includes(step.answerDisplayType)
    ) {
      patch = {
        ...step,
        stepTransfer: realTarget,
      };
    } else if (getChoiceStepAnswerSourceId(step.code) === sourceHandle) {
      patch = {
        ...step,
        choiceStepAnswer: { ...step.choiceStepAnswer, nextStepCode: realTarget },
      };
    } else {
      patch = {
        ...step,
        stepTransfer: undefined,
        answers: step.answers?.map((ans) =>
          getAnswerSourceId(ans.id.toString()) === sourceHandle
            ? { ...ans, transferTo: realTarget }
            : ans,
        ),
      };
    }
  }

  if (step.type === FrontStepType.Router) {
    patch = {
      ...step,
      stepTransfer: sourceHandle === getNodeSourceId(step.code) ? realTarget : step.stepTransfer,
      rules:
        step.rules?.map((rule) =>
          getRuleSourceId(rule.id.toString()) === sourceHandle
            ? { ...rule, transferTo: realTarget }
            : rule,
        ) || [],
    } as IFrontStep;
  }

  if (step.type === FrontStepType.Service) {
    if (sourceHandle === getServiceSuccessSourceId(step.code)) {
      patch = {
        ...step,
        stepTransfer: realTarget,
      } as IFrontStep;
    }

    if (sourceHandle === getServiceFailSourceId(step.code)) {
      patch = {
        ...step,
        stepFallbackTransfer: realTarget,
      } as IFrontStep;
    }
  }

  if (step.type === FrontStepType.Subscript && sourceHandle === getSubscriptSourceId(step.code)) {
    patch = {
      ...step,
      stepTransfer: realTarget,
    } as IFrontStep;
  }

  return patch;
};
