import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IGoalKpiFront, IOperatorKpiFront, IQueueKpiFront } from '../../@types/KpiData';

import extraReducers from './kpi.extraReducers';

export interface IKpiStore {
  operator: IOperatorKpiFront | null;
  goal: IGoalKpiFront | null;
  queues: IQueueKpiFront[];
  lastUpdateDateTime: string;
  loading: boolean;
  error?: SerializedError;
}

const initialState: IKpiStore = {
  operator: null,
  goal: null,
  queues: [],
  lastUpdateDateTime: '',
  loading: false,
};

const kpiSlice = createSlice({
  name: 'kpi',
  initialState,
  reducers: {},
  extraReducers,
});

export default kpiSlice.reducer;
