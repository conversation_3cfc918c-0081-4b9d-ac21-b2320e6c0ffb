import React from 'react';

import clsx from 'clsx';
import React<PERSON>low, {
  Background,
  Controls,
  Edge,
  MiniMap,
  OnConnect,
  OnConnectEnd,
  OnConnectStart,
  OnSelectionChangeParams,
  ReactFlowInstance,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
  useReactFlow,
} from 'reactflow';

import 'reactflow/dist/style.css';
import { helpers, utils } from '@product.front/ui-kit';

import { postMessage } from '@monorepo/common/src/helpers/postMessageHelper';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import {
  getCurrentOperator,
  setCurrentOperator,
} from '@monorepo/common/src/managers/currentOperatorManager';
import { FrontStepType, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { IScriptsDialogsTerminalAction } from '../../../../../@types/settings';
import { AppConfigContext } from '../../../../../context/appConfig';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { getEmptyStep } from '../../../../../helpers/scriptHelpers';
import {
  setSources,
  setTerminalActions,
  setTerminalSubjects,
} from '../../../../../store/externalData/externalData.slice';
import {
  useDialogScriptsAppDispatch,
  useDialogScriptsAppSelector,
} from '../../../../../store/hooks';
import { addScriptStep, updateScriptStep } from '../../../../../store/oneScript/oneScript.slice';
import { nodeMinHeight, nodeMinWidth } from '../../const/nodeSizes';
import { getXYByClientEvent } from '../../helpers/coordinatesHelper';
import { getStepPatchForHandle } from '../../helpers/stepPatchHelper';
import {
  finishNodeId,
  getInitialEntitiesBySteps,
} from '../../mappers/initialEntitiesForVisualEditorMapper';
import { mapNodeToScriptStep } from '../../mappers/visualEditorMappers';
import { NodeType, ScripDialogStepNode } from '../../types/scriptDialogsVisualEditorTypes';

import CustomEdge from './CustomEdge';
import CustomNode from './CustomNode';
import Header from './Header';
import NodeFinish from './NodeFinish';
import NodeStart from './NodeStart';
import RatingNode from './RatingNode';
import RouterNode from './RouterNode';
import ScenarioNode from './ScenarioNode';
import ServiceNode from './ServiceNode';
import Sidebar from './Sidebar';
import SubscriptNode from './SubscriptNode';
import TerminalNode from './TerminalNode';
import Toolbar from './Toolbar';

const nodeTypes = {
  [NodeType.Router]: RouterNode,
  [NodeType.Custom]: CustomNode,
  [NodeType.Service]: ServiceNode,
  [NodeType.Subscript]: SubscriptNode,
  [NodeType.Start]: NodeStart,
  [NodeType.Finish]: NodeFinish,
  [NodeType.Terminal]: TerminalNode,
  [NodeType.Rating]: RatingNode,
  [NodeType.Scenario]: ScenarioNode,
};

const clickableNodeTypes = [
  NodeType.Custom,
  NodeType.Router,
  NodeType.Service,
  NodeType.Subscript,
  NodeType.Scenario,
  NodeType.Terminal,
  NodeType.Rating,
];

const edgeTypes = {
  custom: CustomEdge,
};

const minimapStyle = {
  height: 108,
  margin: 15,
  right: 48,
};

const automaticStepTypes = [FrontStepType.Router, FrontStepType.Service];

const VisualEditor: React.FC = () => {
  const reactFlowWrapper = React.useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedStep, setSelectedStep] = React.useState<IFrontStep | null>(null);
  const [selectedEdge, setSelectedEdge] = React.useState<Edge | null>(null);
  const { zoomTo, getNodes } = useReactFlow();
  const startNodeId = React.useRef<string | null>(null);
  const startHandleId = React.useRef<string | null>(null);

  const dispatch = useDialogScriptsAppDispatch();
  const { script, drawAutoRelations } = useDialogScriptsAppSelector((state) => state.oneScript);
  const { disableVirtualFinishStep, answerTypes, systemOwner } = React.useContext(AppConfigContext);

  React.useEffect(() => {
    postMessage('ScriptDialogsEditorReady', null);
    const handlePostMessage = (e: MessageEvent) => {
      const expectedTypes = [
        'ScriptDialogsEditorSources',
        'ScriptDialogsEditorTerminalSubjects',
        'ScriptDialogsEditorTerminalActions',
        'ScriptDialogsOperatorLogin',
      ];

      if (typeof e.data === 'string' && expectedTypes.some((typeStr) => e.data.includes(typeStr))) {
        const data = JSON.parse(e.data) as { action: string; payload: any };

        switch (data.action) {
          case 'ScriptDialogsEditorSources':
            dispatch(setSources(data.payload));
            break;
          case 'ScriptDialogsEditorTerminalSubjects':
            dispatch(setTerminalSubjects(data.payload));
            break;
          case 'ScriptDialogsEditorTerminalActions':
            dispatch(
              setTerminalActions(
                (data.payload as IScriptsDialogsTerminalAction[]).filter(
                  (a) => !a.forOwner || a.forOwner.includes(systemOwner),
                ),
              ),
            );
            break;
          case 'ScriptDialogsOperatorLogin':
            {
              const cur = getCurrentOperator();
              if (cur) {
                setCurrentOperator({ ...cur, userName: data.payload });
              }
            }

            break;
          default:
            console.warn(`Unhandled ScriptDialog postMessage «${data.action}»`);
        }
      }
    };
    window.addEventListener('message', handlePostMessage, false);

    return () => {
      window.removeEventListener('message', handlePostMessage, false);
    };
  }, [dispatch]);

  React.useEffect(() => {
    if (!script) {
      return;
    }

    const { initialNodes: defaultNodes, initialEdges: defaultEdges } = getInitialEntitiesBySteps(
      script,
      { drawAutoRelations, disableVirtualFinishStep: !!disableVirtualFinishStep },
    );

    setNodes(() =>
      defaultNodes.map((node) => ({
        ...node,
        data: { ...node.data, isSelected: node.data.step.code === selectedStep?.code },
      })),
    );
    setEdges(() => defaultEdges);
  }, [script, selectedStep, drawAutoRelations, disableVirtualFinishStep, setNodes, setEdges]);

  React.useEffect(() => {
    setNodes((nds) =>
      nds.map((node) =>
        node.data.step.code === selectedStep?.code
          ? { ...node, selected: true }
          : { ...node, selected: false },
      ),
    );
  }, [selectedStep, setNodes]);

  React.useEffect(() => {
    const filterEdges = (edge: Edge) => edge.id !== selectedEdge?.id;

    const confirmDelete = () => {
      setEdges((es) => es.filter(filterEdges));
      setSelectedEdge(null);
    };

    const handleDelete = (e: KeyboardEvent) => {
      if (selectedEdge && (e.key === 'Backspace' || e.key === 'Delete')) {
        showConfirmModal({
          header: getLocaleMessageById('app.modals.deleteRelation.header'),
          onConfirm: confirmDelete,
        });
      }
    };
    const unsubscribe = () =>
      reactFlowWrapper.current?.removeEventListener('keydown', handleDelete);
    if (selectedEdge) {
      reactFlowWrapper.current?.addEventListener('keydown', handleDelete);
    } else {
      unsubscribe();
    }
    return unsubscribe;
  }, [selectedEdge, setEdges]);

  const onConnect: OnConnect = React.useCallback(
    ({ source, sourceHandle, target }) => {
      const sourceStep = script?.steps.find((st: { code: string | null }) => st.code === source);

      if (sourceStep) {
        if (target === finishNodeId && automaticStepTypes.includes(sourceStep.type)) return;

        const patch = getStepPatchForHandle(sourceStep, { sourceHandle, target });
        dispatch(updateScriptStep(patch));
      }
    },
    [dispatch, script?.steps],
  );
  const [reactFlowInstance, setReactFlowInstance] = React.useState<ReactFlowInstance | null>(null);

  const handleDragOver: React.DragEventHandler<HTMLDivElement> = React.useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop: React.DragEventHandler<HTMLDivElement> = React.useCallback(
    (event) => {
      event.preventDefault();

      if (!reactFlowWrapper.current) {
        return;
      }

      const type = event.dataTransfer.getData('application/reactflow');

      if (!type) {
        return;
      }

      const { x, y } = getXYByClientEvent(event, {
        bounds: reactFlowWrapper.current.getBoundingClientRect(),
        viewport: reactFlowInstance?.getViewport(),
      });

      const newStep = getEmptyStep({
        ...(answerTypes?.length === 1 ? { answerDisplayType: answerTypes[0] } : {}),
        type:
          (
            {
              [NodeType.Custom]: FrontStepType.Step,
              [NodeType.Router]: FrontStepType.Router,
              [NodeType.Service]: FrontStepType.Service,
              [NodeType.Subscript]: FrontStepType.Subscript,
              [NodeType.Scenario]: FrontStepType.Scenario,
              [NodeType.Terminal]: FrontStepType.Terminal,
              [NodeType.Rating]: FrontStepType.Rating,
            } as any
          )[type] || FrontStepType.Step,
        positionX: x - nodeMinWidth / 2,
        positionY: y - nodeMinHeight / 2,
      });

      setSelectedStep(newStep);
      dispatch(addScriptStep(newStep));
    },
    [answerTypes, dispatch, reactFlowInstance],
  );

  const handleNodeClick = (_e: React.MouseEvent, node: ScripDialogStepNode) => {
    if (node.type && !clickableNodeTypes.includes(node.type as NodeType)) {
      return;
    }
    setSelectedStep(node.data.step);
    setSelectedEdge(null);
  };

  const handlePaneClick = () => {
    setSelectedStep(null);
    setSelectedEdge(null);
  };
  const handlePanContextClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    zoomTo(1);
  };
  const handleNodeDragStop = React.useCallback(
    (_e: React.MouseEvent, node: ScripDialogStepNode) => {
      dispatch(updateScriptStep(mapNodeToScriptStep(node)));
    },
    [dispatch],
  );

  const onConnectStart: OnConnectStart = React.useCallback((_, { handleId, nodeId }) => {
    startHandleId.current = handleId;
    startNodeId.current = nodeId;
  }, []);

  const onConnectEnd: OnConnectEnd = React.useCallback(
    (event) => {
      const targetIsPane = (event.target as HTMLDivElement | null)?.classList.contains(
        'react-flow__pane',
      );

      if (targetIsPane && reactFlowWrapper.current) {
        // we need to remove the wrapper bounds, in order to get the correct position
        const { x, y } = getXYByClientEvent(event, {
          bounds: reactFlowWrapper.current.getBoundingClientRect(),
          viewport: reactFlowInstance?.getViewport(),
        });

        const newStep = getEmptyStep({
          positionX: x - nodeMinWidth / 2,
          positionY: y - nodeMinHeight / 2,
        });
        dispatch(addScriptStep(newStep));
        setSelectedStep(newStep);

        if (startNodeId.current) {
          const sourceStep = getNodes().find((node) => node.id === startNodeId.current)?.data
            ?.step as IFrontStep | undefined;

          if (sourceStep && startHandleId.current) {
            const patch = getStepPatchForHandle(sourceStep, {
              sourceHandle: startHandleId.current,
              target: newStep.code,
            });
            dispatch(updateScriptStep(patch));
          }
        }
      }
    },
    [dispatch, getNodes, reactFlowInstance],
  );

  const onSelectionChange = ({ edges: selectedEdges }: OnSelectionChangeParams) => {
    if (selectedEdges.length) {
      setSelectedEdge(selectedEdges[0]);
      setSelectedStep(null);
    }
  };
  return (
    <div className={clsx(utils.dFlex, utils.w100, utils.h100)}>
      <main className={clsx(utils.flexGrow1, utils.dFlex, utils.flexColumn)}>
        <Header />
        <div ref={reactFlowWrapper} className={clsx(utils.dFlex, utils.flexGrow1)}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onConnectStart={onConnectStart}
            onConnectEnd={onConnectEnd}
            onNodeDragStop={handleNodeDragStop}
            onInit={setReactFlowInstance}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            attributionPosition="bottom-right"
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView={!!script?.id}
            minZoom={0.05}
            maxZoom={1.5}
            onNodeClick={handleNodeClick}
            onPaneClick={handlePaneClick}
            onPaneContextMenu={handlePanContextClick}
            onSelectionChange={onSelectionChange}
          >
            <Controls position="bottom-right" />
            <MiniMap style={minimapStyle} zoomable pannable />
            <Background color="#aaa" gap={16} />
            <div
              style={{ position: 'absolute', bottom: 0, left: 0, zIndex: helpers.getMaxZIndex() }}
            >
              <Toolbar />
            </div>
          </ReactFlow>{' '}
          {selectedStep && (
            <Sidebar
              key={selectedStep.code}
              step={selectedStep}
              onClose={() => setSelectedStep(null)}
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default () => (
  <ReactFlowProvider>
    <VisualEditor />
  </ReactFlowProvider>
);
