import React from 'react';

import clsx from 'clsx';

import { IconButton, Select, utils } from '@product.front/ui-kit';

import IconClose from '@product.front/icons/dist/icons17/MainStuff/IconClose';

import { IFrontRoutingAttribute } from '../../../../../../@types/parameters';
import { IFrontRoutingRule } from '../../../../../../@types/queue';

import RightPartInput from './RightPartInput';

interface IRoutingRuleProps {
  rule: IFrontRoutingRule;
  routingAttributes: IFrontRoutingAttribute[];
  shouldShowError: boolean;
  onChange: (patch: Partial<IFrontRoutingRule>) => void;
  onDelete: () => void;
}

const RoutingRule = ({
  rule,
  routingAttributes,
  shouldShowError,
  onChange,
  onDelete,
}: IRoutingRuleProps) => {
  const currentRoutingAttribute = routingAttributes.find((a) => a.id === rule.attributeId);

  return (
    <div
      className={clsx(utils.dGrid, utils.gap2, utils.alignItemsCenter)}
      style={{ gridTemplateColumns: '1fr 1fr 1fr auto' }}
    >
      <Select
        data={routingAttributes.map((attribute) => ({
          value: attribute.id.toString(),
          text: attribute.displayName,
        }))}
        onChange={({ value }) =>
          onChange({
            attributeId: value ? Number(value) : 0,
            value: '',
            comparisonRule: '',
          })
        }
        value={rule.attributeId.toString()}
        isInvalid={shouldShowError && !rule.attributeId}
      />
      <Select
        data={
          currentRoutingAttribute?.comparisonRules.map((r) => ({
            value: r.code,
            text: r.name,
          })) ?? []
        }
        value={rule.comparisonRule}
        disabled={!rule.attributeId}
        onChange={({ value }) =>
          onChange({
            comparisonRule: value ?? '',
          })
        }
        isInvalid={shouldShowError && !rule.comparisonRule}
      />
      <RightPartInput
        type={currentRoutingAttribute?.rightPartType}
        rule={rule}
        currentRoutingAttribute={currentRoutingAttribute}
        shouldShowError={shouldShowError}
        onChange={onChange}
      />
      <IconButton className={utils.mLauto} onClick={onDelete}>
        <IconClose />
      </IconButton>
    </div>
  );
};

export default RoutingRule;
