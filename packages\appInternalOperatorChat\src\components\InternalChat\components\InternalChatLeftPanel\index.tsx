import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { Text, TextVariant, utils } from '@product.front/ui-kit';

import InternalChatHeader from '../InternalChatHeader';

import styles from './styles.module.scss';

export interface IInternalChatLeftPanelProps extends Omit<HTMLAttributes<HTMLDivElement>, 'title'> {
  title: React.ReactNode;
  buttons: React.ReactNode;
  footer?: React.ReactNode;
}

const InternalChatLeftPanel: React.FC<IInternalChatLeftPanelProps> = ({
  title,
  buttons,
  children,
  footer,
}) => {
  return (
    <aside className={clsx(utils.h100, utils.borderRight, styles.chatLeftPanel)}>
      <InternalChatHeader className={clsx(styles.panelHeader, utils.borderBottom)}>
        <div style={{ visibility: 'hidden' }}>{buttons}</div>
        <Text variant={TextVariant.SubheadSemibold}>{title}</Text>
        {buttons}
      </InternalChatHeader>
      <div className={clsx(styles.panelBody, utils.scrollbar)}>{children}</div>
      {footer && <footer className={clsx(utils.pX6, utils.pY4)}>{footer}</footer>}
    </aside>
  );
};

export default InternalChatLeftPanel;
