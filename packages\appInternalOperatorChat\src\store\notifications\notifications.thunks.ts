import { createAction } from '@reduxjs/toolkit';

import { store } from '../index';
import { selectUserId } from '../user/user.selectors';

import { NotificationActionPayload } from './notifications.types';

export const handleChatNotification = createAction(
  'internalChat/handleChatNotification',
  (payload: NotificationActionPayload) => ({
    payload: {
      ...payload,
      userId: selectUserId(store.getState()),
    },
  }),
);
