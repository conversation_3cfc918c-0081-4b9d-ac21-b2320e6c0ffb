import { Operator } from '../@types/generated/signalr';

export function getOperatorName(operator: Operator): string {
  if (!operator) {
    return '×××';
  }
  const parts = [];
  operator?.lastName && operator?.lastName.trim().length > 0 && parts.push(operator.lastName);
  operator?.firstName && operator?.firstName.trim().length > 0 && parts.push(operator.firstName);

  if (parts.length === 0) {
    parts.push(operator.login);
  }

  return parts.join(' ');
}
