/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface AddUpdateOwnerSystemDto {
  /** @minLength 1 */
  name: string;
  description?: string | null;
  code?: string | null;
}

export type AddUpdateRestServiceDto = AddUpdateServiceDtoCommon & {
  serviceType?: ServiceType;
  authProps?: AuthPropsDto;
  method?: HttpMethods;
  url?: string | null;
  contentType?: ContentTypes;
  headers?: StringStringKeyValuePair[] | null;
  body?: string | null;
  response?: ResponsePropsDto;
  canUploadFiles?: boolean;
};

export type AddUpdateScriptServiceDto = AddUpdateServiceDtoCommon & {
  serviceType?: ServiceType;
  body?: string | null;
};

export interface AddUpdateServiceDtoCommon {
  serviceType: ServiceType;
  name?: string | null;
  description?: string | null;
  code?: string | null;
  system?: string | null;
  ownerSystemCode?: string | null;
  parameters?: ServiceParamDto[] | null;
  customProperties?: Record<string, string>;
}

export interface AuthPropsDto {
  authType?: AuthTypes;
  login?: string | null;
  password?: string | null;
  header?: string | null;
}

export enum AuthTypes {
  None = "none",
  Basic = "basic",
  Bearer = "bearer",
  ApiKey = "apiKey",
}

export enum ContentTypes {
  JSON = "JSON",
  XML = "XML",
  Text = "Text",
  File = "File",
}

export interface ExecuteRequestServiceByCodeDto {
  serviceCode?: string | null;
  params?: Record<string, string>;
}

export interface ExecuteRequestServiceDto {
  /** @format int64 */
  serviceId?: number;
  params?: Record<string, string>;
}

export interface ExecuteResponseFileDto {
  Content:string;
  FileName:string;
  ContentType:string;
}

export interface ExecuteResponseBaseServiceDto {
  serviceType: ServiceType;
  status?: ExecuteStatuses;
  resultMessage?: string | null;
  /** @format int64 */
  executingTime?: number | null;
  outputParameters?: Record<string, string>;
  error?: ServiceError;
}

export type ExecuteResponseRestServiceDto = ExecuteResponseBaseServiceDto & {
  serviceType?: ServiceType;
  httpStatusCode?: HttpStatusCode;
  /** @format int64 */
  contentLength?: number | null;
  contentType?: string | null;
};

export type ExecuteResponseScriptServiceDto = ExecuteResponseBaseServiceDto & {
  serviceType?: ServiceType;
  httpStatusCode?: HttpStatusCode;
  /** @format int64 */
  contentLength?: number | null;
  contentType?: string | null;
};

export enum ExecuteStatuses {
  Success = "success",
  Fail = "fail",
}

export enum HttpMethods {
  GET = "GET",
  POST = "POST",
}

/** @format int32 */
export enum HttpStatusCode {
  Value100 = 100,
  Value101 = 101,
  Value102 = 102,
  Value103 = 103,
  Value200 = 200,
  Value201 = 201,
  Value202 = 202,
  Value203 = 203,
  Value204 = 204,
  Value205 = 205,
  Value206 = 206,
  Value207 = 207,
  Value208 = 208,
  Value226 = 226,
  Value300 = 300,
  Value301 = 301,
  Value302 = 302,
  Value303 = 303,
  Value304 = 304,
  Value305 = 305,
  Value306 = 306,
  Value307 = 307,
  Value308 = 308,
  Value400 = 400,
  Value401 = 401,
  Value402 = 402,
  Value403 = 403,
  Value404 = 404,
  Value405 = 405,
  Value406 = 406,
  Value407 = 407,
  Value408 = 408,
  Value409 = 409,
  Value410 = 410,
  Value411 = 411,
  Value412 = 412,
  Value413 = 413,
  Value414 = 414,
  Value415 = 415,
  Value416 = 416,
  Value417 = 417,
  Value421 = 421,
  Value422 = 422,
  Value423 = 423,
  Value424 = 424,
  Value426 = 426,
  Value428 = 428,
  Value429 = 429,
  Value431 = 431,
  Value451 = 451,
  Value500 = 500,
  Value501 = 501,
  Value502 = 502,
  Value503 = 503,
  Value504 = 504,
  Value505 = 505,
  Value506 = 506,
  Value507 = 507,
  Value508 = 508,
  Value510 = 510,
  Value511 = 511,
}

export interface OwnerSystemDto {
  /** @format int64 */
  id?: number;
  name?: string | null;
  description?: string | null;
  code?: string | null;
}

export interface ResponsePropsDto {
  headers?: StringStringKeyValuePair[] | null;
  contentType?: ContentTypes;
  body?: string | null;
}

export type RestServiceDto = ServiceDtoCommon & {
  serviceType?: ServiceType;
  /** @format int64 */
  id?: number;
  status?: ServiceStatuses;
  createdBy?: string | null;
  /** @format date-time */
  timeCreated?: string;
  lastUpdateBy?: string | null;
  /** @format date-time */
  timeLastUpdate?: string | null;
  activatedBy?: string | null;
  /** @format date-time */
  timeActivated?: string | null;
  deletedBy?: string | null;
  /** @format date-time */
  timeDeleted?: string | null;
  authProps?: AuthPropsDto;
  method?: HttpMethods;
  url?: string | null;
  headers?: StringStringKeyValuePair[] | null;
  canUploadFiles?: boolean | null;
  body?: string | null;
  response?: ResponsePropsDto;
  contentType?: ContentTypes;
};

export type ScriptServiceDto = ServiceDtoCommon & {
  serviceType?: ServiceType;
  /** @format int64 */
  id?: number;
  status?: ServiceStatuses;
  createdBy?: string | null;
  /** @format date-time */
  timeCreated?: string;
  lastUpdateBy?: string | null;
  /** @format date-time */
  timeLastUpdate?: string | null;
  activatedBy?: string | null;
  /** @format date-time */
  timeActivated?: string | null;
  deletedBy?: string | null;
  /** @format date-time */
  timeDeleted?: string | null;
  body?: string | null;
};

export interface ServiceDtoCommon {
  serviceType: ServiceType;
  name?: string | null;
  description?: string | null;
  code?: string | null;
  system?: string | null;
  ownerSystemCode?: string | null;
  parameters?: ServiceParamDto[] | null;
  customProperties?: Record<string, string>;
}

export interface ServiceError {
  message?: string | null;
  originalText?: string | null;
}

export interface ServiceParamDto {
  type?: ServiceParamTypes;
  /** @minLength 1 */
  key: string;
  name?: string | null;
  description?: string | null;
  defaultValue?: string | null;
  /** @format int64 */
  id?: number | null;
}

export enum ServiceParamTypes {
  Input = "input",
  Output = "output",
}

export enum ServiceStatuses {
  Template = "template",
  Active = "active",
  Deleted = "deleted",
}

export enum ServiceType {
  Rest = "rest",
  Soap = "soap",
  Script = "script",
  Plugin = "plugin",
}

export interface StringStringKeyValuePair {
  key?: string | null;
  value?: string | null;
}
