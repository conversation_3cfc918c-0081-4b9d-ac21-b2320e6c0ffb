import React from 'react';

import { UploadedFile } from '@product.front/ui-kit';

import { Attachment } from '../../../../@types/generated/signalr';
import { SettingsContext } from '../../../../contexts/SettingsContext';
import { getExtensionByFileName } from '../InternalChatForm';

interface IInternalChatFileProps {
  url: string;
  file: File;
  onLoad: (attach: Attachment) => void;
  onRemove: () => void;
  className?: string;
}

const InternalChatFile: React.FC<IInternalChatFileProps> = ({
  url: defaultUrl,
  className,
  onLoad,
  onRemove,
  file,
}) => {
  const [err, setErr] = React.useState<string>('');
  const [url, setUrl] = React.useState<string>(defaultUrl);
  const [retryKey, setRetryKey] = React.useState<string>('');
  const appSettings = React.useContext(SettingsContext);
  React.useEffect(() => {
    if (url?.length > 0 || !appSettings?.ApiUrl) {
      return;
    }

    const xhr = new XMLHttpRequest();

    xhr.onload = xhr.onerror = function () {
      if (this.status == 200) {
        const jsonResponse = JSON.parse(xhr.responseText) as any;
        onLoad(jsonResponse);
        setUrl(jsonResponse.url);
      } else {
        console.error('Error uploading file in internal chat: ', this.status);
        setErr(this.statusText);
      }
    };

    xhr.open('POST', appSettings.ApiUrl + '/api/CDN', true);
    xhr.withCredentials = true;

    const formData = new FormData();
    formData.append('file', file, file.name);

    xhr.send(formData);
  }, [url, retryKey, appSettings?.ApiUrl, file, onLoad]);

  return (
    <UploadedFile
      url={url}
      key={file.name + file.size}
      name={file.name}
      size={file.size}
      extension={file?.name && getExtensionByFileName(file.name)?.toUpperCase()}
      error={err}
      onRemove={onRemove}
      className={className}
      onRetry={(e) => {
        e.preventDefault();
        e.stopPropagation();
        setRetryKey(Date.now().toString());
      }}
    />
  );
};

export default InternalChatFile;
