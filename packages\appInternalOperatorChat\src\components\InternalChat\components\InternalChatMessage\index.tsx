import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import {
  Colors,
  Dropdown,
  DropdownPosition,
  IconButton,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconMore from '@product.front/icons/dist/icons17/Sorting/IconMore';

import ChatBubble from '@monorepo/common/src/components/Chat/ChatBubble';
import ChatMessageAttachments from '@monorepo/common/src/components/Chat/ChatMessageAttachments';
import ChatReplyMessage from '@monorepo/common/src/components/Chat/ChatReplyMessage';
import { tryGetHTMLFromText } from '@monorepo/common/src/helpers/msgHtmlHelper';

import { Message, OperatorStatus } from '../../../../@types/generated/signalr';
import { getOperatorName } from '../../../../helpers/operatorHelper';
import { getIndicationTypeForOperatorStatus } from '../../../../helpers/operatorStatusHelper';
import { mapBackendAttachModelToFront } from '../../../../mappers/dtoMappers';
import { useInternalChatSelector } from '../../../../store/hooks';
import ChatUserAvatar from '../../../ChatUserAvatar';

import InternalChatMessageMenu from './components/InternalChatMessageMenu';

import styles from './styles.module.scss';

interface InternalChatMessageProps {
  msg: Message;
  isMy: boolean;
  isInGroup: boolean;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
  messagesListContainerRef?: React.RefObject<HTMLDivElement | null>;
}

const InternalChatMessage: React.FC<InternalChatMessageProps & HTMLAttributes<HTMLHeadElement>> = ({
  msg,
  isMy,
  isInGroup,
  isFirstInGroup,
  isLastInGroup,
  className,
  messagesListContainerRef,
  ...rest
}) => {
  const { allOperators } = useInternalChatSelector((state) => state.internalChat);
  const htmlContent = tryGetHTMLFromText(msg.text || '');
  const body = htmlContent ? <div dangerouslySetInnerHTML={{ __html: htmlContent }} /> : msg.text;

  const isFullInfo = !isInGroup || isLastInGroup;

  return (
    <div
      className={clsx(
        'qa-chat-message',
        utils.pX3,
        !isInGroup && utils.pY1,
        isFirstInGroup && utils.pT3,
        isLastInGroup && utils.pB2,
        utils.dFlex,
        isMy && utils.flexRowReverse,
        utils.alignItemsEnd,
        styles.chatMessage,
        className,
      )}
      {...rest}
    >
      {isFullInfo && msg.author ? (
        <ChatUserAvatar
          id={msg.author.id!}
          name={getOperatorName(msg.author)}
          status={getIndicationTypeForOperatorStatus(
            allOperators.find((op) => op.id === msg.author?.id)?.status ||
              OperatorStatus.NotAvailable,
          )}
          className={utils.mX4}
        />
      ) : (
        <div className={clsx(utils.mX4)} style={{ width: '40px' }} />
      )}
      <div style={{ minWidth: 0 }}>
        {(!isInGroup || isFirstInGroup) && msg.author && (
          <Text
            as="div"
            className={clsx(isMy && utils.textRight)}
            variant={TextVariant.CaptionMedium}
          >
            {getOperatorName(msg.author)}
          </Text>
        )}
        <div
          className={clsx(
            utils.dFlex,
            utils.mT1,
            isMy && utils.flexRowReverse,
            utils.alignItemsCenter,
          )}
          style={{ minWidth: 0 }}
        >
          <ChatBubble
            className={clsx(utils.alignItemsCenter)}
            arrowFrom={isMy ? 'right' : 'left'}
            showArrow={isFullInfo}
          >
            <div style={{ minWidth: 0 }}>
              {msg.replyMessage && (
                <ChatReplyMessage
                  from={msg.replyMessage.author ? getOperatorName(msg.replyMessage.author) : '×'}
                  body={msg.replyMessage.text || ''}
                  attachments={msg.replyMessage?.attachments?.map(mapBackendAttachModelToFront)}
                  className={utils.mB2}
                />
              )}
              {!!msg?.attachments?.length && (
                <ChatMessageAttachments
                  attachments={msg.attachments.map(mapBackendAttachModelToFront)}
                  className={utils.mB2}
                  messagesListContainerRef={messagesListContainerRef}
                />
              )}
              <Text className={styles.msgBody}>{body}</Text>
            </div>
            <Text className={utils.mL3} color={Colors.OnyxBlack80} style={{ alignSelf: 'end' }}>
              {new Date(Date.parse(msg.createDate!)).toLocaleTimeString()}
            </Text>
          </ChatBubble>
          <Dropdown
            position={isMy ? DropdownPosition.BottomRight : DropdownPosition.BottomLeft}
            menu={<InternalChatMessageMenu message={msg} />}
            className={clsx(styles.contextMenu, utils.pX1)}
          >
            <IconButton>
              <IconMore />
            </IconButton>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default InternalChatMessage;
