import React from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  Checkbox,
  Dropdown,
  DropdownPosition,
  IconButton,
  Input,
  Select,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconMore from '@product.front/icons/dist/icons17/Sorting/IconMore';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { IFrontQueueKpiParameter } from '../../../../../@types/parameters';
import { IFrontKpiUnit, IFrontQueueKpi } from '../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

import { IValidationResult } from './validator';

const unitText: Record<IFrontKpiUnit, string> = {
  [IFrontKpiUnit.None]: '',
  [IFrontKpiUnit.Seconds]: getLocaleMessageById('app.common.seconds'),
  [IFrontKpiUnit.Minutes]: getLocaleMessageById('app.common.minutes'),
  [IFrontKpiUnit.Hours]: getLocaleMessageById('app.common.hours'),
  [IFrontKpiUnit.Days]: getLocaleMessageById('app.common.days'),
};

interface IExceedSettingsProps {
  value: number | null;
  onChange: (value: number | null) => void;
}

const ExceedSettingsController = ({ value, onChange }: IExceedSettingsProps) => {
  const [isRedirectEnabled, setIsRedirectEnabled] = React.useState(!!value);

  return (
    <div className={clsx(utils.pX4, utils.dFlex, utils.flexColumn, utils.gap2)}>
      <Checkbox
        label={getLocaleMessageById('queues.editor.kpi.redirectOnExceed')}
        checked={isRedirectEnabled}
        onChange={({ checked }) => setIsRedirectEnabled(checked ?? false)}
      />
      {isRedirectEnabled && (
        <Input
          label={getLocaleMessageById('queues.editor.kpi.redirectPoint')}
          required
          value={value?.toString() ?? ''}
          onChange={({ value: newValue }) => onChange(Number(newValue))}
          type="number"
        />
      )}
    </div>
  );
};

interface IKpiSettingsProps {
  kpiParameters: IFrontQueueKpiParameter[];
  values: Record<string, IFrontQueueKpi>;
  validationResult: IValidationResult['kpi'];
  onChange: (value: IFrontQueueKpi) => void;
}

const KpiSettings = ({ kpiParameters, values, validationResult, onChange }: IKpiSettingsProps) => {
  return (
    <div
      className={clsx(utils.dGrid, utils.gap2)}
      style={{ gridTemplateColumns: 'auto 1fr 1fr 1fr auto' }}
    >
      <Text variant={TextVariant.BodySemibold}>
        {getLocaleMessageById('queues.editor.kpiName')}
      </Text>
      <Text variant={TextVariant.BodySemibold}>{getLocaleMessageById('queues.editor.warn')}</Text>
      <Text variant={TextVariant.BodySemibold}>{getLocaleMessageById('queues.editor.goal')}</Text>
      <Text variant={TextVariant.BodySemibold}>{getLocaleMessageById('queues.editor.unit')}</Text>
      <div />
      {kpiParameters.map((kpi) => {
        const kpiValue = values[kpi.code] ?? {
          unit: kpi.units.length ? IFrontKpiUnit.Seconds : IFrontKpiUnit.None,
          transferCallDestination: null,
          code: kpi.code,
          alarmThreshold: 0,
          warningThreshold: null,
        };

        return (
          <React.Fragment key={kpi.code}>
            <Text>{kpi.displayName}</Text>
            <div>
              {kpi.isWarningAvailable && (
                <Input
                  value={(kpiValue.warningThreshold || '').toString()}
                  placeholder={kpi.defaultWarningValue?.toString()}
                  type="number"
                  step={kpi.isInteger ? 1 : 0.1}
                  min={kpi.warningMinValue ?? 0}
                  max={
                    kpi.warningMaxValue ??
                    (kpi.alarmExceedsWarning ? kpiValue.alarmThreshold : Infinity)
                  }
                  onChange={({ value }) =>
                    onChange({ ...kpiValue, warningThreshold: Number(value ?? 0) })
                  }
                  canClearBehavior={CanClearBehavior.Value}
                  isInvalid={!!validationResult.kpiMap[kpi.code]?.warn}
                  message={
                    <InputErrorMessage>{validationResult.kpiMap[kpi.code]?.warn}</InputErrorMessage>
                  }
                />
              )}
            </div>
            <Input
              value={(kpiValue.alarmThreshold || '').toString()}
              placeholder={kpi.defaultAlarmValue?.toString()}
              type="number"
              step={kpi.isInteger ? 1 : 0.1}
              min={kpi.alarmMinValue ?? 0}
              max={kpi.alarmMaxValue ?? Infinity}
              onChange={({ value }) =>
                onChange({ ...kpiValue, alarmThreshold: Number(value ?? 0) })
              }
              canClearBehavior={CanClearBehavior.Value}
              isInvalid={!!validationResult.kpiMap[kpi.code]?.alarm}
              message={
                <InputErrorMessage>{validationResult.kpiMap[kpi.code]?.alarm}</InputErrorMessage>
              }
            />
            <div>
              {kpi.units.length > 0 && (
                <Select
                  value={kpiValue.unit}
                  onChange={({ value }) => onChange({ ...kpiValue, unit: value as IFrontKpiUnit })}
                  data={kpi.units.map((unit) => ({ value: unit, text: unitText[unit] }))}
                />
              )}
            </div>
            <div>
              {kpi.canExceed && (
                <Dropdown
                  menu={
                    <ExceedSettingsController
                      value={kpiValue.transferCallDestination}
                      onChange={(value) =>
                        onChange({ ...kpiValue, transferCallDestination: value })
                      }
                    />
                  }
                  position={DropdownPosition.BottomRight}
                  closeOnMenuClick={false}
                >
                  <IconButton>
                    <IconMore />
                  </IconButton>
                </Dropdown>
              )}
            </div>
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default KpiSettings;
