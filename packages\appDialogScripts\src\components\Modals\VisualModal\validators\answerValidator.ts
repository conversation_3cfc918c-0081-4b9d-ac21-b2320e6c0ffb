import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import {
  IFrontAnswer,
  IFrontAnswerInvalidReasons,
} from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getAnswerRegExpPatternByStepAnswers } from '../../../../helpers/scriptHelpers';

const valueRequired = getLocaleMessageById('app.modals.formValidation.valueRequired');

export const getAnswerInvalidReasons = (answer: IFrontAnswer, answers: IFrontAnswer[]) => {
  if (answers.length < 1) return {};

  const reasons: IFrontAnswerInvalidReasons = {};

  if (!answer.text) {
    reasons.text = valueRequired;
  }

  if (answers.length <= 1) return reasons;

  const answerRegexp = getAnswerRegExpPatternByStepAnswers(
    answers.filter((currentAnswer) => currentAnswer.id !== answer.id),
  );

  if (answer.text && !new RegExp(answerRegexp, 'ig').test(answer.text)) {
    reasons.text = getLocaleMessageById('app.error.notUniqueAnswer');
  }

  return reasons;
};

export const getAnswerTransformInvalidReasons = (
  answers?: IFrontAnswer[],
  answersInvalidReasons?: Record<IFrontAnswer['id'], IFrontAnswerInvalidReasons>,
  answerDisplayType?: AnswerTypes,
) => {
  if (answerDisplayType && [AnswerTypes.TextTemplate].includes(answerDisplayType)) {
    if (answers && Object.keys(answers).length !== 0) {
      answersInvalidReasons = answersInvalidReasons ?? {};

      answers.forEach((answer) => {
        if ((!answer.transferTo || answer.transferTo === 'default') && answersInvalidReasons) {
          if (answersInvalidReasons[answer.id]) {
            answersInvalidReasons[answer.id].transferText = valueRequired;
          } else {
            answersInvalidReasons[answer.id] = {
              transferText: valueRequired,
            } as IFrontAnswerInvalidReasons;
          }
        }
      });
    }
  }

  return answersInvalidReasons;
};
