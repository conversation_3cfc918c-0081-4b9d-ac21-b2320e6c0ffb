import { ColumnDef } from '@tanstack/react-table';

import { getFormattedDateTime } from '@monorepo/common/src/helpers/dateHelper';

import { IFrontBlackListAddress } from '../../../../@types/blackList';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';

const commonColumnProps = {
  enableSorting: true,
  enableColumnFilter: true,
  enableResizing: true,
} satisfies Partial<ColumnDef<IFrontBlackListAddress>>;

export default [
  {
    ...commonColumnProps,
    accessorKey: 'address',
    header: getLocaleMessageById('blackList.table.address'),
  },
  {
    ...commonColumnProps,
    accessorKey: 'dateCreated',
    header: getLocaleMessageById('blackList.table.dateCreated'),
    accessorFn: (row) => getFormattedDateTime(new Date(row.dateCreated), 'YYYY.MM.DD hh:mm:ss'),
    meta: {
      defaultSorting: 'asc',
      filter: {
        filterFn: 'dateFilterFn',
      },
    },
  },
  {
    ...commonColumnProps,
    accessorKey: 'dueDate',
    header: getLocaleMessageById('blackList.table.dueDate'),
    accessorFn: (row) => getFormattedDateTime(new Date(row.dueDate), 'YYYY.MM.DD hh:mm:ss'),
    meta: {
      filter: {
        filterFn: 'dateFilterFn',
      },
    },
  },
  {
    ...commonColumnProps,
    accessorKey: 'addedByFio',
    header: getLocaleMessageById('blackList.table.addedByFio'),
  },
  {
    ...commonColumnProps,
    accessorKey: 'comment',
    header: getLocaleMessageById('blackList.table.comment'),
    meta: {
      ellipsis: true,
    },
  },
] as ColumnDef<IFrontBlackListAddress>[];
