import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Guid } from 'guid-typescript';

export interface IOperatorStore {
  id: Guid | null;
}

const initialState: IOperatorStore = {
  id: null,
};

const operatorSlice = createSlice({
  name: 'operator',
  initialState,
  reducers: {
    setOperatorId(state, action: PayloadAction<Guid>) {
      state.id = action.payload;
    },
  },
});

export const { setOperatorId } = operatorSlice.actions;

export default operatorSlice.reducer;
