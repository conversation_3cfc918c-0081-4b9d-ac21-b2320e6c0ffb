import {
  IFrontKpiSetting,
  IFrontOperatorBase,
  IFrontSessionSettings,
  IFrontStatusSettings,
} from './operator';

export interface IFrontOperatorGroupBase {
  id: string;
  name: string;
  parentId: string | null;
  childOperatorGroupsIds: string[];
}

export interface IFrontOperatorGroup
  extends Omit<IFrontOperatorGroupBase, 'childOperatorGroupsIds'> {
  operators: IFrontOperatorBase[];
  sessionSettings: IFrontSessionSettings;
  statusSettings: IFrontStatusSettings;
  kpiSettings: IFrontKpiSetting[];
}
