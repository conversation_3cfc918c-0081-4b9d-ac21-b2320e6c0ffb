{"app.common.apply": "Өтініш беру", "app.common.cancel": "Болдырмау", "app.common.date.from": "Бірге", "app.common.date.to": "Ішінде", "app.common.yes": "Иә", "app.common.save": "Сақтау", "app.common.create": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.common.close": "жабық", "app.common.default": "Әдепкі", "app.common.continue": "Continue", "app.header.title": "Диалогтық сценарийлер", "app.header.button.create": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.table.empty": "Жазбалар табылған жоқ", "app.table.header.code": "Коды", "app.table.header.name": "Аты", "app.table.header.priority": "Артықшылық", "app.table.header.status": "Күй", "app.table.header.createdBy": "Құрылды", "app.table.header.changedBy": "Өзгертілді", "app.table.header.changedDate": "Өзгерту күні", "app.table.header.activeFrom": "бастап жарамды", "app.table.header.activeTo": "үшін жарамды", "app.table.header.createdDate": "құрылған күні", "app.table.header.recordsNumber": "Табылған жазбалар саны", "app.table.header.dependentScripts": "Using to", "app.modals.form.name": "Аты*", "app.modals.form.code": "Коды*", "app.modals.form.description": "Сипаттамасы", "app.modals.form.status": "Күй*", "app.modals.form.activeFrom": "Бастал<PERSON>ы", "app.modals.form.activeTo": "Соңы", "app.modals.form.steps": "Қадамдар", "app.modals.form.step": "№ қадам.", "app.modals.form.addStep": "Қадам қосыңыз", "app.modals.form.stepName": "Қадам атауы", "app.modals.form.stepDescription": "Оператор мәтіні", "app.modals.form.displayTextVariant": "Display text", "app.modals.form.keyValueButton": "Button Value", "app.modals.form.choice.addVariant": "Add other variant", "app.modals.form.stepPromptUrl": "Кеңейту нүктесі", "app.modals.form.answerText": "Жауап мәтіні", "app.modals.form.answerTemplate": "Үлгі", "app.modals.form.transfer": "Өту", "app.modals.form.answerDisplayType": "Жауапты көрсету", "app.modals.form.variableName": "Айнымалы аты", "app.modals.form.variableArrayName": "Variable-array", "app.modals.form.variableCode": "Айнымалы код", "app.modals.form.addAnswer": "Жауап қосыңыз", "app.modals.form.addTextTemplate": "Үлгі қосу", "app.modals.closeScript.header": "Редакторды жабыңыз ба?", "app.modals.closeScript.text": "Барлық енгізілген өзгертулер жойылады.", "app.modals.deleteStep.header": "Қадамды жою керек пе?", "app.modals.deleteStep.text": "Қадамды алып тастау тізбекті үзеді", "app.modals.deleteRelation.header": "Сілтемені жою керек пе?", "app.modals.statistics.totalAnswers": "Жал<PERSON>ы жауаптар", "app.scriptStatus.draft": "Жоба", "app.scriptStatus.published": "жария<PERSON>анды", "app.scriptStatus.archived": "Мұрағат", "app.scriptStatus.notAvailable": "Жоқ", "app.answerDisplayType.radio": "Тізімнің бірі", "app.answerDisplayType.select": "Ашылмалы тізім", "app.answerDisplayType.free": "Мәтін (жол)", "app.answerDisplayType.button": "Түйме", "app.answerDisplayType.choice": "Choice", "app.answerDisplayType.file": "<PERSON>а<PERSON><PERSON>", "app.answerDisplayType.template": "Мәтін (үлгі)", "app.answerDisplayType.none": "Жауап опциялары жоқ", "app.stepTransfer.default": "Келесі қадам", "app.modals.form.answerDisplayTypeNotNone": "Жауаптарымен", "app.modals.timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modals.timeoutUnit": "(мин.)", "app.modals.limit": "Үзіліс", "app.modals.form.freeAnswer": "Жауап «Өз опциясы»: Ер<PERSON><PERSON>н формадағы жауап", "app.stepTransfer.end": "Аяқтау", "modal.error.message": "Сұранысты орындау қатесі", "app.script.saveAndPublish": "Сақтау және жариялау", "app.script.publish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.script.saveAndArchive": "Сақтау және мұрағаттау", "app.script.archive": "Мұрағат", "app.tooltip.archive": "Мұрағат", "app.tooltip.notAvailableAction": "Жар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ан жою", "app.modals.notAvailableHeader": "\"{scriptName}\" жарияланымын жою керек пе?", "app.modals.notAvailableText": "Оның күйі \"Қолжетімсіз\" күйіне өзгереді", "app.results.notAvailableError": "Қолжетімділікті өзгерту қатесі", "app.results.notAvailableSuccess": "Қол жетімділік сәтті өзгертілген", "app.tooltip.copy": "Көшірме жасау", "app.tooltip.edit": "Өңдеу", "app.tooltip.play": "Сынақ ойнату", "app.tooltip.refresh": "Жаңарту", "app.tooltip.current": "Сұраныстарды өңдейді", "app.modals.archive.header": "{scriptName} мұрағаттау керек пе?", "app.modals.archive.text": "Оны қалпына келтіруге болмайды", "app.modals.publish.header": "{scriptName} жариялау керек пе?", "app.modals.dependentScript.header": "This script is linked to another «{scriptName}»?", "app.modals.dependentScripts.header": "This scenario is linked to others «{scriptNames}»?", "app.modals.form.canBeAutomated": "Таратуда автоматты іске қосу", "app.modals.form.priority": "Артықшылық", "app.modals.formValidation.invalidRange": "The value must be in the range from {min} to {max}", "app.modals.formValidation.valueRequired": "Мән бос болмауы керек", "app.modals.formValidation.variableRequired": "Variable must not be empty", "app.modals.formValidation.listItemRequired": "Тізім бос болмауы керек", "app.modals.formValidation.invalidRelation": "Дұрыс емес ауысу", "app.modals.formValidation.notActualScenario": "Not actual scenario", "app.modals.subscriptProblem.header": "{scriptName} сценарийін жариялау мүмкін емес, себебі {subscriptState} күйіндегі кірістірілген сценарийлерді қамтиды", "app.modals.subscriptProblem.body": "Кірістір<PERSON><PERSON><PERSON><PERSON>н сценарийлерді жариялап, әрекетті қайталаңыз", "app.error.notUniqueVariable": "Айнымалылар сценарийде ерекше болуы керек", "app.name.copy": "Көшіру", "app.common.collapse": "Ж<PERSON><PERSON><PERSON>а<PERSON>у", "app.common.expand": "Кеңейту", "app.editor.noName": "атау<PERSON>ыз", "app.editor.blockTypeStep": "Қадам", "app.editor.blockTypeRouter": "маршрутизатор", "app.editor.blockTypeService": "Әрекет", "app.editor.blockTypeSubscript": "Сценарий", "app.editor.errorStepsInvalid": "Қадамдарды толтырудың дұрыстығын тексеріңіз (қызыл түспен белгіленген)", "app.editor.blockTypeTerminal": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON>", "app.editor.blockTypeRating": "CSI ұпайы", "app.editor.blockTypeScenario": "<PERSON><PERSON><PERSON>", "app.editor.scriptHeader": "Жаңа сценарий", "app.editor.routerHeader": "Жаңа маршрутизатор", "app.editor.routerName": "Аты", "app.editor.routerDescription": "Сипаттама", "app.editor.routerNewRule": "жаңа ереже", "app.editor.routerRuleName": "Ереже атауы", "app.editor.routerRuleDefaultTransfer": "Әдепкі ауысу", "app.editor.routerRuleVariable": "Айнымалы", "app.editor.routerRuleOperator": "Оператор", "app.editor.routerRuleValue": "Мағынасы", "app.editor.routerRuleCondition": "<PERSON>а<PERSON><PERSON>", "app.editor.routerRule": "ереже", "app.editor.routerRuleConditionEqual": "Тең", "app.editor.routerRuleConditionNotEqual": "Тең емес", "app.editor.routerRuleConditionContains": "Құрамында", "app.editor.routerRuleConditionNotContains": "Құрамында жоқ", "app.editor.routerRuleConditionGt": "Көбірек", "app.editor.routerRuleConditionLt": "Аздау", "app.editor.routerRuleConditionGtEq": "Көп немесе тең", "app.editor.routerRuleConditionLtEq": "Аз немесе тең", "app.editor.routerRuleMoveUp": "Жоғары көтеріңіз", "app.editor.routerRuleMoveDown": "Төменде қалдырыңыз", "app.editor.serviceTypeHeader": "Әрекет түрі", "app.editor.serviceTypePlaceholder": "Әрекет түрін таңдаңыз", "app.editor.serviceInputsHeader": "Параметрлерді сұрау", "app.editor.serviceOutputHeader": "Жауап опциялары", "app.editor.serviceInputsValue": "Мағынасы", "app.editor.serviceInputsManual": "Қолмен енгізу", "app.editor.serviceTransferHeader": "Өту", "app.editor.serviceTransferSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.editor.serviceTransferError": "Қате", "app.editor.scripinformation.noDependentScripts": "No dependent scripts", "app.editor.scripinformation.dependentScripts": "Dependent scripts", "app.results.saveSuccess": "Сәтті сақталды", "app.results.saveError": "Сақтау қатесі", "app.results.publishSuccess": "Сәтті жарияланды", "app.results.publishError": "Жария<PERSON>ау қатесі", "app.results.archiveSuccess": "Сәтті мұрағатталды", "app.results.archiveError": "Мұрағаттау қатесі", "app.editor.toggleAutoRelations": "Автоматты түрде сілтеме", "app.export.errorDescription": "Біраз уақыттан кейін қайта жүктеп көріңіз", "app.import.error": "жүктеу қатесі", "app.import.errorDescription": "Біраз уақыттан кейін қайта жүктеп көріңіз", "app.common.OK": "ЖАРАЙДЫ МА", "app.tooltip.export": "Экспорттау", "app.tooltip.import": "Импорттау", "app.editor.step.addAttachment": "Тіркеме қосу", "app.editor.step.addAttachmentFromVariable": "Add Attachment From Variable", "app.editor.step.attachmentsFromVariables": "Attachments From Variables", "app.editor.step.deleteAll": "Delete All", "app.editor.step.subscriptTitle": "Сценарий", "app.editor.step.subscriptSelect": "Скриптті таңдаңыз", "app.editor.step.scenarioSelect": "Choose scenario", "app.editor.step.subscriptDescription": "Сценарийдің сипаттамасы", "app.editor.step.scenarioDescription": "Description of the scenario", "app.editor.step.subscriptStatus": "Күй", "app.editor.step.scenarioStatus": "Status of the scenario", "app.error.notUniqueAnswer": "Жауаптар қадамның бір бөлігі ретінде ерекше болуы керек", "app.table.header.rating": "Рейтингі", "app.table.header.runsNumber": "Іске қосу", "app.table.header.abortNumber": "Үзбетсіздік", "app.editor.step.variableSource": "Де<PERSON>ектер", "app.editor.step.labelIsSkippable": "Алдын ала толтырылған мәндер үшін қадамды өткізіп жіберіңіз", "app.editor.step.promptVariables": "Айнымалылар: {variables}", "app.modals.form.firstStep": "Басталуын жасаңыз", "app.modals.form.answer.actions.delete": "<PERSON><PERSON><PERSON>", "app.editor.step.terminalSubject": "Тақырып", "app.editor.step.terminalAction": "Әрекет", "app.editor.step.terminalUpdateDataHeader": "Іс деректерін жаңарту", "app.editor.step.terminalUpdateDataVariable": "Айнымалы", "app.editor.step.terminalUpdateDataAttribute": "Aтрибуты", "app.editor.step.terminalUpdateDataVariableAdd": "Айнымалыны қосыңыз", "app.editor.step.terminalSubjectHeader": "Апелляцияның тақырыбын белгілеңіз", "app.editor.step.terminalActionHeader": "Қоңырау арқылы әрекет", "app.table.footer.count": "Жазбалар саны", "app.step.service.delete": "<PERSON><PERSON><PERSON>", "app.step.service.serviceUnavailable": "Жеке куәлікпен автоматтандыру қызметідыру қызметі {automatioжоқ немесе белсенді емеснді емес", "app.modals.forms.addTag": "Тег қосыңыз", "app.editor.step.labelIsBackButtonAvailable": "Артқа қадам қол жетімді", "app.editor.serviceFilesHeader": "Request files", "app.automationService.variableFileAdd": "Add Variable", "app.modals.form.variableFileName": "Variable Files", "app.automationService.requestFileName": "File Name", "app.automationService.variableAdd": "Add Variable", "app.error.notUniqueFileVariable": "File Variable should be unique as part of a step"}