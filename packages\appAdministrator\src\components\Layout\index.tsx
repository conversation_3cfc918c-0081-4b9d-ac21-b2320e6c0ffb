import React from 'react';

import IconMail from '@product.front/icons/dist/icons17/Chat&Mail/IconMail';
import IconStatistic from '@product.front/icons/dist/icons17/MainStuff/IconStatistic';
import IconSupervisor from '@product.front/icons/dist/icons17/MainStuff/IconSupervisor';
import IconDoc from '@product.front/icons/dist/icons17/Person&Doc/IconDoc';
import IconDocCancel from '@product.front/icons/dist/icons17/Person&Doc/IconDocCancel';
import IconDocDelete from '@product.front/icons/dist/icons17/Person&Doc/IconDocDelete';
import IconDocHome from '@product.front/icons/dist/icons17/Person&Doc/IconDocHome';
import IconDocLine from '@product.front/icons/dist/icons17/Person&Doc/IconDocLine';
import IconDocMessage from '@product.front/icons/dist/icons17/Person&Doc/IconDocMessage';
import IconDocRight from '@product.front/icons/dist/icons17/Person&Doc/IconDocRight';
import IconFavorites from '@product.front/icons/dist/icons17/Person&Doc/IconFavorites';
import IconUser from '@product.front/icons/dist/icons17/Person&Doc/IconUser';
import IconUserGroup from '@product.front/icons/dist/icons17/Person&Doc/IconUserGroup';
import IconUserOk from '@product.front/icons/dist/icons17/Person&Doc/IconUserOk';

import { getLocaleMessageById } from '../../helpers/localeHelper';

import Layout from './Layout';
import AutoTemplatesTab from './Tabs/AutoTemplates';
import BlackListTab from './Tabs/BlackList';
import FilesTab from './Tabs/Files';
import KpiTab from './Tabs/Kpi';
import Mailings from './Tabs/Mailings';
import NegativeWordsTab from './Tabs/NegativeWords';
import OperatorGroupsTab from './Tabs/OperatorGroups';
import OperatorsTab from './Tabs/Operators';
import PersonalTemplatesTab from './Tabs/PersonalTemplates';
import PrioritizationTab from './Tabs/Prioritization';
import QueuesTab from './Tabs/Queues';
import SpamWordsTab from './Tabs/SpamWords';
import SubjectsTab from './Tabs/Subjects';
import TemplatesTab from './Tabs/Templates';

export enum AdministrationTab {
  Queues = 'Queues',
  Prioritization = 'Prioritization',
  Templates = 'Templates',
  PersonalTemplates = 'PersonalTemplates',
  AutoTemplates = 'AutoTemplates',
  Subjects = 'Subjects',
  SpamWords = 'SpamWords',
  NegativeWords = 'NegativeWords',
  OperatorGroups = 'OperatorGroups',
  Operators = 'Operators',
  Kpi = 'Kpi',
  BlackList = 'BlackList',
  Files = 'Files',
  Mailing = 'Mailing',
}

const AdministratorLayout = () => {
  const tabs = React.useMemo(
    () => [
      {
        label: getLocaleMessageById('app.administrator.tab.queues'),
        value: AdministrationTab.Queues,
        component: <QueuesTab />,
        icon: <IconSupervisor />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.prioritization'),
        value: AdministrationTab.Prioritization,
        component: <PrioritizationTab />,
        icon: <IconStatistic />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.templates'),
        value: AdministrationTab.Templates,
        component: <TemplatesTab />,
        icon: <IconFavorites />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.personalTemplates'),
        value: AdministrationTab.PersonalTemplates,
        component: <PersonalTemplatesTab />,
        icon: <IconDocHome />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.autoTemplates'),
        value: AdministrationTab.AutoTemplates,
        component: <AutoTemplatesTab />,
        icon: <IconDocRight />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.subjects'),
        value: AdministrationTab.Subjects,
        component: <SubjectsTab />,
        icon: <IconDocLine />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.spamWords'),
        value: AdministrationTab.SpamWords,
        component: <SpamWordsTab />,
        icon: <IconDocMessage />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.negativeWords'),
        value: AdministrationTab.NegativeWords,
        component: <NegativeWordsTab />,
        icon: <IconDocCancel />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.operatorGroups'),
        value: AdministrationTab.OperatorGroups,
        component: <OperatorGroupsTab />,
        icon: <IconUserGroup />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.operators'),
        value: AdministrationTab.Operators,
        component: <OperatorsTab />,
        icon: <IconUser />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.kpi'),
        value: AdministrationTab.Kpi,
        component: <KpiTab />,
        icon: <IconUserOk />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.blackList'),
        value: AdministrationTab.BlackList,
        component: <BlackListTab />,
        icon: <IconDocDelete />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.files'),
        value: AdministrationTab.Files,
        component: <FilesTab />,
        icon: <IconDoc />,
      },
      {
        label: getLocaleMessageById('app.administrator.tab.mailings'),
        value: AdministrationTab.Mailing,
        component: <Mailings />,
        icon: <IconMail />,
      },
    ],
    [],
  );

  return (
    <Layout
      header={getLocaleMessageById('app.administrator.header')}
      defaultTab={AdministrationTab.Queues}
      tabs={tabs}
    />
  );
};

export default AdministratorLayout;
