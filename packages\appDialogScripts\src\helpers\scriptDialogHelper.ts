import {
  ScriptAutomationServiceDto,
  ScriptRouterDto,
  StepDto,
  SubScriptDto,
} from '@monorepo/dialog-scripts/src/@types/generated/scripts';

// ! DEPRECATED удалить после полноценного переезда на startingStepCode
// ! необходимо для старых скриптов, у которых нет startingStepCode

export const defineFirstScriptDialogStepCodeDto = (
  stepsMap?: Record<string, StepDto | ScriptRouterDto | ScriptAutomationServiceDto | SubScriptDto>,
): string | null => {
  if (!stepsMap) return null;

  let returnValue = null;
  const stepCodes = Object.keys(stepsMap);

  stepCodes.some((stepCode) => {
    const hasLinks = stepCodes.some(
      (oneStepCode) =>
        stepCode === stepsMap[oneStepCode].nextStepCode ||
        stepCode === (stepsMap[oneStepCode] as ScriptAutomationServiceDto).nextFailStepCode ||
        (stepsMap[oneStepCode] as StepDto).answers?.some(
          (answer) => stepCode === answer.nextStepCode,
        ) ||
        (stepsMap[oneStepCode] as ScriptRouterDto).rules?.some(
          (rule) => stepCode === rule.nextStepCode,
        ),
    );

    if (!hasLinks) returnValue = stepCode;

    return !hasLinks;
  });

  return returnValue;
};
