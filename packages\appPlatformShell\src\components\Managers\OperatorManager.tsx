import { Guid } from 'guid-typescript';

import {
  IOperatorManager,
  OperatorCustomAttribute,
} from '@monorepo/common/src/platform/awp-web-interfaces';
import { ProxyBase } from '@monorepo/common/src/platform/utils';

export class OperatorManager extends ProxyBase implements IOperatorManager {
  constructor(baseUrl: string) {
    super(baseUrl, 'Operators/');
  }

  async getCustomAttributes(
    operatorId: Guid,
    caCodes: string[],
  ): Promise<OperatorCustomAttribute[]> {
    const params = new URLSearchParams();
    params.append('operatorId', operatorId.toString());
    return await this.postDataToActionWithResult('GetCustomAttributes', params, caCodes);
  }
}
