import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IFrontAutomationService } from '../../@types/automationService.types';

import { IAutomationServiceStore } from './automationService.slice';
import { saveAutomationService, updateAutomationService } from './automationService.thunk';

export default (builder: ActionReducerMapBuilder<IAutomationServiceStore>) => {
  builder
    // saveAutomationService
    .addCase(saveAutomationService.pending, (state) => {
      state.savingError = undefined;
      state.isSaving = true;
    })
    .addCase(saveAutomationService.fulfilled, (state, action) => {
      state.savingError = undefined;
      state.isSaving = false;
      if (state.automationService && action.payload.id) {
        state.automationService.id = action.payload.id;
      }

      state.initialAutomationService = { ...state.automationService } as IFrontAutomationService;
    })
    .addCase(saveAutomationService.rejected, (state, action) => {
      state.isSaving = false;
      state.savingError = action.error;
      console.error(action.error);
    })
    // updateAutomationService
    .addCase(updateAutomationService.pending, (state) => {
      state.savingError = undefined;
      state.isSaving = true;
    })
    .addCase(updateAutomationService.fulfilled, (state, action) => {
      state.savingError = undefined;
      state.isSaving = false;
      if (state.automationService && action?.payload?.id) {
        state.automationService.id = action.payload.id;
      }

      state.initialAutomationService = { ...state.automationService } as IFrontAutomationService;
    })
    .addCase(updateAutomationService.rejected, (state, action) => {
      state.isSaving = false;
      state.savingError = action.error;
      console.error(action.error);
    });
  return builder;
};
