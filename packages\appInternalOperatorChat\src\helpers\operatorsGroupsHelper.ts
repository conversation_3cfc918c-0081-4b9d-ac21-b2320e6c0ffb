import { InternalOperator, OperatorGroup } from '../@types/generated/signalr';

export interface IOperatorWithGroups extends InternalOperator {
  groups: OperatorGroup[];
}

export const injectGroupsToOperator = (
  operator: InternalOperator,
  groups: OperatorGroup[],
): IOperatorWithGroups => {
  return {
    ...operator,
    groups: groups.filter((group) => group.operatorIds?.includes(operator.id!)),
  };
};

export const injectGroupsToOperators = (
  operators: InternalOperator[],
  groups: OperatorGroup[],
): IOperatorWithGroups[] => {
  return operators.map((operator) => injectGroupsToOperator(operator, groups));
};
