import { AdministrationTab } from '../components/Layout';

const getTabKey = (tab: AdministrationTab) => `administratorTab${tab}_asideSize`;
export const getTabAsideSize = (tab: AdministrationTab) => {
  const lsKey = getTabKey(tab);
  return localStorage.getItem(lsKey) || '20vw';
};

export const setTabAsideSize = (tab: AdministrationTab) => (size: string) => {
  const lsKey = getTabKey(tab);
  localStorage.setItem(lsKey, size);
};

export const tabAsideSizeMin = 256;
export const tabAsideSizeMax = 512;
