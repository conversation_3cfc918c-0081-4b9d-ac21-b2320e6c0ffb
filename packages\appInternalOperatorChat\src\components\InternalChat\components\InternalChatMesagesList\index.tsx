import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { colors, Colors, Tag, Text, TextVariant, utils } from '@product.front/ui-kit';

import IconUserAdd from '@product.front/icons/dist/icons17/Person&Doc/IconUserAdd';
import IconUserDelete from '@product.front/icons/dist/icons17/Person&Doc/IconUserDelete';

import ChatMessageDivider from '@monorepo/common/src/components/Chat/ChatDivider';
import ChatError from '@monorepo/common/src/components/Chat/ChatError';
import ChatLoader from '@monorepo/common/src/components/Chat/ChatLoader';
import ChatPlaceholder from '@monorepo/common/src/components/Chat/ChatPlaceholder';
import ChatScrollToBottomButton from '@monorepo/common/src/components/Chat/ChatScrollToBottomButton';
import { getDateGroupName } from '@monorepo/common/src/helpers/dateHelper';
import { initScrollHandlers } from '@monorepo/common/src/helpers/scrollHelper';

import { MessageType, Operator, SystemMessageType } from '../../../../@types/generated/signalr';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorName } from '../../../../helpers/operatorHelper';
import { getPlatformApp } from '../../../../managers/platformAppManager';
import { getPlatformEventManager } from '../../../../managers/platformEventManager';
import { getPlatformHostedAppManager } from '../../../../managers/platformHostedAppManager';
import { useInternalChatDispatch, useInternalChatSelector } from '../../../../store/hooks';
import {
  dropUnreadCountForChat,
  getMessagesData,
  markChatMessagesRead,
  nextPage,
} from '../../../../store/internalChat.slice';
import { selectUserId } from '../../../../store/user/user.selectors';
import InternalChatMessage from '../InternalChatMessage';

import styles from './styles.module.scss';

let lastClientHeight = 0;
let lastMessageTimestamp: number;
let scrollOnActivateApp: number | null = null;
let readReportTimeout: ReturnType<typeof setTimeout>;

const InternalChatMessagesList: React.FC<HTMLAttributes<HTMLDivElement>> = ({
  className,
  ...rest
}) => {
  const [isBottomReached, setIsBottomReached] = React.useState<boolean>(true);
  const [countNewMessages, setCountNewMessages] = React.useState<number>(0);
  const [isFirstRender, setIsFirstRender] = React.useState<boolean>(true);
  const [isInitialization, setIsInitialization] = React.useState<boolean>(true);

  const {
    messages,
    page,
    perPageCount,
    chats,
    chatLoadingError,
    isChatLoading,
    isChatHistoryEndReached,
  } = useInternalChatSelector((state) => state.internalChat);

  const userId = useInternalChatSelector(selectUserId);

  const dispatch = useInternalChatDispatch();

  const containerRef = React.useRef<HTMLDivElement>(null);

  const selectedChatId = React.useMemo(() => chats.find((chat) => chat.isSelected)?.id, [chats]);

  React.useEffect(() => {
    if (!containerRef?.current) {
      return;
    }

    const onTopReached = (messagesListNode: HTMLElement) => {
      if (messages.length >= perPageCount) {
        window.requestAnimationFrame(() => {
          lastClientHeight = messagesListNode.scrollHeight;
          dispatch(nextPage());
        });
      }
    };
    const onTopLeave = () => {};
    const onBottomReached = () => {
      setIsBottomReached(true);
    };
    const onBottomLeave = () => {
      setIsBottomReached(false);
      scrollOnActivateApp = null;
    };

    const { unmount } = initScrollHandlers(containerRef.current, {
      onBottomReached,
      onTopReached,
      onTopLeave,
      onBottomLeave,
    });

    return unmount;
  }, [containerRef, messages, chats, selectedChatId, perPageCount, dispatch]);

  // eslint-disable-next-line sonarjs/cognitive-complexity
  React.useEffect(() => {
    if (!containerRef.current) {
      return;
    }

    let messagesCountChanged = false;
    let hasLastFromMe = false;

    if (isBottomReached || isFirstRender) {
      lastMessageTimestamp =
        messages.length && Date.parse(messages[messages.length - 1].createDate!);
    } else {
      const newMessages = messages.filter(
        (msg) => Date.parse(msg.createDate!) > lastMessageTimestamp,
      );

      if (newMessages.some((msg) => msg?.author?.id === userId)) {
        hasLastFromMe = true;
        lastMessageTimestamp =
          messages.length && Date.parse(messages[messages.length - 1].createDate!);
        setCountNewMessages(0);
      } else {
        const newMessagesCount = newMessages.length;
        messagesCountChanged = newMessagesCount !== countNewMessages;
        messagesCountChanged && setCountNewMessages(newMessagesCount);
      }
    }

    if (messagesCountChanged) {
      return;
    }

    if (
      selectedChatId &&
      hasLastFromMe &&
      (isFirstRender || containerRef.current.scrollHeight <= containerRef.current.clientHeight)
    ) {
      dispatch(dropUnreadCountForChat(selectedChatId));
      dispatch(markChatMessagesRead(selectedChatId));
    }

    let scrollPx = containerRef.current.scrollHeight - lastClientHeight; // for infinity scroll
    const isChatWindowActive = getPlatformHostedAppManager()?.activeAppName
      ? getPlatformHostedAppManager()?.activeAppName === getPlatformApp()?.name
      : document.hasFocus();

    scrollOnActivateApp = null;
    if (isFirstRender || isBottomReached || hasLastFromMe) {
      scrollPx = Number.MAX_SAFE_INTEGER;
      if (!isChatWindowActive) {
        scrollOnActivateApp = scrollPx;
      }
    }

    containerRef.current.scrollTo({ top: scrollPx });

    if (!isInitialization) {
      isFirstRender && setIsFirstRender(false);
    }
  }, [messages, page, containerRef, dispatch]);

  React.useEffect(() => {
    const eventManager = getPlatformEventManager();
    if (!eventManager) {
      return;
    }

    const handleActivateApp = ({ currentActiveApp }: { currentActiveApp: string }) => {
      const appName = getPlatformApp()?.name;
      if (scrollOnActivateApp && appName === currentActiveApp && containerRef.current) {
        containerRef.current.scrollTo({ top: scrollOnActivateApp });
      }
    };

    eventManager && eventManager.subscribe('HostedApplicationActivated', handleActivateApp);

    return () => eventManager.unsubscribe('HostedApplicationActivated', handleActivateApp);
  }, []);

  React.useEffect(() => {
    if (!selectedChatId) {
      throw new Error('Has no selected chat');
    }

    readReportTimeout = setTimeout(() => {
      if (!selectedChatId) {
        return;
      }
      dispatch(dropUnreadCountForChat(selectedChatId));
      dispatch(markChatMessagesRead(selectedChatId));
    }, 3000);

    return () => clearTimeout(readReportTimeout);
  }, [dispatch, selectedChatId]);

  React.useEffect(() => {
    if (isChatHistoryEndReached || !selectedChatId) {
      return;
    }
    isInitialization && setIsInitialization(false);
    dispatch(
      getMessagesData({
        chatId: selectedChatId,
        offset: page * perPageCount,
        limit: perPageCount,
      }),
    );
  }, [dispatch, page, selectedChatId]);

  const handleForceScrollToBottom = React.useCallback(() => {
    const container = containerRef.current;
    if (!container) {
      return;
    }
    const isSmoothKilled = container.classList.contains(styles.noSmooth);
    container.classList.remove(styles.noSmooth);
    container.scrollTop = container.scrollHeight;
    lastMessageTimestamp = messages.length && Date.parse(messages[messages.length - 1].createDate!);
    if (isSmoothKilled) {
      setCountNewMessages(0);
      window.requestAnimationFrame(() => {
        container.classList.add(styles.noSmooth);
      });
    }
    if (selectedChatId) {
      dispatch(markChatMessagesRead(selectedChatId));
      dispatch(dropUnreadCountForChat(selectedChatId));
    }
  }, [messages, selectedChatId, dispatch]);

  if (isChatLoading || !selectedChatId) {
    return (
      <section className={clsx(styles.listMessages, utils.scrollbar, className)} {...rest}>
        <ChatLoader />
      </section>
    );
  }

  if (chatLoadingError) {
    return (
      <section className={clsx(styles.listMessages, utils.scrollbar, className)} {...rest}>
        <ChatError
          title={getLocaleMessageById('app.title.errorLoadingMessages')}
          error={chatLoadingError}
        />
      </section>
    );
  }

  let lastGroupName: string;

  return (
    <>
      <section
        className={clsx(styles.listMessages, utils.scrollbar, styles.noSmooth, className)}
        {...rest}
        ref={containerRef}
      >
        {isChatHistoryEndReached && (
          <ChatPlaceholder>{getLocaleMessageById('app.chat.start')}</ChatPlaceholder>
        )}
        {page === 0 && isChatLoading && (
          <ChatLoader className={styles.messagesContainer}>
            {getLocaleMessageById('app.chat.loading')}
          </ChatLoader>
        )}
        {page > 1 && isChatLoading && <ChatLoader className={styles.absolutePosLoader} />}
        {}
        {messages.map((msg, index, msgs) => {
          if (msg.type === MessageType.System) {
            return (
              <Text
                as="div"
                className={clsx(utils.dFlex, utils.alignItemsCenter, utils.mY5)}
                style={{ marginLeft: '72px' }}
                key={msg.id}
              >
                {msg.systemPayload?.type === SystemMessageType.UserRemoved ? (
                  <IconUserDelete className={utils.mR2} />
                ) : (
                  <IconUserAdd className={utils.mR2} />
                )}
                <Text variant={TextVariant.BodySemibold} className={utils.mR1}>
                  {getOperatorName(msg.systemPayload as Operator)}
                </Text>{' '}
                {msg.systemPayload?.type === SystemMessageType.UserRemoved
                  ? getLocaleMessageById('app.systemMessage.userRemoved')
                  : getLocaleMessageById('app.systemMessage.userAdded')}
                <span className={utils.mL2}>
                  {new Date(Date.parse(msg.createDate!)).toLocaleTimeString()}
                </span>
              </Text>
            );
          }
          const currentGroupName = getDateGroupName(new Date(Date.parse(msg.createDate!)));
          const nextGroupName =
            msgs[index + 1] && getDateGroupName(new Date(Date.parse(msgs[index + 1].createDate!)));
          const my = userId === msg.author?.id;

          const prevAuthorId = msgs[index - 1]?.author?.id;
          const nextAuthorId = msgs[index + 1]?.author?.id;
          const isInGroup = nextAuthorId === msg.author?.id || prevAuthorId === msg.author?.id;
          const isFirstInGroup =
            (nextAuthorId === msg.author?.id && prevAuthorId !== msg.author?.id) ||
            lastGroupName !== currentGroupName;
          const isLastInGroup =
            (prevAuthorId === msg.author?.id && nextAuthorId !== msg.author?.id) ||
            nextGroupName !== currentGroupName;

          const needNewDivider =
            chats.find((chat) => chat.isSelected)?.newDividerPrevMessage &&
            msgs[index - 1] &&
            msgs[index - 1].id === chats.find((chat) => chat.isSelected)?.newDividerPrevMessage;

          const messageForRender = (
            <React.Fragment key={msg.id}>
              {needNewDivider && msg.author?.id !== userId && (
                <div
                  className={clsx(
                    utils.textCenter,
                    utils.p1,
                    utils.mB2,
                    utils.mT3,
                    colors.bgOnyxBlack10,
                  )}
                >
                  <Text variant={TextVariant.CaptionMedium} color={Colors.OnyxBlack80}>
                    {getLocaleMessageById('app.chat.new')}
                  </Text>
                </div>
              )}

              {lastGroupName !== currentGroupName && (
                <ChatMessageDivider>
                  <Tag
                    readonly
                    title={currentGroupName}
                    className={clsx(utils.mX1, styles.chatTag)}
                    key={currentGroupName}
                  >
                    {currentGroupName}
                  </Tag>
                </ChatMessageDivider>
              )}

              <InternalChatMessage
                msg={msg}
                isMy={my}
                isInGroup={isInGroup}
                isFirstInGroup={isFirstInGroup}
                isLastInGroup={isLastInGroup}
                className={clsx(index === msgs.length - 1 && utils.mBn3)}
              />
            </React.Fragment>
          );

          if (lastGroupName !== currentGroupName) {
            lastGroupName = currentGroupName;
          }

          return messageForRender;
        })}
      </section>
      <ChatScrollToBottomButton
        onClick={handleForceScrollToBottom}
        count={countNewMessages}
        isVisible={!isBottomReached}
      />
    </>
  );
};

export default InternalChatMessagesList;
