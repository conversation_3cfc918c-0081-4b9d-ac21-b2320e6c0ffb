import { createAsyncThunk } from '@reduxjs/toolkit';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import { getCurrentOperatorUsernameString } from '@monorepo/common/src/managers/currentOperatorManager';

import { IFrontAutomationService } from '../../@types/automationService.types';
import { RestServiceDto } from '../../@types/generated/automationServices';
import { getSettings } from '../../config/appSettings';
import { isRest } from '../../helpers/serviceTypeHelper';
import {
  mapFrontAutomationServiceToAddUpdateRestServiceDto,
  mapFrontAutomationServiceToAddUpdateScriptServiceDto,
} from '../../mappers/automationServiceToBack';
import { getAutomationServices } from '../automationServices/automationServices.thunk';

export const saveAutomationService = createAsyncThunk<RestServiceDto, IFrontAutomationService>(
  'automationService/save',
  async (automationService, { dispatch }) => {
    const data = isRest(automationService.type)
      ? mapFrontAutomationServiceToAddUpdateRestServiceDto(automationService)
      : mapFrontAutomationServiceToAddUpdateScriptServiceDto(automationService);

    const settings = getSettings();

    const response = await commonFetch(`${settings.automationServicesManagementUrl}/api/services`, {
      method: 'POST',
      body: JSON.stringify(data),
      credentials: 'include',
      headers: {
        ['Content-Type']: 'application/json',
        createdBy: getCurrentOperatorUsernameString({ fallback: 'front' }),
      },
    });

    dispatch(getAutomationServices());

    return (await response.json()) as RestServiceDto;
  },
);

export const updateAutomationService = createAsyncThunk<RestServiceDto, IFrontAutomationService>(
  'automationService/update',
  async (automationService, { dispatch }) => {
    const data = isRest(automationService.type)
      ? mapFrontAutomationServiceToAddUpdateRestServiceDto(automationService)
      : mapFrontAutomationServiceToAddUpdateScriptServiceDto(automationService);

    const settings = getSettings();

    const response = await commonFetch(
      `${settings.automationServicesManagementUrl}/api/services/${automationService.id ?? ''}`,
      {
        method: 'PUT',
        body: JSON.stringify(data),
        credentials: 'include',
        headers: {
          ['Content-Type']: 'application/json',
          changedBy: getCurrentOperatorUsernameString({ fallback: 'front' }),
        },
      },
    );

    const json = (await response.json()) as RestServiceDto;

    dispatch(getAutomationServices());

    return json;
  },
);
