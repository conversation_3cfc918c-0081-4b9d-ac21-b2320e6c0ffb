import React from 'react';

import clsx from 'clsx';

import { Text, Textarea, TextVariant, utils } from '@product.front/ui-kit';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontScript, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import { useDialogScriptsAppSelector } from '../../../store/hooks';
import InputErrorMessage from '../../InputErrorMessage';

import ScriptSelect from './components/ScriptSelect';
import DeleteStepButton from './DeleteStepButton';
import FirstStepButton from './FirstStepButton';
import ScriptStatusSelect from './ScriptStatusSelect';
import TransferSelect from './TransferSelect';

import styles from './styles.module.scss';

interface ISubscriptProps {
  step: IFrontStep;
  steps: IFrontStep[];
  number: number;
  onlyStep: boolean;
  disabled: boolean;
  requiredForActive: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
}

const Subscript = ({
  step,
  steps,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: ISubscriptProps) => {
  const { selectedScript: currentEditableScript } = useDialogScriptsAppSelector(
    (state) => state.scripts,
  );
  const [selectedScript, setSelectedScript] = React.useState<Partial<IFrontScript> | undefined>(
    step?.subscript,
  );

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,

        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {selectedScript?.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
          <FirstStepButton stepCode={step.code} isFirstStep={step.isFirstStep} />
          {!onlyStep && (
            <DeleteStepButton
              needConfirm={Boolean(
                step.description.length || step.name.length || step.rules?.length,
              )}
              disabled={disabled}
              onDelete={onDelete}
            />
          )}
        </aside>
      </div>

      <ScriptSelect
        label={getLocaleMessageById('app.editor.step.subscriptSelect')}
        selectedScriptId={selectedScript?.id?.toString()}
        filter={(s) =>
          [ScriptStatus.Active, ScriptStatus.Template].includes(s.status) &&
          s.id !== currentEditableScript?.id
        }
        enableStatusIcon={true}
        onSelect={(subscript) => {
          setSelectedScript(subscript);
          onChange({
            ...step,
            name: subscript?.name || '',
            description: subscript?.description || '',
            subscript,
          });
        }}
        required={requiredForActive}
        disabled={disabled}
        invalidMessage={step.invalidReasons?.subscript}
      />

      <Textarea
        label={getLocaleMessageById('app.editor.step.subscriptDescription')}
        value={selectedScript?.description}
        style={{ minHeight: '72px', maxHeight: '20vh' }}
        disabled
      />

      <ScriptStatusSelect
        wrapperClassName={utils.w100}
        label={getLocaleMessageById('app.editor.step.subscriptStatus')}
        value={selectedScript?.status || ScriptStatus.Template}
        required
        disabled
      />

      <TransferSelect
        steps={steps.filter((s) => s.code !== step.code)}
        label={getLocaleMessageById('app.modals.form.transfer')}
        value={step.stepTransfer ?? 'default'}
        onChange={({ value }) => onChange({ ...step, stepTransfer: value || 'default' })}
        required={requiredForActive}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.invalidDefaultRelation}
        message={
          <InputErrorMessage>{step.invalidReasons?.invalidDefaultRelation}</InputErrorMessage>
        }
      />

      {!!step.invalidReasons?.invalidRelation && (
        <aside className={utils.mY4}>
          <InputErrorMessage>{step.invalidReasons?.invalidRelation}</InputErrorMessage>
        </aside>
      )}
    </div>
  );
};

export default Subscript;
