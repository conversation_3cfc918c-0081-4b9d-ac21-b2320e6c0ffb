const { extendBaseJestConfig } = require('@product.front/jest-config');

const conf = extendBaseJestConfig({
  roots: ['<rootDir>/src'],
  globals: {
    window: {
      location: {
        hash: '',
      },
    },
  },
  testEnvironment: 'jsdom',
  transformIgnorePatterns: [],
  preset: 'ts-jest',
  transform: {
    '^.+\\.(ts|tsx)?$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest',
    '.+\\.(gql|mp3)$': 'jest-transform-stub',
  },
});

module.exports = conf;
