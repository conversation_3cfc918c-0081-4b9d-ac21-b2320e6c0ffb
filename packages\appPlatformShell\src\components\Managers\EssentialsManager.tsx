import * as React from 'react';

import { Guid } from 'guid-typescript';

import { Select, InputSize } from '@product.front/ui-kit';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

export type ProfileItemsMap = {
  profileId: string;
  itemIds: string[];
};

export class EssentialItemHolder {
  itemId: string;
  parentItemId: string | null;
  name: string;
  item: any;
  displayName: string;

  constructor(itemId: Guid, parentItemId: Guid | null, name: string, item: any) {
    this.itemId = itemId.toString();
    this.parentItemId = parentItemId?.toString() ?? null;
    this.name = name;
    this.item = item;
    this.displayName = name;
  }

  static createForUserRole(userRole: AWP.Role): EssentialItemHolder {
    return new EssentialItemHolder(userRole.id, userRole.parentId, userRole.name, userRole);
  }
  static createForServiceArea(serviceArea: AWP.ServiceArea): EssentialItemHolder {
    return new EssentialItemHolder(
      serviceArea.id,
      serviceArea.parentServiceAreaId,
      serviceArea.name,
      serviceArea,
    );
  }
  static createForWorkplace(workplace: AWP.Workplace): EssentialItemHolder {
    return new EssentialItemHolder(workplace.id, workplace.parentId, workplace.name, workplace);
  }
}

export type EssentialsManagerProps = {
  id: string;
  label: string;
  alowOnlyLeafs: boolean;
  onSelectionChanged: any;
  selectedItemId: string | null;
};

type EssentialsManagerState = {
  itemsAvailableForSelection: EssentialItemHolder[];
  selectedItemId: string | null;
};

export class EssentialsManager extends React.PureComponent<
  EssentialsManagerProps,
  EssentialsManagerState
> {
  id: string;
  label: string;
  alowOnlyLeafs: boolean;
  onSelectionChanged: any;
  selectedItemId: string | null;
  selectedItem: EssentialItemHolder | null = null;

  allConfiguredItems: EssentialItemHolder[] = new Array<EssentialItemHolder>();
  itemsAvailableByProfiles: EssentialItemHolder[] = new Array<EssentialItemHolder>();
  profilesMap: ProfileItemsMap[] = new Array<ProfileItemsMap>();
  selectedProfileIds: string[] | null = null;

  constructor(props: EssentialsManagerProps) {
    super(props);

    this.id = props.id;
    this.label = props.label;
    this.alowOnlyLeafs = props.alowOnlyLeafs;
    this.onSelectionChanged = props.onSelectionChanged;
    this.selectedItemId = props.selectedItemId;

    this.initialize = this.initialize.bind(this);
    this.onSelectSelectionChanged = this.onSelectSelectionChanged.bind(this);

    this.state = {
      itemsAvailableForSelection: new Array<EssentialItemHolder>(),
      selectedItemId: props.selectedItemId,
    };
  }

  initialize(
    allConfiguredItems: EssentialItemHolder[],
    profilesMap: ProfileItemsMap[],
    selectedProfileIds: string[] | null,
  ): void {
    console.info(`initialize requested...`);

    this.allConfiguredItems = allConfiguredItems;
    this.profilesMap = profilesMap;
    this.itemsAvailableByProfiles = new Array<EssentialItemHolder>();

    this.applyProfileIds(selectedProfileIds);
  }

  getSelectionProfileIds(): string[] {
    if (this.selectedItemId == null) return new Array<string>();
    return this.profilesMap
      .filter((profileMap) => profileMap.itemIds.findIndex((x) => x == this.selectedItemId) != -1)
      .map((x) => x.profileId);
  }

  applyProfileIds(selectedProfileIds: string[] | null): void {
    this.selectedProfileIds = selectedProfileIds;
    const newAvailableItems = this.getItemsAvailableByProfileIds();

    this.setState(() => ({ itemsAvailableForSelection: newAvailableItems }));
    if (
      this.state.selectedItemId != null &&
      newAvailableItems.find((x) => x.itemId == this.state.selectedItemId) != null
    ) {
      this.setNewSelectedItemAndNotify(this.state.selectedItemId);
    } else if (newAvailableItems.length > 0) {
      this.setNewSelectedItemAndNotify(newAvailableItems[0].itemId);
    }
  }

  getItemsAvailableByProfileIds(): EssentialItemHolder[] {
    let result = new Array<EssentialItemHolder>();

    let nestingExists: boolean = false;
    this.profilesMap.forEach((profileMap) => {
      if (
        this.selectedProfileIds == null ||
        this.selectedProfileIds.indexOf(profileMap.profileId) > -1
      ) {
        profileMap.itemIds.forEach((itemId) => {
          if (result.find((x) => x.itemId == itemId) == null) {
            const itemToAdd = this.allConfiguredItems.find((x) => x.itemId == itemId);
            if (itemToAdd != null) {
              const leafsToAdd = this.getLeaves(itemToAdd);
              leafsToAdd.forEach((leafToAdd) => {
                if (leafToAdd.parentItemId != null) {
                  nestingExists = true;
                }
                result.push(leafToAdd);
              });
            }
          }
        });
      }
    });

    if (nestingExists) {
      this.appendPathesToRoot(result);
    }

    result = result.sort((a, b) => a.displayName.localeCompare(b.displayName));

    return result;
  }

  appendPathesToRoot(items: EssentialItemHolder[]) {
    items.forEach((item) => {
      item.displayName = item.name;
      this.appendPathToRoot(item, item.parentItemId);
    });
  }

  appendPathToRoot(item: EssentialItemHolder, parentItemId: string | null) {
    if (parentItemId == null) return;

    const parent = this.allConfiguredItems.find((x) => x.itemId == parentItemId);
    if (parent == null) {
      item.displayName = `unknwn\\${item.name}`;
      return;
    }

    item.displayName = `${parent?.name}\\${item.displayName}`;
    this.appendPathToRoot(item, parent?.parentItemId);
  }

  setNewSelectedItemAndNotify(selectedItemId: string | null) {
    this.setState(() => ({ selectedItemId: selectedItemId }));
    this.selectedItemId = selectedItemId;
    const selectedItemAsAny: any = this.allConfiguredItems.find(
      (x) => x.itemId == this.selectedItemId,
    );
    this.selectedItem = selectedItemAsAny;
    console.info(`setNewSelectedItemAndNotify: this.selectedItem=${this.selectedItem?.name}`);
    this.onSelectionChanged();
  }

  getLeaves(item: EssentialItemHolder): EssentialItemHolder[] {
    if (!!this.alowOnlyLeafs === false) {
      return new Array<EssentialItemHolder>(item);
    }

    const result = new Array<EssentialItemHolder>();
    const leaves = this.allConfiguredItems.filter((x) => x.parentItemId == item.itemId);

    if (leaves.length == 0) {
      result.push(item);
    } else {
      leaves.forEach((leaf) => {
        const leafLeaves = this.getLeaves(leaf);
        leafLeaves.forEach((oneMoreLeaf) => {
          result.push(oneMoreLeaf);
        });
      });
    }

    return result;
  }

  onSelectSelectionChanged(selectedId: string | null) {
    console.info(`selection changed: ${selectedId}`);
    this.setNewSelectedItemAndNotify(selectedId);
  }

  render() {
    return (
      <div className="st-splash-mt32">
        <Select
          data={this.state.itemsAvailableForSelection.map((a) => {
            return { value: a.itemId, text: a.displayName };
          })}
          size={InputSize.Big}
          className="st-splash"
          id={this.id}
          label={this.label}
          onChange={(v) => {
            this.onSelectSelectionChanged(v.value);
          }}
          value={this.state.selectedItemId}
        />
      </div>
    );
  }
}

export default EssentialsManager;
