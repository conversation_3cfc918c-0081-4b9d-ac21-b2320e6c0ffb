export enum IFrontKpiUnit {
  None = 'none',
  Seconds = 'seconds',
  Minutes = 'minutes',
  Hours = 'hours',
  Days = 'days',
}

export interface IFrontDistributionRule {
  name: string;
  description: string;
  ruleType: string;
  isDefault: boolean;
}

export interface IFrontQueueBot {
  enabled: boolean;
  code: string | null;
}

export interface IFrontQueueKpi {
  unit: IFrontKpiUnit;
  transferCallDestination: number | null;
  code: string;
  alarmThreshold: number;
  warningThreshold: number | null;
}

export interface IFrontRoutingRule {
  attributeId: number;
  comparisonRule: string;
  value: string;
}

export interface IFrontRoutingRuleSet {
  id: number;
  rules: IFrontRoutingRule[];
}

export enum IAutoHandlerType {
  EmptyAutoHandler = 'EmptyAutoHandler',
  PredictedWaitTimeAutoHandler = 'PredictedWaitTimeAutoHandler',
  TemplateAutoHandler = 'TemplateAutoHandler',
}

export type IFrontQueueAutoHandler = { code: string } & (
  | {
      type: IAutoHandlerType.PredictedWaitTimeAutoHandler;
      min: number;
      max: number;
      templateBelowMin: string | null;
      templateWithinRange: string | null;
      templateAboveMax: string | null;
    }
  | {
      type: IAutoHandlerType.TemplateAutoHandler;
      templateId: string;
    }
  | {
      type: IAutoHandlerType.EmptyAutoHandler;
    }
);

export interface IFrontQueueBase {
  id: number;
  name: string;
  description: string;
  weight: number;
  operatorsNumber: number;
  isDefault: boolean;
  isService: boolean;
}
export interface IFrontQueue extends Omit<IFrontQueueBase, 'operatorsNumber'> {
  division: string;
  operators: { id: string; priority: number }[];
  operatorGroups: string[];
  distributionRuleType: string;
  kpiParameters: IFrontQueueKpi[];
  buttonBot: IFrontQueueBot;
  intelligentBot: IFrontQueueBot;
  ratingBot: IFrontQueueBot;
  routingRules: IFrontRoutingRuleSet[];
  autoHandlers: IFrontQueueAutoHandler[];
}
