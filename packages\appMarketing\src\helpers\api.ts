import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  ICampaignResultFront,
  ICampaignResultOdata,
  ICampaignStatistics,
  IFrontCampaign,
  ResultReason,
} from '../@types/campaign';
import {
  CampaignResponse,
  Channel,
  DisplayCampaignStatus,
  RequestQueue,
  TemplateMessage,
} from '../@types/generated/marketing';
import { IOffer, IOfferType, TemplateType } from '../@types/offer';
import { IClientTableView } from '../@types/settings';
import { getSettings } from '../config/appSettings';
import { mapFrontCampaignToBackCreateOrUpdate } from '../mappers/campaign';

const jsonHeaders = {
  'Content-Type': 'application/json',
};

export const getOffersAsync = async () => {
  const result = await commonFetch(`${getSettings().productMarketingService}/api/Offers`, {
    credentials: 'include',
  });
  return (await result.json()) as IOffer[];
};

export const getOfferTypesAsync = async () => {
  const result = await commonFetch(
    `${getSettings().productMarketingService}/api/Settings/offerTypes`,
    {
      credentials: 'include',
    },
  );
  return (await result.json()) as IOfferType[];
};

export const getTemplatesAsync = async (type: TemplateType) => {
  const result = await commonFetch(
    `${getSettings().productMarketingService}/api/Robot/${type}/dialogs`,
    {
      credentials: 'include',
    },
  );
  return (await result.json()) as TemplateMessage[];
};

export const createOfferAsync = async (offer: IOffer) => {
  const result = await commonFetch(`${getSettings().productMarketingService}/api/Offers`, {
    method: 'POST',
    credentials: 'include',
    headers: {
      ...jsonHeaders,
    },
    body: JSON.stringify(offer),
  });
  const id = (await result.json()) as string;

  return { ...offer, id };
};

export const updateOfferAsync = async (offer: IOffer) => {
  await commonFetch(`${getSettings().productMarketingService}/api/Offers/${offer.id}`, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      ...jsonHeaders,
    },
    body: JSON.stringify(offer),
  });

  return { ...offer };
};

export const deleteOfferAsync = async (offerId: string) => {
  return await commonFetch(`${getSettings().productMarketingService}/api/Offers/${offerId}`, {
    method: 'DELETE',
    credentials: 'include',
  });
};

export const getCampaignsAsync = async (
  displayStatus: DisplayCampaignStatus | null,
): Promise<IFrontCampaign[]> => {
  const query = new URLSearchParams();
  displayStatus !== null && query.append('campaignstatus', displayStatus.toString());

  const result = await commonFetch(
    `${getSettings().productMarketingService}/api/Campaigns?${query.toString()}`,
    {
      credentials: 'include',
    },
  );
  return (((await result.json()) ?? []) as CampaignResponse[]).map((backValue) => ({
    id: backValue.id ?? '',
    name: backValue.name,
    description: backValue.description ?? '',
    dateFrom: backValue.dateFrom,
    dateTo: backValue.dateTo,
    priority: backValue.priority,
    type: backValue.type,
    channels: backValue.channelLinks.map((channelLink) => channelLink.channelCode),
    accomplishmentPercent: backValue.accomplishmentPercent ?? 0,
    feedbackPercent: backValue.feedbackPercent ?? 0,
    needAnswer: !!backValue.needAnswer,
    availableForBot: backValue.availableForBot,
    queueId: backValue.queueId ?? 0,
    startTime: backValue.startTime ?? null,
    stopTime: backValue.stopTime ?? null,
    ignoreClientTimeZone: backValue.ignoreClientTimeZone,
    responseWaitingTime: backValue.responseWaitingTime ?? 0,
    offerId: backValue.offerId,
    filter: backValue.filter ?? null,
    filterDescriptors: backValue.filterDescriptors ?? '',
    status: backValue.status,
    displayStatus: backValue.displayStatus,
    author: backValue.author,
    fileUploadingId: backValue.fileUploadingId ?? null,
  }));
};

export const getChannelsAsync = async () => {
  const result = await commonFetch(
    `${getSettings().productMarketingService}/api/Settings/channels`,
    {
      credentials: 'include',
    },
  );

  const value: Record<number, Channel> = {};

  ((await result.json()) as Channel[]).map((channel) => {
    value[channel.channelCode ?? 0] = { ...channel };
  });

  return value;
};

export const getQueuesAsync = async () => {
  const result = await commonFetch(`${getSettings().productMarketingService}/api/Settings/queues`, {
    credentials: 'include',
  });

  return (await result.json()) as RequestQueue[];
};

export const createCampaignAsync = async (campaign: IFrontCampaign) => {
  const result = await commonFetch(`${getSettings().productMarketingService}/api/Campaigns`, {
    method: 'POST',
    credentials: 'include',
    headers: {
      ...jsonHeaders,
    },
    body: JSON.stringify(mapFrontCampaignToBackCreateOrUpdate(campaign)),
  });

  const id = (await result.json()) as string;

  return { ...campaign, id };
};

export const updateCampaignAsync = async (campaign: IFrontCampaign) => {
  await commonFetch(`${getSettings().productMarketingService}/api/Campaigns/${campaign.id}`, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      ...jsonHeaders,
    },
    body: JSON.stringify(mapFrontCampaignToBackCreateOrUpdate(campaign)),
  });

  return { ...campaign };
};

export const deleteCampaignAsync = async (campaignId: string) => {
  return await commonFetch(`${getSettings().productMarketingService}/api/Campaigns/${campaignId}`, {
    method: 'DELETE',
    credentials: 'include',
  });
};

export const getClientTableViewAsync = async () => {
  const result = await commonFetch(
    `${getSettings().productMarketingService}/api/Settings/gridSetting`,
    {
      credentials: 'include',
    },
  );

  return (await result.json()) as IClientTableView;
};

export const getCampaignResultsAsync = async (campaignId: string) => {
  const result = await commonFetch(
    `${getSettings().productMarketingService}/odata/results?$filter=CampaignId eq ${campaignId}`,
    {
      credentials: 'include',
    },
  );

  const json = (await result.json()) as { value: ICampaignResultOdata[] };

  return json.value.map(
    (odataResult) =>
      ({
        id: odataResult.Id,
        contactDate: odataResult.ResultDate,
        status: odataResult.OfferStatus,
        result: odataResult.ResultType,
        rejectReason: odataResult.Reason,
        fio: [
          odataResult.ContactPersonLastName,
          odataResult.ContactPersonFirstName,
          odataResult.ContactPersonMiddleName,
        ]
          .filter(Boolean)
          .join(' '),
        comment: odataResult.Comment,
      }) as ICampaignResultFront,
  );
};

export const getResultsReasonsAsync = async () => {
  const result = await commonFetch(
    `${getSettings().productMarketingService}/api/Settings/reasons`,
    {
      credentials: 'include',
    },
  );

  return (await result.json()) as ResultReason[];
};

export const getContactPersonsFromFileAsync = async (fileId: string) => {
  const result = await commonFetch(
    `${
      getSettings().productMarketingService
    }/odata/ContactPersonsFromFile?$filter=fileUploadingId eq ${fileId}`,
    {
      credentials: 'include',
    },
  );

  return (await result.json()) as { value: any[] };
};

export const getContactPersonsAsync = async (channels: number[], filterString: string) => {
  const result = await commonFetch(
    `${
      getSettings().productMarketingService
    }/odata/ContactPersons?$filter=channelId in (${channels.join(', ')})${
      filterString ? ` and ${filterString}` : ''
    }`,
    {
      credentials: 'include',
    },
  );

  return (await result.json()) as { value: any[] };
};

export const uploadFilesDataAsync = async (addresses: string[]) => {
  const response = await commonFetch(
    `${getSettings().productMarketingService}/api/ClientLinks/clients`,
    {
      credentials: 'include',
      method: 'POST',
      headers: {
        ...jsonHeaders,
      },
      body: JSON.stringify(addresses),
    },
  );

  return (await response.json()) as string;
};

export const getStatisticsAsync = async (campaignId: string) => {
  const response = await commonFetch(
    `${getSettings().productMarketingService}/api/Statistics/${campaignId}`,
    { credentials: 'include' },
  );

  return (await response.json()) as ICampaignStatistics;
};
