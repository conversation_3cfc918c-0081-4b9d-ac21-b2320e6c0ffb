import React from 'react';

import { ColumnDef } from '@tanstack/react-table';
import clsx from 'clsx';

import {
  Checkbox,
  Jumbotron,
  JumbotronType,
  OverlayLoader,
  Table,
  utils,
} from '@product.front/ui-kit';

import {
  FrontTemplateStatus,
  IFrontFolder,
  IFrontTemplateBase,
} from '@monorepo/common/src/@types/templates';
import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { TemplateCategory } from '../../../../../../@types/generated/administration';
import { IFrontAutoHandler } from '../../../../../../@types/parameters';
import { IAutoHandlerType, IFrontQueueAutoHandler } from '../../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { mapAnswerFolderToFrontFolder } from '../../../../../../mappers/folders';
import { getFolderTemplates } from '../../../../../../services/template';
import { IValidationResult } from '../validator';

import TemplateSelector from './TemplateSelector';

interface IAutoHandlerSettingsProps {
  autoHandlers: IFrontAutoHandler[];
  queueAutoHandlersMap: Record<string, IFrontQueueAutoHandler>;
  validationResult: IValidationResult['autoHandlers'];
  onChange: (autoHandler: IFrontQueueAutoHandler) => void;
}

const AutoHandlerSettings = ({
  autoHandlers,
  queueAutoHandlersMap,
  validationResult,
  onChange,
}: IAutoHandlerSettingsProps) => {
  const [selectedAutoHandler, setSelectedAutoHandler] = React.useState<IFrontAutoHandler | null>(
    null,
  );
  const [autoTemplates, setAutoTemplates] = React.useState<IFrontTemplateBase[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();

  React.useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const foldersMap: Record<string, IFrontFolder> = {};
        mapAnswerFolderToFrontFolder(
          (
            await getFolderTemplates(TemplateCategory.AutoReplies, {
              statuses: [FrontTemplateStatus.Published],
            })
          ).autoReplies,
          foldersMap,
        );

        const templates: IFrontTemplateBase[] = [];

        Object.keys(foldersMap).forEach((folderId) => {
          templates.push(...(foldersMap[folderId]?.templates ?? []));
        });

        setAutoTemplates(templates);
      } catch (e) {
        setError(e);
        console.error('Error getting templates for auto handlers', e);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  if (error) {
    return (
      <AlertError header={getLocaleMessageById('queues.editor.autoHandlers.error')} error={error} />
    );
  }

  return (
    <OverlayLoader loading={loading} wrapperClassName={clsx(utils.dFlex, utils.flexColumn)}>
      <div
        className={clsx(
          utils.flexGrow0,
          utils.scrollbar,
          utils.overflowAuto,
          utils.borderBottom,
          utils.mB4,
        )}
      >
        {validationResult.autoHandlers && (
          <InputErrorMessage>{validationResult.autoHandlers}</InputErrorMessage>
        )}
        <Table<IFrontAutoHandler & { enabled: boolean }>
          data={autoHandlers.map((value) => ({
            ...value,
            enabled:
              (queueAutoHandlersMap[value.code]?.type ?? IAutoHandlerType.EmptyAutoHandler) !==
              IAutoHandlerType.EmptyAutoHandler,
            meta: { active: value.code === selectedAutoHandler?.code },
          }))}
          columns={
            [
              {
                accessorKey: 'name',
                header: getLocaleMessageById('queues.editor.autoHandler'),
                enableColumnFilter: true,
                enableSorting: true,
                meta: {
                  defaultSorting: 'asc',
                },
                maxSize: 1800,
              },
              {
                accessorKey: 'enabled',
                accessorFn: (row) => {
                  const enableAutoHandler = (): IFrontQueueAutoHandler => {
                    return row.isPvoo
                      ? {
                          code: row.code,
                          type: IAutoHandlerType.PredictedWaitTimeAutoHandler,
                          min: 0,
                          max: 0,
                          templateBelowMin: null,
                          templateWithinRange: null,
                          templateAboveMax: null,
                        }
                      : {
                          code: row.code,
                          type: IAutoHandlerType.TemplateAutoHandler,
                          templateId: '',
                        };
                  };

                  return (
                    <Checkbox
                      checked={row.enabled}
                      className={clsx(utils.mLauto, utils.mRauto)}
                      onChange={({ checked }) =>
                        onChange(
                          checked
                            ? enableAutoHandler()
                            : {
                                code: row.code,
                                type: IAutoHandlerType.EmptyAutoHandler,
                              },
                        )
                      }
                    />
                  );
                },
                header: getLocaleMessageById('queues.editor.autoHandlerEnabled'),
                size: 45,
                maxSize: 45,
                enableResizing: false,
              },
            ] as ColumnDef<IFrontAutoHandler & { enabled: boolean }>[]
          }
          onRowClick={(row) => setSelectedAutoHandler(row.data)}
          fixHeader
        />
      </div>
      {selectedAutoHandler ? (
        <TemplateSelector
          templates={autoTemplates}
          queueAutoHandler={
            queueAutoHandlersMap[selectedAutoHandler.code] ?? {
              type: IAutoHandlerType.EmptyAutoHandler,
              code: selectedAutoHandler.code,
            }
          }
          onChange={onChange}
        />
      ) : (
        <div
          className={clsx(
            utils.h50,
            utils.dFlex,
            utils.alignItemsCenter,
            utils.justifyContentCenter,
          )}
        >
          <Jumbotron
            type={JumbotronType.Info}
            header={getLocaleMessageById('queues.editor.noSelectedAutoHandler')}
          />
        </div>
      )}
    </OverlayLoader>
  );
};

export default AutoHandlerSettings;
