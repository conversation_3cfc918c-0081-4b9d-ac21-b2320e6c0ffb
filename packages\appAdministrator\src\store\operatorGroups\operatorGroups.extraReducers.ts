import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IOperatorGroupsStore } from './operatorGroups.slice';
import {
  deleteOperatorGroup,
  getAllOperatorGroups,
  getOperatorGroup,
  saveOperatorGroup,
} from './operatorGroups.thunk';

const getAllOperatorGroupsReducers = (builder: ActionReducerMapBuilder<IOperatorGroupsStore>) =>
  builder
    .addCase(getAllOperatorGroups.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllOperatorGroups.fulfilled, (state, action) => {
      state.loading = false;
      state.operatorGroupsMap = action.payload;
    })
    .addCase(getAllOperatorGroups.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

const getOperatorGroupReducers = (builder: ActionReducerMapBuilder<IOperatorGroupsStore>) =>
  builder
    .addCase(getOperatorGroup.pending, (state) => {
      state.selectedLoading = true;
      state.selectedError = undefined;
      state.selectedOperatorGroup = null;
    })
    .addCase(getOperatorGroup.fulfilled, (state, action) => {
      state.selectedLoading = false;
      state.selectedOperatorGroup = action.payload;
    })
    .addCase(getOperatorGroup.rejected, (state, action) => {
      state.selectedLoading = false;
      state.selectedError = action.error;
    });

const saveOperatorGroupReducers = (builder: ActionReducerMapBuilder<IOperatorGroupsStore>) =>
  builder
    .addCase(saveOperatorGroup.pending, (state) => {
      state.saveLoading = true;
      state.saveError = undefined;
    })
    .addCase(saveOperatorGroup.fulfilled, (state) => {
      state.saveLoading = false;
      state.selectedOperatorGroup = null;
    })
    .addCase(saveOperatorGroup.rejected, (state, action) => {
      state.saveLoading = false;
      state.saveError = action.error;
    });

const deleteOperatorGroupReducers = (builder: ActionReducerMapBuilder<IOperatorGroupsStore>) =>
  builder
    .addCase(deleteOperatorGroup.pending, (state) => {
      state.loading = true;
    })
    .addCase(deleteOperatorGroup.rejected, (state) => {
      state.loading = false;
    })
    .addCase(deleteOperatorGroup.fulfilled, (state) => {
      state.loading = false;
      state.selectedOperatorGroup = null;
    });

export default (builder: ActionReducerMapBuilder<IOperatorGroupsStore>) => {
  getAllOperatorGroupsReducers(builder);
  getOperatorGroupReducers(builder);
  saveOperatorGroupReducers(builder);
  deleteOperatorGroupReducers(builder);

  return builder;
};
