import React, { FormEvent } from 'react';

import clsx from 'clsx';
import { Edge, useReactFlow } from 'reactflow';

import { Button, ButtonVariant, Checkbox, Input, Text, utils } from '@product.front/ui-kit';

import IconDelete from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

interface IEdgeEditorProps {
  edge: Edge;
  onClose(): void;
  ref?: React.RefObject<HTMLFormElement | null>;
}

const EdgeEditor: React.FC<IEdgeEditorProps> = ({ edge, onClose, ref }) => {
  const [label, setLabel] = React.useState(edge.label as string);
  const [isCustom, setIsCustom] = React.useState(false);
  const { setEdges } = useReactFlow();

  const handleSave = (e: FormEvent) => {
    e.preventDefault();
    const type = isCustom ? 'custom' : '';
    setEdges((edges) => edges.map((n) => (n.id === edge.id ? { ...n, label, type } : n)));
    onClose();
  };
  const handleDelete = () => {
    setEdges((nodes) => nodes.filter((n) => n.id !== edge.id));
    onClose();
  };

  const allowEdit = false;

  return (
    <form
      ref={ref}
      className={clsx(utils.dFlex, utils.flexColumn, utils.gap2, utils.h100)}
      onSubmit={handleSave}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.p4, utils.borderBottom)}>
        <Text>*</Text>
        <Button variant={ButtonVariant.Transparent} onClick={handleDelete}>
          <IconDelete />
        </Button>
      </div>
      {allowEdit && (
        <div className={utils.p4}>
          <Input
            autoFocus
            label="Label"
            value={label}
            onChange={({ value }) => setLabel(value || '')}
          />
          <Checkbox
            label="custom"
            onChange={({ checked }) => setIsCustom(!!checked)}
            checked={isCustom}
          />
        </div>
      )}
    </form>
  );
};

export default EdgeEditor;
