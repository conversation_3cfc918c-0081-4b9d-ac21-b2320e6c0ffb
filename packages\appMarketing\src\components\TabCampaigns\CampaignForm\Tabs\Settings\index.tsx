import React from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  Input,
  Textarea,
  Text,
  utils,
  Radio,
  Checkbox,
  Select,
  grids,
  HeaderButton,
  ActionButtonVariant,
  showModal,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';

import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';

import {
  AutomaticType,
  CampaignType,
  DisplayCampaignStatus,
} from '../../../../../@types/generated/marketing';
import { IOffer } from '../../../../../@types/offer';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { updateCampaignField } from '../../../../../store/campaigns/campaigns.slice';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../../../../store/hooks';
import { setSelectedOffer } from '../../../../../store/offers/offers.slice';
import {
  createOrUpdateOffer,
  getNeuroNetTemplates,
  getWhatsAppTemplates,
} from '../../../../../store/offers/offers.thunk';
import { getOfferTypes } from '../../../../../store/settings/settings.thunk';
import { emptyOffer } from '../../../../TabOffers';
import OfferForm from '../../../../TabOffers/OfferForm';
import ErrorMessage from '../../../../TabOffers/OfferForm/ErrorMessage';

import IncomingFields from './IncomingFields';
import OfferPreview from './OfferPreview';
import OutgoingFields from './OutgoingFields';

import styles from './styles.module.scss';

const campaignTypeComponents = {
  [CampaignType.Incoming]: <IncomingFields />,
  [CampaignType.Outgoing]: <OutgoingFields />,
};

const Settings = () => {
  const dispatch = useMarketingAppDispatch();

  const { selectedCampaign, campaignInEdit, validationResult } = useMarketingAppSelector(
    (state) => state.campaigns,
  );
  const {
    offers,
    saving: savingOffer,
    whatsappTemplates,
    neuronetTemplates,
    selectedOffer,
  } = useMarketingAppSelector((state) => state.offers);
  const { channels, offerTypes } = useMarketingAppSelector((state) => state.settings);

  const filteredOffers = React.useMemo(() => {
    if (!campaignInEdit) return offers;

    let offersToReturn: IOffer[] = [...offers];

    const filterOffersByChannel = () => {
      if (
        campaignInEdit.channels.some((channel) =>
          channels[channel].sourceName?.includes?.('WHATSAPP'),
        )
      ) {
        return offersToReturn.filter((offer) => offer.automaticType === AutomaticType.WhatsApp);
      }

      if (
        campaignInEdit.channels.some(
          (channel) => channels[channel].sourceName === UcmmChannel.Voice,
        )
      ) {
        return offersToReturn.filter((offer) => offer.automaticType === AutomaticType.NeuroNet);
      }

      return offersToReturn.filter((offer) => offer.automaticType === AutomaticType.Default);
    };

    if (campaignInEdit.dateFrom && campaignInEdit.dateTo) {
      offersToReturn = offers.filter(
        (offer) =>
          (offer.dateFrom <= campaignInEdit.dateTo! &&
            offer.dateFrom >= campaignInEdit.dateFrom!) ||
          (campaignInEdit.dateFrom! <= offer.dateTo && campaignInEdit.dateFrom! >= offer.dateFrom),
      );
    }

    if (campaignInEdit.type === CampaignType.Outgoing) {
      offersToReturn = filterOffersByChannel();
    }

    return offersToReturn.sort((a, b) => a.name.localeCompare(b.name));
  }, [campaignInEdit, offers, channels]);

  // Менять настройки разрешено только в новой и запланированной кампаниях
  const isChangeDisabled = ![DisplayCampaignStatus.New, DisplayCampaignStatus.Planned].includes(
    selectedCampaign?.displayStatus ?? DisplayCampaignStatus.New,
  );

  // очищаем выбранный offerId, если его нет среди отсортированных
  React.useEffect(() => {
    if (!campaignInEdit?.offerId) return;

    if (filteredOffers.some((offer) => offer.id === campaignInEdit.offerId)) return;

    dispatch(updateCampaignField({ fieldName: 'offerId', fieldValue: '' }));
  }, [filteredOffers, campaignInEdit?.offerId, dispatch]);

  React.useEffect(() => {
    dispatch(setSelectedOffer(null));
    dispatch(getOfferTypes());
    dispatch(getWhatsAppTemplates());
    dispatch(getNeuroNetTemplates());
  }, [dispatch]);

  React.useEffect(() => {
    if (!selectedOffer?.id) return;

    dispatch(updateCampaignField({ fieldName: 'offerId', fieldValue: selectedOffer.id }));
    dispatch(setSelectedOffer(null));
  }, [dispatch, selectedOffer]);

  if (!campaignInEdit) return null;

  const {
    name,
    description,
    type,
    needAnswer,
    offerId,
    channels: campaignChannels,
  } = campaignInEdit;

  return (
    <form className={clsx(utils.dGrid, utils.pX6, utils.pY5, utils.gap6, styles.formContainer)}>
      <Input
        label={getLocaleMessageById('app.campaigns.form.label.name')}
        value={name}
        onChange={({ value }) =>
          dispatch(updateCampaignField({ fieldName: 'name', fieldValue: value ?? '' }))
        }
        canClearBehavior={CanClearBehavior.Always}
        required
        isInvalid={!!validationResult.name}
        message={<ErrorMessage>{validationResult.name}</ErrorMessage>}
        readOnly={isChangeDisabled}
      />
      <Textarea
        label={getLocaleMessageById('app.campaigns.form.label.description')}
        value={description}
        onChange={({ value }) =>
          dispatch(updateCampaignField({ fieldName: 'description', fieldValue: value ?? '' }))
        }
        rows={8}
        readOnly={isChangeDisabled}
      />
      <div className={clsx(utils.dFlex, utils.alignItemsEnd, utils.justifyContentBetween)}>
        <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap3)}>
          <Text>{getLocaleMessageById('app.campaigns.form.label.type')}</Text>
          <div className={clsx(utils.dFlex, utils.gap3)}>
            <Radio
              label={getLocaleMessageById('app.campaigns.form.label.typeIncoming')}
              value={CampaignType.Incoming}
              checked={type === CampaignType.Incoming}
              onChange={() =>
                dispatch(
                  updateCampaignField({ fieldName: 'type', fieldValue: CampaignType.Incoming }),
                )
              }
              disabled={!!campaignInEdit.id}
            />
            <Radio
              label={getLocaleMessageById('app.campaigns.form.label.typeOutgoing')}
              value={CampaignType.Outgoing}
              checked={type === CampaignType.Outgoing}
              onChange={() =>
                dispatch(
                  updateCampaignField({ fieldName: 'type', fieldValue: CampaignType.Outgoing }),
                )
              }
              disabled={!!campaignInEdit.id}
            />
          </div>
        </div>

        {!campaignChannels.some(
          (channel) => channels[channel].sourceName === UcmmChannel.Voice,
        ) && (
          <Checkbox
            label={getLocaleMessageById('app.campaigns.form.label.isInformational')}
            checked={!needAnswer}
            onChange={({ checked }) =>
              dispatch(updateCampaignField({ fieldName: 'needAnswer', fieldValue: !checked }))
            }
            disabled={isChangeDisabled}
          />
        )}
      </div>
      {campaignTypeComponents[type]}
      <div className={clsx(grids.row, utils.alignItemsCenter, styles.fullWidth)}>
        <Select
          wrapperClassName={grids.col8}
          label={getLocaleMessageById('app.campaigns.form.label.offer')}
          value={offerId}
          data={filteredOffers.map((offer) => ({ value: offer.id, text: offer.name }))}
          onChange={({ value }) =>
            dispatch(updateCampaignField({ fieldName: 'offerId', fieldValue: value }))
          }
          required
          isInvalid={!!validationResult.offerId}
          message={<ErrorMessage>{validationResult.offerId}</ErrorMessage>}
          readOnly={isChangeDisabled}
        />
        <HeaderButton
          className={clsx(
            utils.dFlex,
            utils.alignItemsCenter,
            utils.gap2,
            utils.p2,
            styles.addButton,
          )}
          variant={ActionButtonVariant.Light}
          onClick={() => {
            showModal({
              children: (close) => (
                <div style={{ width: '50vw', height: '80vh' }}>
                  <OfferForm
                    selectedOffer={emptyOffer}
                    saving={savingOffer}
                    offerTypes={offerTypes}
                    onCancel={close!}
                    onSubmit={async (offer) => {
                      await dispatch(createOrUpdateOffer(offer));
                      close?.();
                    }}
                    whatsAppTemplates={whatsappTemplates}
                    neuroNetTemplates={neuronetTemplates}
                  />
                </div>
              ),
              header: getLocaleMessageById('app.campaigns.form.addOffer.header'),
              flushBody: true,
              canClose: false,
            });
          }}
          disabled={isChangeDisabled}
        >
          <IconAdd />
          <Text>{getLocaleMessageById('app.campaigns.form.addOffer.button')}</Text>
        </HeaderButton>
      </div>

      <OfferPreview />
    </form>
  );
};

export default Settings;
