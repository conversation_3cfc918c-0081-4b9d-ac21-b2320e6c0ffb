import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  CanClearBehavior,
  grids,
  IconButton,
  Input,
  Text,
  TextVariant,
  utils,
  Collapsible,
  colors,
} from '@product.front/ui-kit';

import IconArrowDown from '@product.front/icons/dist/icons11/MainStuff/IconArrowDown';
import IconArrowUp from '@product.front/icons/dist/icons11/MainStuff/IconArrowUp';
import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconDropDown from '@product.front/icons/dist/icons17/MainStuff/IconDropDown';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';
import IconDelete from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import {
  FrontConditionOperator,
  IFrontRule,
  IFrontRuleInvalidReasons,
  IFrontStep,
} from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import InputErrorMessage from '../../InputErrorMessage';

import Condition from './Condition';
import TransferSelect from './TransferSelect';

interface IRuleProps {
  rule: IFrontRule;
  onDelete: () => void;
  onChange: (updatedRule: IFrontRule) => void;
  disabled?: boolean;
  steps: IFrontStep[];
  variables: Record<string, IFrontStep['variableName']>;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
  invalidReasons?: IFrontRuleInvalidReasons;
  canDelete?: boolean;
}

const Rule = ({
  rule,
  onDelete,
  onChange,
  disabled,
  steps,
  variables,
  onMoveUp,
  onMoveDown,
  invalidReasons,
  canDelete,
}: IRuleProps) => {
  const handleConditionAdd = () => {
    onChange({
      ...rule,
      conditions: [
        ...(rule.conditions || []),
        {
          id: `${rule.id}-condition-${new Date().toISOString()}`,
          variable: null,
          value: '',
          operator: FrontConditionOperator.Eq,
        },
      ],
    });
  };

  return (
    <Collapsible
      open
      activatorComponent={(isOpen) => {
        return (
          <header className={clsx(grids.row, utils.alignItemsCenter)}>
            <div className={clsx(grids.col11, utils.dFlex, utils.alignItemsCenter)}>
              {!isOpen ? (
                <IconDropRight className={utils.flexShrink0} />
              ) : (
                <IconDropDown className={clsx(colors.colorMoodBlue70, utils.flexShrink0)} />
              )}
              <Text
                className={utils.mL2}
                ellipsis
                noWrap
                title={rule.name || ''}
                variant={TextVariant.SubheadSemibold}
              >
                {rule.name || getLocaleMessageById('app.editor.routerNewRule')}
              </Text>
            </div>
            <div className={grids.col1}>
              <IconButton
                onClick={onDelete}
                disabled={disabled || !canDelete}
                style={{ width: '100%' }}
              >
                <IconDelete />
              </IconButton>
            </div>
          </header>
        );
      }}
      className={clsx(utils.pR4, utils.pY4)}
    >
      <section
        className={clsx(utils.mL2, utils.pT2, utils.pL4, utils.borderLeft)}
        style={{ borderColor: 'var(--palette-moodBlue-70)' }}
      >
        <div className={clsx(utils.pY2)}>
          <Input
            wrapperClassName={clsx(utils.w100, utils.mB2)}
            placeholder={getLocaleMessageById('app.editor.noName')}
            label={getLocaleMessageById('app.editor.routerRuleName')}
            required
            canClearBehavior={CanClearBehavior.Value}
            autoFocus={!rule.name}
            value={rule.name || ''}
            withDebounce
            onChange={({ value }) => {
              onChange({
                ...rule,
                name: value ?? '',
              });
            }}
            disabled={disabled}
            isInvalid={!!invalidReasons?.ruleName}
            message={<InputErrorMessage>{invalidReasons?.ruleName}</InputErrorMessage>}
          />
          {!!invalidReasons?.conditionsRequired && (
            <aside className={utils.mY4}>
              <InputErrorMessage>
                {invalidReasons.conditionsRequired}&nbsp; (
                {getLocaleMessageById('app.editor.routerRuleCondition')})
              </InputErrorMessage>
            </aside>
          )}
          {rule.conditions?.map((condition) => {
            return (
              <Condition
                condition={condition}
                variables={variables}
                onDelete={() => {
                  onChange({
                    ...rule,
                    conditions: rule.conditions.filter((c) => c.id !== condition.id),
                  });
                }}
                onChange={(updatedCondition) => {
                  onChange({
                    ...rule,
                    conditions: rule.conditions.map((c) =>
                      c.id === condition.id ? { ...c, ...updatedCondition } : c,
                    ),
                  });
                }}
                key={condition.id}
                invalidReasons={invalidReasons}
              />
            );
          })}
          <footer className={clsx(utils.dFlex, utils.justifyContentBetween)}>
            <Button
              className={clsx(utils.dFlex, utils.gap2)}
              variant={ButtonVariant.Transparent}
              onClick={handleConditionAdd}
              style={{ alignSelf: 'start', padding: 0 }}
              disabled={disabled}
            >
              <IconAdd />
              {getLocaleMessageById('app.editor.routerRuleCondition')}
            </Button>
            <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
              <IconButton
                disabled={!onMoveUp}
                onClick={onMoveUp}
                size="Small"
                title={getLocaleMessageById('app.editor.routerRuleMoveUp')}
              >
                <IconArrowUp />
              </IconButton>
              <IconButton
                disabled={!onMoveDown}
                onClick={onMoveDown}
                size="Small"
                title={getLocaleMessageById('app.editor.routerRuleMoveDown')}
              >
                <IconArrowDown />
              </IconButton>
            </aside>
          </footer>
        </div>
        <footer>
          <TransferSelect
            withEnd={false}
            wrapperClassName={utils.w100}
            steps={steps}
            label={getLocaleMessageById('app.modals.form.transfer')}
            required
            value={rule.transferTo?.toString() ?? 'default'}
            onChange={({ value }) =>
              onChange({
                ...rule,
                transferTo: value ?? 'default',
              })
            }
            disabled={disabled}
            isInvalid={!!invalidReasons?.transition}
            message={<InputErrorMessage>{invalidReasons?.transition}</InputErrorMessage>}
          />
        </footer>
      </section>
    </Collapsible>
  );
};

export default Rule;
