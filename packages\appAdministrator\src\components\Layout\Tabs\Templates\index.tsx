import React from 'react';

import { ResizablePanel, ResizerPosition, showModal, utils } from '@product.front/ui-kit';

import {
  FrontTemplateStatus,
  IFrontFolder,
  IFrontTemplate,
} from '@monorepo/common/src/@types/templates';
import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import TemplatePreview from '@monorepo/template-panel/src/components/Preview';
import TemplateEditor from '@monorepo/template-panel/src/components/TemplateEditor';
import TreeContainer from '@monorepo/template-panel/src/components/Templates/All/Container';
import TemplatesHeader from '@monorepo/template-panel/src/components/Templates/Header';
import TemplateListContainer from '@monorepo/template-panel/src/components/Templates/List/TemplateListContainer';
import TemplatesTree from '@monorepo/template-panel/src/components/Templates/Tree';

import { IAdmTabComponent } from '../../../../@types/components';
import { AnswerTemplate } from '../../../../@types/generated/administration';
import { getSettings } from '../../../../helpers/appSettings';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  getTabAsideSize,
  setTabAsideSize,
  tabAsideSizeMax,
  tabAsideSizeMin,
} from '../../../../helpers/resize.helper';
import { mapFrontTemplateToDto, mapTemplateToFrontTemplate } from '../../../../mappers/templates';
import { getOperatorGroups } from '../../../../services/operatorGroups';
import {
  createTemplate,
  createUpdateFolder,
  deleteFolder,
  deleteTemplate,
  emptyFolder,
  emptyTemplate,
  getTemplate,
  updateTemplate,
} from '../../../../services/template';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { getAllFolderTemplates, selectTemplate } from '../../../../store/templates/templates.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';

import TemplatesToolbarActions from './TemplatesToolbarActions';

const TemplatesTab: React.FC<IAdmTabComponent> = ({ name, tab }) => {
  const dispatch = useAdministratorAppDispatch();

  const { folders, loading, error, selectedTemplate } = useAdministratorAppSelector(
    (state) => state.templates,
  );
  const { addressData } = useAdministratorAppSelector((state) => state.user);

  const [searchText, setSearchText] = React.useState('');
  const [shouldSearchByName, setShouldSearchByName] = React.useState(true);
  const [shouldSearchByText, setShouldSearchByText] = React.useState(true);
  const [shouldSearchByTag, setShouldSearchByTag] = React.useState(true);
  const [statusesToSearch, setStatusesToSearch] = React.useState<FrontTemplateStatus[]>([]);
  const [editFolder, setEditFolder] = React.useState<IFrontFolder | null>(null);
  const [templateInEdit, setTemplateInEdit] = React.useState<IFrontTemplate | null>(null);

  const updateFolderTemplates = React.useCallback(
    async () =>
      dispatch(
        getAllFolderTemplates({
          title: shouldSearchByName ? searchText : undefined,
          text: shouldSearchByText ? searchText : undefined,
          keyWord: shouldSearchByTag ? searchText : undefined,
          statuses: statusesToSearch,
        }),
      ),
    [
      dispatch,
      searchText,
      shouldSearchByName,
      shouldSearchByTag,
      shouldSearchByText,
      statusesToSearch,
    ],
  );

  React.useEffect(() => {
    updateFolderTemplates();
  }, [updateFolderTemplates]);

  React.useEffect(() => {
    if (!templateInEdit) return;

    const titleHeader = templateInEdit.id
      ? getLocaleMessageById('templates.editor.header.edit', {
          template: templateInEdit.title,
        })
      : getLocaleMessageById('templates.editor.header.create');
    showModal({
      header: titleHeader,
      children: (close) => (
        <TemplateEditor
          withoutHeader
          style={{
            width: '80vw',
            height: '80vh',
          }}
          foldersData={Object.keys(folders).map((key) => ({
            text: folders[key].title,
            value: folders[key].id,
          }))}
          templateToEdit={templateInEdit}
          onTemplateCreate={(template) =>
            createTemplate(mapFrontTemplateToDto(template, 'AnswerTemplateAddEdit'))
          }
          onTemplateUpdate={async (template) =>
            updateTemplate(mapFrontTemplateToDto(template, 'AnswerTemplateAddEdit'))
          }
          onClose={() => {
            setTemplateInEdit(null);
            close?.();
          }}
          onEditorSuccess={async (templateId) => {
            setTemplateInEdit(null);
            close?.();
            await updateFolderTemplates();
            await dispatch(selectTemplate(templateId));
          }}
          canEditCode
          canEditStatus
          uploadUrl={getSettings().productFileServer}
          addressData={addressData}
          canEditVersions
        />
      ),
      flushBody: true,
      canClose: false,
    });
  }, [addressData, dispatch, folders, templateInEdit, updateFolderTemplates]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <TemplatesHeader
          onSearchChange={({
            searchText: text,
            isSearchByNameEnabled,
            isSearchByTextEnabled,
            isSearchByTagEnabled,
            statuses,
          }) => {
            setSearchText(text);
            setShouldSearchByName(isSearchByNameEnabled);
            setShouldSearchByText(isSearchByTextEnabled);
            setShouldSearchByTag(isSearchByTagEnabled);
            setStatusesToSearch(statuses);
          }}
          canFilterByStatus
        />
        <TemplatesToolbarActions
          onAddFolder={() =>
            setEditFolder({ ...emptyFolder, title: '', parentId: folders['root'].id })
          }
          onAddTemplate={() => {
            setTemplateInEdit({ ...emptyTemplate, folderId: folders['root'].id });
          }}
        />
      </AdmTabHeader>
      <AdmTabBody noPadding flexRow className={utils.w100} loading={loading}>
        <ResizablePanel
          resizerPosition={ResizerPosition.Right}
          min={tabAsideSizeMin}
          max={tabAsideSizeMax}
          onResize={setTabAsideSize(tab)}
          size={getTabAsideSize(tab)}
          className={utils.flexShrink0}
        >
          <TemplateListContainer className={utils.h100}>
            {error && (
              <JumbotronError header={getLocaleMessageById('templates.error')} error={error} />
            )}
            {!error && (
              <TreeContainer>
                <TemplatesTree
                  rootFolder={folders['root']}
                  selectedTemplate={selectedTemplate}
                  searchState={{
                    searchByTitleString: shouldSearchByName ? searchText : '',
                    searchByTagsString: shouldSearchByTag ? searchText : '',
                    searchByDescriptionString: shouldSearchByText ? searchText : '',
                  }}
                  foldersMap={folders}
                  editFolder={editFolder}
                  onTemplateClick={(template) => dispatch(selectTemplate(template.id))}
                  onAddFolderClick={(folder) =>
                    setEditFolder({ ...emptyFolder, title: '', parentId: folder.id })
                  }
                  onEditFolderClick={(folder) => setEditFolder(folder)}
                  handleFolderSave={async (title) => {
                    if (!editFolder) return;

                    if (!title) {
                      setEditFolder(null);
                      return;
                    }

                    await createUpdateFolder({ ...editFolder, title }, 'AnswerTemplateFolder');

                    await updateFolderTemplates();
                    setEditFolder(null);
                  }}
                  onDeleteFolderClick={async (folder) => {
                    await deleteFolder(folder.id);
                    await updateFolderTemplates();
                  }}
                  onAddTemplateClick={(folder) =>
                    setTemplateInEdit({ ...emptyTemplate, folderId: folder.id })
                  }
                  onEditTemplateClick={async (template) =>
                    setTemplateInEdit(
                      mapTemplateToFrontTemplate(
                        (await getTemplate(template.id)) as AnswerTemplate,
                      ),
                    )
                  }
                  onDeleteTemplateClick={async (template) => {
                    await deleteTemplate(template.id);
                    await updateFolderTemplates();
                  }}
                  operatorGroupsGetter={async () =>
                    (await getOperatorGroups()).map((item) => ({
                      id: item.id,
                      name: item.name,
                    }))
                  }
                  onManageAccessSaveClick={async (folder, operatorGroups) => {
                    await createUpdateFolder(
                      {
                        ...folder,
                        relationIds: operatorGroups.map((item) => item.id),
                      },
                      'AnswerTemplateFolder',
                    );
                    await updateFolderTemplates();
                  }}
                />
              </TreeContainer>
            )}
          </TemplateListContainer>
        </ResizablePanel>
        <TemplatePreview
          template={selectedTemplate}
          searchTextString={shouldSearchByText ? searchText : ''}
          searchTagString={shouldSearchByTag ? searchText : ''}
          uploadFileUrl={getSettings().productFileServer}
          addressData={addressData}
          shouldShowCode
          shouldShowStatus
          shouldShowVersions
          onEditClick={async (template) =>
            setTemplateInEdit(
              mapTemplateToFrontTemplate((await getTemplate(template.id)) as AnswerTemplate),
            )
          }
          onDeleteClick={async (template) => {
            await deleteTemplate(template.id);
            await updateFolderTemplates();
          }}
        />
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default TemplatesTab;
