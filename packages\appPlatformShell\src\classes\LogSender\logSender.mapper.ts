import { WebarmLogLevel } from '../LogInterceptor/LogInterceptor';

import { LogStashDTO, LogStashLogLevels } from './logSender.types';

const mapLogLevelToBack = (lvl: WebarmLogLevel): LogStashLogLevels => {
  let levelStr: LogStashLogLevels = 'UNDEFINED';
  switch (lvl) {
    case 'fatal':
      levelStr = 'FATAL';
      break;
    case 'error':
      levelStr = 'ERROR';
      break;
    case 'warn':
      levelStr = 'WARNING';
      break;
    case 'info':
      levelStr = 'INFO';
      break;
    case 'log':
    case 'debug':
      levelStr = 'DEBUG';

      break;
  }

  return levelStr;
};

export interface AddLogDTO {
  appName: string;
  level: WebarmLogLevel;
  message: string;
  user: string;
  meta: any;
  timestamp: string;
}

export function mapToLogStashDto({
  appName,
  timestamp,
  level,
  message,
  user,
  meta,
}: AddLogDTO): LogStashDTO {
  return {
    index: appName,
    timestamp, //
    ['@timestamp']: new Date().toISOString(),
    level: mapLogLevelToBack(level),
    appName,
    message,
    user,
    meta,
  };
}
