import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  Input,
  Select,
  Text,
  Textarea,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { generateCodeByString } from '@monorepo/common/src/helpers/codeGeneratorHelper';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { IFrontRequestSubject } from '../../../../@types/requestSubject';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';

import { attributes as currentAttributes } from './Preview';

interface ISubjectFormProps {
  subject: IFrontRequestSubject;
  subjects: IFrontRequestSubject[];
  onConfirm: (newSubject: IFrontRequestSubject) => Promise<void>;
  onClose?: () => void;
}

const SubjectForm = ({ subject, subjects, onConfirm, onClose }: ISubjectFormProps) => {
  const {
    name: subjectName,
    code: subjectCode,
    parentId: subjectParentId,
    description: subjectDescription,
    ...subjectAttributes
  } = subject;

  const [name, setName] = React.useState(subjectName);
  const [code, setCode] = React.useState(subjectCode);
  const [parentId, setParentId] = React.useState(subjectParentId);
  const [description, setDescription] = React.useState(subjectDescription);
  const [attributes, setAttributes] = React.useState(subjectAttributes);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();

  const childSubjects = React.useMemo(() => {
    const getAllChildSubjects = (subj: IFrontRequestSubject): IFrontRequestSubject[] => {
      const children = subjects.filter((s) => s.parentId === subj.id);
      return [...children, ...children.map((ch) => getAllChildSubjects(ch))].flat();
    };

    return getAllChildSubjects(subject);
  }, [subjects, subject]);

  const possibleParentSubjects = React.useMemo(() => {
    return subjects.filter((s) => s.id !== subject.id && !childSubjects.includes(s));
  }, [subjects, childSubjects, subject.id]);

  return (
    <form
      onSubmit={async (event) => {
        event.preventDefault();
        setLoading(true);
        setError(undefined);
        try {
          await onConfirm({ ...subject, ...attributes, name, code, description, parentId });
          getPlatformPopupNotificationManager().notifySuccess(
            getLocaleMessageById('subject.saveSuccess'),
          );
          onClose?.();
        } catch (err) {
          console.error('Error saving subject', err);
          setError(err);
        } finally {
          setLoading(false);
        }
      }}
    >
      <div
        className={clsx(utils.dGrid, utils.gap4, utils.p4)}
        style={{ width: 'clamp(400px, 70vw, 1000px)' }}
      >
        <Input
          label={getLocaleMessageById('subject.name.label')}
          value={name}
          onChange={({ value }) => {
            const newName = value ?? '';
            if (generateCodeByString(name) === code) {
              setCode(generateCodeByString(newName));
            }
            setName(newName);
          }}
          required
          maxLength={100}
        />
        <Input
          label={getLocaleMessageById('subject.code.label')}
          value={code}
          onChange={({ value }) => setCode(value ?? '')}
          required
          maxLength={100}
        />
        <Select
          label={getLocaleMessageById('subject.parent.label')}
          value={parentId?.toString() ?? ''}
          onChange={({ value }) => setParentId(value ? Number(value) : null)}
          data={[
            {
              value: '',
              text: getLocaleMessageById('subject.parent.root'),
            },
            ...possibleParentSubjects.map((s) => ({
              value: s.id.toString(),
              text: s.name,
            })),
          ]}
          required
        />
        <Textarea
          label={getLocaleMessageById('subject.description.label')}
          value={description ?? ''}
          onChange={({ value }) => setDescription(value ?? '')}
          rows={7}
          maxLength={1000}
        />
        <Text variant={TextVariant.BodySemibold}>
          {getLocaleMessageById('subject.attributes.label')}
        </Text>
        {currentAttributes.map((attribute) => (
          <Input
            key={attribute.key}
            label={attribute.name}
            value={attributes[attribute.key]?.toString() ?? ''}
            onChange={({ value }) =>
              setAttributes((current) => ({ ...current, [attribute.key]: value ?? null }))
            }
            maxLength={1000}
          />
        ))}
      </div>
      <div
        className={clsx(
          utils.borderTop,
          utils.p4,
          utils.dFlex,
          utils.gap2,
          utils.justifyContentEnd,
        )}
      >
        {error && (
          <AlertError
            className={utils.flexGrow1}
            header={getLocaleMessageById('subject.saveError')}
            error={error}
          />
        )}
        <Button variant={ButtonVariant.Secondary} onClick={onClose} disabled={loading}>
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button variant={ButtonVariant.Primary} type="submit" disabled={loading}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </div>
    </form>
  );
};

export default SubjectForm;
