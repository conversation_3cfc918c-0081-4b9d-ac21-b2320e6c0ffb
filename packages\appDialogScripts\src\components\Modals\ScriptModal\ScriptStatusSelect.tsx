import React from 'react';

import { ISelectProps } from '@product.front/ui-kit/dist/types/components/Select/Select';

import { Select } from '@product.front/ui-kit';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';

import { getLocaleMessageById } from '../../../helpers/localeHelper';

interface IScriptStatusProps extends Omit<ISelectProps, 'value' | 'onChange' | 'data'> {
  value: ScriptStatus;
  onChange?: (changeArgs: { value: ScriptStatus | null }) => void;
}

const ScriptStatusSelect: React.FC<IScriptStatusProps> = ({ value, onChange, ...rest }) => {
  return (
    <Select
      value={value}
      onChange={({ value: v }) => onChange?.({ value: (v as ScriptStatus) ?? null })}
      data={[
        {
          value: ScriptStatus.Template,
          text: getLocaleMessageById('app.scriptStatus.draft'),
        },
        {
          value: ScriptStatus.Active,
          text: getLocaleMessageById('app.scriptStatus.published'),
        },
        {
          value: ScriptStatus.Archive,
          text: getLocaleMessageById('app.scriptStatus.archived'),
        },
        {
          value: ScriptStatus.NotAvailable,
          text: getLocaleMessageById('app.scriptStatus.notAvailable'),
        },
      ]}
      {...rest}
    />
  );
};

export default ScriptStatusSelect;
