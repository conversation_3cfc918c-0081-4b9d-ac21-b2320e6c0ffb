import { combineReducers, configureStore } from '@reduxjs/toolkit';

import automationServiceSlice from './automationService/automationService.slice';
import automationServicesSlice from './automationServices/automationServices.slice';

const rootReducer = combineReducers({
  automationService: automationServiceSlice,
  automationServices: automationServicesSlice,
});

export const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
