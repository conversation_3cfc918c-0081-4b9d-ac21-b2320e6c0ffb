import React from 'react';

import { AppProps } from 'single-spa';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

import { WebAppInitData } from './Commot';

export type WebPageHostedAppAdapterProps = AppProps & AWP.AppComponentProps;

export type WebPageHostedAppAdapterState = {};

type WebAppInit = {
  iframeAllow: string;
};

export default class WebPageHostedAppAdapter
  extends React.PureComponent<WebPageHostedAppAdapterProps, WebPageHostedAppAdapterState>
  implements AWP.IHostedAppAdapter
{
  hostedAppsManager: AWP.IHostedAppsManager;
  iframeElement: HTMLIFrameElement;
  appInit: WebAppInit;
  srcToUse: string;

  appRegistred: boolean = false;

  constructor(props: WebPageHostedAppAdapterProps) {
    super(props);

    this.hostedAppsManager =
      this.props.shell.serviceResolver.resolve<AWP.IHostedAppsManager>('IHostedAppsManager');
    this.state = {};
    this.executeAction = this.executeAction.bind(this);
    this.composeApplicationStartUrl = this.composeApplicationStartUrl.bind(this);

    this.iframeRefCallback = this.iframeRefCallback.bind(this);
    this.appName = this.props.name;

    console.info('Constructing WebPage', this.appName, this.props.initString);
    try {
      this.appInit = JSON.parse(this.props.initString) as any;
    } catch (error) {
      console.error('App init JSON FAILED!', error);
    }

    const startUrl = this.composeApplicationStartUrl(props.appInitData);
    console.info(`Constructed WebPage with name='${this.appName}', url='${startUrl}'`);
    this.srcToUse = startUrl;
  }

  appName: string;
  appState: AWP.HostedAppState;
  executeAction(actionName: string, actionParameters: any) {
    this.iframeElement.contentWindow &&
      this.iframeElement.contentWindow.postMessage(
        {
          awpFlag: true,
          actionName: actionName,
          functionBody: actionParameters.body,
          functionParameters: actionParameters.parameters,
        },
        '*',
      );
  }

  frameLoaded = () => {
    if (!this.appRegistred) {
      console.info(`componentDidMount='${this.appName}'`);
      this.hostedAppsManager.registerHostedAppAdapter(this);
      this.appRegistred = true;
    }
  };

  render() {
    return (
      <iframe
        ref={this.iframeRefCallback}
        name={this.props.name}
        src={this.srcToUse}
        allow={this.appInit.iframeAllow}
        width="100%"
        height="100%"
        style={{ border: '0px' }}
        onLoad={this.frameLoaded}
        title={this.props.name}
      />
    );
  }

  iframeRefCallback(element: any) {
    this.iframeElement = element;
  }

  private composeApplicationStartUrl(appInfo: WebAppInitData): string {
    let startUrl: string = '';
    if (appInfo) {
      startUrl = appInfo.startUrl;

      if (appInfo.proxyMapping) {
        const pathchedCurrentHostFullName = `${location.protocol}//${location.hostname}:${appInfo.proxyMapping.proxyPort}`;
        startUrl = appInfo.startUrl.replace(
          appInfo.proxyMapping.originalUrl,
          pathchedCurrentHostFullName,
        );
        console.info(
          `Proxy - app name='${this.appName}', pathchedCurrentHostFullName=${pathchedCurrentHostFullName}, startUrl=${startUrl}`,
        );
      }
    }
    return startUrl;
  }
}
