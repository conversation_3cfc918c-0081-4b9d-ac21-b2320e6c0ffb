import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import { IShell } from '@monorepo/common/src/platform/awp-web-interfaces';

let settings: any;

export async function getAppSettings(shell?: IShell) {
  if (shell?.serviceResolver) {
    settings = shell.serviceResolver.resolve('AppSettings') as any;
  }

  if (settings) {
    return {
      SignalRUrl: settings.productInternalChatSignalR,
      ApiUrl: settings.productInternalChatApi,
    };
  } else {
    const raw = await commonFetch('config/appSettings.json');
    return (await raw.json()) as any;
  }
}
