/* eslint-disable @typescript-eslint/no-require-imports */
const CopyPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
/* eslint-enable @typescript-eslint/no-require-imports */

module.exports = (webpackConfigEnv) => {
  return {
    mode: 'development',
    devServer: {
      allowedHosts: ['localhost'],
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      },
    },
    plugins: [
      new HtmlWebpackPlugin({
        inject: false,
        template: 'src/index.ejs',
        templateParameters: {
          isLocal: webpackConfigEnv && webpackConfigEnv.isLocal,
        },
      }),
      new CopyPlugin({
        patterns: [
          { from: './config/importmap.json' },
          { from: './config/version.json' },
          { from: './public/favicon.ico' },
          { from: './transform_configs.sh' },
          { from: './src/design/logo/Logo_Product.svg', to: 'favicon.svg' },
          { from: './public/service-worker.js' },
          { from: 'config', to: 'config' },

          { from: '../../node_modules/@product.front/ui-kit/dist', to: 'CDN/lib-uikit' },

          //js CDN build
          { from: '../../node_modules/systemjs/dist/system.js', to: 'CDN/systemjs' },
          { from: '../../node_modules/systemjs/dist/system.min.js', to: 'CDN/systemjs' },

          { from: '../../node_modules/systemjs/dist/extras/amd.js', to: 'CDN/systemjs/extras' },
          { from: '../../node_modules/systemjs/dist/extras/amd.min.js', to: 'CDN/systemjs/extras' },

          {
            from: '../../node_modules/import-map-overrides/dist/import-map-overrides.js',
            to: 'CDN/import-map-overrides',
          },

          {
            from: '../../node_modules/single-spa/lib/es2015/system/single-spa.min.js',
            to: 'CDN/single-spa',
          },
          { from: '../../node_modules/umd-react/dist/react.production.min.js', to: 'CDN/react' },

          {
            from: '../../node_modules/umd-react/dist/react-dom.production.min.js',
            to: 'CDN/react-dom',
          },

          { from: '../../node_modules/regenerator-runtime/runtime.js', to: 'CDN/runtime' },
        ],
      }),
    ],
  };
};
