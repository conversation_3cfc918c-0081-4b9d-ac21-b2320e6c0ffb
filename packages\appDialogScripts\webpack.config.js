const {
  getModeByWebpackConfigEnv,
  singleSpaReactUiConfig,
} = require('@product.front/webpack-config');

module.exports = (webpackConfigEnv) => {
  const mode = getModeByWebpackConfigEnv(webpackConfigEnv);
  return singleSpaReactUiConfig('PRODUCT', 'dialog-scripts', mode, {
    resolve: {
      extensions: ['.gql'],
      plugins: [],
    },
    module: {
      rules: [
        {
          test: /\.(graphql|gql)$/,
          exclude: /node_modules/,
          loader: 'graphql-tag/loader',
        },
      ],
    },
  });
};
