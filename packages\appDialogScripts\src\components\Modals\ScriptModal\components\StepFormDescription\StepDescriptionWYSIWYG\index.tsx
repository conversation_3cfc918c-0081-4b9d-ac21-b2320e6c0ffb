import React from 'react';

import clsx from 'clsx';

import { Loader, Textarea, utils } from '@product.front/ui-kit';

const Wysiwyg = React.lazy(
  () => import(/* webpackChunkName: "wysiwyg" */ '@monorepo/common/src/components/Wysiwyg'),
);
import { EditorControl } from '@monorepo/common/src/components/Wysiwyg/types/wysiwyg.controls';

import { IStepDescriptionProps } from '..';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import InputErrorMessage from '../../../../../InputErrorMessage';

import styles from './styles.module.scss';

interface IStepDescriptionWYSIWYGProps extends IStepDescriptionProps {}

const StepDescriptionWYSIWYG: React.FC<IStepDescriptionWYSIWYGProps> = ({
  step,
  onChange,
  required,
  disabled,
}) => {
  const [description, setDescription] = React.useState(step.description);
  const timeoutRef = React.useRef<ReturnType<typeof setTimeout>>(null);

  React.useEffect(() => {
    timeoutRef.current && clearTimeout(timeoutRef.current);

    timeoutRef.current = setTimeout(() => {
      onChange({ ...step, description });
    }, 1000);

    return () => {
      timeoutRef.current && clearTimeout(timeoutRef.current);
    };
  }, [step, description, onChange]);

  return (
    <div
      className={clsx(
        utils.border,
        utils.radius1,
        utils.w100,
        utils.dFlex,
        utils.alignItemsCenter,
        utils.justifyContentCenter,
        utils.overflowHidden,
      )}
      style={{ height: 140, minHeight: 72, maxHeight: '40vh', resize: 'vertical' }}
    >
      <React.Suspense fallback={<Loader />}>
        <Wysiwyg
          autofocus={false}
          value={description}
          onChange={({ value }) => setDescription(value)}
          toolbarClassName={styles.toolbar}
          editorClassName={styles.editor}
          placeholder={getLocaleMessageById('app.modals.form.stepDescription')}
          controls={[
            EditorControl.Bold,
            EditorControl.Italic,
            EditorControl.Underline,
            EditorControl.Strike,
            EditorControl.FontColor,
            EditorControl.HighlightColor,
            EditorControl.Link,
            EditorControl.Ul,
            EditorControl.Ol,
          ]}
        />
      </React.Suspense>
    </div>
  );

  return (
    <Textarea
      label={getLocaleMessageById('app.modals.form.stepDescription')}
      value={description}
      onChange={({ value }) => setDescription(value || '')}
      onBlur={() => onChange({ ...step, description })}
      required={required}
      style={{ minHeight: '72px', maxHeight: '20vh' }}
      disabled={disabled}
      isInvalid={!!step.invalidReasons?.description}
      message={<InputErrorMessage>{step.invalidReasons?.description}</InputErrorMessage>}
    />
  );
};

export default StepDescriptionWYSIWYG;
