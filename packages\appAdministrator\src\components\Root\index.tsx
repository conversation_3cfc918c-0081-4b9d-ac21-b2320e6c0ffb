import React from 'react';

import clsx from 'clsx';
import { Provider } from 'react-redux';

import { Loader, utils } from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { CommonErrorType } from '@monorepo/common/src/common/types/error.types';
import { setCurrentOperator } from '@monorepo/common/src/managers/currentOperatorManager';
import { setPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';
import { setProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';
import { AppComponentProps } from '@monorepo/common/src/platform/awp-web-interfaces';

import { loadSettings } from '../../helpers/appSettings';
import { store } from '../../store';
import { getAvailableAddressTypesData } from '../../store/user/user.thunk';
import Layout from '../Layout';

export default function Root({ shell }: AppComponentProps) {
  const [isLoading, setIsLoading] = React.useState(true);
  const [err, setErr] = React.useState<Error>();

  React.useEffect(() => {
    const initiateApplication = async () => {
      await loadSettings(shell);
      setPlatformPopupNotificationManager(shell);
      await setProductServicesManager({ shell });
      shell?.currentOperator && setCurrentOperator(shell.currentOperator);
      await store.dispatch(getAvailableAddressTypesData());
    };

    initiateApplication()
      .catch(setErr)
      .finally(() => setIsLoading(false));
  }, [shell]);

  if (err) {
    return (
      <div className={clsx(utils.w100, utils.h100, utils.flexCentredBlock)}>
        <AlertError error={err} commonErrorType={CommonErrorType.InitApp} />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={clsx(utils.w100, utils.h100, utils.flexCentredBlock)}>
        <Loader />
      </div>
    );
  }

  return (
    <Provider store={store}>
      <Layout />
    </Provider>
  );
}
