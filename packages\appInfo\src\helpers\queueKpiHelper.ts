import { IQueueKpiFront } from '../@types/KpiData';

export const isAlarmAht = (queueKpi: IQueueKpiFront) => {
  return queueKpi.aht && queueKpi.alarmAht && queueKpi.aht >= queueKpi.alarmAht;
};

export const isWarnAht = (queueKpi: IQueueKpiFront) => {
  return queueKpi.aht && queueKpi.warnAht && queueKpi.aht > queueKpi.warnAht;
};

export const isAlarmAsa = (queueKpi: IQueueKpiFront) => {
  return queueKpi.asa && queueKpi.alarmAsa && queueKpi.asa >= queueKpi.alarmAsa;
};

export const isWarnAsa = (queueKpi: IQueueKpiFront) => {
  return queueKpi.asa && queueKpi.warnAsa && queueKpi.asa > queueKpi.warnAsa;
};
