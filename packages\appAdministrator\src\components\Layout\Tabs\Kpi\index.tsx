import React from 'react';

import clsx from 'clsx';

import { Button, ButtonVariant, utils } from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import { checkObjectsEqual } from '@monorepo/common/src/helpers/objectsHelper';

import { IAdmTabComponent } from '../../../../@types/components';
import { IFrontKpiThresholdValue } from '../../../../@types/parameters';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorsKpis, updateOperatorsKpis } from '../../../../services/kpis';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';

import OperatorKpi from './OperatorKpi';

const KpiTab: React.FC<IAdmTabComponent> = ({ name }) => {
  const [loading, setLoading] = React.useState(false);
  const [kpiGetError, setKpiGetError] = React.useState<Error>();
  const [error, setError] = React.useState<Error>();
  const [kpis, setKpis] = React.useState<IFrontKpiThresholdValue[]>([]);
  const [kpiTargetsInitial, setKpiTargetsInitial] = React.useState<Record<string, number | null>>(
    {},
  );
  const [kpiTargets, setKpiTargets] = React.useState<Record<string, number | null>>({});

  React.useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        setKpiGetError(undefined);
        const kpisDto = await getOperatorsKpis();
        setKpis(
          kpisDto.map((kpi) => ({
            code: kpi.code,
            displayName: kpi.displayName,
            isInteger: kpi.isInteger ?? true,
            minValue: kpi.minValue ?? 0,
            defaultValue: undefined,
            maxValue: kpi.maxValue ?? undefined,
          })),
        );

        const kpiTargetsNew = kpisDto.reduce(
          (acc, kpi) => ({ ...acc, [kpi.code]: kpi.defaultValue }),
          {},
        );
        setKpiTargets(kpiTargetsNew);
        setKpiTargetsInitial(kpiTargetsNew);
      } catch (e) {
        console.error("Error getting operator's KPIs", e);
        setKpiGetError(e);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(undefined);

    try {
      await updateOperatorsKpis(
        Object.keys(kpiTargets).map((key) => ({
          code: key,
          defaultValue: kpiTargets[key] ?? undefined,
        })),
      );

      setKpiTargetsInitial(kpiTargets);
    } catch (err) {
      console.error("Error saving operator's KPIs", err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name} />
      <AdmTabBody loading={loading}>
        <form
          className={clsx(utils.positionRelative, utils.dFlex, utils.flexColumn, utils.h100)}
          onSubmit={handleSubmit}
          onReset={() => setKpiTargets(kpiTargetsInitial)}
        >
          <div
            className={clsx(
              utils.dFlex,
              utils.flexColumn,
              utils.pY2,
              utils.flexBasis0,
              utils.flexGrow1,
              utils.overflowAuto,
              utils.scrollbar,
            )}
            style={{ maxWidth: kpiGetError ? undefined : '800px' }}
          >
            {kpiGetError && (
              <JumbotronError header={getLocaleMessageById('kpis.error')} error={kpiGetError} />
            )}
            {!kpiGetError && (
              <OperatorKpi
                kpiThresholdValues={kpis}
                kpiTargets={kpiTargets}
                onChange={(key, value) =>
                  setKpiTargets((current) => ({ ...current, [key]: value }))
                }
                kpiValidationResult={{}}
                canClear={false}
                required
              />
            )}
          </div>
          <div
            className={clsx(
              utils.dFlex,
              utils.gap2,
              utils.pT4,
              utils.w100,
              utils.justifyContentEnd,
              utils.positionSticky,
              utils.borderTop,
            )}
            style={{ bottom: 0 }}
          >
            {error && (
              <AlertError
                className={utils.flexGrow1}
                header={getLocaleMessageById('kpis.save.error')}
                error={error}
              />
            )}
            <Button
              variant={ButtonVariant.Secondary}
              disabled={checkObjectsEqual(kpiTargets, kpiTargetsInitial)}
              className={utils.mLauto}
              type="reset"
            >
              {getLocaleMessageById('app.common.cancel')}
            </Button>
            <Button disabled={checkObjectsEqual(kpiTargets, kpiTargetsInitial)} type="submit">
              {getLocaleMessageById('app.common.save')}
            </Button>
          </div>
        </form>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default KpiTab;
