import { ITreeItem } from './TreeItem';

export const checkIfSubjectHasEntry = (
  subject: ITreeItem,
  searchText: string,
  shouldSearchByName: boolean,
  shouldSearchByText: boolean,
): boolean =>
  ((!shouldSearchByName || subject.name.toLowerCase().includes(searchText.toLowerCase())) &&
    (!shouldSearchByText ||
      subject.description?.toLowerCase().includes(searchText.toLowerCase()))) ||
  (subject.items?.some((child) =>
    checkIfSubjectHasEntry(child, searchText, shouldSearchByName, shouldSearchByText),
  ) ??
    false);
