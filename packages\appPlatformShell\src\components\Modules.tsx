﻿import * as React from 'react';

import { Guid } from 'guid-typescript';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

import * as HAModule from './Modules/HostedApplicationsModule';

class LoadedModuleInfo {
  id: string;
  name: string;
  isOptional: boolean;
  module: AWP.IModule | null = null;
  moduleItem: any;
  loadError: Error | null = null;
  starError: Error | null = null;

  constructor(id: string, name: string, isOptional: boolean) {
    this.id = id;
    this.name = name;
    this.isOptional = isOptional;
  }

  public toString = (): string => {
    return `${this.id} ${this.name}`;
  };
}

type ModulesProps = {
  shell: AWP.IShell;
  selectedRoleId: Guid;
  selectedServiceAreaId: Guid;
};

type ModulesState = {
  moduleItems: LoadedModuleInfo[];
};

const modulesList: { [moduleName: string]: any } = {
  HostedApplicationsModule: HAModule.HostedApplicationsModule,
};

export class Modules extends React.PureComponent<ModulesProps, ModulesState> {
  serviceResolver: AWP.IServiceResolver;
  eventManager: AWP.IEventManager;

  modulesInfosLoaded: boolean;
  configurationManager: AWP.IAwpConfigurationManager;

  constructor(props: ModulesProps) {
    super(props);

    this.loadComponent = this.loadComponent.bind(this);
    this.fireStartError = this.fireStartError.bind(this);
    this.modulesInfosLoaded = false;

    this.serviceResolver = props.shell.serviceResolver;
    this.configurationManager = this.serviceResolver.resolve<AWP.IAwpConfigurationManager>(
      'IAwpConfigurationManager',
    );
    this.eventManager = this.serviceResolver.resolve<AWP.IEventManager>('IEventManager');

    this.state = {
      moduleItems: [],
    };
  }

  async componentDidMount() {
    this.configurationManager
      .getModules(
        AWP.AwpClientTypes.Web,
        this.props.selectedRoleId,
        this.props.selectedServiceAreaId,
      )
      .then(async (modules) => {
        console.info('Modules info was loaded', this);

        this.modulesInfosLoaded = true;
        const modulesItems = await Promise.all(
          modules.map(async (moduleInfo) => {
            return await this.loadComponent(moduleInfo);
          }),
        );

        console.info('Modules component loaded', this);

        let allModulesLoaded: boolean = true;
        const errors = new Array<Error>();
        modulesItems.forEach((p) => {
          if (!p.isOptional && p.loadError != null) {
            allModulesLoaded = false;
            return errors.push(p.loadError);
          }
        });

        if (errors.length > 0) {
          console.warn('Modules loading has errors', this, errors);
          this.fireStartError(errors);
        }

        if (allModulesLoaded) {
          console.info('Modules - allModulesLoaded', this);

          this.setState(() => ({
            moduleItems: modulesItems,
          }));

          console.info('Modules - ModulesInitialized event fired', this);
          this.eventManager.fire('ModulesInitialized', null);
        }
      })
      .catch((error) => {
        console.error(error, this);
        this.fireStartError([error as Error]);
      });
  }

  render() {
    return '';
  }

  async loadComponent(moduleInfo: AWP.ModuleInfo): Promise<LoadedModuleInfo> {
    let Module: AWP.IModule | undefined;

    const result: LoadedModuleInfo = new LoadedModuleInfo(
      moduleInfo.id,
      moduleInfo.name,
      moduleInfo.isOptional,
    );

    if (typeof modulesList[moduleInfo.name] !== 'undefined') {
      try {
        Module = new modulesList[moduleInfo.name]() as AWP.IModule;
        await Module.initialize(this.props.shell, {
          initString: moduleInfo.initialization,
        });
        result.module = Module;
        result.moduleItem = (
          <div key={moduleInfo.name}>Module name {moduleInfo.name} initialized</div>
        );
        return result;
      } catch (e) {
        console.error('Module loading FAILED in load component!', e, moduleInfo);
        result.loadError = e as Error;
        result.moduleItem = (
          <div key={moduleInfo.name}>
            Module named {moduleInfo.name} not initialized: {e}
          </div>
        );
        return result;
      }
    } else {
      const moduleBareSpecifier = `Modules/${moduleInfo.name}`;
      try {
        let exc: Error | null | undefined;
        await System.import(moduleBareSpecifier)
          .then((a) => {
            Module = new a.default() as AWP.IModule;
          })
          .catch((e) => {
            exc = e as Error;
          });

        if (Module != null) {
          await Module.initialize(this.props.shell, {
            initString: moduleInfo.initialization,
          });
          result.module = Module;
          result.moduleItem = (
            <div key={moduleInfo.name}>Module name {moduleInfo.name} initialized</div>
          );
          return result;
        } else {
          if (exc != null) {
            console.error('Module loading FAILED!', exc, moduleInfo);
            result.loadError = exc;
          } else {
            result.loadError = new Error(`Module loading FAILED! (${moduleInfo.name})`);
          }

          result.moduleItem = (
            <div key={moduleInfo.name}>
              Module named {moduleInfo.name} ({moduleBareSpecifier}) not loaded: {exc?.toString()}
            </div>
          );
          return result;
        }
      } catch (e) {
        console.error('Module loading FAILED!', e, moduleInfo);
        result.loadError = e as Error;
        result.moduleItem = (
          <div key={moduleInfo.name}>
            Module named {moduleInfo.name} ({moduleBareSpecifier}) not loaded: {e}
          </div>
        );
        return result;
      }
    }
  }

  async startModules(): Promise<any> {
    const notStartedModules = this.state.moduleItems.filter((m) => m.module != null);

    console.info(`Start modules ${notStartedModules.join(',')}`);

    while (notStartedModules.length > 0) {
      const modulesForStart = notStartedModules.filter((m) => m.module?.canStart());
      if (modulesForStart.length == 0) {
        console.warn('Modules not started', notStartedModules);
        const requiredModules = notStartedModules.filter((p) => !p.isOptional);
        if (requiredModules.length > 0) {
          console.warn('Modules not started', requiredModules);
          throw `Modules not started - ${requiredModules.join(',')}`;
        }
        break;
      }

      const modulesStart = modulesForStart.map<Promise<LoadedModuleInfo>>(async (p) => {
        try {
          console.info(`Start module - '${p.name}'`);
          await p.module?.start();
          console.info(`Start module '${p.name}' completed`);
        } catch (e) {
          p.starError = e as Error;
        }
        return p;
      });

      await Promise.allSettled(modulesStart).then((results) =>
        results.forEach((result) => {
          if (result.status == 'rejected') {
            console.error(`Module not started with reason - ${result.reason}`);
          }
        }),
      );

      modulesForStart.forEach((p) => {
        const notStartedModulesIndex = notStartedModules.indexOf(p);
        notStartedModules.splice(notStartedModulesIndex, 1);
      });
    }

    const errors = new Array<Error>();
    this.state.moduleItems.forEach((p) => {
      if (!p.isOptional && p.starError != null) {
        return errors.push(p.starError);
      }
    });

    if (errors.length > 0) {
      this.fireStartError(errors);
    }
  }

  fireStartError(error: Error[]) {
    this.eventManager.fire('StartError', error);
  }
}

export default Modules;
