import {
  AddPrioritizationRule,
  PrioritizationAttribute,
  PrioritizationRightPartType,
  PrioritizationRule,
  UpdatePrioritizationRule,
} from '../@types/generated/administration';
import {
  IFrontPrioritizationAttribute,
  IFrontPrioritizationRule,
  RightPartType,
} from '../@types/prioritization';

export const mapPrioritizationRuleDtoToFront = (
  rule: PrioritizationRule,
): IFrontPrioritizationRule => ({
  id: rule.id,
  title: rule.title,
  isEnabled: rule.isEnabled,
  attributeId: rule.attributeId,
  comparisonRule: rule.comparisonRule,
  values: rule.values.map((v, index) => ({
    key: index.toString(),
    value: v.value ?? '',
    priority: v.priority,
  })),
});

export const mapPrioritizationRuleFrontToDto = (
  rule: IFrontPrioritizationRule,
): UpdatePrioritizationRule | AddPrioritizationRule => ({
  title: rule.title,
  isEnabled: rule.isEnabled,
  attributeId: rule.attributeId,
  comparisonRule: rule.comparisonRule,
  values: rule.values.map((v) => ({
    value: v.value,
    priority: v.priority,
  })),
});

export const mapPrioritizationParameterDtoToFront = (
  parameter: PrioritizationAttribute,
): IFrontPrioritizationAttribute => ({
  id: parameter.id,
  displayName: parameter.displayName,
  comparisonRules: parameter.comparisonRules.map((v) => ({
    name: v.name,
    code: v.code,
  })),
  rightPartType: {
    [PrioritizationRightPartType.Text]: RightPartType.Text,
    [PrioritizationRightPartType.List]: RightPartType.List,
    [PrioritizationRightPartType.None]: RightPartType.None,
    [PrioritizationRightPartType.Unknown]: RightPartType.Unknown,
  }[parameter.rightPartType],
  options:
    parameter.options?.map((v) => ({
      name: v.name,
      value: v.value,
    })) ?? [],
});
