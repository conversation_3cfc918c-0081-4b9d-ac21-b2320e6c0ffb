import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontRequest } from '../../@types/Requests';

import extraReducers from './requests.extraReducers';

export interface IRequestsStore {
  requests: IFrontRequest[];
  lastPageNumber: number;
  totalRequestsNumber: number;
  loading: boolean;
  error?: SerializedError;
}

const initialState: IRequestsStore = {
  requests: [],
  lastPageNumber: 1,
  totalRequestsNumber: 0,
  loading: false,
};

const requestsSlice = createSlice({
  name: 'operator',
  initialState,
  reducers: {},
  extraReducers,
});

export default requestsSlice.reducer;
