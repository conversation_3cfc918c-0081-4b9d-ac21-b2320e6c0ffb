import { IShellAppSettings } from './shellAppSettings.helper';

export type WindowAppSettings = Pick<IShellAppSettings, 'externalHost'>;

const keyForAppSettings = Symbol('webarm-app-settings');

export const setAppSettingsToGlobal = (settings: WindowAppSettings) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  window[keyForAppSettings] = settings;
};

export const getAppSettingsFromGlobal = (): WindowAppSettings | undefined => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  return window[keyForAppSettings];
};
