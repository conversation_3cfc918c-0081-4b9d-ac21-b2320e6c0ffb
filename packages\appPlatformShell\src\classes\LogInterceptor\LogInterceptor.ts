import logSenderInstance from '../LogSender/logSender.manager';

export type WebarmLogLevel = 'fatal' | 'error' | 'warn' | 'info' | 'debug' | 'log';

export interface ILogInterceptorOptions {
  sendToServer: WebarmLogLevel[];
  hideInConsole: WebarmLogLevel[];
}

class LogInterceptor {
  private options: ILogInterceptorOptions;

  constructor() {}
  setOptions(options: ILogInterceptorOptions) {
    this.options = options;

    const needToPatchKeysSet = new Set<WebarmLogLevel>([
      ...options.sendToServer,
      ...options.hideInConsole,
    ]);

    const needToPatchKeys = Array.from(needToPatchKeysSet);

    if (options.sendToServer.includes('fatal')) {
      this.patchWindowError();
    }

    if (needToPatchKeys.filter((k) => k !== 'fatal').length > 0) {
      this.patchWindowConsole(needToPatchKeys);
    }

    if (needToPatchKeys.includes('fatal') || needToPatchKeys.includes('error')) {
      this.patchWindowFetch();
    }
  }

  private windowOnErrorHandler = (e: ErrorEvent) => {
    if (this.options.sendToServer.includes('fatal')) {
      this.sendLogToServer(
        'fatal',
        e.message,
        `file: ${e.filename}:${e.lineno ?? 0}:${e.colno ?? 0}`,
      );
    }
  };

  private patchWindowError() {
    window.removeEventListener('error', this.windowOnErrorHandler);
    window.addEventListener('error', this.windowOnErrorHandler);
  }

  private patchWindowConsole(needToPatchKeys: WebarmLogLevel[]) {
    const originalConsole = window.console;

    const consolePatch = needToPatchKeys.reduce<Partial<Console>>((acc, levelKey) => {
      const consoleKey = levelKey as keyof Console;
      return {
        ...acc,
        [consoleKey]: (message?: any, ...optionalParams: any[]) => {
          if (!this.options.hideInConsole.includes(levelKey)) {
            originalConsole[consoleKey].apply(originalConsole, [message, ...optionalParams]);
          }
          if (this.options.sendToServer.includes(levelKey)) {
            this.sendLogToServer(levelKey, ...[message, ...optionalParams]);
          }
        },
      };
    }, {});

    window.console = {
      ...originalConsole,
      ...consolePatch,
    };

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    window.customLog = this.sendCustomLogToServer;
  }
  private patchWindowFetch() {
    const originalFetch = window.fetch;
    const noErrorsCodesCrutch = [409];

    window.fetch = async (input: RequestInfo | URL, init?: RequestInit | undefined) => {
      const response = await originalFetch(input, init);
      const needLogErrors =
        this.options.sendToServer.includes('fatal') || this.options.sendToServer.includes('error');

      if (needLogErrors && !response.ok && !noErrorsCodesCrutch.includes(response.status)) {
        this.sendLogToServer(
          'error',
          `Fetching ${input}`,
          `${response.status}. ${response.statusText}`,
          JSON.stringify(init ?? {}),
        );
      }
      return response;
    };
  }

  sendCustomLogToServer(...args: any[]) {
    this.sendLogToServer('log', ...args);
  }

  private sendLogToServer(level: WebarmLogLevel, ...args: any[]) {
    logSenderInstance.addLog({
      appName: window.location.pathname.replaceAll('/', '') || 'webarm',
      level: level,
      message: args.join('\t'),
      timestamp: new Date().toISOString(),
      user: '',
      meta: {
        level,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        WEBARM_TITLE: window.WEBARM_TITLE ?? 'unknown',
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        WEBARM_VERSION: window.WEBARM_VERSION ?? 'unknown',
      },
    });
  }
}

export default LogInterceptor;
