import React from 'react';

import { AppComponentProps } from '@monorepo/common/src/platform/awp-web-interfaces';
import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { FrontStepType, SystemOwner } from '@monorepo/dialog-scripts/src/@types/script';
import DialogScriptsApp from '@monorepo/dialog-scripts/src/components/DialogScriptsApp';

import { getLocaleMessageById } from '../../helpers/localeHelper';

export default function Root({ shell }: AppComponentProps) {
  return (
    <DialogScriptsApp
      shell={shell}
      externalConfig={{
        title: getLocaleMessageById('app.header.title'),
        columns: [
          'name',
          'code',
          'status',
          'priority',
          'createdBy',
          'createdDate',
          'changedBy',
          'changedDate',
          'activeFrom',
          'activeTo',
        ],
        editorTitlePlaceholder: getLocaleMessageById('app.editor.header'),
        canAutostart: false,
        canSetPriority: true,
        stepTypes: [
          FrontStepType.Step,
          FrontStepType.Rating,
          FrontStepType.Router,
          FrontStepType.Terminal,
        ],
        answerTypes: [AnswerTypes.Button, AnswerTypes.None],
        allowStepPromptUrl: false,
        disableAutoRelation: true,
        disableVirtualFinishStep: true,
        fileExtension: 'crb',
        canChangeAvailableStatus: true,
        systemOwner: SystemOwner.RatingBot,
      }}
      systemOwner={SystemOwner.RatingBot}
    />
  );
}
