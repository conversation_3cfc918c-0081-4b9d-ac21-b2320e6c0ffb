import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  AddQueue,
  QueueInfo,
  QueueParametersView,
  QueueView,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export async function getQueues() {
  const response = await commonFetch(`${getSettings().administrationApiUrl}/queues`, {
    credentials: 'include',
  });

  return (await response.json()) as QueueInfo[];
}

export async function getQueue(id: number) {
  const response = await commonFetch(`${getSettings().administrationApiUrl}/queues/${id}`, {
    credentials: 'include',
  });

  return (await response.json()) as QueueView;
}

export async function getQueueParameters() {
  const response = await commonFetch(`${getSettings().administrationApiUrl}/queues/parameters`, {
    credentials: 'include',
  });

  return (await response.json()) as QueueParametersView;
}

export async function createQueue(queue: AddQueue) {
  return await commonFetch(`${getSettings().administrationApiUrl}/queues`, {
    credentials: 'include',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(queue),
  });
}

export async function updateQueue(id: number, queue: AddQueue) {
  return await commonFetch(`${getSettings().administrationApiUrl}/queues/${id}`, {
    credentials: 'include',
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(queue),
  });
}

export async function deleteQueue(id: number) {
  return await commonFetch(`${getSettings().administrationApiUrl}/queues/${id}`, {
    method: 'DELETE',
    credentials: 'include',
  });
}

export async function setQueueAsDefault(id: number) {
  return await commonFetch(`${getSettings().administrationApiUrl}/queues/${id}/setAsDefault`, {
    credentials: 'include',
    method: 'PUT',
  });
}
