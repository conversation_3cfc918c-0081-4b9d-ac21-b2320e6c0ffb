import React from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  FloatingTooltip,
  grids,
  IconButton,
  Input,
  utils,
} from '@product.front/ui-kit';

import IconDelete from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import { AnswerTypes } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import {
  IFrontAnswer,
  IFrontAnswerInvalidReasons,
  IFrontStep,
} from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getAnswerRegExpPatternByStepAnswers } from '../../../helpers/scriptHelpers';
import InputErrorMessage from '../../InputErrorMessage';

import TransferSelect from './TransferSelect';

interface IAnswerProps {
  answer: IFrontAnswer;
  answers: IFrontAnswer[];
  onDelete: () => void;
  onChange: (newAnswer: IFrontAnswer) => void;
  onSwap: (currentAnswerOrder: number, newAnswerOrder: number) => void;
  steps: IFrontStep[];
  disabled: boolean;
  canDelete: boolean;
  requiredForActive: boolean;
  invalidReasons?: IFrontAnswerInvalidReasons;
  answerType: AnswerTypes;
}

const draggableAnswerFormat = 'product-ds/answer';
const answerContainerClass = 'product-ds-answer';

const Answer = ({
  answer,
  answers,
  canDelete,
  onDelete,
  onChange,
  onSwap,
  steps,
  disabled,
  requiredForActive,
  invalidReasons,
  answerType,
}: IAnswerProps) => {
  const answerInputRef = React.useRef<HTMLInputElement>(null);

  const [text, setText] = React.useState(answer.text);

  const answerRegexp = React.useMemo(() => {
    return getAnswerRegExpPatternByStepAnswers(
      answers.filter((currentAnswer) => currentAnswer.id !== answer.id),
    );
  }, [answers, answer]);

  React.useEffect(() => {
    answerInputRef.current?.checkValidity();
    if (!answerInputRef.current || !answerInputRef.current.value?.match(answerRegexp)) return;

    answerInputRef.current.setCustomValidity('');
    answerInputRef.current.setAttribute('title', '');
  }, [answerInputRef, answerRegexp]);

  const handleDragEnter = (event: React.DragEvent) => {
    const answerElement = (event.target as HTMLElement).closest<HTMLDivElement>(
      `.${answerContainerClass}`,
    )!;
    answerElement.style.border = '1px dashed var(--palette-onyxBlack-70)';
  };

  const handleDragLeave = (event: React.DragEvent) => {
    const answerElement = (event.target as HTMLElement).closest<HTMLDivElement>(
      `.${answerContainerClass}`,
    )!;
    answerElement.style.border = '1px dashed transparent';
  };

  const getLabelText = () => {
    // eslint-disable-next-line sonarjs/no-small-switch
    switch (answerType) {
      case AnswerTypes.TextTemplate:
        return getLocaleMessageById('app.modals.form.answerTemplate');
      default:
        return getLocaleMessageById('app.modals.form.answerText');
    }
  };

  const transferDisabled = () => {
    // eslint-disable-next-line sonarjs/no-small-switch
    switch (answerType) {
      case AnswerTypes.TextTemplate:
        return disabled || !text;
      default:
        return disabled;
    }
  };

  return (
    <div
      className={clsx(
        grids.row,
        utils.gap4,
        utils.alignItemsCenter,
        utils.radius2,
        answerContainerClass,
      )}
      style={{ cursor: 'move', border: '1px dashed transparent', alignItems: 'self-start' }}
      onDragStart={(event) => {
        event.dataTransfer.setData(draggableAnswerFormat, answer.order.toString());
        const target = event.target as HTMLElement;
        const elementStyle = target.style;
        elementStyle.opacity = '0.4';
        elementStyle.background = 'var(--palette-hollywoodSmile)';
      }}
      onDrop={(event) => {
        const draggedOrder = event.dataTransfer.getData(draggableAnswerFormat);
        if (draggedOrder === answer.order.toString()) return;

        onSwap(Number(draggedOrder), answer.order);
        handleDragLeave(event);
      }}
      onDragEnd={(event) => {
        const elementStyle = (event.target as HTMLElement).style;
        elementStyle.opacity = '1';
        elementStyle.background = '';
        event.dataTransfer.clearData();
      }}
      onDragEnter={handleDragEnter}
      onDragOver={(event) => {
        event.preventDefault();
        handleDragEnter(event);
      }}
      onDragLeave={handleDragLeave}
      draggable
    >
      <div className={grids.col6}>
        <Input
          ref={answerInputRef}
          autoFocus={!text.length}
          wrapperClassName={utils.w100}
          label={getLabelText()}
          required={requiredForActive}
          canClearBehavior={CanClearBehavior.Value}
          value={text}
          onChange={({ value }, event) => {
            event?.target.checkValidity();
            setText(value || '');
            if (!event?.target || !value?.match(answerRegexp)) return;

            event.target.setCustomValidity('');
            event.target.setAttribute('title', '');
          }}
          onBlur={() => onChange({ ...answer, text })}
          disabled={disabled}
          onInvalid={(event) => {
            const target = event.target as HTMLInputElement;
            const validityState = target.validity;
            let errorMessage = '';
            if (validityState.patternMismatch) {
              errorMessage = getLocaleMessageById('app.error.notUniqueAnswer');
            }
            if (!errorMessage) return;

            target.setCustomValidity(errorMessage);
            target.title = errorMessage;
          }}
          pattern={answerRegexp}
          isInvalid={!!invalidReasons?.text}
          message={<InputErrorMessage>{invalidReasons?.text}</InputErrorMessage>}
        />
      </div>
      <div className={clsx(grids.col6, utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
        <TransferSelect
          wrapperClassName={utils.w100}
          steps={steps}
          label={getLocaleMessageById('app.modals.form.transfer')}
          required={requiredForActive}
          value={answer.transferTo.toString()}
          onChange={({ value }) =>
            onChange({
              ...answer,
              transferTo: value ?? 'default',
            })
          }
          disabled={transferDisabled()}
          isInvalid={!!invalidReasons?.transferText}
          message={<InputErrorMessage>{invalidReasons?.transferText}</InputErrorMessage>}
        />
        {canDelete && (
          <FloatingTooltip
            tooltip={getLocaleMessageById('app.modals.form.answer.actions.delete')}
            disabled={disabled}
          >
            <IconButton onClick={onDelete} disabled={disabled}>
              <IconDelete />
            </IconButton>
          </FloatingTooltip>
        )}
      </div>
    </div>
  );
};

export default Answer;
