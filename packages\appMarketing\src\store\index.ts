import { combineReducers, configureStore } from '@reduxjs/toolkit';

import campaignsSlice from './campaigns/campaigns.slice';
import offersSlice from './offers/offers.slice';
import settingsSlice from './settings/settings.slice';

const rootReducer = combineReducers({
  settings: settingsSlice,
  offers: offersSlice,
  campaigns: campaignsSlice,
});

export const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
