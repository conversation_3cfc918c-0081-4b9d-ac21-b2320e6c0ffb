{"app.common.apply": "Apply", "app.common.cancel": "Cancel", "app.common.date.from": "From", "app.common.date.to": "To", "app.common.yes": "Yes", "app.common.save": "Save", "app.common.create": "Create", "app.common.close": "close", "app.common.default": "<PERSON><PERSON><PERSON>", "app.common.continue": "Continue", "app.header.title": "Dialog scripts", "app.header.button.create": "Create", "app.table.empty": "Records not found", "app.table.header.code": "Code", "app.table.header.name": "Name", "app.table.header.priority": "Priority", "app.table.header.status": "Status", "app.table.header.createdBy": "Created", "app.table.header.changedBy": "Changed", "app.table.header.changedDate": "Date of change", "app.table.header.activeFrom": "<PERSON>id from", "app.table.header.activeTo": "Valid for", "app.table.header.createdDate": "date of creation", "app.table.header.recordsNumber": "Number of records found", "app.table.header.dependentScripts": "Using to", "app.modals.form.name": "Script*", "app.modals.form.code": "Script*", "app.modals.form.description": "Description", "app.modals.form.status": "Status*", "app.modals.form.activeFrom": "Start date", "app.modals.form.activeTo": "End date", "app.modals.form.steps": "Steps", "app.modals.form.step": "Step No.", "app.modals.form.addStep": "Add step", "app.modals.form.stepName": "Step name", "app.modals.form.stepDescription": "Operator text", "app.modals.form.displayTextVariant": "Display text", "app.modals.form.keyValueButton": "Button Value", "app.modals.form.choice.addVariant": "Add other variant", "app.modals.form.stepPromptUrl": "Extension point", "app.modals.form.answerText": "Response text", "app.modals.form.answerTemplate": "Template", "app.modals.form.transfer": "Transition", "app.modals.form.answerDisplayType": "Response display", "app.modals.form.variableName": "Variable name", "app.modals.form.variableArrayName": "Variable-array", "app.modals.form.variableCode": "Variable code", "app.modals.form.addAnswer": "Add an answer", "app.modals.form.addTextTemplate": "Add a template", "app.modals.closeScript.header": "Close editor?", "app.modals.closeScript.text": "All changes made will be lost.", "app.modals.deleteStep.header": "Delete step?", "app.modals.deleteStep.text": "Removing a step will break the chain", "app.modals.deleteRelation.header": "Delete link?", "app.modals.statistics.totalAnswers": "Total responses", "app.scriptStatus.draft": "Draft", "app.scriptStatus.published": "published", "app.scriptStatus.archived": "Archive", "app.scriptStatus.notAvailable": "Not available", "app.answerDisplayType.radio": "One of the list", "app.answerDisplayType.select": "Drop-down list", "app.answerDisplayType.free": "Text (string)", "app.answerDisplayType.button": "<PERSON><PERSON>", "app.answerDisplayType.choice": "Choice", "app.answerDisplayType.file": "File", "app.answerDisplayType.template": "Text (template)", "app.answerDisplayType.none": "No answer options", "app.stepTransfer.default": "Next step", "app.modals.form.answerDisplayTypeNotNone": "With answers", "app.modals.timeout": "Timer", "app.modals.timeoutUnit": "(min.)", "app.modals.limit": "Limit", "app.modals.form.freeAnswer": "Answer \"Own option\": Free form answer", "app.stepTransfer.end": "Completion", "modal.error.message": "Request execution error", "app.script.saveAndPublish": "Save and publish", "app.script.publish": "Publish", "app.script.saveAndArchive": "Save and archive", "app.script.archive": "Archive", "app.tooltip.archive": "Archive", "app.tooltip.notAvailableAction": "Remove from publication", "app.modals.notAvailableHeader": "Unpublish \"{scriptName}\"?", "app.modals.notAvailableText": "Its status will be changed to \"Unavailable\"", "app.results.notAvailableError": "Availability change error", "app.results.notAvailableSuccess": "Availability is successfully changed", "app.tooltip.copy": "Create a copy", "app.tooltip.edit": "Edit", "app.tooltip.play": "Test playback", "app.tooltip.refresh": "Refresh", "app.tooltip.current": "Processes requests", "app.modals.archive.header": "Archive \"{scriptName}\"?", "app.modals.archive.text": "It cannot be restored", "app.modals.publish.header": "Publish \"{scriptName}\"?", "app.modals.dependentScript.header": "This script is linked to another «{scriptName}»?", "app.modals.dependentScripts.header": "This scenario is linked to others «{scriptNames}»?", "app.modals.form.canBeAutomated": "Automatic start on distribution", "app.modals.form.priority": "Priority", "app.modals.formValidation.invalidRange": "The value must be in the range from {min} to {max}", "app.modals.formValidation.valueRequired": "Value must not be empty", "app.modals.formValidation.variableRequired": "Variable must not be empty", "app.modals.formValidation.listItemRequired": "List must not be empty", "app.modals.formValidation.invalidRelation": "Invalid Relation", "app.modals.formValidation.notActualScenario": "Not actual scenario", "app.modals.subscriptProblem.header": "The script \"{scriptName}\" could not be published because contains nested scripts in the state {subscriptState}", "app.modals.subscriptProblem.body": "Post nested scripts and try again", "app.error.notUniqueVariable": "Variables must be unique within the scenario", "app.name.copy": "Copy", "app.common.collapse": "Collapse", "app.common.expand": "Expand", "app.editor.noName": "Untitled", "app.editor.blockTypeStep": "Step", "app.editor.blockTypeRouter": "router", "app.editor.blockTypeService": "Action", "app.editor.blockTypeSubscript": "<PERSON><PERSON><PERSON>", "app.editor.blockTypeTerminal": "Terminal", "app.editor.blockTypeRating": "CSI assessment", "app.editor.blockTypeScenario": "<PERSON><PERSON><PERSON>", "app.editor.errorStepsInvalid": "", "app.editor.scriptHeader": "New script", "app.editor.routerHeader": "New router", "app.editor.routerName": "Name", "app.editor.routerDescription": "Description", "app.editor.routerNewRule": "new rule", "app.editor.routerRuleName": "Rule Name", "app.editor.routerRuleDefaultTransfer": "Default transition", "app.editor.routerRuleVariable": "Variable", "app.editor.routerRuleOperator": "Operator", "app.editor.routerRuleValue": "Meaning", "app.editor.routerRuleCondition": "Condition", "app.editor.routerRule": "rule", "app.editor.routerRuleConditionEqual": "Equals", "app.editor.routerRuleConditionNotEqual": "Not equal", "app.editor.routerRuleConditionContains": "Contains", "app.editor.routerRuleConditionNotContains": "Does not contain", "app.editor.routerRuleConditionGt": "Greater", "app.editor.routerRuleConditionLt": "Less", "app.editor.routerRuleConditionGtEq": "Greater or equal", "app.editor.routerRuleConditionLtEq": "Less or equal", "app.editor.routerRuleMoveUp": "Raise higher", "app.editor.routerRuleMoveDown": "Drop below", "app.editor.serviceTypeHeader": "Action type", "app.editor.serviceTypePlaceholder": "Select action type", "app.editor.serviceInputsHeader": "Request parameters", "app.editor.serviceOutputHeader": "Response parameters", "app.editor.serviceInputsValue": "Meaning", "app.editor.serviceInputsManual": "Manual input", "app.editor.serviceTransferHeader": "Transition", "app.editor.serviceTransferSuccess": "Success", "app.editor.serviceTransferError": "Error", "app.editor.scripinformation.noDependentScripts": "No dependent scripts", "app.editor.scripinformation.dependentScripts": "Dependent scripts", "app.results.saveError": "Saving error", "app.results.publishSuccess": "Successfully published", "app.results.publishError": "Publishing error", "app.results.archiveSuccess": "Archived successfully", "app.results.archiveError": "Archiving error", "app.editor.toggleAutoRelations": "Link automatically", "app.export.errorDescription": "Try export after a while", "app.import.error": "Import error", "app.import.errorDescription": "Try import after a while", "app.common.OK": "OK", "app.tooltip.export": "Export", "app.tooltip.import": "Import", "app.editor.step.addAttachment": "Add Attachment", "app.editor.step.addAttachmentFromVariable": "Add Attachment From Variable", "app.editor.step.attachmentsFromVariables": "Attachments From Variables", "app.editor.step.deleteAll": "Delete All", "app.editor.step.subscriptTitle": "<PERSON><PERSON><PERSON>", "app.editor.step.subscriptSelect": "Choose script", "app.editor.step.scenarioSelect": "Choose scenario", "app.editor.step.subscriptDescription": "Description of the script", "app.editor.step.scenarioDescription": "Description of the scenario", "app.editor.step.subscriptStatus": "Status", "app.editor.step.scenarioStatus": "Status of the scenario", "app.error.notUniqueAnswer": "Answers should be unique as part of a step", "app.table.header.rating": "Rating", "app.table.header.runsNumber": "Launches", "app.table.header.abortNumber": "Aborts", "app.editor.step.variableSource": "Source", "app.editor.step.labelIsSkippable": "Skip step for prefilled values", "app.editor.step.promptVariables": "Variables: {variables}", "app.modals.form.firstStep": "Make the starting", "app.modals.form.answer.actions.delete": "Delete", "app.editor.step.terminalSubject": "Subject", "app.editor.step.terminalAction": "Action", "app.editor.step.terminalUpdateDataHeader": "Update case data", "app.editor.step.terminalUpdateDataVariable": "Variable", "app.editor.step.terminalUpdateDataAttribute": "Attribute", "app.editor.step.terminalUpdateDataVariableAdd": "Add variable", "app.editor.step.terminalSubjectHeader": "Set the request subject", "app.editor.step.terminalActionHeader": "Action with request", "app.table.footer.count": "Number of records", "app.step.service.delete": "Delete", "app.step.service.serviceUnavailable": "Automation service with ID {automationServiceId} does not exist or not active", "app.modals.forms.addTag": "Add tag", "app.editor.step.labelIsBackButtonAvailable": "Step back is available", "app.editor.serviceFilesHeader": "Request files", "app.automationService.variableFileAdd": "Add Variable", "app.modals.form.variableFileName": "Variable Files", "app.automationService.requestFileName": "File Name", "app.automationService.variableAdd": "Add Variable", "app.error.notUniqueFileVariable": "File Variable should be unique as part of a step"}