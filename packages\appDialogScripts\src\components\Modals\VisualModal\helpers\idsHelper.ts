export function uuidv4(): string {
  return (+[1e7] + -1e3 + -4e3 + -8e3 + -1e11)
    .toString()
    .replace(/[018]/g, (c) =>
      (+c ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (+c / 4)))).toString(16),
    );
}

export const getNodeTargetId = (nodeId: string) => `target-handle-for-node-${nodeId}`;
export const getNodeSourceId = (nodeId: string) => `source-handle-for-node-${nodeId}`;
export const getAnswerSourceId = (answerId: string) => `source-handle-for-answer-${answerId}`;
export const getRuleSourceId = (ruleId: string) => `source-handle-for-rule-${ruleId}`;
export const getServiceSuccessSourceId = (variant: string) =>
  `source-handle-for-service-success-${variant}`;
export const getServiceFailSourceId = (variant: string) =>
  `source-handle-for-service-fail-${variant}`;
export const getSubscriptSourceId = (variant: string) => `source-handle-for-subscript-${variant}`;
export const getLimitSourceId = (variant: string) => `source-handle-for-limit-${variant}`;
export const getTimeoutSourceId = (variant: string) => `source-handle-for-timeout-${variant}`;
export const getChoiceStepAnswerSourceId = (variant: string) =>
  `source-handle-for-choice-step-answer-${variant}`;

export const getNodeId = () => `node-${uuidv4()}`;
export const getEdgeId = () => `edge-${uuidv4()}`;
