import React from 'react';

import { CanClearBehavior, grids, Select } from '@product.front/ui-kit';

import { TemplateMessage } from '../../../@types/generated/marketing';
import { getLocaleMessageById } from '../../../helpers/localeHelper';

import ErrorMessage from './ErrorMessage';

interface INeuroNetTypeFields {
  value: string;
  validationResult: { automaticOfferValue: string };
  setValue: (value: string) => void;
  templates: TemplateMessage[];
}

const NeuroNetTypeFields = ({
  value,
  validationResult,
  setValue,
  templates,
}: INeuroNetTypeFields) => {
  return (
    <div className={grids.row}>
      <Select
        wrapperClassName={grids.col6}
        label={getLocaleMessageById('app.offer.form.input.automaticOfferValue')}
        value={value}
        data={templates.map((template) => ({
          value: template?.code ?? 'unknown',
          text: template.displayName ?? 'unknown',
        }))}
        onChange={({ value: newValue }) => setValue(newValue ?? '')}
        message={<ErrorMessage>{validationResult.automaticOfferValue}</ErrorMessage>}
        isInvalid={!!validationResult.automaticOfferValue}
        canClearBehavior={CanClearBehavior.Always}
        required
      />
    </div>
  );
};

export default NeuroNetTypeFields;
