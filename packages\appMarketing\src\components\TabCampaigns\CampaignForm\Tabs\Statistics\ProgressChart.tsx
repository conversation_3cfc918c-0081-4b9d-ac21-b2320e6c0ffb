import React from 'react';

import clsx from 'clsx';

import { Colors, Progress, Text, utils } from '@product.front/ui-kit';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { useMarketingAppSelector } from '../../../../../store/hooks';

import styles from './styles.module.scss';

const ProgressChart = () => {
  const { statistics } = useMarketingAppSelector((state) => state.campaigns);

  if (!statistics) return null;

  const filteredValues = statistics.runningCampaign.filter(
    (value) =>
      ['Volume', 'OfferedNumber', 'AnswerNumber', 'AcceptedNumber', 'ConnectedNumber'].includes(
        value.key,
      ) && value.value !== null,
  );
  filteredValues.sort((a, b) => b.value - a.value);

  const step = Math.ceil(filteredValues[0].value / 4) || 1;
  const maxValue = step * 4;

  return (
    <div
      className={clsx(utils.dFlex, utils.flexColumn, utils.gap4, utils.pY2, utils.positionRelative)}
      style={{ isolation: 'isolate' }}
    >
      {filteredValues.map((value) => (
        <div
          key={value.key}
          className={clsx(utils.dGrid, utils.gap1)}
          style={{ gridTemplateColumns: '1fr 4fr' }}
        >
          <Text className={utils.textEnd}>
            {getLocaleMessageById(`app.campaigns.form.statistics.${value.key}`)}
          </Text>
          <div className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap1)}>
            <div style={{ width: `calc(${Math.ceil((value.value * 100) / maxValue)}% - 4ch)` }}>
              <Progress value={100} />
            </div>
            <Text>{value.value}</Text>
          </div>
        </div>
      ))}
      <div
        className={clsx(utils.dGrid, utils.gap1, utils.positionAbsolute, utils.w100, utils.h100)}
        style={{ gridTemplateColumns: '1fr 4fr' }}
      >
        <div />
        <div
          className={clsx(utils.positionRelative, styles.dividersContainer)}
          style={{ zIndex: -1 }}
        >
          <div style={{ left: 0 }}>
            <Text color={Colors.OnyxBlack70}>0</Text>
          </div>
          <div style={{ left: 'calc(25% - 4ch)' }}>
            <Text color={Colors.OnyxBlack70}>{step}</Text>
          </div>
          <div style={{ left: 'calc(50% - 4ch)' }}>
            <Text color={Colors.OnyxBlack70}>{step * 2}</Text>
          </div>
          <div style={{ left: 'calc(75% - 4ch)' }}>
            <Text color={Colors.OnyxBlack70}>{step * 3}</Text>
          </div>
          <div style={{ left: 'calc(100% - 4ch)' }}>
            <Text color={Colors.OnyxBlack70}>{maxValue}</Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressChart;
