import React from 'react';

import clsx from 'clsx';

import { Button, Checkbox, OverlayLoader, Radio, Select, utils } from '@product.front/ui-kit';

import { IFrontCampaign } from '../../@types/campaign';
import {
  CampaignStatus,
  CampaignType,
  DisplayCampaignStatus,
} from '../../@types/generated/marketing';
import { getDateTimeFromTime } from '../../helpers/dateHelper';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import { setSelectedCampaign } from '../../store/campaigns/campaigns.slice';
import { getCampaigns } from '../../store/campaigns/campaigns.thunk';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../store/hooks';
import { getChannels } from '../../store/settings/settings.thunk';

import CampaignForm from './CampaignForm';
import CampaignsList, { ISortState } from './CampaignsList';

import styles from './styles.module.scss';

const sortVariants = [
  {
    value: JSON.stringify({
      field: 'dateFrom',
      direction: 'asc',
      type: 'date',
    }),
    text: getLocaleMessageById('app.campaigns.sort.dateFromAsc'),
  },
  {
    value: JSON.stringify({
      field: 'dateFrom',
      direction: 'desc',
      type: 'date',
    }),
    text: getLocaleMessageById('app.campaigns.sort.dateFromDesc'),
  },
  {
    value: JSON.stringify({
      field: 'dateTo',
      direction: 'asc',
      type: 'date',
    }),
    text: getLocaleMessageById('app.campaigns.sort.dateToAsc'),
  },
  {
    value: JSON.stringify({
      field: 'dateTo',
      direction: 'desc',
      type: 'date',
    }),
    text: getLocaleMessageById('app.campaigns.sort.dateToDesc'),
  },
  {
    value: JSON.stringify({
      field: 'priority',
      direction: 'desc',
      type: 'number',
    }),
    text: getLocaleMessageById('app.campaigns.sort.priorityDesc'),
  },
  {
    value: JSON.stringify({
      field: 'priority',
      direction: 'asc',
      type: 'number',
    }),
    text: getLocaleMessageById('app.campaigns.sort.priorityAsc'),
  },
];

const emptyCampaign: IFrontCampaign = {
  id: '',
  name: '',
  description: '',
  dateFrom: '',
  dateTo: '',
  priority: 1,
  type: CampaignType.Incoming,
  channels: [],
  accomplishmentPercent: 0,
  feedbackPercent: 0,
  needAnswer: true,
  availableForBot: false,
  queueId: 0,
  startTime: getDateTimeFromTime('10:00'),
  stopTime: getDateTimeFromTime('20:00'),
  ignoreClientTimeZone: true,
  responseWaitingTime: 86400,
  offerId: '',
  status: CampaignStatus.Paused,
  displayStatus: DisplayCampaignStatus.New,
  filter: null,
  filterDescriptors: '{}',
  fileUploadingId: null,
  author: '',
};

const TabCampaigns = () => {
  const dispatch = useMarketingAppDispatch();

  const { loading, selectedCampaign } = useMarketingAppSelector((state) => state.campaigns);

  const [campaignDisplayStatus, setDisplayCampaignStatus] =
    React.useState<DisplayCampaignStatus | null>(DisplayCampaignStatus.Active);
  const [campaignTypes, setCampaignTypes] = React.useState<CampaignType[]>([
    CampaignType.Outgoing,
    CampaignType.Incoming,
  ]);
  const [sort, setSort] = React.useState<ISortState>({
    field: 'dateFrom',
    direction: 'asc',
    type: 'date',
  });

  const selectedCampaignRef = React.useRef<IFrontCampaign | null>(null);

  React.useEffect(() => {
    dispatch(getChannels());
    dispatch(getCampaigns(campaignDisplayStatus));
  }, [campaignDisplayStatus, dispatch]);

  React.useEffect(() => {
    if (!selectedCampaign && selectedCampaignRef.current) {
      dispatch(getCampaigns(campaignDisplayStatus));
    }

    selectedCampaignRef.current = selectedCampaign;
  }, [campaignDisplayStatus, dispatch, selectedCampaign]);

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.flexColumn,
        utils.h100,
        utils.w100,
        utils.overflowHidden,
        utils.positionRelative,
      )}
    >
      <div className={clsx(utils.borderBottom, utils.pX5, utils.pY3, utils.dFlex, utils.gap5)}>
        <div className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap3, styles.dividerR)}>
          <Radio
            label={getLocaleMessageById('app.campaigns.displayStatus.new')}
            value={DisplayCampaignStatus.New}
            checked={campaignDisplayStatus === DisplayCampaignStatus.New}
            onChange={() => setDisplayCampaignStatus(DisplayCampaignStatus.New)}
            disabled={loading}
          />
          <Radio
            label={getLocaleMessageById('app.campaigns.displayStatus.planned')}
            value={DisplayCampaignStatus.Planned}
            checked={campaignDisplayStatus === DisplayCampaignStatus.Planned}
            onChange={() => setDisplayCampaignStatus(DisplayCampaignStatus.Planned)}
            disabled={loading}
          />
          <Radio
            label={getLocaleMessageById('app.campaigns.displayStatus.active')}
            value={DisplayCampaignStatus.Active}
            checked={campaignDisplayStatus === DisplayCampaignStatus.Active}
            onChange={() => setDisplayCampaignStatus(DisplayCampaignStatus.Active)}
            disabled={loading}
          />
          <Radio
            label={getLocaleMessageById('app.campaigns.displayStatus.paused')}
            value={DisplayCampaignStatus.Paused}
            checked={campaignDisplayStatus === DisplayCampaignStatus.Paused}
            onChange={() => setDisplayCampaignStatus(DisplayCampaignStatus.Paused)}
            disabled={loading}
          />
          <Radio
            label={getLocaleMessageById('app.campaigns.displayStatus.finished')}
            value={DisplayCampaignStatus.Completed}
            checked={campaignDisplayStatus === DisplayCampaignStatus.Completed}
            onChange={() => setDisplayCampaignStatus(DisplayCampaignStatus.Completed)}
            disabled={loading}
          />
          <Radio
            label={getLocaleMessageById('app.campaigns.displayStatus.all')}
            value=""
            checked={campaignDisplayStatus === null}
            onChange={() => setDisplayCampaignStatus(null)}
            disabled={loading}
          />
        </div>
        <div className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap3, styles.dividerR)}>
          <Checkbox
            label={getLocaleMessageById('app.campaigns.type.outgoing')}
            checked={campaignTypes.includes(CampaignType.Outgoing)}
            onChange={({ checked }) =>
              setCampaignTypes((current) =>
                checked
                  ? [...current, CampaignType.Outgoing]
                  : current.filter((type) => type !== CampaignType.Outgoing),
              )
            }
          />
          <Checkbox
            label={getLocaleMessageById('app.campaigns.type.incoming')}
            checked={campaignTypes.includes(CampaignType.Incoming)}
            onChange={({ checked }) =>
              setCampaignTypes((current) =>
                checked
                  ? [...current, CampaignType.Incoming]
                  : current.filter((type) => type !== CampaignType.Incoming),
              )
            }
          />
        </div>
        <div className={clsx(utils.dFlex, utils.alignItemsCenter)} style={{ width: '265px' }}>
          <Select
            wrapperClassName={clsx(utils.w100, styles.inputWithoutBorder)}
            value={JSON.stringify(sort)}
            onChange={({ value }) => value && setSort(JSON.parse(value) as ISortState)}
            data={sortVariants}
          />
        </div>
        <Button
          className={utils.mLauto}
          onClick={() => dispatch(setSelectedCampaign(emptyCampaign))}
        >
          {getLocaleMessageById('app.campaigns.create')}
        </Button>
      </div>
      <OverlayLoader
        loading={loading}
        wrapperClassName={clsx(utils.overflowAuto, utils.scrollbar, utils.flexGrow1)}
      >
        <CampaignsList types={campaignTypes} sort={sort} />
      </OverlayLoader>
      <CampaignForm onClose={() => dispatch(getCampaigns(campaignDisplayStatus))} />
    </div>
  );
};

export default TabCampaigns;
