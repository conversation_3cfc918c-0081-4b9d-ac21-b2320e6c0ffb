.modalValidated label:has(input:invalid),
.modalValidated label:has(select:invalid),
.modalValidated label:has(textarea:invalid) {
  border-color: var(--palette-amenaza-70) !important;
  color: var(--palette-amenaza-70);
}

.textInput {
  grid-row: span 3;
}

.tagInput {
  grid-row: span 2;
}

@keyframes tagAppear {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.tag {
  transition: width 0.2s ease-in-out;
  animation: tagAppear 0.2s linear;
}
