import React from 'react';

import clsx from 'clsx';

import {
  HeaderButton,
  ActionButtonVariant,
  grids,
  IconButton,
  Switch,
  Text,
  TextVariant,
  utils,
  Button,
  ButtonVariant,
  Menu,
  MenuItem,
  OverlayLoader,
  AttentionIcon,
  AttentionIconSize,
  AttentionIconType,
  colors,
} from '@product.front/ui-kit';

import IconLeftArrow from '@product.front/icons/dist/icons17/MainStuff/IconLeftArrow';
import IconSettings from '@product.front/icons/dist/icons17/MainStuff/IconSettings';
import IconStatistic from '@product.front/icons/dist/icons17/MainStuff/IconStatistic';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';
import IconDocSearch from '@product.front/icons/dist/icons17/Person&Doc/IconDocSearch';
import IconUsers from '@product.front/icons/dist/icons17/Person&Doc/IconUsers';

import ErrorBoundaryContainer from '@monorepo/common/src/common/components/Errors/ErrorBoundary/Container';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { CampaignStatus, DisplayCampaignStatus } from '../../../@types/generated/marketing';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { validateCampaignSettings } from '../../../helpers/validateCampaign';
import {
  setSelectedCampaign,
  updateCampaignField,
  updateValidationResult,
} from '../../../store/campaigns/campaigns.slice';
import { createOrUpdateCampaign, deleteCampaign } from '../../../store/campaigns/campaigns.thunk';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../../store/hooks';
import { getOffers } from '../../../store/offers/offers.thunk';
import { getQueues } from '../../../store/settings/settings.thunk';

import ClientsComponent from './Tabs/Clients';
import ResultsComponent from './Tabs/Results';
import SettingsComponent from './Tabs/Settings';
import StatisticsComponent from './Tabs/Statistics';

import styles from './styles.module.scss';

enum Tab {
  Settings = 1,
  Clients = 2,
  Results = 3,
  Statistics = 4,
}

const tabContent = {
  [Tab.Settings]: <SettingsComponent />,
  [Tab.Clients]: <ClientsComponent />,
  [Tab.Results]: <ResultsComponent />,
  [Tab.Statistics]: <StatisticsComponent />,
};

interface ICampaignFormProps {
  onClose?: () => void;
}

const CampaignForm = ({ onClose }: ICampaignFormProps) => {
  const dispatch = useMarketingAppDispatch();

  const { selectedCampaign, campaignInEdit, saving, validationResult } = useMarketingAppSelector(
    (state) => state.campaigns,
  );

  const [activeTab, setActiveTab] = React.useState<Tab>(Tab.Settings);

  const isChanged = JSON.stringify(selectedCampaign) !== JSON.stringify(campaignInEdit);

  React.useEffect(() => {
    dispatch(getQueues());
    dispatch(getOffers());
  }, [dispatch]);

  React.useEffect(() => {
    if (selectedCampaign && campaignInEdit) return;

    setActiveTab(Tab.Settings);
  }, [selectedCampaign, campaignInEdit]);

  if (!selectedCampaign || !campaignInEdit) return null;

  const close = () => {
    if (isChanged) {
      showConfirmModal({
        header: getLocaleMessageById('app.campaigns.form.closeConfirmHeader'),
        text: getLocaleMessageById('app.campaigns.form.closeConfirmText'),
        onConfirm: () => dispatch(setSelectedCampaign(null)),
        canCancel: true,
      });
      return;
    }

    dispatch(setSelectedCampaign(null));
  };

  const handleDelete = () => {
    showConfirmModal({
      header: getLocaleMessageById('app.campaigns.form.deleteHeader'),
      text: getLocaleMessageById('app.campaigns.form.deleteText', {
        name: selectedCampaign.name,
      }),
      onConfirm: async () => {
        await dispatch(deleteCampaign(selectedCampaign.id)).unwrap();
        onClose?.();
      },
      canCancel: true,
    });
  };

  const validateOfferSettings = () => {
    const { isErrored, validationResult: newValidationResult } =
      validateCampaignSettings(campaignInEdit);

    if (isErrored) {
      dispatch(updateValidationResult(newValidationResult));
    }

    return isErrored;
  };

  const handleSaveClick = async () => {
    if (validateOfferSettings()) return;

    if (campaignInEdit.status === CampaignStatus.Paused) {
      showConfirmModal({
        header: getLocaleMessageById('app.campaigns.form.inactive.header'),
        text: getLocaleMessageById('app.campaigns.form.inactive.text'),
        onConfirm: () => dispatch(createOrUpdateCampaign(campaignInEdit)),
        canCancel: true,
      });
      return;
    }

    await dispatch(createOrUpdateCampaign(campaignInEdit)).unwrap();
    // dispatch(setSelectedCampaign(null)); // Закрывать точно не надо после сохранения?
    onClose?.();
  };

  return (
    <section
      className={clsx(
        utils.w100,
        utils.h100,
        utils.positionAbsolute,
        styles.container,
        colors.bgHollywoodSmile,
      )}
    >
      <ErrorBoundaryContainer
        errorPreContent={() => (
          <HeaderButton
            className={clsx(
              utils.dFlex,
              utils.alignItemsCenter,
              utils.gap2,
              utils.p2,
              styles.backButton,
            )}
            variant={ActionButtonVariant.Light}
            onClick={close}
          >
            <IconLeftArrow />
            <Text variant={TextVariant.BodySemibold}>
              {getLocaleMessageById('app.campaigns.form.back')}
            </Text>
          </HeaderButton>
        )}
        canRetry
      >
        <div
          className={clsx(grids.row, utils.gap0, utils.w100, utils.h100)}
          style={{ gridTemplateRows: 'auto 1fr auto' }}
        >
          <div
            className={clsx(
              grids.col12,
              utils.borderBottom,
              utils.pX5,
              utils.pY1,
              utils.dFlex,
              utils.justifyContentBetween,
              utils.alignItemsCenter,
            )}
          >
            <HeaderButton
              className={clsx(
                utils.dFlex,
                utils.alignItemsCenter,
                utils.gap2,
                utils.p2,
                styles.backButton,
              )}
              variant={ActionButtonVariant.Light}
              onClick={close}
              disabled={saving}
            >
              <IconLeftArrow />
              <Text variant={TextVariant.BodySemibold}>
                {selectedCampaign.name || getLocaleMessageById('app.campaigns.form.back')}
              </Text>
            </HeaderButton>
            <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
              <Text>{getLocaleMessageById('app.campaign.form.campaignActive')}</Text>
              <Switch
                checked={campaignInEdit.status === CampaignStatus.Active}
                onChange={({ checked }) =>
                  dispatch(
                    updateCampaignField({
                      fieldName: 'status',
                      fieldValue: checked ? CampaignStatus.Active : CampaignStatus.Paused,
                    }),
                  )
                }
                disabled={
                  campaignInEdit.displayStatus === DisplayCampaignStatus.Completed || saving
                }
              />
            </div>
            <IconButton
              onClick={handleDelete}
              disabled={
                !selectedCampaign.id ||
                selectedCampaign.displayStatus !== DisplayCampaignStatus.New ||
                saving
              }
            >
              <IconTrash />
            </IconButton>
          </div>
          <Menu className={clsx(grids.col2, utils.borderRight)}>
            {selectedCampaign.id && (
              <>
                <MenuItem
                  className={utils.gap2}
                  active={activeTab === Tab.Statistics}
                  onClick={() => setActiveTab(Tab.Statistics)}
                >
                  <IconStatistic />
                  {getLocaleMessageById('app.campaigns.form.tabs.statistics')}
                </MenuItem>
                <MenuItem
                  className={utils.gap2}
                  active={activeTab === Tab.Results}
                  onClick={() => setActiveTab(Tab.Results)}
                >
                  <IconDocSearch />
                  {getLocaleMessageById('app.campaigns.form.tabs.results')}
                </MenuItem>
              </>
            )}
            <MenuItem
              className={utils.gap2}
              active={activeTab === Tab.Settings}
              onClick={() => setActiveTab(Tab.Settings)}
            >
              <IconSettings className={utils.flexShrink0} />
              {getLocaleMessageById('app.campaigns.form.tabs.config')}
              {Object.keys(validationResult).some(
                (resultKey) =>
                  resultKey !== 'clients' &&
                  !!validationResult[resultKey as keyof typeof validationResult],
              ) && (
                <AttentionIcon
                  size={AttentionIconSize.Small}
                  type={AttentionIconType.Error}
                  className={utils.mLauto}
                />
              )}
            </MenuItem>
            <MenuItem
              className={utils.gap2}
              active={activeTab === Tab.Clients}
              onClick={() => setActiveTab(Tab.Clients)}
            >
              <IconUsers className={utils.flexShrink0} />
              {getLocaleMessageById('app.campaigns.form.tabs.clients')}
              {validationResult.clients && (
                <AttentionIcon
                  size={AttentionIconSize.Small}
                  type={AttentionIconType.Error}
                  className={utils.mLauto}
                />
              )}
            </MenuItem>
          </Menu>
          <OverlayLoader
            loading={saving}
            wrapperClassName={clsx(grids.col10, utils.scrollbar, utils.overflowAuto)}
          >
            {tabContent[activeTab]}
          </OverlayLoader>
          <div className={clsx(grids.col12, grids.row, utils.borderTop)}>
            <div
              className={clsx(utils.pY3, utils.pR6, utils.dFlex, utils.gap5)}
              style={{ gridColumnStart: 3, gridColumnEnd: 13 }}
            >
              <Button onClick={handleSaveClick} disabled={saving}>
                {getLocaleMessageById('app.campaigns.form.save')}
              </Button>
              <Button variant={ButtonVariant.Secondary} onClick={close} disabled={saving}>
                {getLocaleMessageById('app.campaigns.form.cancel')}
              </Button>
            </div>
          </div>
        </div>
      </ErrorBoundaryContainer>
    </section>
  );
};

export default CampaignForm;
