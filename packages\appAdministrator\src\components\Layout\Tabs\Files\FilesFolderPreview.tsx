import React, { DragEventHandler } from 'react';

import clsx from 'clsx';

import { utils, Text, Colors, TextVariant } from '@product.front/ui-kit';

import { FrontFileOrFolder } from '../../../../@types/files';
import { isFolder, normalizePathFor<PERSON>pi, normalizePathForDisplay } from '../../../../helpers/files';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { addTrustedFileAsync, downloadTrustedFile } from '../../../../services/files';
import { selectDisplayedFolder, setSelectedItem } from '../../../../store/files/files.slice';
import { getFoldersAndFiles } from '../../../../store/files/files.thunk';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';

import FilesFolderPreviewItem from './FilesFolderPreviewItem';

import styles from './styles.module.scss';

const FilesFolderPreview: React.FC = () => {
  const { displayedFolder, selectedItem } = useAdministratorAppSelector((store) => store.files);
  const dispatch = useAdministratorAppDispatch();

  const hoverClassName = styles.dragHover;

  const handleDragenter: DragEventHandler<HTMLElement> = (e) => {
    e.preventDefault();
    if (e.dataTransfer.types[0] !== 'Files') return;

    const dropZone = e.currentTarget;
    dropZone.classList.add(hoverClassName);
  };

  const handleDragleave: DragEventHandler<HTMLElement> = (e) => {
    e.preventDefault();
    if (e.dataTransfer.types[0] !== 'Files') return;
    const dropZone = e.currentTarget;
    dropZone.classList.remove(hoverClassName);
  };
  const handleDrop: DragEventHandler<HTMLElement> = async (e) => {
    e.preventDefault();
    if (e.dataTransfer.types[0] !== 'Files') return;
    const dropZone = e.currentTarget;
    dropZone.classList.remove(hoverClassName);

    if (!e.dataTransfer) return;

    const files = Array.from(e.dataTransfer.files);
    const path = displayedFolder?.path ?? '/';

    for (const file of files) {
      await addTrustedFileAsync(file, file.name, normalizePathForApi(path));
    }

    dispatch(getFoldersAndFiles());
  };

  const extraClickHandler = (f: FrontFileOrFolder) => {
    isFolder(f)
      ? dispatch(selectDisplayedFolder(f))
      : downloadTrustedFile(normalizePathForApi(f.path), f.name);
  };

  if (!displayedFolder) return null;

  if (displayedFolder.items.length === 0) {
    return (
      <div
        className={clsx(utils.flexCentredBlock, utils.w100, utils.h100)}
        onDragLeave={handleDragleave}
        onDragEnter={handleDragenter}
        onDragOver={handleDragenter}
        onDrop={handleDrop}
      >
        <div className={utils.textCenter}>
          <Text color={Colors.OnyxBlack60} as="div">
            {getLocaleMessageById('files.folder.empty')}
          </Text>
          <Text color={Colors.OnyxBlack40} variant={TextVariant.CaptionMedium}>
            {displayedFolder.name}
          </Text>
        </div>
      </div>
    );
  }

  return (
    <section
      className={clsx(utils.w100, utils.h100, utils.overflowYAuto, utils.scrollbar)}
      onDragLeave={handleDragleave}
      onDragEnter={handleDragenter}
      onDragOver={handleDragenter}
      onDrop={handleDrop}
      onClick={() => dispatch(setSelectedItem(undefined))}
      onKeyDown={() => {}}
      tabIndex={-1}
      role="button"
    >
      <div className={clsx(utils.dFlex, utils.flexWrap, utils.gap2, utils.p4)}>
        {displayedFolder.items.map((folderOrFile) => {
          const fullPath = folderOrFile.path;
          const isSelected = selectedItem?.path === fullPath;

          return (
            <FilesFolderPreviewItem
              active={isSelected}
              onClick={(e) => {
                dispatch(setSelectedItem(folderOrFile));
                e.stopPropagation();

                if (e.ctrlKey) {
                  extraClickHandler(folderOrFile);
                }
              }}
              onDoubleClick={() => extraClickHandler(folderOrFile)}
              folderOrFile={folderOrFile}
              key={fullPath}
              title={normalizePathForDisplay(fullPath)}
            />
          );
        })}
      </div>
    </section>
  );
};

export default FilesFolderPreview;
