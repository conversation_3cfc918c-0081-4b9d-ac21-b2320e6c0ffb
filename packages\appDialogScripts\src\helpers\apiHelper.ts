import {
  AutomationServiceStatus,
  AutomationServiceType,
  IFrontAutomationService,
} from '@monorepo/automation-services/src/@types/automationService.types';
import { mapBackServiceToFront } from '@monorepo/automation-services/src/mappers/automationServiceToFront';
import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import { getCurrentOperatorUsernameString } from '@monorepo/common/src/managers/currentOperatorManager';
import {
  BffScriptDto,
  RestServiceDto,
  ScriptDto,
} from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import {
  IFrontBffScript,
  IFrontScript,
  SystemOwner,
} from '@monorepo/dialog-scripts/src/@types/script';
import {
  mapFrontScriptToAddDto,
  mapFrontScriptToUpdateDto,
} from '@monorepo/dialog-scripts/src/mappers/dialogScriptsEditorMappersToBack';
import {
  mapBffScriptToFront,
  mapScriptDtoToFront,
} from '@monorepo/dialog-scripts/src/mappers/dialogScriptsEditorMappersToFront';

import { getSettings } from '../config/appSettings';
import { getSystemOwner } from '../managers/systemOwnerManager';

export const getAllScripts = async (): Promise<IFrontBffScript[]> => {
  const appSettings = getSettings();

  const ownerSystemCode = getSystemOwner();
  const query = new URLSearchParams();
  query.append('OwnerSystemCode', ownerSystemCode ?? '');

  const result = await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/bff/scripts?${query.toString()}`,
    {
      credentials: 'include',
    },
  );

  const json = (await result.json()) as BffScriptDto[];

  return json.map(mapBffScriptToFront);
};

export const getScript = async (scriptId: number): Promise<IFrontScript> => {
  const appSettings = getSettings();

  const result = await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/api/scripts/${scriptId}`,
    {
      credentials: 'include',
    },
  );

  const json = (await result.json()) as ScriptDto;

  return mapScriptDtoToFront(json);
};

export const addScript = async (script: IFrontScript): Promise<IFrontScript> => {
  const appSettings = getSettings();

  const body = mapFrontScriptToAddDto(script, getSystemOwner());

  const result = await commonFetch(`${appSettings.dialogScriptsManagementUrl}/api/scripts`, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(body),
    headers: {
      ['Content-Type']: 'application/json',
      userLogin: getCurrentOperatorUsernameString({ fallback: 'front' }),
    },
  });

  const json = (await result.json()) as ScriptDto;

  return mapScriptDtoToFront(json);
};

export const updateScript = async (script: IFrontScript): Promise<IFrontScript> => {
  const appSettings = getSettings();

  const body = mapFrontScriptToUpdateDto(script, getSystemOwner());

  const result = await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/api/scripts/${script.id}`,
    {
      method: 'PUT',
      credentials: 'include',
      headers: {
        ['Content-Type']: 'application/json',
        userLogin: getCurrentOperatorUsernameString({ fallback: 'front' }),
      },
      body: JSON.stringify(body),
    },
  );

  const json = (await result.json()) as ScriptDto;

  return mapScriptDtoToFront(json);
};

export const publishScript = async (scriptId: number) => {
  const appSettings = getSettings();

  return await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/api/scripts/${scriptId}/activate`,
    {
      method: 'PUT',
      credentials: 'include',
      headers: {
        userLogin: getCurrentOperatorUsernameString({ fallback: 'front' }),
      },
    },
  );
};

export const archiveScript = async (scriptId: number) => {
  const appSettings = getSettings();

  return await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/api/scripts/${scriptId}/archive`,
    {
      method: 'PUT',
      credentials: 'include',
      headers: {
        userLogin: getCurrentOperatorUsernameString({ fallback: 'front' }),
      },
    },
  );
};

export const getAllAutomationServices = async (): Promise<IFrontAutomationService[]> => {
  const appSettings = getSettings();
  const query = new URLSearchParams();
  const ownerSystemCode = getSystemOwner();

  query.append('Status', AutomationServiceStatus.Active);
  query.append('OwnerSystemCode', SystemOwner.DialogScripts);

  const result = await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/bff/services?${query}`,
    {
      credentials: 'include',
    },
  );

  const json = (await result.json()) as RestServiceDto[];

  const automationServices: IFrontAutomationService[] = json.map(mapBackServiceToFront);

  /**
   * @todo back. Фильтр ниже актуален до тех пор, пока бекендный плеер для кнопочного бота не умеет js-скрипты
   * выпилить как только научится, либо будет фильтроваться беком
   */
  return automationServices.filter((s) => {
    return ownerSystemCode !== SystemOwner.ButtonBot || s.type !== AutomationServiceType.Code;
  });
};

export const makeUnavailable = async (scriptId: number) => {
  const appSettings = getSettings();

  return await commonFetch(
    `${appSettings.dialogScriptsManagementUrl}/api/scripts/${scriptId}/notavailable`,
    {
      method: 'PUT',
      credentials: 'include',
      headers: {
        userLogin: getCurrentOperatorUsernameString({ fallback: 'front' }),
      },
    },
  );
};
