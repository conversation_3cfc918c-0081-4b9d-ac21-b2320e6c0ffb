import React from 'react';

import clsx from 'clsx';

import {
  <PERSON><PERSON>,
  <PERSON>tonV<PERSON>t,
  CanClearBehavior,
  Checkbox,
  grids,
  Input,
  Jumbotron,
  JumbotronType,
  OverlayLoader,
  Radio,
  Select,
  Textarea,
  utils,
} from '@product.front/ui-kit';

import { AutomaticType, OfferType, TemplateMessage } from '../../../@types/generated/marketing';
import { ConnectionType, IOffer, IOfferType } from '../../../@types/offer';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { defaultValidationResult, validateOffer } from '../../../helpers/validateOffer';

import DefaultTypeFields from './DefaultTypeFields';
import ErrorMessage from './ErrorMessage';
import NeuroNetTypeFields from './NeuroNetTypeFields';
import WhatsAppTypeFields from './WhatsAppTypeFields';

import styles from './styles.module.scss';

interface IOfferForm {
  selectedOffer: IOffer | null;
  saving: boolean;
  offerTypes: IOfferType[];
  onCancel: () => void;
  onSubmit: (offer: IOffer) => void;
  whatsAppTemplates: TemplateMessage[];
  neuroNetTemplates: TemplateMessage[];
}

const OfferForm = ({
  selectedOffer,
  saving,
  offerTypes,
  onCancel,
  onSubmit,
  whatsAppTemplates,
  neuroNetTemplates,
}: IOfferForm) => {
  const [validationResult, setValidationResult] = React.useState(defaultValidationResult);
  const [name, setName] = React.useState('');
  const [description, setDescription] = React.useState('');
  const [externalReference, setExternalReference] = React.useState('');
  const [dateFrom, setDateFrom] = React.useState(new Date().toISOString().split('T')[0]);
  const [dateTo, setDateTo] = React.useState(new Date().toISOString().split('T')[0]);
  const [automaticType, setAutomaticType] = React.useState<AutomaticType | null>(null);
  const [type, setType] = React.useState<OfferType | null>(null);
  const [text, setText] = React.useState('');
  const [automaticOfferValue, setAutomaticOfferValue] = React.useState('');
  const [script, setScript] = React.useState('');
  const [availableForOperator, setAvailableForOperator] = React.useState(true);
  const [connectionType, setConnectionType] = React.useState(ConnectionType.Auto);
  const [parameters, setParameters] = React.useState('');

  React.useEffect(() => {
    setName(selectedOffer?.name ?? '');
    setDescription(selectedOffer?.description ?? '');
    setExternalReference(selectedOffer?.externalReference ?? '');
    setDateFrom(selectedOffer?.dateFrom.split('T')[0] ?? new Date().toISOString().split('T')[0]);
    setDateTo(selectedOffer?.dateTo.split('T')[0] ?? new Date().toISOString().split('T')[0]);
    setAutomaticType(selectedOffer?.automaticType ?? null);
    setType(selectedOffer?.type ?? null);
    setText(selectedOffer?.text ?? '');
    setAutomaticOfferValue(selectedOffer?.automaticOfferValue ?? '');
    setScript(selectedOffer?.script ?? '');
    setAvailableForOperator(selectedOffer?.availableForOperator ?? true);
    setConnectionType(selectedOffer?.connectionType ?? ConnectionType.Auto);
    setParameters(selectedOffer?.parameters ?? '');
  }, [selectedOffer]);

  const automaticTypeFields: Record<AutomaticType, React.ReactNode> = {
    [AutomaticType.Default]: (
      <DefaultTypeFields text={text} validationResult={validationResult} onTextChange={setText} />
    ),
    [AutomaticType.NeuroNet]: (
      <NeuroNetTypeFields
        value={automaticOfferValue}
        setValue={setAutomaticOfferValue}
        validationResult={validationResult}
        templates={neuroNetTemplates}
      />
    ),
    [AutomaticType.WhatsApp]: (
      <WhatsAppTypeFields
        value={automaticOfferValue}
        setValue={setAutomaticOfferValue}
        validationResult={validationResult}
        templates={whatsAppTemplates}
      />
    ),
  };

  if (!selectedOffer) {
    return (
      <div
        className={clsx(
          utils.h100,
          utils.w100,
          utils.dFlex,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
        )}
      >
        <Jumbotron
          type={JumbotronType.Info}
          header={getLocaleMessageById('app.offer.notSelected')}
        />
      </div>
    );
  }

  const getOffer = () => {
    const offer: IOffer = {
      id: selectedOffer?.id ?? '',
      // TODO: не передавать на бэк
      updateTime: selectedOffer?.updateTime ?? '',
      author: selectedOffer?.author ?? 'DefaultAuthor',
      name,
      description,
      externalReference,
      // TODO: хранится datetime, хотя выбирается только дата
      dateFrom: new Date(dateFrom).toISOString(),
      dateTo: new Date(dateTo).toISOString(),
      automaticType,
      type,
      text: '',
      automaticOfferValue: null,
      script,
      availableForOperator,
      connectionType,
      parameters,
    };

    if (automaticType === AutomaticType.Default) {
      offer.text = text;
    }
    if (automaticType && [AutomaticType.NeuroNet, AutomaticType.WhatsApp].includes(automaticType)) {
      offer.automaticOfferValue = automaticOfferValue;
    }

    return offer;
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    const offer = { ...getOffer(), updateTime: new Date().toISOString() };
    const { newValidationResult, isErrored } = validateOffer(offer);

    setValidationResult({ ...newValidationResult });

    if (isErrored) return;

    onSubmit(offer);
  };

  return (
    <form
      className={clsx(utils.dFlex, utils.flexColumn, utils.h100, utils.w100)}
      onSubmit={handleSubmit}
    >
      <OverlayLoader
        loading={saving}
        wrapperClassName={clsx(utils.flexGrow1, utils.flexBasis0, utils.overflowHidden)}
      >
        <div
          className={clsx(
            utils.flexGrow1,
            utils.flexBasis0,
            utils.p6,
            utils.dFlex,
            utils.flexColumn,
            utils.gap4,
            utils.overflowAuto,
            utils.scrollbar,
            utils.h100,
            styles.offerContainer,
          )}
        >
          <Input
            label={getLocaleMessageById('app.offer.form.input.name')}
            value={name}
            onChange={({ value }) => setName(value ?? '')}
            message={<ErrorMessage>{validationResult.name}</ErrorMessage>}
            isInvalid={!!validationResult.name}
            canClearBehavior={CanClearBehavior.Always}
            required
          />
          <Textarea
            label={getLocaleMessageById('app.offer.form.input.description')}
            value={description}
            onChange={({ value }) => setDescription(value ?? '')}
            message={<ErrorMessage>{validationResult.description}</ErrorMessage>}
            isInvalid={!!validationResult.description}
            rows={8}
            required
          />
          <Input
            label={getLocaleMessageById('app.offer.form.input.externalReference')}
            value={externalReference}
            onChange={({ value }) => setExternalReference(value ?? '')}
            canClearBehavior={CanClearBehavior.Always}
          />
          <div className={grids.row}>
            <Input
              wrapperClassName={grids.col6}
              label={getLocaleMessageById('app.offer.form.input.dateFrom')}
              value={dateFrom}
              onChange={({ value }) => setDateFrom(value ?? '')}
              message={<ErrorMessage>{validationResult.dateFrom}</ErrorMessage>}
              isInvalid={!!validationResult.dateFrom}
              canClearBehavior={CanClearBehavior.Always}
              type="date"
              required
            />
            <Input
              wrapperClassName={grids.col6}
              label={getLocaleMessageById('app.offer.form.input.dateTo')}
              value={dateTo}
              onChange={({ value }) => setDateTo(value ?? '')}
              message={<ErrorMessage>{validationResult.dateTo}</ErrorMessage>}
              isInvalid={!!validationResult.dateTo}
              canClearBehavior={CanClearBehavior.Always}
              type="date"
              required
            />
          </div>
          <div className={grids.row}>
            <Select
              wrapperClassName={grids.col6}
              label={getLocaleMessageById('app.offer.form.input.type')}
              value={JSON.stringify({ automaticType, type })}
              data={offerTypes.map((offerType) => ({
                value: JSON.stringify({
                  automaticType: offerType.automaticType,
                  type: offerType.offerType,
                }),
                text: offerType.displayName,
              }))}
              onChange={({ value }) => {
                const newValue = JSON.parse(value ?? '') as {
                  automaticType: AutomaticType;
                  type: OfferType;
                };
                setAutomaticType((newValue?.automaticType as AutomaticType) ?? null);
                setType((newValue?.type as OfferType) ?? null);
              }}
              message={<ErrorMessage>{validationResult.automaticType}</ErrorMessage>}
              isInvalid={!!validationResult.automaticType}
              canClearBehavior={CanClearBehavior.Always}
              required
            />
          </div>
          {automaticTypeFields[automaticType as AutomaticType]}
          <Textarea
            label={getLocaleMessageById('app.offer.form.input.script')}
            value={script}
            onChange={({ value }) => setScript(value ?? '')}
            message={<ErrorMessage>{validationResult.script}</ErrorMessage>}
            isInvalid={!!validationResult.script}
            rows={15}
            required
          />
          <Checkbox
            label={getLocaleMessageById('app.offer.form.input.availableForOperator')}
            checked={availableForOperator}
            onChange={({ checked }) => setAvailableForOperator(checked ?? false)}
          />
          <div className={clsx(utils.dFlex, utils.gap6)}>
            <Radio
              label={getLocaleMessageById('app.offer.form.input.connectionType.manual')}
              value={OfferType.Manual}
              checked={connectionType === ConnectionType.Manual}
              onChange={() => setConnectionType(ConnectionType.Manual)}
            />
            <Radio
              label={getLocaleMessageById('app.offer.form.input.connectionType.auto')}
              value={OfferType.Automatic}
              checked={connectionType === ConnectionType.Auto}
              onChange={() => setConnectionType(ConnectionType.Auto)}
            />
          </div>
          <Textarea
            label={getLocaleMessageById('app.offer.form.input.parameters')}
            value={parameters}
            onChange={({ value }) => setParameters(value ?? '')}
            message={<ErrorMessage>{validationResult.parameters}</ErrorMessage>}
            isInvalid={!!validationResult.parameters}
            rows={8}
            required
          />
        </div>
      </OverlayLoader>

      <div className={clsx(utils.borderTop, utils.pX6, utils.pY3, utils.dFlex, utils.gap4)}>
        <Button type="submit" disabled={saving}>
          {getLocaleMessageById('app.offer.form.save')}
        </Button>
        <Button variant={ButtonVariant.Secondary} onClick={onCancel}>
          {getLocaleMessageById('app.offer.form.cancel')}
        </Button>
      </div>
    </form>
  );
};

export default OfferForm;
