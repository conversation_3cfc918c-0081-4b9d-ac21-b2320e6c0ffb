import React from 'react';

import { Provider } from 'react-redux';

import { HubConnection } from '@product.front/redux-signalr';

import signalRClient from '../../clients/signalRClient';
import { SettingsContext } from '../../contexts/SettingsContext';
import store from '../../store';
import FullScreenLoader from '../FullScreenLoader';
import InternalChat from '../InternalChat';

export interface IAppSettings {
  SignalRUrl: string;
  ApiUrl: string;
}

const InternalChatApp = ({ settings }: { settings: IAppSettings }) => {
  const [wsConnection, setWsConnection] = React.useState<HubConnection | null>(null);

  React.useEffect(() => {
    signalRClient.startConnection().then((conn) => {
      setWsConnection(conn as HubConnection);
    });
  }, []);

  if (!wsConnection) {
    return <FullScreenLoader />;
  }

  return (
    <SettingsContext.Provider value={settings}>
      <Provider store={store}>
        <InternalChat />
      </Provider>
    </SettingsContext.Provider>
  );
};

export default InternalChatApp;
