import React from 'react';

import clsx from 'clsx';

import { Button, ButtonVariant, Input, utils } from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import { SpamWord } from '../../../../@types/generated/administration';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';

interface IEditorProps {
  spamWord: SpamWord | null;
  onSubmit: (word: string) => Promise<void>;
  onClose?: () => void;
}

const Editor = ({ spamWord, onSubmit, onClose }: IEditorProps) => {
  const [word, setWord] = React.useState(spamWord?.name ?? '');
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();

  return (
    <form
      style={{ width: 'clamp(200px, 40vw, 500px)' }}
      onSubmit={async (event) => {
        event.preventDefault();
        if (loading) return;

        setLoading(true);
        try {
          await onSubmit(word);
          onClose?.();
        } catch (err) {
          console.error('Spam or negative words editor submit error', error);
          setError(err);
        } finally {
          setLoading(false);
        }
      }}
    >
      <Input
        wrapperClassName={clsx(utils.pX6, utils.pY4)}
        value={word}
        onChange={({ value }) => setWord(value ?? '')}
        required
        maxLength={60}
      />
      {error && (
        <AlertError
          error={error}
          header={getLocaleMessageById('spamWords.save.error')}
          className={clsx(utils.mX6, utils.mB4)}
        />
      )}
      <div className={clsx(utils.dFlex, utils.gap2, utils.borderTop, utils.pX6, utils.pY4)}>
        <Button
          variant={ButtonVariant.Secondary}
          onClick={onClose}
          disabled={loading}
          className={utils.mLauto}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button type="submit" disabled={loading}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </div>
    </form>
  );
};

export default Editor;
