import React, { FormEvent } from 'react';

import clsx from 'clsx';

import { Button, ButtonVariant, Input, Select, showModal, utils } from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import { FolderContentType, FrontFileOrFolder, IFrontFolder } from '../../../../../../@types/files';
import { needConfirmWhenCompareFalse } from '../../../../../../helpers/confirmSave.helper';
import { getPathsForSelect, normalizePathForApi } from '../../../../../../helpers/files';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { addTrustedFolderAsync, moveTrustedFolderAsync } from '../../../../../../services/files';

interface IFolderAddOrEditModalProps {
  folder?: IFrontFolder;
  contextFolderPath: string;
  onSuccess(newFolder: IFrontFolder): void;
  onClose?: () => void;
  filesAndFolders: FrontFileOrFolder[];
}

interface FormElements extends HTMLFormControlsCollection {
  name: HTMLInputElement;
  path: HTMLInputElement;
}

const FolderAddOrEditModal: React.FC<IFolderAddOrEditModalProps> = ({
  folder,
  onClose,
  onSuccess,
  contextFolderPath,
  filesAndFolders,
}) => {
  const [isSaving, setIsSaving] = React.useState(false);
  const [err, setErr] = React.useState<Error>();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const elements = e.currentTarget.elements as FormElements;

    try {
      setErr(undefined);
      setIsSaving(true);

      const name = elements.name.value.trim();
      const path = elements.path.value.trim();
      const newPath = [path, name].join('/');

      if (folder) {
        await moveTrustedFolderAsync(
          normalizePathForApi(folder.path),
          normalizePathForApi(newPath),
        );

        onSuccess({
          ...folder,
          name,
          path: newPath,
        });
      } else {
        await addTrustedFolderAsync(newPath);
        onSuccess({
          name,
          path: newPath,
          parent: path,
          type: FolderContentType.Folder,
          items: [],
        });
      }

      onClose?.();
    } catch (error) {
      setErr(error);
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = (e: FormEvent<HTMLFormElement>) => {
    const elements = e.currentTarget.elements as FormElements;
    e.preventDefault();
    const hasNoChanges =
      !folder ||
      (folder.name === elements.name.value &&
        normalizePathForApi(folder.parent) === elements.path.value);

    needConfirmWhenCompareFalse(hasNoChanges, onClose);
  };

  return (
    <div className={clsx(utils.dFlex, utils.flexColumn)}>
      <form
        id="FolderEditorForm"
        className={clsx(utils.dFlex, utils.flexColumn, utils.gap5, utils.p6)}
        onSubmit={handleSubmit}
        onReset={handleReset}
      >
        <Input
          name="name"
          label={getLocaleMessageById('files.modal.addFolder.name')}
          value={folder?.name ?? getLocaleMessageById('files.modal.addFolder.nameDefault')}
          disabled={isSaving}
          required
        />
        <Select
          data={getPathsForSelect(filesAndFolders)}
          name="path"
          label={getLocaleMessageById('files.modal.addFolder.folder')}
          value={normalizePathForApi(folder?.parent ?? contextFolderPath ?? '/')}
          disabled={isSaving}
          required
        />
      </form>
      <footer className={clsx(utils.borderTop, utils.pX6, utils.pY4, utils.dFlex, utils.gap2)}>
        {err && (
          <AlertError
            header={
              folder
                ? getLocaleMessageById('files.modal.editFolder.error')
                : getLocaleMessageById('files.modal.addFolder.error')
            }
            error={err}
          />
        )}

        <Button
          className={utils.mLauto}
          variant={ButtonVariant.Secondary}
          type="reset"
          form="FolderEditorForm"
          disabled={isSaving}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button type="submit" form="FolderEditorForm" disabled={isSaving}>
          {getLocaleMessageById('app.common.save')}
        </Button>
      </footer>
    </div>
  );
};

export default FolderAddOrEditModal;

export const addOrEditFolderModal = ({
  contextFolderPath,
  folder,
  onSuccess,
  onClose,
  filesAndFolders,
}: IFolderAddOrEditModalProps) => {
  showModal({
    style: { width: 480 },
    header: getLocaleMessageById(
      folder ? 'files.modal.editFolder.title' : 'files.modal.addFolder.title',
    ),
    children: (closeModal) => (
      <FolderAddOrEditModal
        filesAndFolders={filesAndFolders}
        folder={folder}
        contextFolderPath={contextFolderPath}
        onSuccess={onSuccess}
        onClose={closeModal}
      />
    ),
    flushBody: true,
    canClose: false,
    onClose: onClose,
  });
};
