import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IRequestsStore } from './requests.slice';
import { getRequests } from './requests.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<IRequestsStore>) =>
  builder
    .addCase(getRequests.fulfilled, (state, action) => {
      state.loading = false;
      state.error = undefined;
      state.requests = action.payload.requests;
      state.lastPageNumber = action.payload.lastPageNumber;
      state.totalRequestsNumber = action.payload.requestsNumber;
    })
    .addCase(getRequests.pending, (state) => {
      state.loading = true;
    })
    .addCase(getRequests.rejected, (state, action) => {
      state.loading = false;
      state.requests = [];
      state.lastPageNumber = 1;
      state.totalRequestsNumber = 0;
      state.error = action.error;
    });

export default extraReducers;
