import React from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonVariant,
  grids,
  helpers,
  IconButton,
  Input,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import { AutomationServiceHeader } from '../../@types/automationService.types';
import { getLocaleMessageById } from '../../helpers/localeHelper';

interface IRequestHeadersEditorProps {
  value: AutomationServiceHeader[];
  onChange({ value }: { value: AutomationServiceHeader[] }): void;
  disabled: boolean;
  readonly: boolean;
}

const RequestHeadersEditor: React.FC<IRequestHeadersEditorProps> = ({
  value: requestHeaders,
  onChange,
  disabled,
  readonly,
}) => {
  const eq = (as1: AutomationServiceHeader, as2: AutomationServiceHeader): boolean =>
    as1._key === as2._key;

  return (
    <section>
      {requestHeaders.map((rh) => {
        return (
          <div className={clsx(grids.row, utils.mT3, utils.alignItemsCenter)} key={rh._key}>
            <Input
              withDebounce
              wrapperClassName={grids.col5}
              label={getLocaleMessageById('app.automationService.requestHeadersHeader')}
              disabled={disabled}
              value={rh.name}
              onChange={({ value }) => {
                onChange({
                  value: requestHeaders.map((r) => (eq(r, rh) ? { ...r, name: value || '' } : r)),
                });
              }}
              readOnly={readonly}
            />
            <Input
              withDebounce
              wrapperClassName={grids.col6}
              label={getLocaleMessageById('app.automationService.requestHeadersValue')}
              disabled={disabled}
              value={rh.value}
              onChange={({ value }) =>
                onChange({
                  value: requestHeaders.map((r) => (eq(r, rh) ? { ...r, value: value || '' } : r)),
                })
              }
              readOnly={readonly}
            />
            <div className={clsx(grids.col1, utils.alignItemsCenter)}>
              <IconButton
                onClick={() => {
                  onChange({ value: requestHeaders.filter((r) => !eq(r, rh)) });
                }}
                disabled={disabled || readonly}
              >
                <IconTrash />
              </IconButton>
            </div>
          </div>
        );
      })}
      <div className={clsx(grids.row, utils.mY2)}>
        <Button
          variant={ButtonVariant.Transparent}
          className={utils.mLn4}
          disabled={disabled || readonly}
          onClick={() => {
            onChange({
              value: [
                ...requestHeaders,
                { _key: helpers.getUniqueId('requestHeader-'), name: '', value: '' },
              ],
            });
          }}
        >
          <IconAdd className={utils.mR2} />
          {getLocaleMessageById('app.automationService.requestHeadersAdd')}
        </Button>
      </div>
    </section>
  );
};

export default RequestHeadersEditor;
