import React from 'react';

import { ResizablePanel, ResizerPosition, showModal, utils } from '@product.front/ui-kit';

import {
  FrontTemplateStatus,
  IFrontFolder,
  IFrontTemplate,
} from '@monorepo/common/src/@types/templates';
import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import TemplatePreview from '@monorepo/template-panel/src/components/Preview';
import TemplateEditor from '@monorepo/template-panel/src/components/TemplateEditor';
import TreeContainer from '@monorepo/template-panel/src/components/Templates/All/Container';
import TemplatesHeader from '@monorepo/template-panel/src/components/Templates/Header';
import TemplateListContainer from '@monorepo/template-panel/src/components/Templates/List/TemplateListContainer';
import TemplatesTree from '@monorepo/template-panel/src/components/Templates/Tree';

import { IAdmTabComponent } from '../../../../@types/components';
import { AutoReplyTemplate } from '../../../../@types/generated/administration';
import { getSettings } from '../../../../helpers/appSettings';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  getTabAsideSize,
  setTabAsideSize,
  tabAsideSizeMax,
  tabAsideSizeMin,
} from '../../../../helpers/resize.helper';
import { mapFrontTemplateToDto, mapTemplateToFrontTemplate } from '../../../../mappers/templates';
import {
  createTemplate,
  createUpdateFolder,
  deleteTemplate,
  emptyTemplate,
  getTemplate,
  updateTemplate,
} from '../../../../services/template';
import {
  getAllAutoFolderTemplates,
  selectAutoTemplate,
} from '../../../../store/autoTemplates/autoTemplates.thunk';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import TemplatesToolbarActions from '../Templates/TemplatesToolbarActions';

const TemplatesTab: React.FC<IAdmTabComponent> = ({ name, tab }) => {
  const dispatch = useAdministratorAppDispatch();

  const { folders, loading, error, selectedTemplate } = useAdministratorAppSelector(
    (state) => state.autoTemplates,
  );
  const { addressData } = useAdministratorAppSelector((state) => state.user);

  const [searchText, setSearchText] = React.useState('');
  const [shouldSearchByName, setShouldSearchByName] = React.useState(true);
  const [shouldSearchByText, setShouldSearchByText] = React.useState(true);
  const [shouldSearchByTag, setShouldSearchByTag] = React.useState(true);
  const [statusesToSearch, setStatusesToSearch] = React.useState<FrontTemplateStatus[]>([]);
  const [editFolder, setEditFolder] = React.useState<IFrontFolder | null>(null);
  const [templateInEdit, setTemplateInEdit] = React.useState<IFrontTemplate | null>(null);

  const updateFolderTemplates = React.useCallback(
    () =>
      dispatch(
        getAllAutoFolderTemplates({
          title: shouldSearchByName ? searchText : undefined,
          text: shouldSearchByText ? searchText : undefined,
          keyWord: shouldSearchByTag ? searchText : undefined,
          statuses: statusesToSearch,
        }),
      ),
    [
      dispatch,
      searchText,
      shouldSearchByName,
      shouldSearchByTag,
      shouldSearchByText,
      statusesToSearch,
    ],
  );

  React.useEffect(() => {
    updateFolderTemplates();
  }, [updateFolderTemplates]);

  React.useEffect(() => {
    if (!templateInEdit) return;

    const titleHeader = templateInEdit.id
      ? getLocaleMessageById('templates.editor.header.edit', {
          template: templateInEdit.title,
        })
      : getLocaleMessageById('templates.editor.header.create');

    showModal({
      header: titleHeader,
      children: (close) => (
        <TemplateEditor
          withoutHeader
          style={{
            width: '80vw',
            height: '80vh',
          }}
          foldersData={Object.keys(folders).map((key) => ({
            text: folders[key].title,
            value: folders[key].id,
          }))}
          templateToEdit={templateInEdit}
          onTemplateCreate={(template) =>
            createTemplate(mapFrontTemplateToDto(template, 'AutoReplyAddEdit'))
          }
          onTemplateUpdate={async (template) =>
            updateTemplate(mapFrontTemplateToDto(template, 'AutoReplyAddEdit'))
          }
          onClose={() => {
            setTemplateInEdit(null);
            close?.();
          }}
          onEditorSuccess={async (templateId) => {
            setTemplateInEdit(null);
            close?.();
            await updateFolderTemplates();
            await dispatch(selectAutoTemplate(templateId));
          }}
          canEditCode
          canEditStatus
          uploadUrl={getSettings().productFileServer}
          addressData={addressData}
          canEditVersions
        />
      ),
      flushBody: true,
      canClose: false,
    });
  }, [addressData, dispatch, folders, templateInEdit, updateFolderTemplates]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <TemplatesHeader
          onSearchChange={({
            searchText: text,
            isSearchByNameEnabled,
            isSearchByTextEnabled,
            isSearchByTagEnabled,
            statuses,
          }) => {
            setSearchText(text);
            setShouldSearchByName(isSearchByNameEnabled);
            setShouldSearchByText(isSearchByTextEnabled);
            setShouldSearchByTag(isSearchByTagEnabled);
            setStatusesToSearch(statuses);
          }}
          canFilterByStatus
        />
        <TemplatesToolbarActions
          onAddTemplate={() => {
            setTemplateInEdit({ ...emptyTemplate, folderId: folders['root'].id });
          }}
        />
      </AdmTabHeader>
      <AdmTabBody noPadding flexRow className={utils.w100} loading={loading}>
        <ResizablePanel
          resizerPosition={ResizerPosition.Right}
          min={tabAsideSizeMin}
          max={tabAsideSizeMax}
          onResize={setTabAsideSize(tab)}
          size={getTabAsideSize(tab)}
          className={utils.flexShrink0}
        >
          <TemplateListContainer className={utils.h100}>
            {error && (
              <JumbotronError header={getLocaleMessageById('templates.error')} error={error} />
            )}
            {!error && (
              <TreeContainer>
                <TemplatesTree
                  rootFolder={folders['root']}
                  selectedTemplate={selectedTemplate}
                  searchState={{
                    searchByTitleString: shouldSearchByName ? searchText : '',
                    searchByTagsString: shouldSearchByTag ? searchText : '',
                    searchByDescriptionString: shouldSearchByText ? searchText : '',
                  }}
                  foldersMap={folders}
                  editFolder={null}
                  onTemplateClick={(template) => dispatch(selectAutoTemplate(template.id))}
                  handleFolderSave={async (title) => {
                    if (!editFolder) return;

                    if (!title) {
                      setEditFolder(null);
                      return;
                    }

                    await createUpdateFolder({ ...editFolder, title }, 'AutoReplyTemplateFolder');

                    await updateFolderTemplates();
                    setEditFolder(null);
                  }}
                  onEditTemplateClick={async (template) =>
                    setTemplateInEdit(
                      mapTemplateToFrontTemplate(
                        (await getTemplate(template.id)) as AutoReplyTemplate,
                      ),
                    )
                  }
                  onDeleteTemplateClick={async (template) => {
                    await deleteTemplate(template.id);
                    await updateFolderTemplates();
                  }}
                />
              </TreeContainer>
            )}
          </TemplateListContainer>
        </ResizablePanel>
        <TemplatePreview
          template={selectedTemplate}
          searchTextString={shouldSearchByText ? searchText : ''}
          searchTagString={shouldSearchByTag ? searchText : ''}
          uploadFileUrl={getSettings().productFileServer}
          addressData={addressData}
          shouldShowStatus
          shouldShowVersions
          onEditClick={async (template) =>
            setTemplateInEdit(
              mapTemplateToFrontTemplate((await getTemplate(template.id)) as AutoReplyTemplate),
            )
          }
          onDeleteClick={async (template) => {
            await deleteTemplate(template.id);
            await updateFolderTemplates();
          }}
        />
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default TemplatesTab;
