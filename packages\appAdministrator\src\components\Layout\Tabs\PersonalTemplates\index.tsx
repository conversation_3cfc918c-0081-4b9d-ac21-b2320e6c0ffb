import React from 'react';

import { ResizablePanel, ResizerPosition, utils } from '@product.front/ui-kit';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import TemplatePreview from '@monorepo/template-panel/src/components/Preview';
import TreeContainer from '@monorepo/template-panel/src/components/Templates/All/Container';
import TemplatesHeader from '@monorepo/template-panel/src/components/Templates/Header';
import TemplateListContainer from '@monorepo/template-panel/src/components/Templates/List/TemplateListContainer';
import TemplatesTree from '@monorepo/template-panel/src/components/Templates/Tree';

import { IAdmTabComponent } from '../../../../@types/components';
import { getSettings } from '../../../../helpers/appSettings';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  getTabAsideSize,
  setTabAsideSize,
  tabAsideSizeMax,
  tabAsideSizeMin,
} from '../../../../helpers/resize.helper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import {
  getAllPersonalFolderTemplates,
  selectPersonalTemplate,
} from '../../../../store/personalTemplates/personalTemplates.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';

const PersonalTemplatesTab: React.FC<IAdmTabComponent> = ({ name, tab }) => {
  const dispatch = useAdministratorAppDispatch();

  const { folders, loading, error, selectedTemplate } = useAdministratorAppSelector(
    (state) => state.personalTemplates,
  );
  const { addressData } = useAdministratorAppSelector((state) => state.user);

  const [searchText, setSearchText] = React.useState('');
  const [shouldSearchByName, setShouldSearchByName] = React.useState(true);
  const [shouldSearchByText, setShouldSearchByText] = React.useState(true);
  const [shouldSearchByTag, setShouldSearchByTag] = React.useState(true);

  React.useEffect(() => {
    dispatch(
      getAllPersonalFolderTemplates({
        title: shouldSearchByName ? searchText : undefined,
        text: shouldSearchByText ? searchText : undefined,
        keyWord: shouldSearchByTag ? searchText : undefined,
      }),
    );
  }, [searchText, shouldSearchByName, shouldSearchByText, shouldSearchByTag, dispatch]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <TemplatesHeader
          onSearchChange={({
            searchText: text,
            isSearchByNameEnabled,
            isSearchByTextEnabled,
            isSearchByTagEnabled,
          }) => {
            setSearchText(text);
            setShouldSearchByName(isSearchByNameEnabled);
            setShouldSearchByText(isSearchByTextEnabled);
            setShouldSearchByTag(isSearchByTagEnabled);
          }}
        />
      </AdmTabHeader>
      <AdmTabBody noPadding flexRow className={utils.w100} loading={loading}>
        <ResizablePanel
          resizerPosition={ResizerPosition.Right}
          min={tabAsideSizeMin}
          max={tabAsideSizeMax}
          onResize={setTabAsideSize(tab)}
          size={getTabAsideSize(tab)}
          className={utils.flexShrink0}
        >
          <TemplateListContainer className={utils.h100}>
            {error && (
              <JumbotronError
                header={getLocaleMessageById('personalTemplates.error')}
                error={error}
              />
            )}
            {!error && (
              <TreeContainer>
                <TemplatesTree
                  rootFolder={folders['root']}
                  selectedTemplate={selectedTemplate}
                  searchState={{
                    searchByTitleString: shouldSearchByName ? searchText : '',
                    searchByTagsString: shouldSearchByTag ? searchText : '',
                    searchByDescriptionString: shouldSearchByText ? searchText : '',
                  }}
                  foldersMap={folders}
                  editFolder={null}
                  onTemplateClick={(template) => dispatch(selectPersonalTemplate(template.id))}
                  startUnfoldLevel={1}
                />
              </TreeContainer>
            )}
          </TemplateListContainer>
        </ResizablePanel>
        <TemplatePreview
          template={selectedTemplate}
          searchTextString={shouldSearchByText ? searchText : ''}
          searchTagString={shouldSearchByTag ? searchText : ''}
          uploadFileUrl={getSettings().productFileServer}
          addressData={addressData}
        />
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default PersonalTemplatesTab;
