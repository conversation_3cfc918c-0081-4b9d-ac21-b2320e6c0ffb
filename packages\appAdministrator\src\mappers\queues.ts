import {
  AddQueue,
  AttributeModel,
  AttributeType,
  ButtonBot,
  DataType,
  IntelligentBot,
  KpiParameterMeasurement,
  QueueInfo,
  QueueParametersView,
  QueueView,
  RatingBot,
  RightPartType,
} from '../@types/generated/administration';
import {
  IFrontAttributeModel,
  IFrontAttributeType,
  IFrontAutoHandler,
  IFrontBot,
  IFrontDataType,
  IFrontQueueKpiParameter,
  IFrontRightPartType,
  IFrontRoutingAttribute,
} from '../@types/parameters';
import {
  IAutoHandlerType,
  IFrontDistributionRule,
  IFrontKpiUnit,
  IFrontQueue,
  IFrontQueueBase,
  IFrontQueueBot,
} from '../@types/queue';

export const mapQueueDtoToFront = (queue: QueueInfo): IFrontQueueBase => ({
  id: queue.id,
  name: queue.name,
  description: queue.description ?? '',
  weight: queue.weight,
  operatorsNumber: queue.operatorsCount,
  isDefault: queue.isDefault ?? false,
  isService: queue.isService ?? false,
});

export const mapQueueKpiUnitDtoToFront = (unit: string): IFrontKpiUnit => {
  const unitMap: Record<string, IFrontKpiUnit> = {
    Sec: IFrontKpiUnit.Seconds,
    Min: IFrontKpiUnit.Minutes,
    Hours: IFrontKpiUnit.Hours,
    Days: IFrontKpiUnit.Days,
  };

  return unitMap[unit] ?? IFrontKpiUnit.Seconds;
};

export const mapQueueKpiParameterMeasurementDtoToFront = (
  unit: KpiParameterMeasurement,
): IFrontKpiUnit => {
  const unitMap: Record<KpiParameterMeasurement, IFrontKpiUnit> = {
    [KpiParameterMeasurement.None]: IFrontKpiUnit.None,
    [KpiParameterMeasurement.Sec]: IFrontKpiUnit.Seconds,
    [KpiParameterMeasurement.Min]: IFrontKpiUnit.Minutes,
    [KpiParameterMeasurement.Hours]: IFrontKpiUnit.Hours,
    [KpiParameterMeasurement.Days]: IFrontKpiUnit.Days,
  };

  return unitMap[unit] ?? IFrontKpiUnit.Seconds;
};

export const mapBotDtoToFront = (bot: ButtonBot | IntelligentBot | RatingBot): IFrontQueueBot =>
  bot.enabled
    ? {
        enabled: true,
        code: bot.botCode ?? '',
      }
    : {
        enabled: false,
        code: null,
      };

export const mapFullQueueDtoToFront = (queue: QueueView): IFrontQueue => ({
  id: queue.id,
  name: queue.name,
  description: queue.description ?? '',
  weight: queue.weight,
  division: queue.division ?? '',
  isDefault: queue.isDefault ?? false,
  isService: queue.isService ?? false,
  operators:
    queue.operators?.map((operator) => ({ id: operator.id, priority: operator.priority })) ?? [],
  operatorGroups: queue.operatorGroups ?? [],
  distributionRuleType: queue.workSelectorType,
  kpiParameters:
    queue.kpiParameters?.map((kpi) => ({
      unit: mapQueueKpiParameterMeasurementDtoToFront(
        kpi.measurement ?? KpiParameterMeasurement.None,
      ),
      transferCallDestination: kpi.transferCallDestination ?? null,
      code: kpi.code,
      alarmThreshold: kpi.alarmThreshold,
      warningThreshold: kpi.warningThreshold ?? null,
    })) ?? [],
  buttonBot: mapBotDtoToFront(queue.buttonBot),
  intelligentBot: mapBotDtoToFront(queue.intelligentBot),
  ratingBot: mapBotDtoToFront(queue.ratingBot),
  routingRules:
    queue.routingRuleSets?.map((routing) => ({
      id: routing.id,
      rules: routing.rules.map((rule) => ({
        attributeId: rule.attributeId,
        comparisonRule: rule.comparisonRule,
        value: rule.value,
      })),
    })) ?? [],
  autoHandlers:
    queue.autoHandlers?.map((autoHandler) => {
      switch (autoHandler.kind) {
        case 'PredictedWaitTimeAutoHandler':
          return {
            type: IAutoHandlerType.PredictedWaitTimeAutoHandler,
            code: autoHandler.code,
            min: autoHandler.minWaitTimeSeconds,
            max: autoHandler.maxWaitTimeSeconds,
            templateBelowMin: autoHandler.templateIdBelowMin ?? null,
            templateWithinRange: autoHandler.templateIdWithinRange ?? null,
            templateAboveMax: autoHandler.templateIdAboveMax ?? null,
          };
        case 'TemplateAutoHandler':
          return {
            type: IAutoHandlerType.TemplateAutoHandler,
            code: autoHandler.code,
            templateId: autoHandler.templateId,
          };
        default:
          return { type: IAutoHandlerType.EmptyAutoHandler, code: autoHandler.code };
      }
    }) ?? [],
});

export const mapQueueParametersToFront = (
  parameters: QueueParametersView,
): {
  divisions: { id: string; name: string }[];
  routingAttributes: IFrontRoutingAttribute[];
  distributionRules: IFrontDistributionRule[];
  buttonBots: IFrontBot[];
  intelligentBots: IFrontBot[];
  ratingBots: IFrontBot[];
  kpiParameters: IFrontQueueKpiParameter[];
  autoHandlers: IFrontAutoHandler[];
} => ({
  divisions: parameters.divisions.map((division) => ({
    id: division.id,
    name: division.name,
  })),

  routingAttributes:
    parameters.routingAttributes?.map((value) => ({
      id: value.id,
      displayName: value.displayName,
      model: {
        [AttributeModel.Client]: IFrontAttributeModel.Client,
        [AttributeModel.Request]: IFrontAttributeModel.Request,
      }[value.model],
      attributeType: {
        [AttributeType.Base]: IFrontAttributeType.Client,
        [AttributeType.Custom]: IFrontAttributeType.Request,
      }[value.attributeType],
      code: value.code,
      dataType: {
        [DataType.Date]: IFrontDataType.Date,
        [DataType.Number]: IFrontDataType.Number,
        [DataType.String]: IFrontDataType.String,
      }[value.dataType],
      rightPartType: {
        [RightPartType.List]: IFrontRightPartType.List,
        [RightPartType.Text]: IFrontRightPartType.Text,
        [RightPartType.MiltiselectList]: IFrontRightPartType.MiltiselectList,
      }[value.rightPartType],
      options:
        value.options?.map((option) => ({
          name: option.name,
          value: option.value,
        })) ?? [],
      comparisonRules:
        value.comparisonRules?.map((rule) => ({
          name: rule.name,
          code: rule.code,
        })) ?? [],
    })) ?? [],

  distributionRules:
    parameters.distributionRules?.map((value) => ({
      name: value.name,
      description: value.description ?? '',
      ruleType: value.ruleType,
      isDefault: value.isDefault,
    })) ?? [],

  buttonBots:
    parameters.buttonBots?.map((bot) => ({
      name: bot.name,
      code: bot.code,
    })) ?? [],
  intelligentBots:
    parameters.intelligentBots?.map((bot) => ({
      name: bot.name,
      code: bot.code,
    })) ?? [],

  ratingBots:
    parameters.ratingBots?.map((bot) => ({
      name: bot.name,
      code: bot.code,
    })) ?? [],

  kpiParameters:
    parameters.kpiParameters?.map((value) => ({
      displayName: value.displayName,
      code: value.code,
      isInteger: value.isInteger,
      alarmExceedsWarning: value.alarmExceedsWarning,
      defaultAlarmValue: value.defaultAlarmValue ?? null,
      alarmMinValue: value.alarmMinValue ?? null,
      alarmMaxValue: value.alarmMaxValue ?? null,
      defaultWarningValue: value.defaultWarningValue ?? null,
      warningMinValue: value.warningMinValue ?? null,
      warningMaxValue: value.warningMaxValue ?? null,
      units: value.units?.map(mapQueueKpiUnitDtoToFront) ?? [],
      isWarningAvailable: value.isWarningAvailable ?? false,
      canExceed: value.isTransferCallDestinationAvailable ?? false,
    })) ?? [],

  autoHandlers:
    parameters.autoHandlers?.map((value) => ({
      name: value.name,
      code: value.code,
      isPvoo: value.isPVOO,
    })) ?? [],
});

export const mapQueueKpiParameterMeasurementFrontToDto = (
  unit: IFrontKpiUnit,
): KpiParameterMeasurement => {
  const unitMap: Record<IFrontKpiUnit, KpiParameterMeasurement> = {
    [IFrontKpiUnit.None]: KpiParameterMeasurement.None,
    [IFrontKpiUnit.Seconds]: KpiParameterMeasurement.Sec,
    [IFrontKpiUnit.Minutes]: KpiParameterMeasurement.Min,
    [IFrontKpiUnit.Hours]: KpiParameterMeasurement.Hours,
    [IFrontKpiUnit.Days]: KpiParameterMeasurement.Days,
  };

  return unitMap[unit] ?? IFrontKpiUnit.Seconds;
};

export const mapFullQueueFrontToNewDto = (queue: IFrontQueue): AddQueue => ({
  name: queue.name,
  description: queue.description || null,
  divisionId: queue.division,
  weight: queue.weight,
  operators:
    queue.operators?.map((operator) => ({ id: operator.id, priority: operator.priority })) ?? [],
  operatorGroups: queue.operatorGroups,
  routingRuleSets: queue.routingRules?.map((routing) => ({
    id: routing.id,
    rules: routing.rules.map((rule) => ({
      attributeId: rule.attributeId,
      comparisonRule: rule.comparisonRule,
      value: rule.value,
    })),
  })),
  distributionRuleType: queue.distributionRuleType,
  autoHandlers: queue.autoHandlers.map((autoHandler) => {
    switch (autoHandler.type) {
      case IAutoHandlerType.TemplateAutoHandler:
        return {
          kind: 'TemplateAutoHandler',
          code: autoHandler.code,
          templateId: autoHandler.templateId,
        };
      case IAutoHandlerType.PredictedWaitTimeAutoHandler:
        return {
          kind: 'PredictedWaitTimeAutoHandler',
          code: autoHandler.code,
          minWaitTimeSeconds: autoHandler.min,
          maxWaitTimeSeconds: autoHandler.max,
          templateIdBelowMin: autoHandler.templateBelowMin,
          templateIdWithinRange: autoHandler.templateWithinRange,
          templateIdAboveMax: autoHandler.templateAboveMax,
        };
      default:
        return {
          kind: 'EmptyAutoHandler',
          code: autoHandler.code,
        };
    }
  }),
  intelligentBot: {
    enabled: queue.intelligentBot.enabled,
    botCode: queue.intelligentBot.code || null,
  },
  buttonBot: {
    enabled: queue.buttonBot.enabled,
    botCode: queue.buttonBot.code || null,
  },
  ratingBot: {
    enabled: queue.ratingBot.enabled,
    botCode: queue.ratingBot.code || null,
  },
  kpiParameters:
    queue.kpiParameters?.map((kpi) => ({
      code: kpi.code,
      alarmThreshold: kpi.alarmThreshold,
      warningThreshold: kpi.warningThreshold,
      measurement: mapQueueKpiParameterMeasurementFrontToDto(kpi.unit),
      transferCallDestination: kpi.transferCallDestination,
    })) ?? [],
});
