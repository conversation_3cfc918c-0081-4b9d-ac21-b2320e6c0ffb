import { createIntl, createIntlCache } from 'react-intl';

import messages_en from '../_locales/en.json';
import messages_kk from '../_locales/kk.json';
import messages_ru from '../_locales/ru.json';
import messages_uz from '../_locales/uz.json';

export const getLang = () => {
  return navigator.language.split(/[-_]/)[0] || 'ru';
};

const messages: { [twoCharsLocale: string]: { [id: string]: string } } = {
  ru: messages_ru,
  en: messages_en,
  kk: messages_kk,
  uz: messages_uz,
};

export const getMessagesForLang = (lang = 'ru') => {
  return messages[lang];
};

const cache = createIntlCache();
export const localeIntl = createIntl(
  { locale: getLang(), messages: getMessagesForLang(getLang()) },
  cache,
);

export const getLocaleMessageById = (id: string, values?: any) => {
  return localeIntl.formatMessage({ id }, values);
};
