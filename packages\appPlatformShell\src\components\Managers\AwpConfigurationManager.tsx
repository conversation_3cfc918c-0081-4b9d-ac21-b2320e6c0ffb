import { Guid } from 'guid-typescript';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';
import { ProxyBase } from '@monorepo/common/src/platform/utils';

export class AwpConfigurationManager extends ProxyBase implements AWP.IAwpConfigurationManager {
  constructor(baseUrl: string) {
    super(baseUrl, 'AwpConfiguration/');
  }

  public async getAvailableProfiles(): Promise<AWP.ProfileData[]> {
    return await this.getDataFromAction('GetAvailableProfiles');
  }

  public async getAvailableProfilesWithAllEssentials(): Promise<AWP.ProfilesAndEssentialsData> {
    return await this.getDataFromAction('GetAvailableProfilesWithAllEssentials');
  }

  public async getModules(
    clientTypes: AWP.AwpClientTypes,
    userRoleId: Guid,
    serviceAreaId: Guid,
  ): Promise<AWP.ModuleInfo[]> {
    return await this.getDataFromAction('GetModules', {
      clientTypes: clientTypes.toString(),
      userRoleId: userRoleId.toString(),
      serviceAreaId: serviceAreaId.toString(),
    });
  }

  public async getHostedApplications(
    clientTypes: AWP.AwpClientTypes,
    userRoleId: Guid,
    serviceAreaId: Guid,
  ): Promise<AWP.AppInfo[]> {
    return await this.getDataFromAction('GetHostedApplications', {
      clientTypes: clientTypes.toString(),
      userRoleId: userRoleId.toString(),
      serviceAreaId: serviceAreaId.toString(),
    });
  }

  public async getWorkflows(
    clientTypes: AWP.AwpClientTypes,
    userRoleId: Guid,
  ): Promise<AWP.Workflow[]> {
    return await this.getDataFromAction('GetWorkflows', {
      clientTypes: clientTypes.toString(),
      userRoleId: userRoleId.toString(),
    });
  }
}
