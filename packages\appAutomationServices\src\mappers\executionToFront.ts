import {
  ExecuteResultStatus,
  IExecuteFileResult,
  IExecuteResult,
} from '../@types/automationService.types';
import {
  ExecuteResponseBaseServiceDto,
  ExecuteResponseFileDto,
  ExecuteStatuses,
  ResponsePropsDto,
  ServiceType,
} from '../@types/generated/automationServices';
import { runJSScriptCode } from '../helpers/scriptCodeJS/scriptCodeJSHelper';

const mapResultStatusToFront = (status?: ExecuteStatuses) => {
  if (!status) return ExecuteResultStatus.Fail;

  const statusMap: Record<ExecuteStatuses, ExecuteResultStatus> = {
    [ExecuteStatuses.Success]: ExecuteResultStatus.Success,
    [ExecuteStatuses.Fail]: ExecuteResultStatus.Fail,
  };

  return statusMap[status];
};

const mapFileToFront = (outputParameters: Record<string, string>): IExecuteFileResult | null => {
  const valueFileParam = outputParameters['file'];
  if (!valueFileParam) {
    return null;
  }

  const responseFile = (JSON.parse(valueFileParam) as ExecuteResponseFileDto) ?? null;

  if (!responseFile || !responseFile.Content) {
    return null;
  }

  return {
    content: Uint8Array.from(atob(responseFile.Content), (c) => c.charCodeAt(0)).buffer,
    fileName: responseFile.FileName,
    contentType: responseFile.ContentType,
  };
};

export const mapServiceExecutionResponseToFront = async (
  resultDto: ExecuteResponseBaseServiceDto &
    ResponsePropsDto & { httpStatusCode: number; contentLength: number },
): Promise<IExecuteResult> => {
  let body = resultDto.resultMessage ?? '';
  let outputParameters = resultDto.outputParameters ?? {};
  let contentType = resultDto.contentType ?? '';
  let executingTime = resultDto.executingTime ?? 0;
  let status = mapResultStatusToFront(resultDto.status);
  let contentLength: number | null = resultDto.contentLength ?? 0;
  let httpStatusCode: number | null = resultDto.httpStatusCode ?? 0;
  const file: IExecuteFileResult | null = mapFileToFront(outputParameters);

  if (resultDto.serviceType === ServiceType.Script) {
    const startTime = performance.now();
    try {
      const jsonResult = await runJSScriptCode(resultDto.resultMessage ?? '');
      body = JSON.stringify(jsonResult);
      outputParameters = jsonResult;
      contentType = 'application/json';
      status = ExecuteResultStatus.Success;
    } catch (error) {
      console.error('Something went wrong', error);
      body = error.message;
      outputParameters = {};
      contentType = 'Text';
      status = ExecuteResultStatus.Fail;
    }
    const endTime = performance.now();
    executingTime = Math.ceil(endTime - startTime);
    contentLength = null;
    httpStatusCode = null;
  }

  return {
    status,
    executingTime,
    error: resultDto.error
      ? {
          message: resultDto.error?.message ?? '',
          originalText: resultDto.error?.originalText ?? '',
        }
      : null,
    body,
    contentType,
    httpStatusCode,
    contentLength,
    outputParameters,
    file,
  };
};
