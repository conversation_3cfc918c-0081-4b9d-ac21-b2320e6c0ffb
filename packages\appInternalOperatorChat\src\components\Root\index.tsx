import React from 'react';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { getLocaleMessageById as getCommonLocaleMessageById } from '@monorepo/common/src/helpers/localeHelper';
import { setProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';
import {
  AppComponentProps,
  IHostedAppsManager,
  INotificationsManager,
} from '@monorepo/common/src/platform/awp-web-interfaces';

import signalRClient from '../../clients/signalRClient';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import { setPlatformApp } from '../../managers/platformAppManager';
import { setPlatformEventManager } from '../../managers/platformEventManager';
import { setPlatformHostedAppManager } from '../../managers/platformHostedAppManager';
import { setPlatformNotificationManager } from '../../managers/platformNotificationManager';
import { getAppSettings } from '../../services/appSettings.service';
import FullScreenLoader from '../FullScreenLoader';

const Root = (props: AppComponentProps) => {
  const [settingsError, setSettingsError] = React.useState<Error>();
  const [isLoading, setIsLoading] = React.useState(false);
  const [ApplicationComponent, setApplicationComponent] = React.useState<React.ReactElement | null>(
    null,
  );

  React.useEffect(() => {
    const initialize = async () => {
      setIsLoading(true);
      if (props.shell) {
        setPlatformHostedAppManager(
          props.shell.serviceResolver.resolve<IHostedAppsManager>('IHostedAppsManager'),
        );
        setPlatformNotificationManager(
          props.shell.serviceResolver.resolve<INotificationsManager>('INotificationsManager'),
        );
      }

      await setProductServicesManager({ shell: props.shell });

      try {
        const settingsObj = (await getAppSettings(props.shell)) as any;

        signalRClient.init({
          SignalRUrl: settingsObj.SignalRUrl,
        });
        const { default: DynamicImportedComponent } = await import(
          /* webpackChunkName: "internal-chat" */ '../InternalChatApp'
        );

        setApplicationComponent(<DynamicImportedComponent settings={settingsObj} />);
      } catch (e) {
        setSettingsError(e);
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, [props.shell]);

  React.useEffect(() => {
    props.shell?.eventManager && setPlatformEventManager(props.shell.eventManager);
    props.name && setPlatformApp({ name: props.name });
  }, [props]);

  if (settingsError) {
    return (
      <AlertError
        error={settingsError}
        header={getLocaleMessageById('app.error.loadingSettings')}
      />
    );
  }

  if (isLoading) {
    return (
      <FullScreenLoader>
        {getCommonLocaleMessageById('app.common.configurationLoading')}...
      </FullScreenLoader>
    );
  }

  if (ApplicationComponent === null) {
    return (
      <FullScreenLoader>{getCommonLocaleMessageById('app.common.loading')}...</FullScreenLoader>
    );
  }

  return ApplicationComponent;
};

export default Root;
