import React, { FormEvent } from 'react';

import clsx from 'clsx';

import { utils } from '@product.front/ui-kit';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { FrontStepType, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import {
  useDialogScriptsAppDispatch,
  useDialogScriptsAppSelector,
} from '../../../../../../store/hooks';
import {
  deleteScriptStep,
  updateScriptStep,
} from '../../../../../../store/oneScript/oneScript.slice';
import RatingStep from '../../../../ScriptModal/RatingStep';
import Router from '../../../../ScriptModal/Router';
import Scenario from '../../../../ScriptModal/Scenario';
import Service from '../../../../ScriptModal/Service';
import Step from '../../../../ScriptModal/Step';
import Subscript from '../../../../ScriptModal/Subscript';
import Terminal from '../../../../ScriptModal/Terminal';

interface INodeEditorProps {
  step: IFrontStep;
  onClose(): void;
  ref?: React.RefObject<HTMLFormElement | null>;
}

const VisualStepEditor: React.FC<INodeEditorProps> = ({ step, onClose, ref }) => {
  const dispatch = useDialogScriptsAppDispatch();
  const { script } = useDialogScriptsAppSelector((store) => store.oneScript);

  const handleSave = (e: FormEvent) => {
    e.preventDefault();

    onClose();
  };

  const handleDelete = () => {
    dispatch(deleteScriptStep(step.code));
    onClose();
  };

  if (!script) {
    return null;
  }

  return (
    <form
      ref={ref}
      className={clsx(utils.dFlex, utils.flexColumn, utils.gap2, utils.h100)}
      onSubmit={handleSave}
    >
      <div
        className={clsx(
          utils.flexGrow1,
          utils.overflowYAuto,
          utils.flexBasis0,
          utils.scrollbar,
          utils.p4,
        )}
      >
        {step.type === FrontStepType.Step && (
          <Step
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            steps={script.steps}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
        {step.type === FrontStepType.Router && (
          <Router
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            steps={script.steps}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
        {step.type === FrontStepType.Service && (
          <Service
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            steps={script.steps}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
        {step.type === FrontStepType.Subscript && (
          <Subscript
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            steps={script.steps}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
        {step.type === FrontStepType.Scenario && (
          <Scenario
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
        {step.type === FrontStepType.Terminal && (
          <Terminal
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            steps={script.steps}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
        {step.type === FrontStepType.Rating && (
          <RatingStep
            flush={true}
            step={script.steps.find((s) => step.code === s.code)!}
            steps={script.steps}
            number={script.steps.findIndex((s) => step.code === s.code) + 1}
            onlyStep={false}
            disabled={script.status === ScriptStatus.Archive}
            requiredForActive={script.status === ScriptStatus.Active}
            onDelete={handleDelete}
            onChange={(updatedStep) => {
              dispatch(updateScriptStep({ ...updatedStep }));
            }}
          />
        )}
      </div>
    </form>
  );
};

export default VisualStepEditor;
