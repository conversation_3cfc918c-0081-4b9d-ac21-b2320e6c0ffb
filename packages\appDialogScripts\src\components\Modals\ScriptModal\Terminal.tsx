import React from 'react';

import clsx from 'clsx';

import { CanClearBehavior, Input, Select, Text, TextVariant, utils } from '@product.front/ui-kit';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getAllVariablesFromScriptSteps } from '../../../helpers/scriptVariablesHelpers';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import { useDialogScriptsAppSelector } from '../../../store/hooks';
import InputErrorMessage from '../../InputErrorMessage';

import SubjectSelect from './components/SubjectSelect';
import VariablesToSources from './components/VariablesToSources';
import DeleteStepButton from './DeleteStepButton';

import styles from './styles.module.scss';

interface ITerminalProps {
  step: IFrontStep;
  steps: IFrontStep[];
  number: number;
  onlyStep: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
  disabled: boolean;
  requiredForActive: boolean;
}

const Terminal = ({
  step,
  steps,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: ITerminalProps) => {
  const [name, setName] = React.useState(step.name);
  const { terminalActions } = useDialogScriptsAppSelector((state) => state.externalData);

  const variables: Record<string, IFrontStep['variableName']> =
    getAllVariablesFromScriptSteps(steps);

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,

        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {step.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        {!onlyStep && (
          <DeleteStepButton needConfirm={!!name.length} disabled={disabled} onDelete={onDelete} />
        )}
      </div>
      <Input
        label={getLocaleMessageById('app.modals.form.stepName')}
        placeholder={getLocaleMessageById('app.editor.noName')}
        value={name}
        autoFocus={!name.length}
        onChange={({ value }) => setName(value || '')}
        onBlur={() => onChange({ ...step, name })}
        canClearBehavior={CanClearBehavior.Value}
        required={requiredForActive}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.name}
        message={<InputErrorMessage>{step.invalidReasons?.name}</InputErrorMessage>}
      />

      <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
        {getLocaleMessageById('app.editor.step.terminalActionHeader')}
      </Text>
      <Select
        placeholder={getLocaleMessageById('app.editor.step.terminalAction')}
        wrapperClassName={utils.flexGrow6}
        data={terminalActions.map((ta) => ({ value: ta.actionCode, text: ta.label }))}
        required
        value={step.terminalAction}
        onChange={({ value }) => onChange({ ...step, terminalAction: value ?? '' })}
        disabled={disabled || !terminalActions.length}
        withDebounce
        isInvalid={!!step.invalidReasons?.terminalAction}
        message={<InputErrorMessage>{step.invalidReasons?.terminalAction}</InputErrorMessage>}
      />

      <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
        {getLocaleMessageById('app.editor.step.terminalSubjectHeader')}
      </Text>
      <SubjectSelect
        value={step.terminalSubject}
        onChange={({ value }) => onChange({ ...step, terminalSubject: value })}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.terminalSubject}
        message={<InputErrorMessage>{step.invalidReasons?.terminalSubject}</InputErrorMessage>}
      />

      <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
        {getLocaleMessageById('app.editor.step.terminalUpdateDataHeader')}
      </Text>
      <VariablesToSources
        value={step.terminalUpdatesMap ?? { _: '' }}
        onChange={(map) => onChange({ ...step, terminalUpdatesMap: map })}
        variables={variables}
      />
    </div>
  );
};

export default Terminal;
