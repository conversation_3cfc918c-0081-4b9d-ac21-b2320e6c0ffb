import React from 'react';

import clsx from 'clsx';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IIdNameScript } from '../../@types/script';
import { getLocaleMessageById } from '../localeHelper';
import { getDependentScriptNamesSeparatedSemicolon } from '../scriptHelpers';

import styles from './styles.module.scss';

export const showChangeSriptStatusConfirmModal = (
  script: { dependentScripts: IIdNameScript[] },
  showTargetModal: () => void,
  onCancel?: () => void,
) => {
  if (script.dependentScripts?.length > 0) {
    let modalHeaderMessage = '';
    if (script.dependentScripts.length === 1) {
      modalHeaderMessage = getLocaleMessageById('app.modals.dependentScript.header', {
        scriptName: script.dependentScripts[0].name,
      });
    } else {
      modalHeaderMessage = getLocaleMessageById('app.modals.dependentScripts.header', {
        scriptNames: getDependentScriptNamesSeparatedSemicolon(script.dependentScripts),
      });
    }

    showConfirmModal({
      header: (
        <div className={clsx(styles.availableDependentScriptsTextWrapper)}>
          {modalHeaderMessage}
        </div>
      ),
      onConfirm: showTargetModal,
      onCancel: onCancel,
      confirmText: getLocaleMessageById('app.common.continue'),
    });

    return;
  }

  showTargetModal();
};
