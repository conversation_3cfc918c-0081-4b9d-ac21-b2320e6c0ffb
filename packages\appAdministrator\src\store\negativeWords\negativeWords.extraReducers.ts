import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { INegativeWordsStore } from './negativeWords.slice';
import { getAllNegativeWords } from './negativeWords.thunk';

const getNegativeWordsReducers = (builder: ActionReducerMapBuilder<INegativeWordsStore>) =>
  builder
    .addCase(getAllNegativeWords.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllNegativeWords.fulfilled, (state, action) => {
      state.negativeWords = action.payload;
      state.loading = false;
    })
    .addCase(getAllNegativeWords.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<INegativeWordsStore>) => {
  getNegativeWordsReducers(builder);

  return builder;
};
