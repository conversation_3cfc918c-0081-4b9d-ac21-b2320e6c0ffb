import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import {
  filterFoldersAndFilesBySearchString,
  getFolderByPath,
  isFolder,
} from '../../helpers/files';

import { IFilesStore } from './files.slice';
import { getFoldersAndFiles } from './files.thunk';

const getFilesReducers = (builder: ActionReducerMapBuilder<IFilesStore>) =>
  builder
    .addCase(getFoldersAndFiles.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getFoldersAndFiles.fulfilled, (state, action) => {
      state.foldersAndFiles = state.searchString.length
        ? filterFoldersAndFilesBySearchString(action.payload, state.searchString)
        : action.payload;

      if (state.displayedFolder) {
        const targetItem = getFolderByPath(state.foldersAndFiles, state.displayedFolder.path);
        if (targetItem) {
          state.displayedFolder = targetItem;
        } else {
          const targetItem2 = getFolderByPath(state.foldersAndFiles, state.displayedFolder.parent);
          state.displayedFolder = targetItem2;
        }
      }

      if (state.selectedItem && state.displayedFolder) {
        state.selectedItem = state.displayedFolder.items.find(
          (item) => item.path === state.selectedItem?.path,
        );
      }

      state.displayedFolder = state.displayedFolder ?? state.foldersAndFiles.filter(isFolder)?.[0];
      state.loading = false;
    })
    .addCase(getFoldersAndFiles.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

export default (builder: ActionReducerMapBuilder<IFilesStore>) => {
  getFilesReducers(builder);

  return builder;
};
