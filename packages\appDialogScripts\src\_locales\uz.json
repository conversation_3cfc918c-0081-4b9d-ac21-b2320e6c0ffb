{"app.common.apply": "Q<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.common.cancel": "Bekor qilish", "app.common.date.from": "Bilan", "app.common.date.to": "Yoqilgan", "app.common.yes": "Ha", "app.common.save": "<PERSON><PERSON><PERSON>", "app.common.create": "<PERSON><PERSON><PERSON><PERSON>", "app.common.close": "yaqin", "app.common.default": "<PERSON><PERSON>", "app.common.continue": "Continue", "app.header.title": "Dialog skriptlari", "app.header.button.create": "<PERSON><PERSON><PERSON><PERSON>", "app.table.empty": "<PERSON><PERSON> qanday yozuv topilmadi", "app.table.header.code": "<PERSON><PERSON>", "app.table.header.name": "<PERSON><PERSON>", "app.table.header.priority": "Us<PERSON><PERSON>lik", "app.table.header.status": "<PERSON><PERSON>", "app.table.header.createdBy": "<PERSON><PERSON><PERSON><PERSON>", "app.table.header.changedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.table.header.changedDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sanasi", "app.table.header.activeFrom": "dan amal qiladi", "app.table.header.activeTo": "uchun amal qiladi", "app.table.header.createdDate": "yarat<PERSON>gan sana", "app.table.header.recordsNumber": "Topilgan yozuvlar soni", "app.table.header.dependentScripts": "Using to", "app.modals.form.name": "Nomi*", "app.modals.form.code": "<PERSON><PERSON>*", "app.modals.form.description": "<PERSON><PERSON><PERSON><PERSON>", "app.modals.form.status": "Holat*", "app.modals.form.activeFrom": "<PERSON><PERSON><PERSON><PERSON>", "app.modals.form.activeTo": "<PERSON><PERSON><PERSON>", "app.modals.form.steps": "Qadamlar", "app.modals.form.step": "<PERSON><PERSON><PERSON> r<PERSON>.", "app.modals.form.addStep": "<PERSON><PERSON><PERSON> qo'shing", "app.modals.form.stepName": "<PERSON><PERSON><PERSON><PERSON> nomi", "app.modals.form.stepDescription": "Operator matni", "app.modals.form.displayTextVariant": "Display text", "app.modals.form.keyValueButton": "Button Value", "app.modals.form.choice.addVariant": "Add other variant", "app.modals.form.stepPromptUrl": "Kengaytma nuq<PERSON>i", "app.modals.form.answerText": "<PERSON><PERSON><PERSON>", "app.modals.form.answerTemplate": "<PERSON><PERSON>", "app.modals.form.transfer": "O'tish", "app.modals.form.answerDisplayType": "<PERSON><PERSON><PERSON> k<PERSON>'r<PERSON>", "app.modals.form.variableName": "<PERSON>'<PERSON>garu<PERSON><PERSON> nomi", "app.modals.form.variableArrayName": "Variable-array", "app.modals.form.variableCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kod", "app.modals.form.addAnswer": "<PERSON><PERSON><PERSON><PERSON> qo'shing", "app.modals.form.addTextTemplate": "Добавить шаблон", "app.modals.closeScript.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> yo<PERSON><PERSON>?", "app.modals.closeScript.text": "<PERSON><PERSON> kiri<PERSON> o'<PERSON> yo'qoladi.", "app.modals.deleteStep.header": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>?", "app.modals.deleteStep.text": "<PERSON><PERSON> qadamni olib tashlash zan<PERSON><PERSON>i buzadi", "app.modals.deleteRelation.header": "<PERSON><PERSON><PERSON><PERSON>?", "app.modals.statistics.totalAnswers": "<PERSON><PERSON>", "app.scriptStatus.draft": "Q<PERSON><PERSON>", "app.scriptStatus.published": "nashr etilgan", "app.scriptStatus.archived": "Arxiv", "app.scriptStatus.notAvailable": "<PERSON><PERSON><PERSON><PERSON>as", "app.answerDisplayType.radio": "R<PERSON>'yxatl<PERSON><PERSON> biri", "app.answerDisplayType.select": "Ochiladigan ro'yxat", "app.answerDisplayType.free": "<PERSON><PERSON> (string)", "app.answerDisplayType.button": "<PERSON><PERSON><PERSON>", "app.answerDisplayType.choice": "Choice", "app.answerDisplayType.file": "<PERSON>а<PERSON><PERSON>", "app.answerDisplayType.template": "Mat<PERSON> (namuna)", "app.answerDisplayType.none": "<PERSON><PERSON><PERSON> yo'q", "app.stepTransfer.default": "<PERSON><PERSON><PERSON> qadam", "app.modals.form.answerDisplayTypeNotNone": "Javoblar bilan", "app.modals.timeout": "Ta<PERSON>r", "app.modals.timeoutUnit": "(daq.)", "app.modals.limit": "Cheklash", "app.modals.form.freeAnswer": "<PERSON><PERSON><PERSON> \"O'<PERSON> varianti\": <PERSON><PERSON> s<PERSON> javob", "app.stepTransfer.end": "Tugallash", "modal.error.message": "So<PERSON>rovni baja<PERSON>h xatosi", "app.script.saveAndPublish": "<PERSON><PERSON><PERSON> va nashr qilish", "app.script.publish": "<PERSON><PERSON>", "app.script.saveAndArchive": "Saqlash va arxivlash", "app.script.archive": "Arxiv", "app.tooltip.archive": "Arxiv", "app.tooltip.notAvailableAction": "<PERSON><PERSON><PERSON> o<PERSON>b tash<PERSON>", "app.modals.notAvailableHeader": "“{scriptName}” nashri bekor qili<PERSON>?", "app.modals.notAvailableText": "Uning holati “<PERSON><PERSON><PERSON>d emas” ga o‘zgartiriladi", "app.results.notAvailableError": "Mavjudlik<PERSON> o'zgartirish xatosi", "app.results.notAvailableSuccess": "Qulaylik muvaffaq<PERSON><PERSON>i o'z<PERSON>ldi", "app.tooltip.copy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.tooltip.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.tooltip.play": "<PERSON><PERSON> i<PERSON>i", "app.tooltip.refresh": "<PERSON><PERSON><PERSON>", "app.tooltip.current": "<PERSON>'rov<PERSON><PERSON> qayta is<PERSON>aydi", "app.modals.archive.header": "“{scriptName}” arxiv<PERSON><PERSON><PERSON>?", "app.modals.archive.text": "Uni tiklash mumkin emas", "app.modals.publish.header": "“{script<PERSON>ame}” nashr qilins<PERSON>?", "app.modals.dependentScript.header": "This script is linked to another «{scriptName}»?", "app.modals.dependentScripts.header": "This scenario is linked to others «{scriptNames}»?", "app.modals.form.canBeAutomated": "Tarqatishda avtomatik boshlash", "app.modals.form.priority": "Us<PERSON><PERSON>lik", "app.modals.formValidation.invalidRange": "The value must be in the range from {min} to {max}", "app.modals.formValidation.valueRequired": "<PERSON><PERSON><PERSON> bo'sh bo'lmasligi kerak", "app.modals.formValidation.variableRequired": "Variable must not be empty", "app.modals.formValidation.listItemRequired": "R<PERSON>ʻ<PERSON>at boʻsh boʻlmasligi kerak", "app.modals.formValidation.invalidRelation": "Noto'g'ri o'tish", "app.modals.formValidation.notActualScenario": "Not actual scenario", "app.modals.subscriptProblem.header": "“{scriptName}” skrip<PERSON>i nashr qilib bo‘l<PERSON>i, chunki {subscriptState} holatida ichki o'rnatilgan skriptlarni o'z ichiga oladi", "app.modals.subscriptProblem.body": "<PERSON><PERSON><PERSON> sk<PERSON><PERSON><PERSON>ni joy<PERSON> va qayta urinib ko'ring", "app.error.notUniqueVariable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> noyob bo'lishi kerak", "app.name.copy": "Nus<PERSON>lash", "app.common.collapse": "<PERSON><PERSON><PERSON>", "app.common.expand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.editor.noName": "Nomsiz", "app.editor.blockTypeStep": "Qadam", "app.editor.blockTypeRouter": "router", "app.editor.blockTypeService": "Hara<PERSON>", "app.editor.blockTypeSubscript": "S<PERSON><PERSON><PERSON>", "app.editor.blockTypeTerminal": "Terminal", "app.editor.blockTypeRating": "CSI reytingi", "app.editor.blockTypeScenario": "<PERSON><PERSON><PERSON>", "app.editor.errorStepsInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to'ldirishning to'g'r<PERSON><PERSON><PERSON> tekshiring (qizil rang bilan belgilangan)", "app.editor.scriptHeader": "<PERSON><PERSON>", "app.editor.routerHeader": "Yangi router", "app.editor.routerName": "Ism", "app.editor.routerDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.editor.routerNewRule": "yangi qoida", "app.editor.routerRuleName": "<PERSON>oid<PERSON> nomi", "app.editor.routerRuleDefaultTransfer": "Standart o'tish", "app.editor.routerRuleVariable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.editor.routerRuleOperator": "Operator", "app.editor.routerRuleValue": "<PERSON><PERSON><PERSON><PERSON>", "app.editor.routerRuleCondition": "Vaziyat", "app.editor.routerRule": "qoida", "app.editor.routerRuleConditionEqual": "Teng", "app.editor.routerRuleConditionNotEqual": "Teng emas", "app.editor.routerRuleConditionContains": "Tarkibida", "app.editor.routerRuleConditionNotContains": "Tark<PERSON> yo'q", "app.editor.routerRuleConditionGt": "<PERSON><PERSON>proq", "app.editor.routerRuleConditionLt": "Ozroq", "app.editor.routerRuleConditionGtEq": "<PERSON><PERSON>proq yoki teng", "app.editor.routerRuleConditionLtEq": "<PERSON><PERSON><PERSON><PERSON> yoki teng", "app.editor.routerRuleMoveUp": "<PERSON><PERSON><PERSON> k<PERSON>'<PERSON>", "app.editor.routerRuleMoveDown": "<PERSON>ga tushing", "app.editor.serviceTypeHeader": "Harakat turi", "app.editor.serviceTypePlaceholder": "Harakat turini tanlang", "app.editor.serviceInputsHeader": "Parametrlarni so'rash", "app.editor.serviceOutputHeader": "<PERSON><PERSON><PERSON>", "app.editor.serviceInputsValue": "<PERSON><PERSON><PERSON><PERSON>", "app.editor.serviceInputsManual": "<PERSON><PERSON><PERSON><PERSON><PERSON> kiritish", "app.editor.serviceTransferHeader": "O'tish", "app.editor.serviceTransferSuccess": "Mu<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.editor.serviceTransferError": "Xato", "app.editor.scripinformation.noDependentScripts": "No dependent scripts", "app.editor.scripinformation.dependentScripts": "Dependent scripts", "app.results.saveSuccess": "Muvaffaq<PERSON><PERSON><PERSON> sa<PERSON>i", "app.results.saveError": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "app.results.publishSuccess": "Muvaffaqiyatli chop etildi", "app.results.publishError": "<PERSON><PERSON> et<PERSON> xato", "app.results.archiveSuccess": "Muvaffaqiyatli arxivlandi", "app.results.archiveError": "Arxiv<PERSON><PERSON> xato", "app.editor.toggleAutoRelations": "Avtomatik bog'lanish", "app.export.errorDescription": "<PERSON><PERSON><PERSON> vaqt o'tgach, qayta yuk<PERSON>ga harakat qiling", "app.import.error": "yuk<PERSON> x<PERSON>", "app.import.errorDescription": "<PERSON><PERSON><PERSON> vaqt o'tgach, qayta yuk<PERSON>ga harakat qiling", "app.common.OK": "KELISHDIKMI", "app.tooltip.export": "Eksport", "app.tooltip.import": "Import", "app.editor.step.addAttachment": "<PERSON><PERSON>'<PERSON><PERSON>q qo'shish", "app.editor.step.addAttachmentFromVariable": "Add Attachment From Variable", "app.editor.step.attachmentsFromVariables": "Attachments From Variables", "app.editor.step.deleteAll": "Delete All", "app.editor.step.subscriptTitle": "S<PERSON><PERSON><PERSON>", "app.editor.step.subscriptSelect": "Skriptni tanlang", "app.editor.step.scenarioSelect": "Choose scenario", "app.editor.step.subscriptDescription": "Skript tavsifi", "app.editor.step.scenarioDescription": "Description of the scenario", "app.editor.step.subscriptStatus": "<PERSON><PERSON>", "app.editor.step.scenarioStatus": "Status of the scenario", "app.error.notUniqueAnswer": "Javoblar qadamning bir qismi sifatida noyob bo'lishi kerak", "app.table.header.rating": "<PERSON><PERSON><PERSON>", "app.table.header.runsNumber": "Bo<PERSON>lang'ich", "app.table.header.abortNumber": "<PERSON><PERSON><PERSON><PERSON>", "app.editor.step.variableSource": "Manbai", "app.editor.step.labelIsSkippable": "Oldindan to'l<PERSON><PERSON><PERSON> qiymatlar uchun qadamni o'tkazib yuboring", "app.editor.step.promptVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {variables}", "app.modals.form.firstStep": "Boshlang'<PERSON>ch qil<PERSON>q", "app.modals.form.answer.actions.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.editor.step.terminalSubject": "<PERSON><PERSON><PERSON>", "app.editor.step.terminalAction": "Hara<PERSON>", "app.editor.step.terminalUpdateDataHeader": "<PERSON><PERSON> ma'l<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "app.editor.step.terminalUpdateDataVariable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.editor.step.terminalUpdateDataAttribute": "Atributi", "app.editor.step.terminalUpdateDataVariableAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qo'shish", "app.editor.step.terminalSubjectHeader": "Murojaat mav<PERSON><PERSON>i bel<PERSON>", "app.editor.step.terminalActionHeader": "Qo'ng'iroq bilan harakat", "app.table.footer.count": "<PERSON><PERSON><PERSON><PERSON> soni", "app.step.service.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.step.service.serviceUnavailable": "ID bilan avtomatlashtirish xizmati {automationServiceId} mavjud emas yoki faol emas", "app.modals.forms.addTag": "Teg qo'shing", "app.editor.step.labelIsBackButtonAvailable": "O'tish or<PERSON><PERSON> qaytish", "app.editor.serviceFilesHeader": "Request files", "app.automationService.variableFileAdd": "Add Variable", "app.modals.form.variableFileName": "Variable Files", "app.automationService.requestFileName": "File Name", "app.automationService.variableAdd": "Add Variable", "app.error.notUniqueFileVariable": "File Variable should be unique as part of a step"}