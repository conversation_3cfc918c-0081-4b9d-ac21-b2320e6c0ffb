import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { SpamWord } from '../../@types/generated/administration';

import extraReducers from './negativeWords.extraReducers';

export interface INegativeWordsStore {
  loading: boolean;
  error?: SerializedError;
  negativeWords: SpamWord[];
}

const initialState: INegativeWordsStore = {
  loading: false,
  negativeWords: [],
};

const negativeWordsSlice = createSlice({
  name: 'negativeWords',
  initialState,
  reducers: {},
  extraReducers,
});

export default negativeWordsSlice.reducer;
