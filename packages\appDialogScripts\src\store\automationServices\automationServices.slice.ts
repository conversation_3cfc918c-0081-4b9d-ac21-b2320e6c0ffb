import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontAutomationService } from '@monorepo/automation-services/src/@types/automationService.types';

import extraReducers from './automationServices.extraReducers';

export interface IAutomationServicesStore {
  automationServices?: IFrontAutomationService[];
  loading: boolean;
  error?: SerializedError;
}

const initialState: IAutomationServicesStore = {
  automationServices: undefined,
  loading: false,
  error: undefined,
};

const automationServicesSlice = createSlice({
  name: 'automationServices',
  initialState,
  reducers: {},
  extraReducers,
});

export default automationServicesSlice.reducer;
