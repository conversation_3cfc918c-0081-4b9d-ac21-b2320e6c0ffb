import React from 'react';

import clsx from 'clsx';

import { CanClearBehavior, colors, Input, Text, TextVariant, utils } from '@product.front/ui-kit';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { IFrontKpiThresholdValue } from '../../../../@types/parameters';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';

interface IOperatorKpiProps {
  kpiThresholdValues: IFrontKpiThresholdValue[];
  kpiTargets: Record<string, number | null>;
  onChange: (key: string, value: number | null) => void;
  kpiValidationResult: Record<string, string>;
  canClear?: boolean;
  required?: boolean;
}

const OperatorKpi = ({
  kpiThresholdValues,
  kpiTargets,
  onChange,
  kpiValidationResult,
  canClear = true,
  required = false,
}: IOperatorKpiProps) => {
  return (
    <div
      className={clsx(utils.dGrid, utils.alignItemsCenter)}
      style={{ gridTemplateColumns: 'auto 1fr', gap: '8px 16px' }}
    >
      <Text
        variant={TextVariant.BodySemibold}
        className={clsx(utils.positionSticky, colors.bgHollywoodSmile)}
        style={{ top: 0, zIndex: 1 }}
      >
        {getLocaleMessageById('operators.modal.form.kpiName')}
      </Text>
      <Text
        variant={TextVariant.BodySemibold}
        className={clsx(utils.positionSticky, colors.bgHollywoodSmile)}
        style={{ top: 0, zIndex: 1 }}
      >
        {getLocaleMessageById('operators.modal.form.kpiGoal')}
      </Text>
      {kpiThresholdValues.map((kpi) => (
        <React.Fragment key={kpi.code}>
          <Text variant={TextVariant.CaptionMedium}>{kpi.displayName}</Text>
          <Input
            type="number"
            value={(kpiTargets[kpi.code] ?? '').toString()}
            placeholder={kpi.defaultValue?.toString()}
            onChange={({ value }) => onChange?.(kpi.code, value ? Number(value) : null)}
            min={kpi.minValue ?? 0}
            step={kpi.isInteger ? 1 : 0.1}
            max={kpi.maxValue ?? Infinity}
            isInvalid={!!kpiValidationResult[kpi.code]}
            message={<InputErrorMessage>{kpiValidationResult[kpi.code]}</InputErrorMessage>}
            canClearBehavior={canClear ? CanClearBehavior.Value : undefined}
            required={required}
          />
        </React.Fragment>
      ))}
    </div>
  );
};

export default OperatorKpi;
