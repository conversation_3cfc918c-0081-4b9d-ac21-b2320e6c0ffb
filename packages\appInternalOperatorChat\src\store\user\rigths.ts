import { OperatorRole } from '../../@types/generated/signalr';

import { UserRights } from './user.types';

export const userRightsMap: { [k in OperatorRole]: UserRights } = {
  [OperatorRole.User]: {
    canAddGroup: false,
    canRenameAll: false,
    canChangeMembersAll: false,
    canRemoveAll: false,
  },
  [OperatorRole.Admin]: {
    canAddGroup: true,
    canRenameAll: true,
    canChangeMembersAll: true,
    canRemoveAll: true,
  },
  [OperatorRole.SuperAdmin]: {
    canAddGroup: true,
    canRenameAll: true,
    canChangeMembersAll: true,
    canRemoveAll: true,
  },
};

export const userDenyAll: UserRights = {
  canAddGroup: false,
  canRenameAll: false,
  canChangeMembersAll: false,
  canRemoveAll: true,
};
