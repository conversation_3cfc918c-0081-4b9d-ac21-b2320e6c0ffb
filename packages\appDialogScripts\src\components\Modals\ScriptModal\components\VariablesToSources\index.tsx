import React from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import {
  <PERSON>ton,
  ButtonVariant,
  CanClearBehavior,
  grids,
  IconButton,
  Select,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconDelete from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { useDialogScriptsAppSelector } from '../../../../../store/hooks';

type VariableToSourceRecords = { [sourceKey: string]: string };

interface IVariablesToSourcesProps {
  value: VariableToSourceRecords;
  onChange: (selected: VariableToSourceRecords) => void;
  variables: Record<string, IFrontStep['variableName']>;
  disabled?: boolean;
}

const VariablesToSources: React.FC<IVariablesToSourcesProps> = ({
  value,
  onChange,
  disabled = false,
  variables,
}) => {
  const { sources } = useDialogScriptsAppSelector((state) => state.externalData);
  return (
    <div>
      {Object.entries(value).map(([sourceCode, variableCode]) => {
        return (
          <div className={clsx(grids.row, utils.mB3)} key={variableCode + sourceCode}>
            <div className={grids.col6}>
              <Select
                placeholder={getLocaleMessageById('app.editor.step.terminalUpdateDataAttribute')}
                data={sources
                  .filter((s) => !s.readonly)
                  .map(({ key, label }, i) => ({
                    text: label ?? '—',
                    value: key ?? `unknown-${i}`,
                    disabled: !!key && !!value[key],
                  }))}
                value={sourceCode}
                onChange={({ value: val }) => {
                  const ownVal = value[sourceCode];
                  // TODO: fix eslint
                  // eslint-disable-next-line sonarjs/no-unused-vars
                  const { [sourceCode]: _, ...rest } = value;
                  onChange({ ...rest, [val as string]: ownVal });
                }}
                canClearBehavior={CanClearBehavior.Value}
                disabled={disabled || !sources.length}
              />
            </div>
            <div className={grids.col5}>
              <Select
                placeholder={getLocaleMessageById('app.editor.step.terminalUpdateDataVariable')}
                wrapperClassName={utils.flexGrow6}
                data={(
                  Object.entries(variables).map(([code, name]) => ({
                    value: code,
                    text: name || code,
                  })) as ISelectDataItem[]
                ).sort((a, b) => a.text.localeCompare(b.text) ?? 0)}
                required
                value={variableCode}
                onChange={({ value: val }) => {
                  onChange({ ...value, [sourceCode as string]: val ?? '' });
                }}
                disabled={disabled || Object.keys(variables).length === 0}
                withDebounce
              />
            </div>
            <div className={grids.col1}>
              <IconButton
                onClick={() => {
                  // TODO: fix eslint
                  // eslint-disable-next-line sonarjs/no-unused-vars
                  const { [sourceCode]: _, ...rest } = value;
                  onChange(rest);
                }}
                disabled={disabled}
              >
                <IconDelete />
              </IconButton>
            </div>
          </div>
        );
      })}
      <Button
        className={clsx(utils.mT2, utils.p0)}
        variant={ButtonVariant.Transparent}
        onClick={() => {
          onChange({ ...value, _: '' });
        }}
        disabled={disabled}
      >
        <IconAdd />
        &nbsp;&nbsp;
        {getLocaleMessageById('app.editor.step.terminalUpdateDataVariableAdd')}
      </Button>
    </div>
  );
};

export default VariablesToSources;
