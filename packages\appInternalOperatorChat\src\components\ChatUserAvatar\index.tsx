import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import {
  Avatar,
  AvatarVariant,
  Indication,
  IndicationType,
  MaskVariant,
} from '@product.front/ui-kit';

import IconUser from '@product.front/icons/dist/icons17/Person&Doc/IconUser';

import { getAbbr } from '../../helpers/abbrHelper';

import styles from './styles.module.scss';

interface IChatUserAvatarProps {
  id: string;
  name: string;
  status: IndicationType;
}

const ChatUserAvatar: React.FC<IChatUserAvatarProps & HTMLAttributes<HTMLDivElement>> = ({
  id,
  name,
  status,
  className,
  ...rest
}) => {
  // mask={MaskVariant.CircleBottomRight}

  const squares = [IndicationType.Default, IndicationType.Busy, IndicationType.Away];

  return (
    <div className={clsx(styles.avatarWithStatus, className)} {...rest}>
      {name?.trim()?.length ? (
        <Avatar
          title={name}
          alt={getAbbr(name)}
          colorize={name + id}
          mask={
            squares.includes(status) ? MaskVariant.SquareBottomRight : MaskVariant.CircleBottomRight
          }
          variant={AvatarVariant.Dark}
        />
      ) : (
        <Avatar
          colorize={id}
          icon={<IconUser />}
          mask={
            squares.includes(status) ? MaskVariant.SquareBottomRight : MaskVariant.CircleBottomRight
          }
          variant={AvatarVariant.Dark}
        />
      )}
      <Indication type={status} className={styles.avatarWithStatusIndicator} />
    </div>
  );
};

export default ChatUserAvatar;
