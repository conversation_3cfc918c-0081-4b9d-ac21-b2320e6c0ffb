import React from 'react';

import { Loader } from '@product.front/ui-kit';

import ChatError from '@monorepo/common/src/components/Chat/ChatError';
import { usePersonalSettingsInitialization } from '@monorepo/common/src/hooks/usePersonalSettingsInitialization';
import { getProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';

import { getLocaleMessageById } from '../../helpers/localeHelper';
import { getPlatformApp } from '../../managers/platformAppManager';
import { getPlatformEventManager } from '../../managers/platformEventManager';
import { getPlatformHostedAppManager } from '../../managers/platformHostedAppManager';
import { getPlatformNotificationManager } from '../../managers/platformNotificationManager';
import { useInternalChatDispatch, useInternalChatSelector } from '../../store/hooks';
import { getOperatorsAndGroupsAction } from '../../store/internalChat.slice';
import { getUserData } from '../../store/user/user.thunks';

import InternalChatChatsList from './components/InternalChatChatsList';
import InternalChatInfo from './components/InternalChatInfo';
import InternalChatMain from './components/InternalChatMain';

import styles from './styles.module.scss';

const InternalChat = () => {
  const dispatch = useInternalChatDispatch();

  const { showChatInfo, chats } = useInternalChatSelector((state) => state.internalChat);
  const { user, loading, error } = useInternalChatSelector((state) => state.user);

  usePersonalSettingsInitialization(getProductServicesManager(), getPlatformNotificationManager());

  React.useEffect(() => {
    dispatch(getUserData());
    dispatch(getOperatorsAndGroupsAction());
  }, [dispatch]);

  React.useEffect(() => {
    const manager = getPlatformHostedAppManager();
    const eventManager = getPlatformEventManager();
    const app = getPlatformApp();
    if (!app || !eventManager || !manager) {
      return;
    }

    const updateCounter = () => {
      if (manager.activeAppName === app.name) {
        manager.setAppNotificationCount(app.name, 0);
      } else {
        const count = chats.reduce(
          (sum, { unreadedMessagesCount = 0 }) => sum + unreadedMessagesCount,
          0,
        );
        manager.setAppNotificationCount(app.name, count);
      }
    };

    updateCounter();

    eventManager && eventManager.subscribe('HostedApplicationActivated', updateCounter);

    return () => eventManager.unsubscribe('HostedApplicationActivated', updateCounter);
  }, [chats]);

  if (loading) {
    return (
      <section className={styles.appContainer}>
        <Loader />
      </section>
    );
  }

  if (!user || error) {
    return <ChatError title={getLocaleMessageById('app.error.getUserData')} error={error} />;
  }

  return (
    <section className={styles.appContainer}>
      <InternalChatChatsList />
      <InternalChatMain />
      {showChatInfo && <InternalChatInfo />}
    </section>
  );
};

export default InternalChat;
