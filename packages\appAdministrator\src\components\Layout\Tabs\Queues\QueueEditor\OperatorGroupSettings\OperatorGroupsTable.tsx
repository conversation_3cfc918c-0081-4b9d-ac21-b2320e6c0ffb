import React from 'react';

import { ColumnDef } from '@tanstack/react-table';
import clsx from 'clsx';

import { Checkbox, Table, Text, TextVariant, utils } from '@product.front/ui-kit';

import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';

import { IFrontOperatorGroupBaseWithChecked } from '.';

const OperatorGroupsTable = ({
  header,
  data,
  onChange: onCheckChange,
}: {
  header: string;
  data: IFrontOperatorGroupBaseWithChecked[];
  onChange: (operatorGroups: IFrontOperatorGroupBaseWithChecked[]) => void;
}) => {
  const columns = [
    {
      accessorKey: 'checked',
      accessorFn: (row) => (
        <div role="presentation" onClick={(e) => e.stopPropagation()}>
          <Checkbox
            onChange={({ checked }) =>
              onCheckChange(
                data.map((operatorGroup) => ({
                  ...operatorGroup,
                  checked: (row.id === operatorGroup.id ? checked : operatorGroup.checked) ?? false,
                })),
              )
            }
            checked={row.checked}
          />
        </div>
      ),
      header: () => (
        <Checkbox
          onChange={({ checked }) =>
            onCheckChange(data.map((operator) => ({ ...operator, checked: checked ?? false })))
          }
          intermediate={
            data.some(({ checked }) => checked) && !data.every(({ checked }) => checked)
          }
          checked={data.length > 0 && data.every(({ checked }) => checked)}
          disabled={data.length === 0}
        />
      ),
      size: 35,
      maxSize: 35,
    },
    {
      accessorKey: 'name',
      header: getLocaleMessageById('operatorGroups.modal.form.operatorGroupName'),
      enableSorting: true,
      enableResizing: true,
      enableColumnFilter: true,
      meta: {
        defaultSorting: 'asc',
        filter: {
          filterFn: 'includesString',
        },
      },
    },
  ] as ColumnDef<IFrontOperatorGroupBaseWithChecked>[];

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.flexColumn,
        utils.alignItemsCenter,
        utils.overflowHidden,
        utils.border,
        utils.radius2,
      )}
    >
      <Text variant={TextVariant.BodySemibold} className={utils.pY2}>
        {header}
      </Text>

      <div
        className={clsx(
          utils.flexBasis0,
          utils.flexGrow1,
          utils.overflowAuto,
          utils.scrollbar,
          utils.w100,
        )}
      >
        <Table
          data={data}
          columns={columns}
          onRowClick={({ data: original }) =>
            onCheckChange(
              data.map((operatorGroup) => ({
                ...operatorGroup,
                checked:
                  operatorGroup.id === original.id ? !operatorGroup.checked : operatorGroup.checked,
              })),
            )
          }
        />
      </div>
    </div>
  );
};

export default OperatorGroupsTable;
