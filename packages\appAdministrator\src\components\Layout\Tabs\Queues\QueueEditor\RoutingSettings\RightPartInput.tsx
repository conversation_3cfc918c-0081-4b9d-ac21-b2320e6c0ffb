import React from 'react';

import { Input, Select, MultiSelect } from '@product.front/ui-kit';

import { IFrontRightPartType, IFrontRoutingAttribute } from '../../../../../../@types/parameters';
import { IFrontRoutingRule } from '../../../../../../@types/queue';

interface IRightPartInputProps {
  type?: IFrontRightPartType;
  rule: IFrontRoutingRule;
  shouldShowError: boolean;
  onChange: (patch: Partial<IFrontRoutingRule>) => void;
  currentRoutingAttribute?: IFrontRoutingAttribute;
}

const RightPartInput = ({
  type,
  rule,
  shouldShowError,
  onChange,
  currentRoutingAttribute,
}: IRightPartInputProps) => {
  if (type === IFrontRightPartType.List) {
    return (
      <Select
        data={
          currentRoutingAttribute?.options.map((option) => ({
            value: option.value,
            text: option.name,
          })) ?? []
        }
        value={rule.value}
        disabled={!rule.attributeId}
        onChange={({ value }) => onChange({ value: value ?? '' })}
        isInvalid={shouldShowError && !rule.value}
      />
    );
  }

  if (type === IFrontRightPartType.MiltiselectList) {
    return (
      <MultiSelect
        data={
          currentRoutingAttribute?.options?.map(({ name, value }) => ({ value, text: name })) ?? []
        }
        value={rule.value.split(';').filter(Boolean)}
        onChange={(list) => {
          onChange({ value: list.join(';') });
        }}
        disabled={!rule.attributeId}
        isInvalid={shouldShowError && !rule.value}
      />
    );
  }

  return (
    <Input
      value={rule.value}
      disabled={!rule.attributeId}
      onChange={({ value }) => onChange({ value: value ?? '' })}
      isInvalid={shouldShowError && !rule.value}
    />
  );
};

export default RightPartInput;
