<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <meta http-equiv="pragma" content="no-cache">
    <title>__WEBARM_TITLE__</title>
    <meta name="version" content="__WEBARM_VERSION__">
    <link rel="icon" href="./favicon.ico" type="image/x-icon" />
    <link rel="icon" href="./favicon.svg" type="image/svg+xml" />

    <link rel="stylesheet" href="./CDN/lib-uikit/product-ui-kit.css" crossorigin="anonymous" />
    <!--
      Remove this if you only support browsers that support async/await.
      This is needed by babel to share largeish helper code for compiling async/await in older
      browsers. More information at https://github.com/single-spa/create-single-spa/issues/112
    -->
    <script src="./CDN/runtime/runtime.js" rel="preload prefetch"></script>

    <!--
      This CSP allows any SSL-enabled host and for arbitrary eval(), but you should limit these directives further to increase your app's security.
      Learn more about CSP policies at https://content-security-policy.com/#directive
    -->
    <!-- <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' https: localhost:*;
    img-src 'self' data: https: localhost:*;
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https: localhost:*;
    connect-src https: localhost:* ws: ws://localhost:* http://*************:8080 http://**********:8801;
    style-src 'unsafe-inline' https:;
    object-src 'none';"> -->

    <meta name="importmap-type" content="systemjs-importmap" />
    <!-- If you wish to turn off import-map-overrides for specific environments (prod), uncomment the line below -->
    <!-- More info at https://github.com/joeldenning/import-map-overrides/blob/master/docs/configuration.md#domain-list -->
    <!-- <meta name="import-map-overrides-domains" content="denylist:prod.example.com" /> -->

    <!-- Shared dependencies go into this import map. Your shared dependencies must be of one of the following formats:

      1. System.register (preferred when possible) - https://github.com/systemjs/systemjs/blob/master/docs/system-register.md
      2. UMD - https://github.com/umdjs/umd
      3. Global variable

      More information about shared dependencies can be found at https://single-spa.js.org/docs/recommended-setup#sharing-with-import-maps.
    -->
    <script type="systemjs-importmap">
      {
        "imports": {
          "single-spa": "./CDN/single-spa/single-spa.min.js",
          "react": "./CDN/react/react.production.min.js",
          "React": "./CDN/react/react.production.min.js",
          "react-dom": "./CDN/react-dom/react-dom.production.min.js",
          "ReactDom": "./CDN/react-dom/react-dom.production.min.js",
          "react-dom/client": "./CDN/react-dom/react-dom.production.min.js"
        }
      }
    </script>
    <script type="systemjs-importmap" src="importmap.json?v=__WEBARM_VERSION__"></script>
    <script src="./CDN/import-map-overrides/import-map-overrides.js"></script>

    <link
      rel="preload"
      href="./CDN/single-spa/single-spa.min.js"
      as="script"
      crossorigin="anonymous"
    />

    <script src="./CDN/systemjs/system.min.js"></script>
    <script src="./CDN/systemjs/extras/amd.min.js"></script>

    <template id="single-spa-layout">
      <single-spa-router></single-spa-router>
    </template>
    <script>
      window.WEBARM_TITLE="__WEBARM_TITLE__";
      window.WEBARM_VERSION="__WEBARM_VERSION__";
    </script>
  </head>
  <body>
    <main id="root" class="st-root" />
    <import-map-overrides-full
      show-when-local-storage="devtools"
      dev-libs
    ></import-map-overrides-full>
    <script>
      try {
        System.import('@Product/root-config');
      } catch (e) {
        console.error('Can not start without valid importmap');
        throw e;
      }
    </script>
  </body>
</html>
