import { Guid } from 'guid-typescript';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

import {
  WorkSessionClientOverSignalR,
  WorkSession as HubWorkSession,
} from './../Proxies/WorkSessionClientOverSignalR';

export class AwpWorkSession implements AWP.IWorkSession {
  workSessionId: Guid;

  constructor(workSessionId: Guid) {
    this.workSessionId = workSessionId;
  }
}

export class WorkSessionManager {
  private operatorId: Guid;
  private hubClient: WorkSessionClientOverSignalR;
  private startCalled: boolean;
  private awpWorkSession: AwpWorkSession | null;
  private hubWorkSession: HubWorkSession;

  constructor(
    operatorId: Guid,
    hubClient: WorkSessionClientOverSignalR,
    description: string,
    userRoleId: Guid,
    serviceAreaId: Guid,
    workplaceId: Guid,
    utcOffset: number,
  ) {
    this.operatorId = operatorId;
    this.hubClient = hubClient;
    this.startCalled = false;
    this.awpWorkSession = null;
    this.hubWorkSession = {
      startedWorkSessionId: null,

      clientType: 'Web',
      description: description,

      userRoleId: userRoleId.toString(),
      serviceAreaId: serviceAreaId.toString(),
      workplaceId: workplaceId.toString(),

      utcOffset: utcOffset,
    };

    this.hubClient.onConnected.on(() => this.onHubClientConnected());
    this.hubClient.onReconnected.on(() => this.onHubClientReconnected());

    this.startWorkSession = this.startWorkSession.bind(this);
    this.endWorkSession = this.endWorkSession.bind(this);

    this.onHubClientConnected = this.onHubClientConnected.bind(this);
    this.onHubClientReconnected = this.onHubClientReconnected.bind(this);
  }

  public async startWorkSession(): Promise<AwpWorkSession | null> {
    if (!this.hubClient.isConnected()) {
      await this.hubClient.connect();
    }

    this.startCalled = true;
    if (this.awpWorkSession?.workSessionId) {
      this.hubWorkSession.startedWorkSessionId = this.awpWorkSession?.workSessionId?.toString();
    }

    const workSessionId = await this.hubClient.startWorkSession(
      this.operatorId,
      this.hubWorkSession,
    );
    if (workSessionId) {
      this.awpWorkSession = new AwpWorkSession(workSessionId);
    }

    return this.awpWorkSession;
  }
  public async endWorkSession(): Promise<void> {
    await this.hubClient.endWorkSession();
  }

  private async onHubClientConnected(): Promise<void> {
    console.info(`onHubClientConnected requested: startCalled='${this.startCalled}'`);
    if (this.startCalled) {
      this.startWorkSession();
    }
  }

  private async onHubClientReconnected(): Promise<void> {
    console.info(`onHubClientReconnected requested: startCalled='${this.startCalled}'`);
    if (this.startCalled) {
      this.startWorkSession();
    }
  }
}
