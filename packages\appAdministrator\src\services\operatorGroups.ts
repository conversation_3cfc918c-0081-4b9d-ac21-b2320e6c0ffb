import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  NewOperatorGroup,
  OperatorGroupParametersView,
  OperatorGroupTreeView,
  OperatorGroupView,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export async function getOperatorGroups() {
  const url = `${getSettings().administrationApiUrl}/operatorGroups`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorGroupTreeView[];
}

export async function getOperatorGroup(id: string) {
  const url = `${getSettings().administrationApiUrl}/operatorGroups/${id}`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorGroupView;
}

export async function createOperatorGroup(operatorGroup: NewOperatorGroup) {
  const url = `${getSettings().administrationApiUrl}/operatorGroups`;
  return await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(operatorGroup),
  });
}

export async function updateOperatorGroup(id: string, operatorGroup: NewOperatorGroup) {
  const url = `${getSettings().administrationApiUrl}/operatorGroups/${id}`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(operatorGroup),
  });
}

export async function deleteOperatorGroup(id: string) {
  const url = `${getSettings().administrationApiUrl}/operatorGroups/${id}`;
  return await commonFetch(url, {
    method: 'DELETE',
    credentials: 'include',
  });
}

export async function getOperatorGroupParameters() {
  const url = `${getSettings().administrationApiUrl}/operatorGroups/parameters`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorGroupParametersView;
}
