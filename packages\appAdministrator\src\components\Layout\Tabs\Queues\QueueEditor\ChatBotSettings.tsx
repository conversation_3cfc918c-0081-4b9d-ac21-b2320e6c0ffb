import React from 'react';

import clsx from 'clsx';

import { Select, Switch, Text, utils } from '@product.front/ui-kit';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { IFrontBot } from '../../../../../@types/parameters';
import { IFrontQueueBot } from '../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

import { IValidationResult } from './validator';

interface IChatBotSettingsProps {
  buttonBot: IFrontQueueBot;
  buttonBots: IFrontBot[];
  onButtonBotChange: (bot: IFrontQueueBot) => void;
  intelligentBot: IFrontQueueBot;
  intelligentBots: IFrontBot[];
  onIntelligentBotChange: (bot: IFrontQueueBot) => void;
  ratingBot: IFrontQueueBot;
  ratingBots: IFrontBot[];
  onRatingBotChange: (bot: IFrontQueueBot) => void;
  validationResult: IValidationResult['chatBot'];
}

const ChatBotSettings = ({
  buttonBot,
  intelligentBot,
  ratingBot,
  buttonBots,
  intelligentBots,
  ratingBots,
  onButtonBotChange,
  onIntelligentBotChange,
  onRatingBotChange,
  validationResult,
}: IChatBotSettingsProps) => {
  return (
    <div className={clsx(utils.dFlex, utils.gap2, utils.flexColumn)}>
      <label className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
        <Text>{getLocaleMessageById('queues.editor.buttonBotEnabled')}</Text>
        <Switch
          checked={buttonBot.enabled}
          onChange={({ checked }) =>
            onButtonBotChange({
              enabled: checked ?? false,
              code: checked ? buttonBot.code : null,
            })
          }
          disabled={!buttonBots.length}
        />
      </label>
      <Select
        value={buttonBot.code}
        data={buttonBots.map((bot) => ({ value: bot.code, text: bot.name }))}
        disabled={!buttonBot.enabled}
        onChange={({ value }) => onButtonBotChange({ ...buttonBot, code: value ?? null })}
        isInvalid={!!validationResult.buttonBot}
        message={<InputErrorMessage>{validationResult.buttonBot}</InputErrorMessage>}
      />
      <label className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter, utils.mT2)}>
        <Text>{getLocaleMessageById('queues.editor.externalChatBotEnabled')}</Text>
        <Switch
          checked={intelligentBot.enabled}
          onChange={({ checked }) =>
            onIntelligentBotChange({
              enabled: checked ?? false,
              code: checked ? buttonBot.code : null,
            })
          }
          disabled={!intelligentBots.length}
        />
      </label>
      <Select
        value={intelligentBot.code}
        data={intelligentBots.map((bot) => ({ value: bot.code, text: bot.name }))}
        disabled={!intelligentBot.enabled}
        onChange={({ value }) => onIntelligentBotChange({ ...intelligentBot, code: value ?? null })}
        isInvalid={!!validationResult.intelligentBot}
        message={<InputErrorMessage>{validationResult.intelligentBot}</InputErrorMessage>}
      />
      <label className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter, utils.mT2)}>
        <Text>{getLocaleMessageById('queues.editor.ratingBotEnabled')}</Text>
        <Switch
          checked={ratingBot.enabled}
          onChange={({ checked }) =>
            onRatingBotChange({
              enabled: checked ?? false,
              code: checked ? ratingBot.code : null,
            })
          }
          disabled={!ratingBots.length}
        />
      </label>
      <Select
        value={ratingBot.code}
        data={ratingBots.map((bot) => ({ value: bot.code, text: bot.name }))}
        disabled={!ratingBot.enabled}
        onChange={({ value }) => onRatingBotChange({ ...ratingBot, code: value ?? null })}
        isInvalid={!!validationResult.ratingBot}
        message={<InputErrorMessage>{validationResult.ratingBot}</InputErrorMessage>}
      />
    </div>
  );
};

export default ChatBotSettings;
