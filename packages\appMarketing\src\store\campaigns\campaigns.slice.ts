import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { ICampaignResultFront, ICampaignStatistics, IFrontCampaign } from '../../@types/campaign';
import { defaultValidationResult, validateCampaignSettings } from '../../helpers/validateCampaign';

import extraReducers from './campaigns.extraReducers';

export interface ICampaignsStore {
  campaigns: IFrontCampaign[];
  selectedCampaign: IFrontCampaign | null;
  campaignInEdit: IFrontCampaign | null;
  saving: boolean;
  loading: boolean;
  validationResult: typeof defaultValidationResult;
  resultsLoading: boolean;
  resultsError?: SerializedError;
  campaignResults: ICampaignResultFront[];
  clientsLoading: boolean;
  clientsError?: SerializedError;
  clients: any[];
  statistics: ICampaignStatistics | null;
  statisticsLoading: boolean;
  statisticsError?: SerializedError;
}

const initialState: ICampaignsStore = {
  campaigns: [],
  selectedCampaign: null,
  campaignInEdit: null,
  saving: false,
  loading: false,
  validationResult: defaultValidationResult,
  campaignResults: [],
  resultsLoading: false,
  clientsLoading: false,
  clients: [],
  statistics: null,
  statisticsLoading: false,
};

const campaignsSlice = createSlice({
  name: 'campaigns',
  initialState,
  reducers: {
    setSelectedCampaign: (state, action: PayloadAction<IFrontCampaign | null>) => {
      state.selectedCampaign = action.payload;
      state.campaignInEdit = action.payload ? { ...action.payload } : null;
      state.validationResult = { ...defaultValidationResult };
    },
    updateCampaignField: (
      state,
      action: PayloadAction<{ fieldName: keyof IFrontCampaign; fieldValue: any }>,
    ) => {
      if (!state.campaignInEdit) return;

      state.campaignInEdit = {
        ...state.campaignInEdit,
        [action.payload.fieldName]: action.payload.fieldValue,
      };

      if (Object.values(state.validationResult).some((value) => !!value)) {
        state.validationResult = validateCampaignSettings(state.campaignInEdit).validationResult;
      }
    },

    updateValidationResult: (state, action: PayloadAction<typeof defaultValidationResult>) => {
      state.validationResult = action.payload;
    },

    resetClients(state) {
      state.clients = [];
    },
  },
  extraReducers,
});

export const { setSelectedCampaign, updateCampaignField, updateValidationResult, resetClients } =
  campaignsSlice.actions;

export default campaignsSlice.reducer;
