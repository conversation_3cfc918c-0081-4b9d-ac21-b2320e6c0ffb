import { Colors } from '@product.front/ui-kit';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';

export const getStatusColorForScript = (status?: ScriptStatus): Colors => {
  switch (status) {
    case ScriptStatus.Active:
      return Colors.Grassios80;
    case ScriptStatus.Archive:
      return Colors.Orangelle40;
    case ScriptStatus.NotAvailable:
      return Colors.Amenaza70;
    default:
      return Colors.OnyxBlack60;
  }
};
