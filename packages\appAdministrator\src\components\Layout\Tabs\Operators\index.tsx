import React from 'react';

import { showModal, Table } from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import InfoMessage from '@monorepo/common/src/components/Table/InfoMessage';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { IAdmTabComponent } from '../../../../@types/components';
import { IFrontOperator, IFrontOperatorBase } from '../../../../@types/operator';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { getOperatorFio } from '../../../../helpers/operatorHelper';
import { mapFullOperatorDtoToFront } from '../../../../mappers/operators';
import { getOperator } from '../../../../services/operators';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { deleteOperator, getAllOperators } from '../../../../store/operators/operators.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import getColumns from './columns';
import OperatorEditor from './OperatorEditor';

const OperatorsTab: React.FC<IAdmTabComponent> = ({ name }) => {
  const dispatch = useAdministratorAppDispatch();

  const { operators, loading, error } = useAdministratorAppSelector((store) => store.operators);

  const [selectedOperator, setSelectedOperator] = React.useState<IFrontOperatorBase | null>(null);
  const [isEditOpening, setIsEditOpening] = React.useState(false);

  const openOperatorEditor = async (operator: IFrontOperatorBase | null) => {
    let fullOperator: IFrontOperator | null = null;

    if (operator) {
      try {
        fullOperator = mapFullOperatorDtoToFront(await getOperator(operator.id));
      } catch (err) {
        console.log('Error getting operator', err);
        const errText = getLocaleMessageById('operators.getFull.error');
        const errArgs = getNotificationArgsByError(errText, err);
        getPlatformPopupNotificationManager().notifyError(...errArgs);
      }

      if (!fullOperator) return;
    }

    showModal({
      header: getLocaleMessageById(
        operator ? 'operators.modal.header.edit' : 'operators.modal.header.create',
        {
          operatorName: getOperatorFio(fullOperator),
        },
      ),
      children: (onClose) => (
        <OperatorEditor
          fullOperator={fullOperator}
          allOperators={operators}
          onSubmit={() => dispatch(getAllOperators())}
          onClose={onClose}
        />
      ),
      flushBody: true,
      canClose: false,
    });
  };

  const updateOperators = React.useCallback(async () => dispatch(getAllOperators()), [dispatch]);

  React.useEffect(() => {
    updateOperators();
  }, [updateOperators]);

  React.useEffect(() => {
    setSelectedOperator(null);
  }, [operators]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('operators.button.refresh')}
          onClick={updateOperators}
        >
          <IconRefresh />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('operators.button.delete')}
          disabled={!selectedOperator}
          onClick={() =>
            selectedOperator &&
            showConfirmModal({
              header: getLocaleMessageById('operators.delete.confirm', {
                operatorFullName:
                  [selectedOperator.lastName, selectedOperator.firstName]
                    .filter(Boolean)
                    .join(' ') || selectedOperator.login,
              }),
              onConfirm: () => dispatch(deleteOperator(selectedOperator.id)),
            })
          }
        >
          <IconTrash />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          loading={isEditOpening}
          tooltip={getLocaleMessageById('operators.button.edit')}
          disabled={!selectedOperator}
          onClick={async () => {
            setIsEditOpening(true);
            await openOperatorEditor(selectedOperator);
            setIsEditOpening(false);
          }}
        >
          <IconEdit />
        </AdmToolbarIconButton>
        <AdmToolbarCtaButton onClick={() => openOperatorEditor(null)}>
          {getLocaleMessageById('operators.button.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody loading={loading}>
        <Table<IFrontOperatorBase>
          data={operators.map((row) => ({
            ...row,
            meta: {
              active: row.login === selectedOperator?.login,
            },
          }))}
          columns={getColumns(operators)}
          onRowClick={(row) => setSelectedOperator(row.data)}
          rendererAfter={() => (
            <>
              {!error && operators.length === 0 && (
                <InfoMessage header={getLocaleMessageById('operators.empty')} />
              )}
              {error && <JumbotronError error={error} sticky />}
            </>
          )}
        />
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default OperatorsTab;
