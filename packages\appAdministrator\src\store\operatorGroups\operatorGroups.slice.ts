import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontOperatorGroup, IFrontOperatorGroupBase } from '../../@types/operatorGroup';

import extraReducers from './operatorGroups.extraReducers';

export interface IOperatorGroupsStore {
  loading: boolean;
  error?: SerializedError;
  operatorGroupsMap: Record<string, IFrontOperatorGroupBase>;
  selectedOperatorGroup: IFrontOperatorGroup | null;
  selectedLoading: boolean;
  selectedError?: SerializedError;
  saveLoading: boolean;
  saveError?: SerializedError;
}

const initialState: IOperatorGroupsStore = {
  loading: false,
  selectedLoading: false,
  saveLoading: false,
  operatorGroupsMap: {},
  selectedOperatorGroup: null,
};

const operatorGroupsSlice = createSlice({
  name: 'operatorGroups',
  initialState,
  reducers: {},
  extraReducers,
});

export default operatorGroupsSlice.reducer;
