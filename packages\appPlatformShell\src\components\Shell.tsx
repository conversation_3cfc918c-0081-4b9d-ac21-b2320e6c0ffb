﻿import * as React from 'react';

import clsx from 'clsx';
import { Guid } from 'guid-typescript';

import { helpers, utils, ZIndexElementType } from '@product.front/ui-kit';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import { normalizeWs } from '@monorepo/common/src/common/helpers/url.helper';
import {
  featureFlagHandlersAdd,
  featureFlagHandlersRemove,
  saveFeatureFlags,
} from '@monorepo/common/src/helpers/featureFlagsHelper';
import { getMvpValue } from '@monorepo/common/src/helpers/mvpHelper';
import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';
import * as CM from '@monorepo/common/src/platform/cm-interfaces';

import { ILogInterceptorOptions } from '../classes/LogInterceptor/LogInterceptor';
import logInterceptorInstance from '../classes/LogInterceptor/logInterceptor.manager';
import logSenderInstance from '../classes/LogSender/logSender.manager';
import { setAppSettingsToGlobal } from '../helpers/global.helper';
import { getLocaleMessageById } from '../helpers/localeHelper';
import { getShellAppSettings, IShellAppSettings } from '../helpers/shellAppSettings.helper';

import { ConfigurationManager } from './Managers/ConfigurationManager';
import * as EM from './Managers/EventManager';
import * as NM from './Managers/NotificationManager';
import { NotificationManager } from './Managers/NotificationManager';
import { OperatorGroupManager } from './Managers/OperatorGroupManager';
import { OperatorManager } from './Managers/OperatorManager';
import { WorkSessionManager } from './Managers/WorkSessionManager';
import * as M from './Modules';
import { CredentialsProvider } from './Proxies/CredentialsProvider';
import { WorkSessionClientOverSignalR } from './Proxies/WorkSessionClientOverSignalR';
import * as SR from './ServiceResolver';
import Splash from './Splash';

type ShellProps = {};
type ShellState = {
  configReceived: boolean;
  systemStarted: boolean;
  startError: Error[] | null;
  showLoader: boolean;
};

class Shell extends React.PureComponent<ShellProps, ShellState> implements AWP.IShell {
  private _configurationManager: AWP.IConfigurationManager | null = null;
  private zIndexElements: number = 0;

  serviceResolver: AWP.IServiceResolver;
  eventManager: AWP.IEventManager;
  modules: M.Modules | null = null;
  popupNotificationManager: AWP.IPopupNotificationManager;
  systemStarted: boolean = false;
  selectedRoleId: Guid = Guid.createEmpty();
  selectedUserRole: AWP.Role | null = null;
  selectedServiceAreaId: Guid = Guid.createEmpty();
  selectedServiceArea: AWP.ServiceArea | null = null;
  selectedWorkPlaceId: Guid = Guid.createEmpty();
  selectedWorkPlace: AWP.Workplace | null = null;
  currentOperator: AWP.Operator | null = null;
  workSession: AWP.IWorkSession | null = null;

  private appSettings: IShellAppSettings;

  public get configurationManager(): AWP.IConfigurationManager {
    if (this._configurationManager == null) {
      throw 'ConfigurationManager not initialized';
    } else {
      return this._configurationManager;
    }
  }

  constructor(props: ShellProps) {
    super(props);

    this.onSplashSelectionMade = this.onSplashSelectionMade.bind(this);
    this.onStartError = this.onStartError.bind(this);
    this.onModulesInitialized = this.onModulesInitialized.bind(this);
    this.setShellToWindow = this.setShellToWindow.bind(this);
    this.onNotificationManagerRendered = this.onNotificationManagerRendered.bind(this);
    this.getZIndex = this.getZIndex.bind(this);

    this.state = {
      configReceived: false,
      systemStarted: false,
      startError: null,
      showLoader: true,
    };

    this.serviceResolver = new SR.ServiceResolver();

    this.popupNotificationManager = new NM.NotificationManager({
      notificationsContainerProps: { className: 'st-notification-container' },
    });
    this.serviceResolver.register<AWP.IPopupNotificationManager>(
      'IPopupNotificationManager',
      this.popupNotificationManager,
    );

    this.eventManager = new EM.EventManager();
    this.serviceResolver.register<AWP.IEventManager>('IEventManager', this.eventManager);
    this.eventManager.subscribe('SplashSelectionMade', this.onSplashSelectionMade);
    this.eventManager.subscribe('StartError', this.onStartError);
    this.eventManager.subscribe('ModulesInitialized', this.onModulesInitialized);

    const getAppSettings = async () => {
      try {
        const settings = await getShellAppSettings();
        this.serviceResolver.register<IShellAppSettings>('AppSettings', settings);
        this.setState(() => ({ configReceived: true }));
        if (settings.webarmLogsBus) {
          logSenderInstance.updateLoggerBusURL(normalizeWs(settings.webarmLogsBus));
        } else {
          logSenderInstance.setLoggingAvailable(false);
        }
        settings.externalHost && setAppSettingsToGlobal({ externalHost: settings.externalHost });
      } catch (e) {
        console.error((e as Error).message);
        this.setState(() => ({
          startError: [e],
        }));
        throw e;
      }
    };

    getAppSettings();
  }

  getZIndex(): number {
    this.zIndexElements += 1;
    return helpers.addZIndexElement(ZIndexElementType.Modal);
  }

  onNotificationManagerRendered(element: any) {
    this.popupNotificationManager = element;
    this.serviceResolver.register<AWP.IPopupNotificationManager>(
      'IPopupNotificationManager',
      this.popupNotificationManager,
    );
  }

  onModulesRendered(element: any) {
    this.modules = element;
  }

  componentDidMount(): void {
    featureFlagHandlersAdd();
  }
  componentDidUnmount() {
    if ('serviceWorker' in navigator && !getMvpValue('skipRegisterServiceWorker')) {
      navigator.serviceWorker.register('service-worker.js').catch((e) => {
        console.error('Service worker registration failed', e);
      });
    }

    if (this.zIndexElements === 0) return;

    for (let i = 0; i <= this.zIndexElements; i += 1) {
      helpers.removeZIndexElement(ZIndexElementType.Modal);
    }

    featureFlagHandlersRemove();
  }

  render() {
    return (
      <div
        className="st-shell"
        ref={() => {
          this.setShellToWindow();
        }}
      >
        <NotificationManager
          ref={(element) => {
            this.onNotificationManagerRendered(element);
          }}
        />
        {this.state.startError != null && this.state.startError.length > 0 && (
          <div className="st-splash-wrapper" style={{ zIndex: this.getZIndex() }}>
            <div className={clsx('st-splash-outerDiv')}>
              <div
                className={clsx('st-splash-innerDiv', utils.pY6, utils.mY6, utils.flexCentredBlock)}
              >
                <JumbotronError
                  style={{ width: 299 }}
                  className={utils.mY6}
                  error={this.state.startError[0]}
                  header={getLocaleMessageById('app.shell.splash.errorTitle')}
                  subheader={getLocaleMessageById('app.shell.splash.errorSubtitle')}
                  summary={
                    <summary className={utils.mT4}>
                      {getLocaleMessageById('app.shell.splash.errorShowMore')}
                    </summary>
                  }
                  retryText={getLocaleMessageById('app.shell.splash.errorButtonText')}
                  onRetry={() => {
                    if ('caches' in window) {
                      caches.keys().then((names) => {
                        // Delete all the cache files
                        names.forEach((name) => {
                          caches.delete(name);
                        });
                      });

                      // Makes sure the page reloads. Changes are only visible after you refresh.
                      window.location.reload();
                    }
                  }}
                />
              </div>
            </div>
          </div>
        )}
        {!this.systemStarted && this.state.configReceived && !this.state.startError && (
          <div className="st-splash-wrapper" style={{ zIndex: this.getZIndex() }}>
            <Splash serviceResolver={this.serviceResolver} />
          </div>
        )}
        {this.systemStarted && (
          <div className="st-shell-content">
            <div className="st-shell-header">
              <div className="st-shell-header-left">
                <span className="st-logo-white" />
              </div>
              <div className="st-shell-header-right">
                {/* TODO: replace table with grid/flex */}
                {/* eslint-disable-next-line sonarjs/table-header */}
                <table className="st-header-panel">
                  <tbody>
                    <tr>
                      <td id="HP-LEFT-1" />
                      <td id="HP-LEFT-2" />
                      <td id="HP-LEFT-3" />
                      <td id="HP-LEFT-4" />
                      <td id="HP-LEFT-5" />

                      <td style={{ width: '100%' }} />

                      <td id="HP-RIGHT-5" />
                      <td id="HP-RIGHT-4" />
                      <td id="HP-RIGHT-3" />
                      <td id="HP-RIGHT-2" />
                      <td id="HP-RIGHT-1" />
                    </tr>
                  </tbody>
                </table>
                <M.Modules
                  shell={this}
                  selectedRoleId={this.selectedRoleId}
                  selectedServiceAreaId={this.selectedServiceAreaId}
                  ref={(element) => {
                    this.onModulesRendered(element);
                  }}
                />
              </div>
            </div>

            {this.state.showLoader && (
              <div id="loader">
                <div className="loading-white solid">
                  <div className="loading-content small"></div>
                </div>
              </div>
            )}

            <div id="layout" className="st-shell-layout"></div>
          </div>
        )}
      </div>
    );
  }

  setShellToWindow() {
    (window as any).awpShell = this;
  }

  async onSplashSelectionMade(selection: any) {
    this.selectedRoleId = selection.selectedRoleId;
    this.selectedServiceAreaId = selection.selectedServiceAreaId;
    this.selectedWorkPlaceId = selection.selectedWorkPlaceId;
    this.selectedUserRole = selection.selectedRole;
    this.selectedServiceArea = selection.selectedServiceArea;
    this.selectedWorkPlace = selection.selectedWorkPlace;

    console.info(`selectedUserRole: ${this.selectedUserRole?.name} (${this.selectedUserRole?.id})`);
    console.info(
      `selectedServiceArea: ${this.selectedServiceArea?.name} (${this.selectedServiceArea?.id})`,
    );
    console.info(
      `selectedWorkPlace: ${this.selectedWorkPlace?.name} (${this.selectedWorkPlace?.id})`,
    );

    this.appSettings = this.serviceResolver.resolve<any>('AppSettings');
    const manager = new ConfigurationManager(
      this.appSettings.infrastructureServicesUrl,
      this.selectedServiceAreaId,
    );
    this._configurationManager = manager;
    this.serviceResolver.register<AWP.IConfigurationManager>(
      'IConfigurationManager',
      this.configurationManager,
    );
    await manager.prefetchConfigurations([
      'webarmLogsSettings',
      'featureFlags',
      'SoftphoneCredmanName',
      'RequestsAwaitingStatusCodes',
      'isMyEvaluationFormsEnabled',
      'TimeOffsetJson',
      'ACWModeVoice',
      'ACWTimerVoice',
      'SendMessageKeysSetting',
      'WebarmTicketingSettings',
      'OperatorSuspendedStatusCode',
      'OperatorSuspendedOnTimerStatusCode',
    ]);

    await manager.getConfiguration('webarmLogsSettings').then((rawLogsSettings) => {
      if (rawLogsSettings && rawLogsSettings.stringValue) {
        const logsSettings = JSON.parse(rawLogsSettings.stringValue) as ILogInterceptorOptions;
        if (logsSettings?.sendToServer || logsSettings?.hideInConsole) {
          const isLocalhost = location.hostname === 'localhost';

          logInterceptorInstance.setOptions({
            hideInConsole:
              isLocalhost || location.hash.includes('logs')
                ? []
                : (logsSettings?.hideInConsole ?? ['log', 'debug', 'info']),
            sendToServer: isLocalhost
              ? []
              : (logsSettings?.sendToServer ?? ['fatal', 'error', 'custom']),
          });
        }
      }
    });

    await manager.getConfiguration('featureFlags').then((rawFeatureFlagsSettings) => {
      if (rawFeatureFlagsSettings?.stringValue) {
        const featureFlagsSettings = JSON.parse(rawFeatureFlagsSettings.stringValue) as Record<
          string,
          any
        >;
        saveFeatureFlags(featureFlagsSettings);
      }
    });

    const operatorManager = new OperatorManager(this.appSettings.infrastructureServicesUrl);
    this.serviceResolver.register<AWP.IOperatorManager>('IOperatorManager', operatorManager);

    const operatorGroupManager = new OperatorGroupManager(
      this.appSettings.infrastructureServicesUrl,
    );
    this.serviceResolver.register<AWP.IOperatorGroupManager>(
      'IOperatorGroupManager',
      operatorGroupManager,
    );

    const credentialsProvider = new CredentialsProvider(
      this.appSettings.credentialsProviderServicesUrl,
    );
    this.serviceResolver.register<CM.ICredentialsProvider>(
      'ICredentialsProvider',
      credentialsProvider,
    );

    this.setState(() => ({ systemStarted: true }));
    this.systemStarted = true;
  }

  onStartError(error: Error[]) {
    console.error('onStartError FIRED', error);

    this.setState(() => ({
      startError: error,
      systemStarted: false,
      showLoader: false,
    }));
  }

  onModulesInitialized(eventArgs: any) {
    const authenticationManager = this.serviceResolver.resolve<AWP.IOperatorAuthenticationManager>(
      'IOperatorAuthenticationManager',
    );

    this.setState(() => ({ showLoader: true }));

    authenticationManager
      .authenticateOperator()
      .then((operator) => {
        this.currentOperator = operator;
        logSenderInstance.updateCurrentUser(operator.id.toString());
        this.eventManager.fire('OperatorAuthenticated', operator);
      })
      .then(async () => {
        if (
          this.currentOperator != null &&
          this.selectedRoleId &&
          this.selectedServiceAreaId &&
          this.selectedWorkPlaceId
        ) {
          const utcOffset = new Date().getTimezoneOffset();
          const hubClient = new WorkSessionClientOverSignalR(
            this.appSettings.communicationServicesUrl,
            5,
          );
          const man = new WorkSessionManager(
            this.currentOperator.id,
            hubClient,
            `url='${window.location.href}'`,
            this.selectedRoleId,
            this.selectedServiceAreaId,
            this.selectedWorkPlaceId,
            -utcOffset / 60,
          );
          this.workSession = await man.startWorkSession();
        } else {
          console.error(
            `Can not start WorkSession: NOT this.currentOperator != null && this.selectedRoleId && this.selectedServiceAreaId && this.selectedWorkPlaceId`,
            eventArgs,
          );
        }
      })
      .then(() => {
        console.info('Shell: start modules');
        this.modules
          ?.startModules()
          .then(() => {
            this.setState(() => ({ showLoader: false }));
          })
          .catch((error: Error) => {
            console.error('Modules start FAILED!', error);
            this.onStartError([error]);
          });
      })
      .catch((error: Error) => {
        console.error('Operator authentication FAILED!', error);
        this.onStartError([error]);

        const errText = 'Operator authenticate FAILED'; // @todo need to localize
        const errArgs = getNotificationArgsByError(errText, error);
        this.popupNotificationManager.notifyError(...errArgs);
      });
  }
}

export default Shell;
