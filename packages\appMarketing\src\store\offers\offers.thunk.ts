import { createAsyncThunk } from '@reduxjs/toolkit';

import { IOffer } from '../../@types/offer';
import {
  createOfferAsync,
  deleteOfferAsync,
  getOffersAsync,
  getTemplatesAsync,
  updateOfferAsync,
} from '../../helpers/api';

export const getOffers = createAsyncThunk('offers/getOffers', async () => {
  return await getOffersAsync();
});

export const getWhatsAppTemplates = createAsyncThunk('offers/getWhatsAppTemplates', async () => {
  return await getTemplatesAsync('WhatsApp');
});

export const getNeuroNetTemplates = createAsyncThunk('offers/getNeuroNetTemplates', async () => {
  return await getTemplatesAsync('NeuroNet');
});

export const createOrUpdateOffer = createAsyncThunk(
  'offers/createOrUpdate',
  async (offer: IOffer, { dispatch }) => {
    const updatedOffer = await (offer.id ? updateOfferAsync(offer) : createOfferAsync(offer));
    await dispatch(getOffers());
    return updatedOffer;
  },
);

export const deleteOffer = createAsyncThunk(
  'offers/delete',
  async (offerId: string, { dispatch }) => {
    await deleteOfferAsync(offerId);
    dispatch(getOffers());
  },
);
