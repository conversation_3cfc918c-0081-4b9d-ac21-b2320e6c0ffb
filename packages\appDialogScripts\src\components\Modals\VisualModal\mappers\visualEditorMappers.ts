import { FrontStepType, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getNextXPositionForNode, getNextYPositionForNode } from '../helpers/nodePositionHelper';
import { NodeType, ScripDialogStepNode } from '../types/scriptDialogsVisualEditorTypes';

export const mapScriptStepToNode = (
  step: IFrontStep,
  wellKnownNodeData: Partial<ScripDialogStepNode> = {},
): ScripDialogStepNode => {
  return {
    id: step.code,
    position: {
      x: step.positionX || getNextXPositionForNode(),
      y: step.positionY || getNextYPositionForNode(),
    },
    data: { step },
    ...wellKnownNodeData,
  };
};

export const mapNodeToScriptStep = (
  node: ScripDialogStepNode,
  wellKnownStepData: Partial<IFrontStep> = {},
): IFrontStep => {
  return {
    ...node.data.step,
    positionX: node.positionAbsolute?.x ?? node.position.x,
    positionY: node.positionAbsolute?.y ?? node.position.y,
    ...wellKnownStepData,
  };
};

export const mapNodeTypeToFrontStepType = (nodeType: NodeType): FrontStepType => {
  switch (nodeType) {
    case NodeType.Custom:
      return FrontStepType.Step;
    case NodeType.Router:
      return FrontStepType.Router;
    case NodeType.Service:
      return FrontStepType.Service;
    case NodeType.Subscript:
      return FrontStepType.Subscript;
    case NodeType.Scenario:
      return FrontStepType.Scenario;
    case NodeType.Terminal:
      return FrontStepType.Terminal;
    case NodeType.Rating:
      return FrontStepType.Rating;
    default:
      throw new Error(`Unconverted node type ${nodeType}`);
  }
};

export const mapFrontStepTypeToNodeType = (frontStepType: FrontStepType): NodeType => {
  switch (frontStepType) {
    case FrontStepType.Step:
      return NodeType.Custom;
    case FrontStepType.Router:
      return NodeType.Router;
    case FrontStepType.Service:
      return NodeType.Service;
    case FrontStepType.Subscript:
      return NodeType.Subscript;
    case FrontStepType.Scenario:
      return NodeType.Scenario;
    case FrontStepType.Terminal:
      return NodeType.Terminal;
    case FrontStepType.Rating:
      return NodeType.Rating;
    default:
      throw new Error(`Unconverted front step type ${frontStepType}`);
  }
};
