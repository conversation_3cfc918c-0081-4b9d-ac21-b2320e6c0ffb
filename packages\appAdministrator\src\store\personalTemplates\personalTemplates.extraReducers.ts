import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IPersonalTemplatesStore } from './personalTemplates.slice';
import { getAllPersonalFolderTemplates, selectPersonalTemplate } from './personalTemplates.thunk';

const getFolderTemplatesReducers = (builder: ActionReducerMapBuilder<IPersonalTemplatesStore>) =>
  builder
    .addCase(getAllPersonalFolderTemplates.pending, (state) => {
      state.loading = true;
      state.error = undefined;
      state.selectedTemplate = null;
    })
    .addCase(getAllPersonalFolderTemplates.fulfilled, (state, action) => {
      state.folders = action.payload;
      state.loading = false;
    })
    .addCase(getAllPersonalFolderTemplates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });

const getTemplatesReducers = (builder: ActionReducerMapBuilder<IPersonalTemplatesStore>) =>
  builder.addCase(selectPersonalTemplate.fulfilled, (state, action) => {
    state.selectedTemplate = action.payload;
  });

export default (builder: ActionReducerMapBuilder<IPersonalTemplatesStore>) => {
  getFolderTemplatesReducers(builder);
  getTemplatesReducers(builder);

  return builder;
};
