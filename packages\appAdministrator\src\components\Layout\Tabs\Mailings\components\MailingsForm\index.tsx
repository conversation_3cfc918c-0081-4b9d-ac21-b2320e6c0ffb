import React, { FormEvent } from 'react';

import clsx from 'clsx';

import {
  colors,
  IconButton,
  Switch,
  utils,
  Text,
  Input,
  Colors,
  grids,
  Select,
  Button,
  ButtonVariant,
  OverlayLoader,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { checkObjectsEqual } from '@monorepo/common/src/helpers/objectsHelper';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import {
  CreatePeriodicMailing,
  CreateThresholdMailing,
  MessageType,
  PeriodicMailing,
  ThresholdMailing,
} from '../../../../../../@types/generated/administration';
import { MailingType } from '../../../../../../@types/mailings';
import { needConfirmWhenCompareFalse } from '../../../../../../helpers/confirmSave.helper';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import {
  addMailingAsync,
  deleteMailingAsync,
  updateMailingAsync,
} from '../../../../../../services/mailings';
import { useAdministratorAppSelector } from '../../../../../../store/hooks';

interface IMailingFormData {
  name: string;
  isEnabled: boolean;
  queueId: number;
  metricId: number;
  channel: string;
  threshold: string;
  periodMinutes: string;
  timeStart: string;
  timeEnd: string;
  recipients: { key: number; recipient: { address: string; utcOffset: string } }[];
  senderAddress: string;
}

const mapThresholdMailingToFromData = (mailing: ThresholdMailing): IMailingFormData => {
  return {
    name: mailing.name,
    isEnabled: mailing.isEnabled,
    queueId: mailing.queueId,
    metricId: mailing.metric.id,
    channel: mailing.channel.code,
    threshold: mailing.threshold.toString(),
    timeStart: mailing.allowedTime.start,
    timeEnd: mailing.allowedTime.end,
    recipients: mailing.recipients.map((r, i) => ({
      key: i,
      recipient: { address: r.address, utcOffset: r.utcOffset.toString() },
    })),
    periodMinutes: '', // not supported in ThresholdMailing
    senderAddress: mailing.channel.senderAddress,
  };
};

const mapPeriodicMailingToFormData = (mailing: PeriodicMailing): IMailingFormData => {
  return {
    name: mailing.name,
    isEnabled: mailing.isEnabled,
    metricId: mailing.metric.id,
    channel: mailing.channel.code,
    timeStart: mailing.allowedTime.start,
    timeEnd: mailing.allowedTime.end,
    recipients: mailing.recipients.map((r, i) => ({
      key: i,
      recipient: { address: r.address, utcOffset: r.utcOffset.toString() },
    })),
    periodMinutes: mailing.periodMinutes.toString(),
    threshold: '', // not supported in PeriodicMailing
    queueId: 0, // not supported in PeriodicMailing
    senderAddress: mailing.channel.senderAddress,
  };
};

const MailingsForm: React.FC<{
  mailing?: PeriodicMailing | ThresholdMailing;
  onSave: (needRefresh?: boolean) => void;
}> = ({ mailing, onSave }) => {
  const { queues, metrics, channels, selectedMailingType, senders } = useAdministratorAppSelector(
    (store) => store.mailings,
  );
  const isThreshold = selectedMailingType === MailingType.ThresholdMailing;

  const defaultRecipient = {
    address: '',
    utcOffset: String((new Date().getTimezoneOffset() / 60) * -1),
  };

  const getDefaultDataFromMailing = () =>
    isThreshold
      ? mapThresholdMailingToFromData(mailing as ThresholdMailing)
      : mapPeriodicMailingToFormData(mailing as PeriodicMailing);

  const defaultData = !mailing
    ? {
        name: '',
        isEnabled: false,
        metricId: metrics[0].id,
        channel: MessageType.Telegram,
        timeStart: '09:00:00',
        timeEnd: '18:00:00',
        recipients: [{ key: 0, recipient: defaultRecipient }],
        periodMinutes: '',
        threshold: '',
        queueId: queues[0].id,
        senderAddress: senders.filter((s) => s.channel === MessageType.Telegram)[0].source ?? '',
      }
    : getDefaultDataFromMailing();

  const [data, setData] = React.useState<IMailingFormData>(defaultData);

  const [isSaving, setIsSaving] = React.useState(false);
  const [err, setErr] = React.useState<Error>();

  const changeData = (patch: Partial<IMailingFormData>) => {
    setData((cur) => ({ ...cur, ...patch }));
  };

  const recipientsRef = React.useRef<HTMLDivElement>(null);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      const commonPart: Partial<CreatePeriodicMailing | CreateThresholdMailing> = {
        name: data.name.trim(),
        isEnabled: data.isEnabled,
        metricId: data.metricId,

        channelCode: data.channel,
        allowedTime: {
          start: data.timeStart,
          end: data.timeEnd,
        },
        recipients: data.recipients.map(({ recipient }) => {
          return {
            address: recipient.address,
            utcOffset: parseFloat(recipient.utcOffset),
          };
        }),

        senderAddress: data.senderAddress,
      };

      let model: CreatePeriodicMailing | CreateThresholdMailing;

      if (selectedMailingType === MailingType.ThresholdMailing) {
        model = {
          mailing: 'CreateThresholdMailing',
          kind: 'CreateThresholdMailing',
          queueId: data.queueId,
          threshold: parseFloat(data.threshold),
          ...commonPart,
        } satisfies CreateThresholdMailing;
      } else {
        model = {
          mailing: 'CreatePeriodicMailing',
          kind: 'CreatePeriodicMailing',
          periodMinutes: parseInt(data.periodMinutes),
          ...commonPart,
        } satisfies CreatePeriodicMailing;
      }

      if (mailing) {
        await updateMailingAsync(mailing.id, model);
      } else {
        await addMailingAsync(model);
      }
      onSave(true);
    } catch (er) {
      setErr(er);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    const del = async () => {
      try {
        setIsSaving(true);

        if (mailing) {
          await deleteMailingAsync(mailing.id);
          onSave(true);
        } else {
          await onSave(false);
        }
      } catch (er) {
        setErr(er);
      } finally {
        setIsSaving(false);
      }
    };

    if (mailing) {
      showConfirmModal({
        header: getLocaleMessageById('mailings.modal.delete.title'),
        text: getLocaleMessageById('mailings.modal.delete.text'),
        onConfirm: del,
      });
    } else {
      needConfirmWhenCompareFalse(false, del);
    }
  };

  return (
    <form className={clsx(utils.border, utils.radius1)} onSubmit={handleSubmit}>
      <OverlayLoader loading={isSaving}>
        <header
          className={clsx(
            colors.bgOnyxBlack30,
            utils.dFlex,
            utils.alignItemsCenter,
            utils.gap4,
            utils.pX3,
            utils.pY2,
          )}
        >
          <Input
            // TODO: need autofocus?
            // eslint-disable-next-line jsx-a11y/no-autofocus
            autoFocus={!mailing}
            name="name"
            placeholder={getLocaleMessageById('mailings.form.name')}
            style={{ width: 320 }}
            value={data.name}
            onChange={({ value }) => changeData({ name: value ?? '' })}
            required
          />
          <label className={clsx(utils.dInlineFlex, utils.alignItemsCenter, utils.gap1)}>
            <Text>{getLocaleMessageById('mailings.form.enabled')}</Text>
            <Switch
              name="isEnabled"
              checked={data.isEnabled}
              onChange={({ checked }) => changeData({ isEnabled: !!checked })}
            />
          </label>
          {mailing?.creator && (
            <>
              <Text>
                {getLocaleMessageById('mailings.form.createdAt')}:{' '}
                <Text color={Colors.OnyxBlack80} title={mailing.creator.createdAt}>
                  {new Date(mailing.creator.createdAt).toLocaleString()}
                </Text>
              </Text>
              <Text>
                {getLocaleMessageById('mailings.form.createdBy')}:{' '}
                <Text color={Colors.OnyxBlack80} title={mailing.creator.login}>
                  {mailing.creator.name}
                </Text>
              </Text>
            </>
          )}

          <aside className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap3, utils.mLauto)}>
            {mailing ? (
              <IconButton onClick={handleDelete} className={utils.mLauto}>
                <IconTrash />
              </IconButton>
            ) : (
              <Button variant={ButtonVariant.Secondary} onClick={handleDelete}>
                {getLocaleMessageById('app.common.cancel')}
              </Button>
            )}
            {(!checkObjectsEqual(defaultData, data) || !mailing) && (
              <Button type="submit" variant={ButtonVariant.Primary} className={utils.mLauto}>
                {getLocaleMessageById('app.common.save')}
              </Button>
            )}
          </aside>
        </header>
        {err && (
          <div className={utils.p3}>
            <AlertError header={getLocaleMessageById('mailings.form.saveError')} error={err} />
          </div>
        )}
        <section className={clsx(utils.p3, utils.dFlex, utils.flexColumn, utils.gap2)}>
          <div className={clsx(grids.row, utils.alignItemsCenter, utils.mB2)}>
            {isThreshold && (
              <>
                <div className={grids.col1}>
                  <Text>{getLocaleMessageById('mailings.form.queue')}</Text>
                </div>
                <div className={grids.col3}>
                  <Select
                    name="queue"
                    className={utils.w100}
                    data={queues.map((q) => ({ text: q.name, value: q.id.toString() }))}
                    value={data.queueId.toString()}
                    onChange={({ value }) => value && changeData({ queueId: parseInt(value) })}
                    required
                  />
                </div>
              </>
            )}
            <div className={clsx(grids.col1, isThreshold && utils.textRight)}>
              <Text>{getLocaleMessageById('mailings.form.metric')}</Text>
            </div>
            <div className={isThreshold ? grids.col1 : grids.col3}>
              <Select
                name="metric"
                className={utils.w100}
                data={metrics.map(({ id, displayName }) => ({
                  value: id.toString(),
                  text: displayName,
                }))}
                value={data.metricId.toString()}
                onChange={({ value }) => value && changeData({ metricId: parseInt(value) })}
                required
              />
            </div>
            {isThreshold && (
              <>
                <div className={clsx(grids.col1, utils.textRight)}>
                  <Text>{getLocaleMessageById('mailings.form.threshold')}</Text>
                </div>
                <div className={grids.col1}>
                  <Input
                    name="threshold"
                    type="number"
                    value={data.threshold.toString()}
                    onChange={({ value }) => changeData({ threshold: value ?? '' })}
                  />
                </div>
              </>
            )}
          </div>
          <div className={clsx(grids.row, utils.alignItemsCenter)}>
            <div className={grids.col1}>
              <Text>{getLocaleMessageById('mailings.form.channel')}</Text>
            </div>
            <div className={grids.col3}>
              <Select
                name="channel"
                className={utils.w100}
                data={channels
                  .filter((d) => d.type && d.displayName)
                  .map(({ type, displayName }) => ({ text: displayName!, value: type! }))}
                value={data.channel}
                onChange={({ value }) => value && changeData({ channel: value })}
                required
              />
            </div>
            <div className={grids.col1}>
              <Text>{getLocaleMessageById('mailings.form.sender')}</Text>
            </div>
            <div className={grids.col3}>
              <Select
                name="sender"
                className={utils.w100}
                data={senders
                  .filter((sender) => sender.channel === data.channel)
                  .map((sender) => ({ text: sender.title ?? '', value: sender.source ?? '' }))}
                value={data.senderAddress}
                onChange={({ value }) => value && changeData({ senderAddress: value })}
                required
              />
            </div>
          </div>
          <div className={clsx(grids.row, utils.alignItemsCenter)}>
            {!isThreshold && (
              <>
                <div className={clsx(grids.col1)}>
                  <Text>{getLocaleMessageById('mailings.form.period')}</Text>
                </div>
                <div className={grids.col1}>
                  <Input
                    name="periodMinutes"
                    type="number"
                    value={data.periodMinutes.toString()}
                    onChange={({ value }) => changeData({ periodMinutes: value })}
                    min={1}
                    max={2147483647}
                    required
                  />
                </div>
              </>
            )}
            <div className={clsx(grids.col1, { [utils.textRight]: !isThreshold })}>
              <Text>{getLocaleMessageById('mailings.form.time')}</Text>
            </div>
            <div className={clsx(grids.col3, utils.dFlex, utils.alignItemsCenter)}>
              <Input
                name="timeStart"
                type="time"
                value={data.timeStart}
                max={data.timeEnd}
                onChange={({ value }) => changeData({ timeStart: value })}
                required
              />
              <Text>&nbsp;—&nbsp;</Text>
              <Input
                name="timeEnd"
                type="time"
                value={data.timeEnd}
                min={data.timeStart}
                onChange={({ value }) => changeData({ timeEnd: value })}
                required
              />
            </div>
          </div>
          <div className={clsx(grids.row, utils.alignItemsCenter)}>
            <div className={grids.col1}>
              <Text>{getLocaleMessageById('mailings.form.recipients')}</Text>
            </div>
            <div className={grids.col3}>
              <Button
                className={utils.mLn4}
                variant={ButtonVariant.Transparent}
                onClick={() => {
                  changeData({
                    recipients: [
                      {
                        key: performance.now(),
                        recipient: defaultRecipient,
                      },
                      ...data.recipients,
                    ],
                  });
                }}
              >
                <IconAdd />
                &nbsp; {getLocaleMessageById('mailings.form.recipients.add')}
              </Button>
            </div>
          </div>
          <div ref={recipientsRef} className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
            {data.recipients.map(({ key, recipient }) => (
              <div key={key} className={clsx(grids.row, utils.alignItemsCenter)}>
                <div
                  className={clsx(
                    grids.col1,
                    utils.dFlex,
                    utils.alignItemsCenter,
                    utils.justifyContentBetween,
                  )}
                >
                  <IconButton
                    onClick={() => {
                      changeData({ recipients: data.recipients.filter((x) => x.key !== key) });
                    }}
                    disabled={data.recipients.length === 1}
                  >
                    <IconTrash />
                  </IconButton>
                  <Text>{getLocaleMessageById('mailings.form.address')}</Text>
                </div>
                <div className={grids.col3}>
                  <Input
                    className={clsx(utils.w100, 'recipient-address')}
                    minLength={1}
                    value={recipient.address}
                    required
                    onChange={({ value }) =>
                      changeData({
                        recipients: data.recipients.map((x) =>
                          x.key !== key
                            ? x
                            : { ...x, recipient: { ...x.recipient, address: value ?? '' } },
                        ),
                      })
                    }
                  />
                </div>
                <div className={clsx(grids.col1, utils.textRight)}>
                  <Text>{getLocaleMessageById('mailings.form.utcOffset')}</Text>
                </div>
                <div className={grids.col1}>
                  <Input
                    type="number"
                    min="-12"
                    max="14"
                    step="1"
                    value={recipient.utcOffset.toString()}
                    onChange={({ value }) =>
                      changeData({
                        recipients: data.recipients.map((x) =>
                          x.key !== key
                            ? x
                            : { ...x, recipient: { ...x.recipient, utcOffset: value ?? '' } },
                        ),
                      })
                    }
                    required
                  />
                </div>
              </div>
            ))}
          </div>
        </section>
      </OverlayLoader>
    </form>
  );
};

export default MailingsForm;
