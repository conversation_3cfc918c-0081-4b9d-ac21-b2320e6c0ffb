import React from 'react';

import { ColumnDef } from '@tanstack/react-table';
import clsx from 'clsx';

import { Checkbox, Input, InputSize, Table, Text, TextVariant, utils } from '@product.front/ui-kit';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { hackNumberInput } from '../../../../../helpers/numberInputHack';

import { IOperatorWithChecked } from './OperatorsSettings';

const OperatorsTable = ({
  header,
  data,
  withPriority = false,
  error,
  onChange: onCheckChange,
}: {
  header: string;
  data: (IOperatorWithChecked & { priority?: number })[];
  withPriority?: boolean;
  error?: string;
  onChange: (operators: IOperatorWithChecked[]) => void;
}) => {
  const baseColumns = [
    {
      accessorKey: 'checked',
      accessorFn: (row) => (
        <div
          className={utils.positionRelative}
          role="presentation"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <Checkbox
            onChange={({ checked }) =>
              onCheckChange(
                data.map((operator) => ({
                  ...operator,
                  checked: (row.id === operator.id ? checked : operator.checked) ?? false,
                })),
              )
            }
            checked={row.checked}
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      ),
      header: () => (
        <Checkbox
          onChange={({ checked }) =>
            onCheckChange(data.map((operator) => ({ ...operator, checked: checked ?? false })))
          }
          intermediate={
            data.some(({ checked }) => checked) && !data.every(({ checked }) => checked)
          }
          checked={data.length > 0 && data.every(({ checked }) => checked)}
          disabled={data.length === 0}
        />
      ),
      size: 35,
      maxSize: 35,
    },
    {
      accessorKey: 'FullName',
      accessorFn: (row) => [row.lastName, row.firstName, row.middleName].filter(Boolean).join(' '),
      header: getLocaleMessageById('operatorGroups.modal.form.operatorName'),
      enableSorting: true,
      enableResizing: true,
      enableColumnFilter: true,
      maxSize: 300,
      meta: {
        defaultSorting: 'asc',
        filter: {
          filterFn: 'includesString',
        },
      },
    },
    {
      accessorKey: 'login',
      header: getLocaleMessageById('operatorGroups.modal.form.login'),
      enableSorting: true,
      enableResizing: true,
      enableColumnFilter: true,
      maxSize: 300,
      meta: {
        filter: {
          filterFn: 'includesString',
        },
      },
    },
    {
      accessorKey: 'groups',
      accessorFn: (row) =>
        row.groups.reduce((acc, group) => `${acc}${acc ? ', ' : ''}${group.name}`, ''),
      header: getLocaleMessageById('operatorGroups.modal.form.groups'),
      enableSorting: true,
      enableResizing: true,
    },
  ] as ColumnDef<IOperatorWithChecked>[];

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.flexColumn,
        utils.alignItemsCenter,
        utils.overflowHidden,
        utils.border,
        utils.radius2,
      )}
    >
      <Text variant={TextVariant.BodySemibold} className={utils.pY2}>
        {header}
      </Text>

      {error && <InputErrorMessage>{error}</InputErrorMessage>}
      <div
        className={clsx(
          utils.flexBasis0,
          utils.flexGrow1,
          utils.overflowAuto,
          utils.scrollbar,
          utils.w100,
        )}
      >
        <Table
          data={data}
          columns={
            withPriority
              ? [
                  ...baseColumns,
                  {
                    accessorKey: 'priority',
                    header: getLocaleMessageById('operatorGroups.modal.form.priority'),
                    accessorFn: (row) => (
                      <Input
                        wrapperClassName={utils.w100}
                        value={(row.priority ?? 0).toString()}
                        onClick={(event) => event.stopPropagation()}
                        onChange={({ value }) =>
                          onCheckChange(
                            data.map((operator) =>
                              operator.id === row.id
                                ? {
                                    ...operator,
                                    priority: value ? Number(value) : 0,
                                  }
                                : operator,
                            ),
                          )
                        }
                        min={0}
                        max={9}
                        type="number"
                        isInvalid={!!row.priority && (row.priority > 9 || row.priority < 0)}
                        onInput={hackNumberInput(1)}
                        size={InputSize.Small}
                      />
                    ),
                    enableSorting: true,
                    maxSize: 300,
                  } as ColumnDef<IOperatorWithChecked>,
                ]
              : baseColumns
          }
          onRowClick={({ data: original }) =>
            onCheckChange(
              data.map((operator) => ({
                ...operator,
                checked: operator.id === original.id ? !operator.checked : operator.checked,
              })),
            )
          }
        />
      </div>
    </div>
  );
};

export default OperatorsTable;
