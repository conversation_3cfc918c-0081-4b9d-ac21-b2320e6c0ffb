import React from 'react';

import { IconButton, FloatingTooltip, FloatingTooltipPosition } from '@product.front/ui-kit';

import IconStart from '@product.front/icons/dist/icons17/Other/IconStart';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { useDialogScriptsAppDispatch } from '../../../store/hooks';
import { setFirstStep } from '../../../store/oneScript/oneScript.slice';

interface IFirstStepButtonProps {
  stepCode: string;
  isFirstStep: boolean;
}

const FirstStepButton = ({ stepCode, isFirstStep }: IFirstStepButtonProps) => {
  const dispatch = useDialogScriptsAppDispatch();

  return (
    <FloatingTooltip
      tooltip={getLocaleMessageById('app.modals.form.firstStep')}
      position={FloatingTooltipPosition.Top}
    >
      <IconButton onClick={() => dispatch(setFirstStep(stepCode))} disabled={isFirstStep}>
        <IconStart />
      </IconButton>
    </FloatingTooltip>
  );
};

export default FirstStepButton;
