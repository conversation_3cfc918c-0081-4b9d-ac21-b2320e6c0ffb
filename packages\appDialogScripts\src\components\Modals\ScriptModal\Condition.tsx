import React from 'react';

import clsx from 'clsx';

import {
  <PERSON>ton,
  ButtonSize,
  ButtonV<PERSON>t,
  CanClearBehavior,
  FloatingDropdown,
  grids,
  IconButton,
  Input,
  InputSize,
  utils,
} from '@product.front/ui-kit';

import IconDelete from '@product.front/icons/dist/icons17/MainStuff/IconClose';

import {
  FrontConditionOperator,
  IFrontRuleCondition,
  IFrontRuleInvalidReasons,
  IFrontStep,
} from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import InputErrorMessage from '../../InputErrorMessage';

import VariableSelect from './components/VariableSelect';

interface IConditionProps {
  condition: IFrontRuleCondition;
  onDelete: () => void;
  onChange: (updatedCondition: IFrontRuleCondition) => void;
  disabled?: boolean;
  variables: Record<string, IFrontStep['variableName']>;
  invalidReasons?: IFrontRuleInvalidReasons;
}

const Condition = ({
  condition,
  onDelete,
  onChange,
  disabled,
  variables,
  invalidReasons,
}: IConditionProps) => {
  const operators: Record<string, { text: string; title: string }> = {
    [FrontConditionOperator.Eq]: {
      text: '=',
      title: getLocaleMessageById('app.editor.routerRuleConditionEqual'),
    },
    [FrontConditionOperator.NotEq]: {
      text: '≠',
      title: getLocaleMessageById('app.editor.routerRuleConditionNotEqual'),
    },
    [FrontConditionOperator.Contain]: {
      text: '∈',
      title: getLocaleMessageById('app.editor.routerRuleConditionContains'),
    },
    [FrontConditionOperator.NotContain]: {
      text: '∉',
      title: getLocaleMessageById('app.editor.routerRuleConditionNotContains'),
    },
    [FrontConditionOperator.Gt]: {
      text: '>',
      title: getLocaleMessageById('app.editor.routerRuleConditionGt'),
    },
    [FrontConditionOperator.GtEq]: {
      text: '≥',
      title: getLocaleMessageById('app.editor.routerRuleConditionGtEq'),
    },
    [FrontConditionOperator.Lt]: {
      text: '<',
      title: getLocaleMessageById('app.editor.routerRuleConditionLt'),
    },
    [FrontConditionOperator.LtEq]: {
      text: '≤',
      title: getLocaleMessageById('app.editor.routerRuleConditionLtEq'),
    },
  };

  return (
    <div className={clsx(grids.row, utils.alignItemsCenter, utils.positionStatic)}>
      <div className={clsx(grids.col11, utils.dFlex, utils.pY1)}>
        <VariableSelect
          placeHolderResourceId="app.editor.routerRuleVariable"
          variables={variables}
          onChange={(variable, variableName) =>
            onChange({ ...condition, variable: variable, variableName: variableName })
          }
          disabled={disabled}
          variable={condition.variable}
          wrapperClassName={utils.flexGrow6}
          size={InputSize.Small}
          viewLabel={false}
          isInvalid={!!invalidReasons?.conditionsRequired}
          message={invalidReasons?.conditionsRequired}
        />

        <FloatingDropdown
          className={utils.flexGrow0}
          menu={
            <div
              className={clsx(utils.dFlex, utils.gap2, utils.pX2, utils.flexWrap)}
              style={{ width: 64 }}
            >
              {Object.entries(operators).map(([k, { text, title }]) => (
                <IconButton
                  key={k}
                  title={title}
                  onClick={() => {
                    onChange({ ...condition, operator: k as FrontConditionOperator });
                  }}
                >
                  <>{text}</>
                </IconButton>
              ))}
            </div>
          }
        >
          <Button
            size={ButtonSize.Small}
            variant={ButtonVariant.Transparent}
            title={`${getLocaleMessageById('app.editor.routerRuleOperator')}: ${
              operators[condition.operator].title
            }`}
          >
            {operators[condition.operator].text}
          </Button>
        </FloatingDropdown>

        <Input
          placeholder={getLocaleMessageById('app.editor.routerRuleValue')}
          withDebounce
          size={InputSize.Small}
          wrapperClassName={utils.flexGrow6}
          value={condition.value || ''}
          onChange={({ value }) => onChange({ ...condition, value: value ?? null })}
          disabled={disabled}
          canClearBehavior={CanClearBehavior.Value}
          isInvalid={!condition.value && !!invalidReasons?.conditionsRequired}
          message={
            !condition.value &&
            !!invalidReasons?.conditionsRequired && (
              <InputErrorMessage>{invalidReasons?.conditionsRequired}</InputErrorMessage>
            )
          }
        />
      </div>

      <div className={grids.col1}>
        <IconButton onClick={onDelete} disabled={disabled} size="Small">
          <IconDelete />
        </IconButton>
      </div>
    </div>
  );
};

export default Condition;
