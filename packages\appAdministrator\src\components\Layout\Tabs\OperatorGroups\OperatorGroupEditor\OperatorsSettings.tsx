import React from 'react';

import clsx from 'clsx';

import { <PERSON><PERSON>, ButtonVariant, OverlayLoader, utils } from '@product.front/ui-kit';

import IconDropLeft from '@product.front/icons/dist/icons17/MainStuff/IconDropLeft';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';

import { OperatorGroup, OperatorViewBase } from '../../../../../@types/generated/administration';
import { IFrontOperatorBase } from '../../../../../@types/operator';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { getOperators } from '../../../../../services/operators';

import OperatorsTable from './OperatorsTable';

import styles from './styles.module.scss';

export interface IOperatorWithChecked extends IFrontOperatorBase {
  checked: boolean;
  priority?: number;
}

interface IFrontOperatorWithPriority extends IFrontOperatorBase {
  priority?: number;
  checked: boolean;
}
interface IOperatorSettingsProps {
  operators: Partial<IOperatorWithChecked>[];
  onChangeOperators: (operators: IFrontOperatorWithPriority[]) => void;
  withPriority?: boolean;
  validationResult?: { isErrored: boolean; operators: string };
}

const OperatorsSettings = ({
  operators,
  onChangeOperators,
  withPriority = false,
  validationResult,
}: IOperatorSettingsProps) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();

  const [allOperators, setAllOperators] = React.useState<IFrontOperatorBase[]>([]);

  const [availableOperators, setAvailableOperators] = React.useState<IOperatorWithChecked[]>([]);
  const [selectedOperators, setSelectedOperators] = React.useState<IOperatorWithChecked[]>([]);

  React.useEffect(() => {
    const fetchOperators = async () => {
      const mapOperatorGroupsDtoToFront = ({ id, name }: OperatorGroup) => ({ id, name });
      try {
        setLoading(true);
        setError(undefined);
        const operatorsDto = await getOperators();

        const mapOperatorDtoToFrontWithChecked = (
          operator: OperatorViewBase,
        ): IFrontOperatorBase => ({
          id: operator.id,
          login: operator.login ?? '',
          lastName: operator.lastName ?? '',
          firstName: operator.firstName ?? '',
          middleName: operator.middleName ?? '',
          groups: operator.groups?.map(mapOperatorGroupsDtoToFront) ?? [],
        });

        setAllOperators(operatorsDto.map(mapOperatorDtoToFrontWithChecked));
      } catch (err) {
        console.error('Get operators for OperatorSettings error', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchOperators();
  }, []);

  React.useEffect(() => {
    if (!allOperators.length) return;

    const getOperatorPriority = (operator: OperatorViewBase) =>
      operators.find(({ id }) => id === operator.id)?.priority ?? 0;

    const getOperatorChecked = (operator: OperatorViewBase) =>
      !!operators.find(({ id }) => id === operator.id)?.checked;

    const filterAvailableOperators = (operator: OperatorViewBase) =>
      !operators.find(({ id }) => id === operator.id);

    const filterSelectedOperators = (operator: OperatorViewBase) =>
      operators.find(({ id }) => id === operator.id);

    const mapOperatorDtoToFrontWithChecked = (
      operator: IFrontOperatorBase,
    ): IOperatorWithChecked => ({
      ...operator,
      checked: getOperatorChecked(operator),
      priority: getOperatorPriority(operator),
    });

    setAvailableOperators(
      allOperators.filter(filterAvailableOperators).map(mapOperatorDtoToFrontWithChecked),
    );
    setSelectedOperators(
      allOperators.filter(filterSelectedOperators).map(mapOperatorDtoToFrontWithChecked),
    );
  }, [allOperators, operators]);

  const handleChange = (newOperators: IOperatorWithChecked[]) => {
    setSelectedOperators(newOperators);
    onChangeOperators(
      newOperators.map((operator) => ({
        id: operator.id,
        login: operator.login,
        lastName: operator.lastName,
        firstName: operator.firstName,
        middleName: operator.middleName,
        groups: operator.groups,
        priority: operator.priority,
        checked: !!operator.checked,
      })),
    );
  };

  return (
    <OverlayLoader
      loading={loading}
      wrapperClassName={clsx(utils.dGrid, utils.h100, styles.operatorsWrapper)}
    >
      <OperatorsTable
        header={getLocaleMessageById('operatorGroups.modal.form.availableOperators')}
        data={availableOperators}
        onChange={(newOperators) => setAvailableOperators(newOperators)}
      />
      <div
        className={clsx(
          utils.dFlex,
          utils.flexColumn,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
          utils.gap2,
          utils.pX4,
        )}
      >
        <Button
          className={clsx(utils.pX0, styles.moveButton)}
          variant={ButtonVariant.Secondary}
          onClick={() => {
            const operatorsToTransfer = availableOperators
              .filter((operator) => operator.checked)
              .map((operator) => ({ ...operator, checked: false }));

            setAvailableOperators(availableOperators.filter((operator) => !operator.checked));

            handleChange([...selectedOperators, ...operatorsToTransfer]);
          }}
          disabled={!availableOperators.some((operator) => operator.checked)}
        >
          <IconDropRight />
        </Button>
        <Button
          className={clsx(utils.pX0, styles.moveButton)}
          variant={ButtonVariant.Secondary}
          onClick={() => {
            const operatorsToTransfer = selectedOperators
              .filter((operator) => operator.checked)
              .map((operator) => ({ ...operator, checked: false }));
            setAvailableOperators([...availableOperators, ...operatorsToTransfer]);
            handleChange(selectedOperators.filter((operator) => !operator.checked));
          }}
          disabled={!selectedOperators.some((operator) => operator.checked)}
        >
          <IconDropLeft />
        </Button>
      </div>
      <OperatorsTable
        header={getLocaleMessageById('operatorGroups.modal.form.selectedOperators')}
        data={selectedOperators}
        onChange={(newOperators) => {
          handleChange(newOperators);
        }}
        withPriority={withPriority}
        error={validationResult?.operators}
      />
      {error && (
        <AlertError
          className={utils.flexGrow1}
          header={getLocaleMessageById('operators.get.error')}
          error={error}
        />
      )}
    </OverlayLoader>
  );
};

export default OperatorsSettings;
