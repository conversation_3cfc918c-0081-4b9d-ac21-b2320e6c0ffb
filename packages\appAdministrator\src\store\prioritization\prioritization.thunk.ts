import { createAsyncThunk } from '@reduxjs/toolkit';

import { IFrontPrioritizationRule } from '../../@types/prioritization';
import {
  mapPrioritizationRuleDtoToFront,
  mapPrioritizationRuleFrontToDto,
} from '../../mappers/prioritization';
import * as api from '../../services/prioritization';

export const getAllPrioritizationRules = createAsyncThunk('prioritization/all', async () =>
  (await api.getPrioritizationRules()).map(mapPrioritizationRuleDtoToFront),
);

export const savePrioritizationRule = createAsyncThunk(
  'prioritization/update',
  async (rule: IFrontPrioritizationRule) => {
    await (rule.id
      ? api.updatePrioritizationRule(rule.id, mapPrioritizationRuleFrontToDto(rule))
      : api.createPrioritizationRule(mapPrioritizationRuleFrontToDto(rule)));
  },
);
