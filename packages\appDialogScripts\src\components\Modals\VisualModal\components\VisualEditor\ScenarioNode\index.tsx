import React from 'react';

import clsx from 'clsx';
import { Handle, NodeProps, Position } from 'reactflow';

import { colors, Colors, Menu, MenuItem, Text, TextVariant, utils } from '@product.front/ui-kit';

import IconExit from '@product.front/icons/dist/icons17/MainStuff/IconExit';

import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';

import { getNoNameNameForStep } from '../../../../../../helpers/stepListHelper';
import Status from '../../../../../Body/components/Status';
import { nodeMinHeight, nodeMinWidth } from '../../../const/nodeSizes';
import { getNodeTargetId } from '../../../helpers/idsHelper';
import { NodeWithStepData } from '../../../types/scriptDialogsVisualEditorTypes';
import StepDescription from '../StepDescription';
import StepFooter from '../StepFooter';

import styles from './styles.module.scss';

type IScenarioNodeProps = NodeProps<NodeWithStepData>;

const ScenarioNode: React.FC<IScenarioNodeProps> = ({ id, data }) => {
  const hasVariants = data.step?.rules?.length;
  const isActive = data.isSelected;
  const isInvalid = data.step.invalidReasons && Object.entries(data.step.invalidReasons).length;
  return (
    <div
      className={clsx(utils.border, styles.scenarioNode, {
        [styles.invalid]: isInvalid,
        [styles.active]: isActive,
      })}
      style={{ minWidth: nodeMinWidth, minHeight: nodeMinHeight }}
    >
      <header className={clsx(styles.scenarioNodeHeader, !hasVariants && styles.onlyHeader)}>
        <Handle
          type="target"
          position={Position.Left}
          id={getNodeTargetId(id)}
          title={getNodeTargetId(id)}
        />

        <div className={clsx(utils.dFlex, utils.p2)}>
          <div className={clsx(utils.pR2)}>
            <IconExit />
          </div>
          <Text
            color={Colors.HollywoodSmile}
            variant={TextVariant.BodyMedium}
            title={[data.step.name, data.step.description].join('\n')}
            className={styles.headerText}
          >
            {data.step.name || getNoNameNameForStep(data.step)}
          </Text>
        </div>
      </header>
      <section>
        <StepDescription text={data?.step?.description} className={colors.bgMoodBlue10} />
        <Menu>
          <MenuItem key={id} className={clsx(utils.positionRelative)}>
            <Status status={data.step.scenario?.status ?? ScriptStatus.Template} />
          </MenuItem>
        </Menu>
        <StepFooter step={data.step} />
      </section>
    </div>
  );
};

export default React.memo(ScenarioNode);
