import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';

import {
  IFrontOperator,
  IFrontSessionSettings,
  IFrontStatusSettings,
} from '../../../../../@types/operator';
import { IFrontKpiThresholdValue } from '../../../../../@types/parameters';
import { IValidationResultBase } from '../../../../../@types/validation';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

export interface IValidationResult extends IValidationResultBase {
  main: {
    isErrored: boolean;
    login: string;
  };
  sessions: {
    isErrored: boolean;
    maxSessions: string;
    channelsErrors: Partial<Record<UcmmChannel, string>>;
  };
  statuses: {
    isErrored: boolean;
    maxBreakTime: string;
    statusesErrors: Record<string, string>;
  };
  kpi: {
    isErrored: boolean;
    kpiThresholds: Record<string, string>;
  };
}

export const defaultValidationResult: IValidationResult = {
  isErrored: false,
  main: {
    isErrored: false,
    login: '',
  },
  sessions: {
    isErrored: false,
    maxSessions: '',
    channelsErrors: {},
  },
  statuses: {
    isErrored: false,
    maxBreakTime: '',
    statusesErrors: {},
  },
  kpi: {
    isErrored: false,
    kpiThresholds: {},
  },
};

const requiredMessage = getLocaleMessageById('app.common.validation.required');
const validationRangeLocaleId = 'app.common.validation.range';

export const validateSessionSettings = (
  sessionSettings: IFrontSessionSettings,
  validationResult: IValidationResultBase & { sessions: IValidationResult['sessions'] },
) => {
  if (
    sessionSettings.maxSessions &&
    (sessionSettings.maxSessions < 0 || sessionSettings.maxSessions > 100)
  ) {
    validationResult.isErrored = true;
    validationResult.sessions.isErrored = true;
    validationResult.sessions.maxSessions = getLocaleMessageById(validationRangeLocaleId, {
      min: 0,
      max: 100,
    });
  }

  sessionSettings.channelSessions.forEach((channelSession) => {
    if (channelSession.maxSessions <= 0 || channelSession.maxSessions > 100) {
      validationResult.isErrored = true;
      validationResult.sessions.isErrored = true;
      validationResult.sessions.channelsErrors[channelSession.channel] = getLocaleMessageById(
        validationRangeLocaleId,
        {
          min: 1,
          max: 100,
        },
      );
    }
  });
};

export const validateStatusSettings = (
  statusSettings: IFrontStatusSettings,
  validationResult: IValidationResultBase & { statuses: IValidationResult['statuses'] },
) => {
  if (
    statusSettings.maxBreakTime &&
    (statusSettings.maxBreakTime < 0 || statusSettings.maxBreakTime > 24 * 60 * 60)
  ) {
    validationResult.isErrored = true;
    validationResult.statuses.isErrored = true;
    validationResult.statuses.maxBreakTime = getLocaleMessageById(
      'operator.validation.maxBreakTime',
    );
  }

  statusSettings.maxStatusTime.forEach((maxStatusTime) => {
    if (maxStatusTime.maxTime <= 0 || maxStatusTime.maxTime > 24 * 60 * 60) {
      validationResult.isErrored = true;
      validationResult.statuses.isErrored = true;
      validationResult.statuses.statusesErrors[maxStatusTime.status] = getLocaleMessageById(
        'operator.validation.maxStatusBreakTime',
      );
    }
  });
};

export const validateOperator = (
  operator: IFrontOperator,
  kpiThresholds: IFrontKpiThresholdValue[],
) => {
  const validationResult: IValidationResult = {
    ...defaultValidationResult,
    main: { ...defaultValidationResult.main },
    sessions: { ...defaultValidationResult.sessions, channelsErrors: {} },
    statuses: { ...defaultValidationResult.statuses, statusesErrors: {} },
    kpi: { ...defaultValidationResult.kpi, kpiThresholds: {} },
  };

  if (!operator.login) {
    validationResult.isErrored = true;
    validationResult.main.isErrored = true;
    validationResult.main.login = requiredMessage;
  }

  validateSessionSettings(operator.sessionSettings, validationResult);
  validateStatusSettings(operator.statusSettings, validationResult);

  operator.kpiSettings.forEach((kpiSetting) => {
    const kpiThreshold = kpiThresholds.find((kpi) => kpi.code === kpiSetting.code);
    if (!kpiThreshold) return;

    if (
      kpiSetting.target != null &&
      (kpiSetting.target < kpiThreshold.minValue ||
        (kpiThreshold.maxValue && kpiSetting.target > kpiThreshold.maxValue))
    ) {
      validationResult.isErrored = true;
      validationResult.kpi.isErrored = true;
      validationResult.kpi.kpiThresholds[kpiSetting.code] = getLocaleMessageById(
        validationRangeLocaleId,
        {
          min: kpiThreshold.minValue,
          max: kpiThreshold.maxValue ?? '∞',
        },
      );
    }
  });

  return validationResult;
};
