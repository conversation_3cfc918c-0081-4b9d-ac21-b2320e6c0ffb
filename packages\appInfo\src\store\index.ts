import { combineReducers, configureStore } from '@reduxjs/toolkit';

import filterDataSlice from './filterData/filterData.slice';
import kpiSlice from './kpi/kpi.slice';
import operatorSlice from './operator/operator.slice';
import requestsSlice from './requests/requests.slice';

const rootReducer = combineReducers({
  filterData: filterDataSlice,
  kpi: kpiSlice,
  operator: operatorSlice,
  requests: requestsSlice,
});

export const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
