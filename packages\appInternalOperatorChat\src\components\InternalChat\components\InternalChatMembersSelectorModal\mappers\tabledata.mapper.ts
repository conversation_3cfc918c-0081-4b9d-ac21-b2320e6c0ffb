import { IOperatorFotSelect } from '..';
import { getOperatorName } from '../../../../../helpers/operatorHelper';

export const mapOperatorToTableData = (
  operators: IOperatorFotSelect[],
  disableUnselectOperatorId?: string,
) => {
  return operators.map((operator) => {
    const opName = getOperatorName(operator);
    const groups = operator.groups.map(({ name }) => name).join(',\n ');
    return {
      id: operator.id,
      checked: operator.isSelected,
      disabled: disableUnselectOperatorId === operator.id,
      fullName: opName,
      login: operator.login,
      groups: groups,
      active: operator.isSelected,
      operator: operator,
    };
  });
};
