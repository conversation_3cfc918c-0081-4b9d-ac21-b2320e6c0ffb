import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { IFrontSourceData } from '@monorepo/services/src/@types/sourceData';
import { IFrontRequestSubject } from '@monorepo/services/src/CRPMConfiguration/crpmCofiguration.types';

import { IScriptsDialogsTerminalAction } from '../../@types/settings';

export interface IAutomationServicesStore {
  sources: IFrontSourceData[];
  terminalSubjects: IFrontRequestSubject[];
  terminalActions: IScriptsDialogsTerminalAction[];
}

const initialState: IAutomationServicesStore = {
  sources: [],
  terminalSubjects: [],
  terminalActions: [],
};

const externalDataSlice = createSlice({
  name: 'sources',
  initialState,
  reducers: {
    setSources(state, action: PayloadAction<IFrontSourceData[]>) {
      state.sources = action.payload.sort((a, b) => a.label?.localeCompare(b?.label ?? '') ?? 0);
    },
    setTerminalSubjects(
      state,
      action: PayloadAction<IAutomationServicesStore['terminalSubjects']>,
    ) {
      state.terminalSubjects = action.payload;
    },
    setTerminalActions(state, action: PayloadAction<IAutomationServicesStore['terminalActions']>) {
      state.terminalActions = action.payload;
    },
  },
});

export const { setSources, setTerminalSubjects, setTerminalActions } = externalDataSlice.actions;

export default externalDataSlice.reducer;
