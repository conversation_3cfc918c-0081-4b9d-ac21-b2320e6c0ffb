import { createAsyncThunk } from '@reduxjs/toolkit';

import { getCurrentOperatorUsernameString } from '@monorepo/common/src/managers/currentOperatorManager';
import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontScript } from '@monorepo/dialog-scripts/src/@types/script';

import * as api from '../../helpers/apiHelper';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import { notifyErrorModal, notifySuccessModal } from '../../helpers/notifyHelper';
import { RootState } from '../index';
import { setCurrentScript, updateScriptStatus } from '../oneScript/oneScript.slice';

export const getAllScripts = createAsyncThunk('scripts/all', async () => {
  return api.getAllScripts();
});

export const addScript = createAsyncThunk(
  'scripts/add',
  async (script: <PERSON>rontScript, { dispatch, getState }) => {
    try {
      const state = getState() as RootState;
      const scriptToSave: IFrontScript = {
        ...script,
        createdBy: getCurrentOperatorUsernameString({ fallback: 'front' }),
        activeFrom: script.activeFrom ? new Date(script.activeFrom).toISOString() : '',
        activeTo: script.activeTo ? new Date(script.activeTo).toISOString() : '',
      } as IFrontScript;
      const addedScript = await api.addScript(scriptToSave);
      dispatch(getAllScripts());
      state.oneScript.script && dispatch(setCurrentScript(addedScript));
      notifySuccessModal(getLocaleMessageById('app.results.saveSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.saveError'), error);
      throw error;
    }
  },
);

export const updateScript = createAsyncThunk(
  'scripts/update',
  async (script: IFrontScript, { dispatch, getState }) => {
    try {
      const state = getState() as RootState;

      const scriptToSave: IFrontScript = {
        ...script,
        changedBy: getCurrentOperatorUsernameString({ fallback: 'front' }),
        activeFrom: script.activeFrom ? new Date(script.activeFrom).toISOString() : '',
        activeTo: script.activeTo ? new Date(script.activeTo).toISOString() : '',
      } as IFrontScript;
      const updatedScript = await api.updateScript(scriptToSave);
      dispatch(getAllScripts());
      state.oneScript.script && dispatch(setCurrentScript(updatedScript));
      notifySuccessModal(getLocaleMessageById('app.results.saveSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.saveError'), error);
      throw error;
    }
  },
);

export const publishScript = createAsyncThunk(
  'scripts/publish',
  async (scriptId: number, { dispatch, getState }) => {
    try {
      const state = getState() as RootState;

      await api.publishScript(scriptId);
      dispatch(getAllScripts());
      state.oneScript.script &&
        dispatch(updateScriptStatus({ scriptId, status: ScriptStatus.Active }));
      notifySuccessModal(getLocaleMessageById('app.results.publishSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.publishError'), error);
      throw error;
    }
  },
);

export const publishAndSaveScript = createAsyncThunk(
  'scripts/publishAndSave',
  async (script: IFrontScript, { dispatch, getState }) => {
    try {
      if (!script.id) return;

      const state = getState() as RootState;

      const updatedScript = await api.updateScript(script);
      await api.publishScript(script.id);
      dispatch(getAllScripts());
      if (state.oneScript.script) {
        dispatch(setCurrentScript(updatedScript));
        dispatch(updateScriptStatus({ scriptId: updatedScript.id!, status: ScriptStatus.Active }));
      }
      notifySuccessModal(getLocaleMessageById('app.results.publishSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.publishError'), error);
      throw error;
    }
  },
);

export const archiveScript = createAsyncThunk(
  'scripts/archive',
  async (scriptId: number, { dispatch, getState }) => {
    try {
      const state = getState() as RootState;

      await api.archiveScript(scriptId);
      dispatch(getAllScripts());
      state.oneScript.script &&
        dispatch(updateScriptStatus({ scriptId, status: ScriptStatus.Archive }));
      notifySuccessModal(getLocaleMessageById('app.results.archiveSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.archiveError'), error);
      throw error;
    }
  },
);

export const archiveAndSaveScript = createAsyncThunk(
  'scripts/archiveAndSave',
  async (script: IFrontScript, { dispatch, getState }) => {
    try {
      if (!script.id) return;

      const state = getState() as RootState;

      const updatedScript = await api.updateScript(script);
      await api.archiveScript(script.id);
      if (state.oneScript.script) {
        dispatch(setCurrentScript(updatedScript));
        dispatch(updateScriptStatus({ scriptId: updatedScript.id!, status: ScriptStatus.Archive }));
      }
      dispatch(getAllScripts());
      notifySuccessModal(getLocaleMessageById('app.results.archiveSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.archiveError'), error);
      throw error;
    }
  },
);

export const makeScriptUnavailable = createAsyncThunk(
  'scripts/makeUnavailable',
  async (scriptId: number, { dispatch, getState }) => {
    try {
      const state = getState() as RootState;

      await api.makeUnavailable(scriptId);
      dispatch(getAllScripts());
      state.oneScript.script &&
        dispatch(updateScriptStatus({ scriptId, status: ScriptStatus.NotAvailable }));
      notifySuccessModal(getLocaleMessageById('app.results.notAvailableSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.notAvailableError'), error);
      throw error;
    }
  },
);

export const makeUnavailableAndSaveScript = createAsyncThunk(
  'scripts/makeUnavailableAndSave',
  async (script: IFrontScript, { dispatch, getState }) => {
    try {
      if (!script.id) return;

      const state = getState() as RootState;

      const updatedScript = await api.updateScript(script);
      await api.makeUnavailable(script.id);
      if (state.oneScript.script) {
        dispatch(setCurrentScript(updatedScript));
        dispatch(
          updateScriptStatus({ scriptId: updatedScript.id!, status: ScriptStatus.NotAvailable }),
        );
      }
      dispatch(getAllScripts());
      notifySuccessModal(getLocaleMessageById('app.results.notAvailableSuccess'));
      return;
    } catch (error) {
      console.error(error);
      notifyErrorModal(getLocaleMessageById('app.results.notAvailableError'), error);
      throw error;
    }
  },
);
