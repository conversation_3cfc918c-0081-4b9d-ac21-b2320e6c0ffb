import React from 'react';

import clsx from 'clsx';

import { utils } from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconFolderAdd from '@product.front/icons/dist/icons17/MainStuff/IconFolderAdd';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';
import IconDocAdd from '@product.front/icons/dist/icons17/Person&Doc/IconDocAdd';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IFrontFolder } from '../../../../../@types/files';
import { isFolder, normalizePathForApi } from '../../../../../helpers/files';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { deleteTrustedFileAsync, deleteTrustedFolderAsync } from '../../../../../services/files';
import { selectDisplayedFolder } from '../../../../../store/files/files.slice';
import { getFoldersAndFiles } from '../../../../../store/files/files.thunk';
import {
  useAdministratorAppDispatch,
  useAdministratorAppSelector,
} from '../../../../../store/hooks';
import AdmToolbarIconButton from '../../../AdmToolbarIconButton';

import { addOrEditFileModal } from './modal/FileAddOrEditModal';
import { addOrEditFolderModal } from './modal/FolderAddOrEditModal';

const FilesToolbar: React.FC = () => {
  const dispatch = useAdministratorAppDispatch();
  const { foldersAndFiles, displayedFolder, selectedItem } = useAdministratorAppSelector(
    (store) => store.files,
  );

  const toolbarRef = React.useRef<HTMLDivElement>(null);

  const [isFolderCreating, setFolderIsCreating] = React.useState(false);
  const [isFileCreating, setFileIsCreating] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const canAddFolder = !!displayedFolder;
  const canAddFile = !!displayedFolder;
  const canEdit = !!selectedItem;
  const canDelete = !!selectedItem;

  const handleAddFolder = React.useCallback(() => {
    setFolderIsCreating(true);
    addOrEditFolderModal({
      filesAndFolders: foldersAndFiles,
      contextFolderPath: displayedFolder?.path ?? '/',
      onSuccess: (x) => {
        dispatch(getFoldersAndFiles());
        dispatch(selectDisplayedFolder(x));
        setFolderIsCreating(false);
      },
      onClose: () => {
        setFolderIsCreating(false);
      },
    });
  }, [dispatch, displayedFolder?.path, foldersAndFiles]);

  const handleAddFile = React.useCallback(() => {
    setFileIsCreating(true);
    addOrEditFileModal({
      filesAndFolders: foldersAndFiles,
      contextFolderPath: displayedFolder?.path ?? '/',
      onSuccess: () => {
        dispatch(getFoldersAndFiles());
        setFileIsCreating(false);
      },
      onClose: () => {
        setFileIsCreating(false);
      },
    });
  }, [dispatch, displayedFolder?.path, foldersAndFiles]);

  const handleEdit = React.useCallback(() => {
    if (!selectedItem) return;

    setIsEditing(true);

    if (isFolder(selectedItem)) {
      addOrEditFolderModal({
        filesAndFolders: foldersAndFiles,
        folder: selectedItem,
        contextFolderPath: displayedFolder?.path ?? '/',
        onSuccess: (x) => {
          dispatch(getFoldersAndFiles());
          dispatch(selectDisplayedFolder(x));
          setIsEditing(false);
        },
        onClose: () => {
          setIsEditing(false);
        },
      });
    } else {
      addOrEditFileModal({
        filesAndFolders: foldersAndFiles,
        file: selectedItem,
        contextFolderPath: displayedFolder?.path ?? '/',
        onSuccess: () => {
          dispatch(getFoldersAndFiles());
          setIsEditing(false);
        },
        onClose: () => {
          setIsEditing(false);
        },
      });
    }
  }, [dispatch, displayedFolder?.path, foldersAndFiles, selectedItem]);

  const handleDelete = React.useCallback(() => {
    if (!selectedItem) return;

    setIsDeleting(true);

    const getModalHeaderLocaleId = (item: IFrontFolder) =>
      item.items.length ? 'files.modal.delete.notEmptyFolder' : 'files.modal.delete.folder';

    showConfirmModal({
      header: isFolder(selectedItem)
        ? getLocaleMessageById(getModalHeaderLocaleId(selectedItem), { folder: selectedItem.name })
        : getLocaleMessageById('files.modal.delete.file', { file: selectedItem.name }),
      onConfirm: async () => {
        if (isFolder(selectedItem)) {
          await deleteTrustedFolderAsync(normalizePathForApi(selectedItem.path));
        } else {
          await deleteTrustedFileAsync(normalizePathForApi(selectedItem.path));
        }
        await dispatch(getFoldersAndFiles()).unwrap();
        setIsDeleting(false);
      },
      onCancel: () => setIsDeleting(false),
    });
  }, [dispatch, selectedItem]);

  React.useEffect(() => {
    const toolbar = toolbarRef.current;
    if (!toolbar) return;

    const tabContent = toolbar.closest('.qa-administration-files-tab') as HTMLDivElement;
    if (!tabContent) return;

    const handleKeydown = (e: KeyboardEvent) => {
      if (canEdit && e.key === 'F2') {
        e.preventDefault();
        handleEdit();
      } else if (canDelete && e.key === 'Delete') {
        e.preventDefault();
        handleDelete();
      } else if (canAddFolder && e.code === 'KeyN' && e.altKey && e.shiftKey) {
        e.preventDefault();
        handleAddFolder();
      } else if (canAddFile && e.code === 'KeyN' && e.altKey) {
        e.preventDefault();
        handleAddFile();
      }
    };

    tabContent.addEventListener('keydown', handleKeydown);

    return () => {
      tabContent.removeEventListener('keydown', handleKeydown);
    };
  }, [
    canEdit,
    canDelete,
    canAddFile,
    canAddFolder,
    handleEdit,
    handleDelete,
    handleAddFolder,
    handleAddFile,
  ]);

  return (
    <aside className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)} ref={toolbarRef}>
      <AdmToolbarIconButton
        tooltip={getLocaleMessageById('files.action.delete')}
        hotkey={['Del']}
        disabled={!canDelete}
        onClick={handleDelete}
        loading={isDeleting}
      >
        <IconTrash />
      </AdmToolbarIconButton>

      <AdmToolbarIconButton
        tooltip={getLocaleMessageById('files.action.edit')}
        hotkey={['F2']}
        disabled={!canEdit}
        onClick={handleEdit}
        loading={isEditing}
      >
        <IconEdit />
      </AdmToolbarIconButton>

      <AdmToolbarIconButton
        tooltip={getLocaleMessageById('files.action.addFolder')}
        hotkey={['Alt', 'Shift', 'N']}
        disabled={!canAddFolder}
        onClick={handleAddFolder}
        loading={isFolderCreating}
      >
        <IconFolderAdd />
      </AdmToolbarIconButton>

      <AdmToolbarIconButton
        tooltip={getLocaleMessageById('files.action.addFile')}
        hotkey={['Alt', 'N']}
        disabled={!canAddFile}
        onClick={handleAddFile}
        loading={isFileCreating}
      >
        <IconDocAdd />
      </AdmToolbarIconButton>
    </aside>
  );
};

export default FilesToolbar;
