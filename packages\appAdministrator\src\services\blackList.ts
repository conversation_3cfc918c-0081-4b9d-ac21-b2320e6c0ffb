import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  AddBlackListAddress,
  BlackListAddress,
  UpdateBlackListAddress,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export async function getBlackListAddresses(includeDeleted = false) {
  const url = `${getSettings().administrationApiUrl}/blacklist`;
  const response = await commonFetch(url, { credentials: 'include' });
  const allAddresses = (await response.json()) as BlackListAddress[];

  return includeDeleted ? allAddresses : allAddresses.filter((a) => !a.dateDeleted);
}

export async function addBlackListAddress(blackListAddress: AddBlackListAddress) {
  const url = `${getSettings().administrationApiUrl}/blacklist`;
  return await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(blackListAddress),
  });
}

export async function updateBlackListAddress(id: string, blackListAddress: UpdateBlackListAddress) {
  const url = `${getSettings().administrationApiUrl}/blacklist/${id}`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(blackListAddress),
  });
}

export async function deleteBlackListAddress(id: string, deletedBy: string) {
  const url = `${getSettings().administrationApiUrl}/blacklist/${id}?deletedBy=${deletedBy}`;
  return await commonFetch(url, {
    method: 'DELETE',
    credentials: 'include',
  });
}
