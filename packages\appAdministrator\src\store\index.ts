import { combineReducers, configureStore } from '@reduxjs/toolkit';

import autoTemplatesSlice from './autoTemplates/autoTemplates.slice';
import blackListSlice from './blackList/blackList.slice';
import filesSlice from './files/files.slice';
import mailingsSlice from './mailings/mailings.slice';
import negativeWordsSlice from './negativeWords/negativeWords.slice';
import operatorGroupsSlice from './operatorGroups/operatorGroups.slice';
import operatorsSlice from './operators/operators.slice';
import personalTemplatesSlice from './personalTemplates/personalTemplates.slice';
import prioritizationSlice from './prioritization/prioritization.slice';
import queuesSlice from './queues/queues.slice';
import spamWordsSlice from './spamWords/spamWords.slice';
import subjectsSlice from './subjects/subjects.slice';
import templatesSlice from './templates/templates.slice';
import userSlice from './user/user.slice';

const rootReducer = combineReducers({
  subjects: subjectsSlice,
  spamWords: spamWordsSlice,
  negativeWords: negativeWordsSlice,
  templates: templatesSlice,
  personalTemplates: personalTemplatesSlice,
  autoTemplates: autoTemplatesSlice,
  user: userSlice,
  operatorGroups: operatorGroupsSlice,
  operators: operatorsSlice,
  blackList: blackListSlice,
  queues: queuesSlice,
  prioritization: prioritizationSlice,
  files: filesSlice,
  mailings: mailingsSlice,
});

export const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
