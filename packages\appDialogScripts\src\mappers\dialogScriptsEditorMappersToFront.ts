import { helpers } from '@product.front/ui-kit';

import { IFrontAttachmentWithRealFile } from '@monorepo/common/src/@types/frontendChat';
import { getFormattedDateTime } from '@monorepo/common/src/helpers/dateHelper';
import {
  AnswerDto,
  AttachmentDto,
  AutomationServiceParamDto,
  BffScriptDto,
  ProductActionDto,
  ConditionOperations,
  RouterConditionDto,
  RouterRuleDto,
  ScriptAutomationServiceDto,
  ScriptDto,
  ScriptRouterDto,
  ScriptStatisticsDto,
  ScriptStatus,
  ScriptVariableDto,
  ServiceParamTypes,
  StepDto,
  StepType,
  SubScriptDto,
  RatingStepDto,
  AnswerTypes,
  ProductActionParameterDto,
  FileVariable,
  ScenarioStepDto,
  IdNameScriptDto,
  ChoiceStepAnswerDto,
} from '@monorepo/dialog-scripts/src/@types/generated/scripts';

import {
  IFrontVariable,
  FrontConditionOperator,
  FrontStepType,
  IChoiceStepAnswer,
  IFrontAnswer,
  IFrontBffScript,
  IFrontRule,
  IFrontRuleCondition,
  IFrontScript,
  IFrontServiceParameter,
  IFrontStatistics,
  IFrontStep,
  IFileVariable,
  IIdNameScript,
  IVariableCode,
} from '../@types/script';
import { defineFirstScriptDialogStepCodeDto } from '../helpers/scriptDialogHelper';

export const getIsoDateFrontFormattedString = (value: string) => {
  if (!value.includes(' ')) return '';

  const [date, time] = value.split(' ');
  return `${date.replaceAll('.', '-')}T${time}`;
};

export const getDateTimeString = (date?: string | null) => {
  if (!date) return '';

  return getFormattedDateTime(new Date(date), 'YYYY.MM.DD hh:mm:ss');
};

// * BFF model
export const mapScriptStatisticsDtoToFront = (
  scriptStatisticsDto?: ScriptStatisticsDto,
): IFrontStatistics => ({
  rating: scriptStatisticsDto?.rating ?? undefined,
  runs: scriptStatisticsDto?.runs ?? undefined,
  aborted: scriptStatisticsDto?.aborted ?? undefined,
});

export const mapScriptVariablesDtoToFront = (
  scriptVariablesDto?: Record<string, ScriptVariableDto>,
): Record<string, IFrontVariable> => {
  if (!scriptVariablesDto) return {};

  const result: Record<string, IFrontVariable> = {};
  Object.keys(scriptVariablesDto).forEach((variableCode) => {
    result[variableCode] = {
      name: scriptVariablesDto[variableCode].name ?? '',
      source: scriptVariablesDto[variableCode].source ?? '',
    };
  });

  return result;
};

export const mapDependentScriptsDtoToFront = (
  dependentScripts: IdNameScriptDto[] | null | undefined,
): IIdNameScript[] => {
  if (!dependentScripts) return [];

  return dependentScripts.map<IIdNameScript>((ds) => ({
    id: ds.id ?? 0,
    name: ds.name ?? '',
  }));
};

export const mapBffScriptToFront = (scriptDto: BffScriptDto): IFrontBffScript => ({
  id: scriptDto.id,
  code: scriptDto.code ?? '',
  name: scriptDto.name ?? '',
  description: scriptDto.description ?? '',
  status: scriptDto.scriptStatus ?? ScriptStatus.Template,
  createdBy: scriptDto.createdBy ?? '',
  changedBy: scriptDto.lastUpdateBy ?? '',
  createdDate: scriptDto.timeCreated ?? '',
  changedDate: scriptDto.timeLastUpdate ?? '',
  scriptStatistics: mapScriptStatisticsDtoToFront(scriptDto.scriptStatistics),
  activeFrom: scriptDto.activateFrom ?? '',
  activeTo: scriptDto.activateTo ?? '',
  canBeAutomated: scriptDto.canBeAutomated ?? false,
  tags: scriptDto.scriptKeywords ?? [],
  scriptVariables: mapScriptVariablesDtoToFront(scriptDto.scriptVariables),
  priority: scriptDto.priority,
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  isActive: !!scriptDto?.isActiveBot,
  dependentScripts: mapDependentScriptsDtoToFront(scriptDto.dependentScripts),
});

// * DTO to Front models

const mapAnswerDtoToFront = (answerDto: AnswerDto, index: number): IFrontAnswer => ({
  id: answerDto.id ?? '',
  text: answerDto.valueText ?? '',
  transferTo: answerDto.nextStepCode ?? '',
  order: answerDto.order === index ? answerDto.order : index,
});

const mapChoiceStepAnswerDtoToFront = (answerDto: ChoiceStepAnswerDto): IChoiceStepAnswer => ({
  nextStepCode: answerDto.nextStepCode,
  variableArrayCode: answerDto.variableArrayCode,
  keyValueButton: answerDto.keyValueButton,
  displayTextVariant: answerDto.displayTextVariant,
});

export const mapStepTypeToFrontStepType = (backStepType?: StepType): FrontStepType => {
  if (backStepType === StepType.Router) {
    return FrontStepType.Router;
  }

  if (backStepType === StepType.Service) {
    return FrontStepType.Service;
  }
  if (backStepType === StepType.SubScript) {
    return FrontStepType.Subscript;
  }
  if (backStepType === StepType.Scenario) {
    return FrontStepType.Scenario;
  }
  if (backStepType === StepType.ProductAction) {
    return FrontStepType.Terminal;
  }
  if (backStepType === StepType.Rating) {
    return FrontStepType.Rating;
  }
  return FrontStepType.Step;
};

export const mapConditionOperationToFront = (
  operator: ConditionOperations,
): FrontConditionOperator => {
  const map = {
    [ConditionOperations.Eq]: FrontConditionOperator.Eq,
    [ConditionOperations.NotEq]: FrontConditionOperator.NotEq,
    [ConditionOperations.Lt]: FrontConditionOperator.Lt,
    [ConditionOperations.Gt]: FrontConditionOperator.Gt,
    [ConditionOperations.LtEq]: FrontConditionOperator.LtEq,
    [ConditionOperations.GtEq]: FrontConditionOperator.GtEq,
    [ConditionOperations.Contain]: FrontConditionOperator.Contain,
    [ConditionOperations.NotContain]: FrontConditionOperator.NotContain,
  };

  return map[operator];
};

const mapRouterConditionDtoToFront = (
  backRouterCondition?: RouterConditionDto,
): IFrontRuleCondition => ({
  id: backRouterCondition?.id ?? '',
  operator: mapConditionOperationToFront(backRouterCondition?.operation || ConditionOperations.Eq),
  value: backRouterCondition?.rightPartValue,
  variable: backRouterCondition?.leftPartVariableCode,
});

const mapRouterRuleDtoToFront = (backRouterRule?: RouterRuleDto): IFrontRule => ({
  id: backRouterRule?.id ?? '',
  name: backRouterRule?.name ?? '',
  transferTo: backRouterRule?.nextStepCode ?? '',
  priority: backRouterRule?.priority,
  conditions: backRouterRule?.conditions?.map(mapRouterConditionDtoToFront) || [],
});

export const mapAttachmentDtoToFront = (
  backAttachment: AttachmentDto,
): IFrontAttachmentWithRealFile => {
  return {
    id: backAttachment?.id?.toString() ?? '',
    name: backAttachment.name ?? '',
    extension: backAttachment.extension ?? '',
    url: '',
    mime: backAttachment.contentType ?? '',
    externalId: backAttachment.externalId ?? '',
    size: backAttachment.fileSize ?? 0,
  };
};

const mapBackServiceParametersToFront = (
  backServiceParameter: AutomationServiceParamDto,
): IFrontServiceParameter => {
  return {
    key: backServiceParameter.serviceParameter.key,
    name: backServiceParameter.serviceParameter.name ?? '',
    description: backServiceParameter.serviceParameter.description ?? '',
    value: backServiceParameter.manualValue ?? '',
    variableCode: backServiceParameter.scriptVariable?.code,
    variableName: backServiceParameter.scriptVariable?.name ?? undefined,
  };
};

const mapFileVariableToFront = (variable: FileVariable, prefix: string): IFileVariable => {
  return {
    _key: helpers.getUniqueId(`${prefix}-`),
    variableCode: variable.variableCode ?? '',
    fileName: variable.fileName ?? null,
  };
};

const mapFileVariablesToFront = (
  fileVariables: FileVariable[] | null,
  prefix: string,
): IFileVariable[] | undefined => {
  return fileVariables?.map((fileVariable) => mapFileVariableToFront(fileVariable, prefix));
};

const mapVariableCodeToFront = (variableCode: string, prefix: string): IVariableCode => {
  return {
    _key: helpers.getUniqueId(`${prefix}-`),
    variableCode: variableCode,
  };
};

const mapVariableCodesToFront = (
  variableCodes: string[] | null,
  prefix: string,
): IVariableCode[] | undefined => {
  return variableCodes?.map((code) => mapVariableCodeToFront(code, prefix));
};

const mapStepDtoToFront = (
  stepDto: StepDto | ScriptRouterDto | SubScriptDto | ScriptAutomationServiceDto | ProductActionDto,
  stepCode?: string,
  scriptVariables?: Record<string, ScriptVariableDto>,
  firstStepCode?: string | null,
  // eslint-disable-next-line sonarjs/cognitive-complexity
): IFrontStep => {
  const commonPart = {
    type: mapStepTypeToFrontStepType(stepDto.stepType),
    id: undefined, // Раньше был stepDto.id Ваня говорит что больше не нужен
    code: stepCode ?? '',
    name: stepDto.name ?? '',
    description: stepDto.description ?? '',
    positionX: stepDto.designerProps?.coordX,
    positionY: stepDto.designerProps?.coordY,
    stepTransfer: stepDto.nextStepCode || '',
    isFirstStep: firstStepCode === stepCode,
  };

  const specificPart = {};

  if (stepDto.stepType === StepType.Step) {
    const step = stepDto as StepDto;
    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      answerDisplayType: step.answersType,
      choiceStepAnswer: step?.choiceStepAnswer
        ? mapChoiceStepAnswerDtoToFront(step.choiceStepAnswer)
        : undefined,
      answers:
        step.answers
          ?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
          .map((answer, index) => mapAnswerDtoToFront(answer, index)) ?? [],
      variable: step.variableCode ?? undefined,
      limit: step.limit,
      timeout: step.timeout,
      variableName: step.variableCode
        ? scriptVariables?.[step.variableCode]?.name || step.variableCode
        : '',
      variableSource: step.variableCode
        ? (scriptVariables?.[step.variableCode].source ?? undefined)
        : undefined,
      attachments: step.attachments?.map(mapAttachmentDtoToFront),
      promptUrl: step.promptUrl ?? undefined,
      isSkippable: !!step.isSkippable,
      limitStepTransfer: step.limitStepCode ?? undefined,
      timeoutStepTransfer: step.timeoutStepCode ?? undefined,
      isBackButtonAvailable: step.isBackButtonAvailable ?? false,
      attachmentVariableCodes:
        mapVariableCodesToFront(step.attachmentVariableCodes ?? null, 'attachmentVariable') ?? [],
    });
  }
  if (stepDto.stepType === StepType.Rating) {
    const step = stepDto as RatingStepDto;

    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      answerDisplayType: AnswerTypes.Button,
      answers: ((step.answers || []) as AnswerDto[])
        .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
        .map((answer, index) => mapAnswerDtoToFront(answer, index)),
      variable: step.variableCode ?? undefined,
      variableName: step.variableCode
        ? scriptVariables?.[step.variableCode]?.name || step.variableCode
        : '',
      isSkippable: !!step.isSkippable,
      isBackButtonAvailable: step.isBackButtonAvailable ?? false,
    });
  }
  if (stepDto.stepType === StepType.Router) {
    const router = stepDto as ScriptRouterDto;
    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      rules:
        ((router.rules ?? []) as RouterRuleDto[])
          .map(mapRouterRuleDtoToFront)
          .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0)) || [],
    });
  }

  if (stepDto.stepType === StepType.Service) {
    const service = stepDto as ScriptAutomationServiceDto;
    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      automationServiceId: service.serviceId,
      serviceParameters: ((service.serviceParameters ?? []) as AutomationServiceParamDto[])
        .filter((param) => param.serviceParameter.type === ServiceParamTypes.Input)
        .map(mapBackServiceParametersToFront),
      serviceOutputParameters: ((service.serviceParameters ?? []) as AutomationServiceParamDto[])
        .filter((param) => param.serviceParameter.type === ServiceParamTypes.Output)
        .map(mapBackServiceParametersToFront),
      stepFallbackTransfer: service.nextFailStepCode ?? undefined,
      files: mapFileVariablesToFront(service.fileVariables ?? null, 'automationServiceFile') ?? [],
    });
  }

  if (stepDto.stepType === StepType.SubScript) {
    const ss = stepDto as SubScriptDto;
    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      name: ss.scriptName || '',
      description: ss.scriptDescription || '',
      subscript: {
        id: ss.scriptId || undefined,
        name: ss.scriptName || '',
        description: ss.scriptDescription || '',
        status: ss.scriptStatus || ScriptStatus.Template,
        code: ss.scriptCode || '',
      },
    });
  }

  if (stepDto.stepType === StepType.Scenario) {
    const ss = stepDto as ScenarioStepDto;
    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      name: ss.scriptName || '',
      description: ss.scriptDescription || '',
      scenario: {
        id: ss.scriptId || undefined,
        name: ss.scriptName || '',
        description: ss.scriptDescription || '',
        status: ss.scriptStatus || ScriptStatus.Template,
        code: ss.scriptCode || '',
        activeFrom: ss.scripActivateFrom || '',
        activeTo: ss.scripActivateFrom || '',
      },
    });
  }

  if (stepDto.stepType === StepType.ProductAction) {
    const terminalStep = stepDto as ProductActionDto;
    Object.assign<Partial<IFrontStep>, Partial<IFrontStep>>(specificPart, {
      terminalUpdatesMap:
        ((terminalStep.productActionParameters ?? []) as ProductActionParameterDto[]).reduce<
          Record<string, string>
        >((acc, val) => ({ ...acc, [val.source ?? '']: val.scriptVariableCode ?? '' }), {}) ?? {},
      terminalAction: terminalStep.requestAction ?? undefined,
      terminalSubject: terminalStep.requestThemeCode ?? undefined,
    });
  }

  return { ...commonPart, ...specificPart };
};

export const mapScriptDtoToFront = (scriptDto: ScriptDto): IFrontScript => {
  const firstStepCode =
    scriptDto.startingStepCode || defineFirstScriptDialogStepCodeDto(scriptDto.stepsMap);

  return {
    name: scriptDto.name ?? '-',
    id: scriptDto.id,
    code: scriptDto.code ?? '-',
    description: scriptDto.description ?? '-',
    status: scriptDto.scriptStatus ?? ScriptStatus.Template,
    createdBy: scriptDto.createdBy ?? '-',
    changedBy: scriptDto.lastUpdateBy ?? '-',
    changedDate: scriptDto.timeLastUpdate ?? '',
    createdDate: scriptDto.timeCreated ?? '',
    activeFrom: scriptDto.activateFrom ?? '',
    activeTo: scriptDto.activateTo ?? '',
    steps: scriptDto.stepsMap
      ? Object.keys(scriptDto.stepsMap).map((stepCode) =>
          mapStepDtoToFront(
            scriptDto.stepsMap![stepCode],
            stepCode,
            scriptDto.scriptVariables || {},
            firstStepCode,
          ),
        )
      : [],
    tags: scriptDto.scriptKeywords ?? [],
    canBeAutomated: scriptDto.canBeAutomated ?? false,
    scriptVariables: scriptDto.scriptVariables || {},
    priority: scriptDto.priority,
    dependentScripts: mapDependentScriptsDtoToFront(scriptDto.dependentScripts),
  };
};

export const mapFileToIFrontAttachmentWithRealFile = (file: File): IFrontAttachmentWithRealFile => {
  return {
    name: file.name,
    id: '',
    url: '',
    contentId: '',
    isInlined: false,
    mime: file.type,
    size: file.size,
    extension: helpers.getExtensionByFileName(file.name) || '',
    externalId: '',
    file: file,
  };
};
