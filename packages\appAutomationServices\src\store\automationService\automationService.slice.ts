import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { generateCodeByString } from '@monorepo/common/src/helpers/codeGeneratorHelper';

import {
  AutomationServiceAuthType,
  AutomationServiceRequestBodyType,
  AutomationServiceRequestType,
  AutomationServiceResponseBodyType,
  AutomationServiceStatus,
  AutomationServiceType,
  IFrontAutomationService,
  IFrontAutomationServiceInvalidReasons,
} from '../../@types/automationService.types';
import {
  getInputVariablesFromJSCodeString,
  getOutputVariablesFromJSCodeString,
} from '../../helpers/scriptCodeJS/scriptCodeJSHelper';
import { getVariablesFromString } from '../../helpers/stringVariablesHelper';

import oneScriptExtraReducers from './automationService.extraReducers';

export const emptyAutomationService: IFrontAutomationService = {
  name: '',
  code: '',
  status: AutomationServiceStatus.Draft,
  description: '',
  system: null,
  type: AutomationServiceType.REST,
  authType: AutomationServiceAuthType.Basic,
  authLogin: '',
  authPassword: '',
  authToken: '',
  authHeaderName: '',
  authHeaderValue: '',
  requestType: AutomationServiceRequestType.POST,
  requestUrl: '',
  requestHeaders: [],
  requestBodyType: AutomationServiceRequestBodyType.Text,
  requestBody: '',
  requestParameters: [],
  responseParameters: [],
  responseBodyType: AutomationServiceResponseBodyType.Text,
  responseBody: '',
  createdBy: '',
  createdDate: '',
  changedBy: '',
  changedDate: '',
  publishedBy: '',
  publishedDate: '',
  canUploadFiles: false,
};

export interface IAutomationServiceStore {
  automationService: IFrontAutomationService | null;
  initialAutomationService: IFrontAutomationService | null; // Чтобы сравнивать что сейчас с тем что было при открытии (setCurrentAutomationService)
  invalidReasons: IFrontAutomationServiceInvalidReasons;
  isSaving: boolean;
  savingError?: SerializedError;
}

const initialState: IAutomationServiceStore = {
  automationService: null,
  initialAutomationService: null,
  invalidReasons: {},
  isSaving: false,
  savingError: undefined,
};

const automationServiceSlice = createSlice({
  name: 'automationService',
  initialState,
  reducers: {
    setCurrentAutomationService(state, action: PayloadAction<IFrontAutomationService | null>) {
      const { payload } = action;

      state.automationService = payload;
      state.initialAutomationService = payload;

      state.isSaving = false;
      state.savingError = undefined;
    },

    updateAutomationService(state, action: PayloadAction<Partial<IFrontAutomationService>>) {
      if (!state.automationService) {
        throw new Error('Update automationService on null automationService failed');
      }

      const newAutomationService = { ...state.automationService, ...action.payload };

      if (
        typeof action.payload.name !== 'undefined' &&
        state.automationService.name !== action.payload.name
      ) {
        const oldCode = generateCodeByString(state.automationService.name);
        const newCode = generateCodeByString(action.payload.name);

        if (state.automationService.code === oldCode) {
          newAutomationService.code = newCode;
        }
      }

      if (
        state.automationService.type !== AutomationServiceType.Code &&
        (action.payload.requestUrl || action.payload.requestBody || action.payload.requestHeaders)
      ) {
        let requestKeys = [];
        requestKeys = getVariablesFromString(
          [
            action.payload.requestUrl || state.automationService?.requestUrl,
            action.payload.requestBody || state.automationService?.requestBody,
            JSON.stringify(
              action.payload.requestHeaders || state.automationService?.requestHeaders,
            ),
          ]
            .filter(Boolean)
            .join('\n'),
        );
        newAutomationService.requestParameters = requestKeys.map((k) => ({
          key: k,
          description: '',
          reachDescription: '',
          ...(state.automationService?.requestParameters?.find((f) => f.key === k) || {}),
        }));
      }

      if (
        state.automationService.type === AutomationServiceType.Code &&
        action.payload.requestBody
      ) {
        const jsCode = action.payload.requestBody || state.automationService?.requestBody;
        const inputVariables = getInputVariablesFromJSCodeString(jsCode);
        const outputVariables = getOutputVariablesFromJSCodeString(jsCode);
        newAutomationService.requestParameters = inputVariables.map((k) => ({
          key: k,
          description: '',
          reachDescription: '',
          ...(state.automationService?.requestParameters?.find((f) => f.key === k) || {}),
        }));
        newAutomationService.responseParameters = outputVariables.map((k) => ({
          key: k,
          description: '',
          reachDescription: '',
          ...(state.automationService?.responseParameters?.find((f) => f.key === k) || {}),
        }));
      }

      if (action.payload.responseBodyType) {
        const responseKeys =
          action.payload.responseBodyType === AutomationServiceResponseBodyType.File
            ? ['file']
            : getVariablesFromString(
                action.payload.responseBody || state.automationService?.responseBody,
              );

        newAutomationService.responseParameters = responseKeys.map((k) => ({
          key: k,
          description: '',
          reachDescription: '',
        }));
      }

      if (action.payload.responseBody) {
        const responseKeys = getVariablesFromString(
          action.payload.responseBody || state.automationService?.responseBody,
        );
        newAutomationService.responseParameters = responseKeys.map((k) => ({
          key: k,
          description: '',
          reachDescription: '',
          ...(state.automationService?.responseParameters?.find((f) => f.key === k) || {}),
        }));
      }

      state.automationService = newAutomationService;
    },
    updateAutomationServiceInvalidReasons(
      state,
      action: PayloadAction<Partial<IFrontAutomationServiceInvalidReasons>>,
    ) {
      state.invalidReasons = action.payload;
    },
  },
  extraReducers: oneScriptExtraReducers,
});

export const {
  setCurrentAutomationService,
  updateAutomationService,
  updateAutomationServiceInvalidReasons,
} = automationServiceSlice.actions;

export default automationServiceSlice.reducer;
