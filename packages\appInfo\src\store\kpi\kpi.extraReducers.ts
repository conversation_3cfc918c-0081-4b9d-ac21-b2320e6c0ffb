import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IKpiStore } from './kpi.slice';
import { getAllKpiData } from './kpi.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<IKpiStore>) =>
  builder
    .addCase(getAllKpiData.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllKpiData.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    })
    .addCase(getAllKpiData.fulfilled, (state, action) => {
      state.loading = false;
      state.error = undefined;
      state.goal = {
        aht: action.payload.alarmSettings.alarmAht,
        processedCount: action.payload.alarmSettings.alarmProcessedCount,
        acsi: action.payload.alarmSettings.alarmAcsi,
        assi: action.payload.alarmSettings.alarmAssi,
        closedCount: action.payload.alarmSettings.alarmClosedCount,
        rt: action.payload.alarmSettings.alarmRt,
        onBreak: action.payload.alarmSettings.alarmOnBreak,
      };

      state.queues = action.payload.operatorQueues.map((value) => {
        const queueSettings = action.payload.kpiSettings.find(
          (setting) => setting.queueId === value.queueId,
        );

        return {
          queueId: value.queueId,
          queueTitle: value.queueTitle,
          awaitingOperator: value.awaitingOperator,
          activeOperators: value.activeOperators,
          aht: value.aht,
          asa: value.asa,
          alarmAht: queueSettings?.alarmAHT ?? 0,
          warnAht: queueSettings?.warningAHT ?? 0,
          alarmAsa: queueSettings?.alarmASA ?? 0,
          warnAsa: queueSettings?.warningASA ?? 0,
        };
      });

      state.operator = {
        aht: action.payload.total.aht ?? 0,
        processedCount: action.payload.total.processedCount ?? 0,
        acsi: action.payload.total.acsi ?? 0,
        assi: action.payload.total.assi ?? 0,
        closedCount: action.payload.total.closedCount ?? 0,
        art: action.payload.total.art ?? 0,
        inLineDuration: action.payload.total.inLineDuration ?? 0,
        inWorkDuration: action.payload.total.inWorkDuration ?? 0,
        callsMissedCount: action.payload.total.callsMissedCount ?? 0,
        conversationTime: action.payload.total.conversationTime ?? 0,
        onBreak: action.payload.total.onBreak ?? 0,
      };

      state.lastUpdateDateTime = new Date().toISOString();
    });

export default extraReducers;
