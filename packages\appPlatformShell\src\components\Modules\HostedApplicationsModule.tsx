﻿import * as React from 'react';

import clsx from 'clsx';
import ReactDOM from 'react-dom/client';
import * as singleSpa from 'single-spa';

import { utils } from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import { getFeatureFlag } from '@monorepo/common/src/helpers/featureFlagsHelper';
import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';
import {
  AppInfo,
  AwpClientTypes,
  HostedApplicationNotificationCountChangedEventArgs,
  HostedApplicationStateChangedEventArgs,
  IAwpConfigurationManager,
} from '@monorepo/common/src/platform/awp-web-interfaces';
import * as CM from '@monorepo/common/src/platform/cm-interfaces';
import './HostedApplicationsModule.css';

import { getLocaleMessageById } from '../../helpers/localeHelper';

import { HostedApplicationsModuleConfig } from './HostedApplicationsModuleConfig';

const applicationIdPrefix = 'single-spa-application:';

const getAppIdByName = (appName: string) => `${applicationIdPrefix}${appName}`;

export type AppAction = {
  name: string;
  argument: string;
};

enum AppRenderMode {
  ZIndex = 'zIndex',
  Display = 'display',
}

const getInactiveAppClassName = (mode: AppRenderMode) =>
  ({
    [AppRenderMode.Display]: 'inactiveApp',
    [AppRenderMode.ZIndex]: 'inactiveAppZ',
  })[mode];

export class HostedApplicationsModule implements AWP.IHostedAppsManager, AWP.IModule {
  shell: AWP.IShell | null = null;
  hostedAppsInstances: { [appName: string]: AWP.IHostedAppAdapter } = {};
  hostedApps: AppInfo[];
  activeAppName: string | null;
  moduleConfiguration: HostedApplicationsModuleConfig | null = null;
  configurationManager: IAwpConfigurationManager | null = null;
  credentialsProvider: CM.ICredentialsProvider | null = null;

  appRenderMode: AppRenderMode = AppRenderMode.Display;

  constructor() {
    this.initialize = this.initialize.bind(this);
    this.doRender = this.doRender.bind(this);
    this.handleSingleSpaError = this.handleSingleSpaError.bind(this);
    this.doLoadApp = this.doLoadApp.bind(this);

    this.getHostedApps = this.getHostedApps.bind(this);
    this.registerHostedAppAdapter = this.registerHostedAppAdapter.bind(this);
    this.getHostedAppAdapter = this.getHostedAppAdapter.bind(this);
    this.getHostedAppInfo = this.getHostedAppInfo.bind(this);
    this.activateApp = this.activateApp.bind(this);
    this.activateAppByComponentName = this.activateAppByComponentName.bind(this);
    this.restartApp = this.restartApp.bind(this);
    this.closeApp = this.closeApp.bind(this);
    this.setAppState = this.setAppState.bind(this);
    this.setAppNotificationCount = this.setAppNotificationCount.bind(this);

    this.hostedApps = [];
    this.activeAppName = '';
    this.appRenderMode = getFeatureFlag('appRenderZIndexMode')
      ? AppRenderMode.ZIndex
      : AppRenderMode.Display;
  }

  canStart(): boolean {
    return this.shell?.currentOperator != null;
  }

  async start(): Promise<void> {
    if (this.shell == null) {
      throw 'Shell not initialized';
    }

    this.shell.serviceResolver.register<AWP.IHostedAppsManager>('IHostedAppsManager', this);

    this.renderLayout();
    if (this.configurationManager != null) {
      await this.configurationManager
        .getHostedApplications(
          AwpClientTypes.Web,
          this.shell.selectedRoleId,
          this.shell.selectedServiceAreaId,
        )
        .then(
          (fetchResult) => {
            this.hostedApps = fetchResult;
          },
          (fetchError) => {
            console.error(`GetHostedApps fetch error: ${fetchError}`);
          },
        )
        .then(() => this.doRender())
        .catch((error) => console.error(error));
    }
  }

  async initialize(shell: AWP.IShell, init: AWP.ModuleInit): Promise<boolean> {
    this.shell = shell;
    this.credentialsProvider =
      this.shell.serviceResolver.resolve<CM.ICredentialsProvider>('ICredentialsProvider');
    this.configurationManager = this.shell.serviceResolver.resolve<IAwpConfigurationManager>(
      'IAwpConfigurationManager',
    );

    //module configuration
    const configurationJson = JSON.parse(init.initString);
    this.moduleConfiguration = Object.assign(
      {},
      configurationJson,
    ) as HostedApplicationsModuleConfig;

    return true;
  }

  registerHostedAppAdapter(adapterInstance: AWP.IHostedAppAdapter): void {
    const appName = adapterInstance.appName;
    if (this.hostedAppsInstances[appName] != null) {
      throw new Error(`Adapter already registered for app name='${appName}'`);
    }
    this.hostedAppsInstances[appName] = adapterInstance;

    const appInfo = this.getHostedAppInfo(appName);

    console.info(
      `MB appInfo:(${appName})`,
      appInfo,
      appInfo?.loginWorkflows,
      appInfo?.credentialsConfigurationName,
    );

    if (appInfo?.loginWorkflows != null && appInfo?.loginWorkflows.length > 0) {
      if (!appInfo.credentialsConfigurationName) {
        console.error(`CredentialsConfigurationName for application '${appName}' not defined`);
        return;
      }

      this.credentialsProvider
        ?.getCredentials(appInfo.credentialsConfigurationName)
        .then((creds) => {
          try {
            if (appInfo?.loginWorkflows != null) {
              const lf = appInfo.loginWorkflows[0];

              let workflow = lf.workflowDefinition;
              workflow = workflow.replace('{transform:login}', creds.userId);
              for (const [key, value] of Object.entries(creds.credentials)) {
                const regEx = new RegExp(`{transform:${key}}`, 'ig');
                workflow = workflow.replace(regEx, value);
              }

              adapterInstance.executeAction('js-function', {
                body: workflow,
                parameters: null,
              });
            }
          } catch (error) {
            const errText = "App start action error: app='" + appName + "'"; // @todo need to localize
            const errArgs = getNotificationArgsByError(errText, error);
            this.shell?.popupNotificationManager.notifyError(...errArgs);
          }
        });
    }
  }

  async LoginToApp() {}

  getHostedAppInfo(appName: string): AppInfo | undefined {
    return this.hostedApps.find((appInfo) => appInfo.name == appName);
  }

  getHostedAppAdapter(appName: string): AWP.IHostedAppAdapter {
    return this.hostedAppsInstances[appName];
  }

  activateApp(appName: string): void {
    console.info(`Activate App - (${appName})`);

    const newActiveAppId = getAppIdByName(appName);

    if (this.activeAppName === appName) {
      console.info(`Application id=${getAppIdByName(appName)} already active`);
    } else {
      if (this.activeAppName) {
        console.info(`Deactivating Application id=${getAppIdByName(this.activeAppName)}`);
        document
          .getElementById(getAppIdByName(this.activeAppName))
          ?.classList.add(getInactiveAppClassName(this.appRenderMode));
      }

      document
        .getElementById(newActiveAppId)
        ?.classList.remove(getInactiveAppClassName(this.appRenderMode));

      const prevActiveApp = this.activeAppName;
      this.activeAppName = appName;
      this.shell?.eventManager.fire('HostedApplicationActivated', {
        prevActiveApp: prevActiveApp,
        currentActiveApp: appName,
      });
    }
  }

  activateAppByComponentName(appComponentName: AWP.HostedAppComponentName | string): void {
    const application = this.hostedApps.find((app) => {
      app.webClientInitialization.componentName == appComponentName;
    });

    if (!application) {
      throw new Error(`App not found with component "${appComponentName}" not found`);
    }

    this.activateApp(application.name);
  }

  restartApp(appName: string): void {
    throw new Error('Method not implemented.' + appName);
  }

  closeApp(appName: string): void {
    throw new Error('Method not implemented.' + appName);
  }

  setAppNotificationCount(appName: string, notificationCount: number): void {
    const args: HostedApplicationNotificationCountChangedEventArgs = {
      appName: appName,
      notificationCount: notificationCount,
    };

    this.shell?.eventManager.fire('HostedApplicationNotificationCountChanged', args);
  }

  setAppState(appName: string, appState: AWP.HostedAppState): void {
    const args: HostedApplicationStateChangedEventArgs = {
      appName: appName,
      state: appState,
    };

    this.shell?.eventManager.fire('HostedApplicationStateChanged', args);
  }

  getHostedApps(): string[] {
    return this.hostedApps
      .sort((x1, x2) => x1.sortOrder - x2.sortOrder)
      .filter(
        (appInfo) =>
          !appInfo.isService && appInfo.webClientInitialization.displayGroup === 'mainPanel',
      )
      .map((appInfo) => appInfo.name);
  }

  doLoadApp(appInfo: AppInfo): any {
    console.info(
      `Loading app name='${appInfo.name}', type='${appInfo.applicationType}', component name='${appInfo.webClientInitialization?.componentName}', component init=' ${appInfo.webClientInitialization?.componentInit}`,
    );
    if (appInfo.webClientInitialization == null) {
      return null;
    }

    if (appInfo.webClientInitialization.displayGroup.startsWith('HP')) {
      return System.import(`HeaderItems/${appInfo.webClientInitialization.componentName}`).then(
        (module) => {
          return module as any;
        },
      );
    }

    return System.import(`AppAdapters/${appInfo.webClientInitialization.componentName}`).then(
      (module) => {
        return module as any;
      },
    );
  }

  doRender() {
    const defaultAppName = this.moduleConfiguration?.defaultApplicationName;
    console.info('doRender requested with default appName: ', defaultAppName);

    const panelsNames: string[] = [];

    this.hostedApps.forEach((appInfo) => {
      const shell = this.shell;
      const appInit = appInfo.webClientInitialization;
      if (appInit == null) {
        console.warn(`appInfo.webClientInitialization is null for app name='${appInfo.name}'`);
      } else {
        console.info(`Registering single-spa app name='${appInfo.name}' with init='${appInit}'`);

        const initString = appInit.componentInit;
        const appInitData = appInfo.initialization?.appInitData;
        console.info(
          `Registering single-spa app name='${appInfo.name}' with initData`,
          appInitData,
        );

        singleSpa.registerApplication(
          appInfo.name,
          () => this.doLoadApp(appInfo),
          //  activeAt:
          () => true,
          // customProps: { ...globalProps, path, setAppStyle }
          { shell, initString, appInitData },
        );

        if (!panelsNames.includes(appInit.displayGroup)) {
          panelsNames.push(appInit.displayGroup);
        }
      }
    });

    singleSpa.addErrorHandler((err) => this.handleSingleSpaError(err));

    panelsNames.forEach((panelName) => {
      const appsForPanel = this.hostedApps.filter(
        (x) => x.webClientInitialization?.displayGroup == panelName,
      );
      appsForPanel.sort((x1, x2) => x1.sortOrder - x2.sortOrder);

      const firstApp = appsForPanel.find(
        (app) => app.webClientInitialization?.displayGroup == 'mainPanel' && !app.isService,
      );

      let containerPanelElement = document.getElementById(panelName);
      if (containerPanelElement == null) {
        console.error(`Panel '${panelName}' does not exist. Using 'mainPanel' instead`);
        containerPanelElement = document.getElementById('mainPanel');
      }
      if (!containerPanelElement) throw new Error('No element with id mainPanel or ' + panelName);

      const containerPanel = ReactDOM.createRoot(containerPanelElement);

      containerPanel.render(
        <div className={clsx(utils.h100, { stAppHost: !panelName.startsWith('HP') })}>
          {appsForPanel.map((appInfo) => {
            const appName = appInfo.name;
            return (
              <div
                id={getAppIdByName(appName)}
                key={`integratedApp_${appName}`}
                className={clsx({
                  ['app']: panelName === 'mainPanel',
                  [getInactiveAppClassName(this.appRenderMode)]:
                    'mainPanel' === panelName &&
                    (defaultAppName ? appName !== defaultAppName : firstApp?.name !== appName),
                })}
                style={{ height: 'inherit' }}
              ></div>
            );
          })}
        </div>,
      );
      this.activateApp(defaultAppName ?? firstApp?.name ?? '');
    });
  }

  handleSingleSpaError(error: singleSpa.AppError) {
    this.shell?.popupNotificationManager.notifyError(
      error.appOrParcelName,
      getLocaleMessageById('app.shell.app.loadingError'),
      10000,
      true,
      undefined,
      false,
      () => {
        this.activateApp(error.appOrParcelName);
      },
    );

    const spaElement = document.getElementById(applicationIdPrefix + error.appOrParcelName);
    if (!spaElement) {
      throw new Error(`No element with ID ${applicationIdPrefix}` + error.appOrParcelName);
    }

    const spa = ReactDOM.createRoot(spaElement);

    spa.render(
      <div className={clsx(utils.w100, utils.h100, utils.flexCentredBlock)}>
        <AlertError
          header={`${getLocaleMessageById('app.shell.app.loadingError')} «${error.appOrParcelName}»`}
          error={error}
        />
      </div>,
    );
  }

  render() {
    return <div>HAM render method</div>;
  }

  renderLayout() {
    const layoutElement = document.getElementById('layout');
    if (!layoutElement) throw new Error('No element with id layout');

    const layout = ReactDOM.createRoot(layoutElement);
    layout.render(
      <div className="st-apps-host">
        <div className="st-apps-header">
          <div id="topPanel"></div>
        </div>

        <div className="st-app-panels">
          <div id="leftPanel" className="st-app-panel st-app-left-panel"></div>

          <div id="mainPanel" className="st-app-panel st-app-main-panel"></div>

          {/* <div id="rightPanel" className="col-2 st-gray-border"></div> */}
        </div>

        <footer id="bottomPanel" className="st-app-footer">
          bottom
        </footer>
      </div>,
    );
  }
}

export default HostedApplicationsModule;
