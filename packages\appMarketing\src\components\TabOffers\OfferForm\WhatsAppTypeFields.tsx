import React from 'react';

import { CanClearBehavior, grids, Select, Textarea } from '@product.front/ui-kit';

import { TemplateMessage } from '../../../@types/generated/marketing';
import { getLocaleMessageById } from '../../../helpers/localeHelper';

import ErrorMessage from './ErrorMessage';

interface IWhatsAppTypeFields {
  value: string;
  validationResult: { automaticOfferValue: string };
  setValue: (value: string) => void;
  templates: TemplateMessage[];
}

const WhatsAppTypeFields = ({
  value,
  validationResult,
  setValue,
  templates,
}: IWhatsAppTypeFields) => {
  return (
    <div className={grids.row}>
      <Select
        wrapperClassName={grids.col6}
        label={getLocaleMessageById('app.offer.form.input.automaticOfferValue')}
        value={value}
        data={templates.map((template) => ({
          value: template.code ?? 'unknown',
          text: template.displayName ?? 'unknown',
        }))}
        onChange={({ value: newValue }) => setValue(newValue ?? '')}
        message={<ErrorMessage>{validationResult.automaticOfferValue}</ErrorMessage>}
        isInvalid={!!validationResult.automaticOfferValue}
        canClearBehavior={CanClearBehavior.Always}
        required
      />
      <Textarea
        wrapperClassName={grids.col12}
        value={templates.find((template) => template.code === value)?.preview ?? ''}
        label={getLocaleMessageById('app.offer.form.input.automaticOfferValuePreview')}
        rows={14}
        readOnly
      />
    </div>
  );
};

export default WhatsAppTypeFields;
