import React from 'react';

import clsx from 'clsx';
import { Handle, Position } from 'reactflow';

import { Text, utils, colors, Colors } from '@product.front/ui-kit';

import { fictionNodeHeight, fictionNodeWidth } from '../../../const/nodeSizes';
import { getNodeSourceId } from '../../../helpers/idsHelper';

import styles from './styles.module.scss';

function NodeStart({ id }: any) {
  return (
    <div
      className={clsx(
        styles.nodeStart,
        utils.p2,
        utils.dFlex,
        utils.alignItemsCenter,
        utils.justifyContentCenter,
        colors.bgGrassios80,
      )}
      style={{ width: fictionNodeWidth, height: fictionNodeHeight }}
    >
      <Text color={Colors.HollywoodSmile}>Старт</Text>
      <Handle type="source" position={Position.Right} id={getNodeSourceId(id)} />
    </div>
  );
}

export default React.memo(NodeStart);
