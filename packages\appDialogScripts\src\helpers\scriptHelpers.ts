import { AnswerTypes, ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import {
  FrontStepType,
  IFrontAnswer,
  IFrontScript,
  IFrontStep,
  IIdNameScript,
} from '@monorepo/dialog-scripts/src/@types/script';
import { IFrontSourceData } from '@monorepo/services/src/@types/sourceData';

import { getLocaleMessageById } from './localeHelper';

export const getEmptyStep = (wellKnownStepParts: Partial<IFrontStep> = {}): IFrontStep => {
  const stepCode = `step-${new Date().toISOString()}`;
  const sampleStep = {
    type: FrontStepType.Step,
    name: '',
    description: '',
    answerDisplayType: AnswerTypes.None,
    answers: [],
    stepTransfer: 'default',
    isFirstStep: false,
    isBackButtonAvailable: false,
    ...wellKnownStepParts,
  };

  if (sampleStep.type === FrontStepType.Rating) {
    sampleStep.answerDisplayType = AnswerTypes.Button;

    const maxRating = 5;

    sampleStep.answers = Array(maxRating)
      .fill('default')
      .map((transferTo, i) => {
        return {
          id: `answer_note_${i + 1}_for_${stepCode}`,
          text: String(maxRating - i),
          transferTo,
          order: i,
        };
      });
  }

  return { ...sampleStep, code: stepCode };
};

const getInitialStateForCopy = (copyElement: IFrontScript) => ({
  name: `${getLocaleMessageById('app.name.copy')} ${copyElement.name ?? ''}`,
  code: `COPY-${copyElement.code ?? ''}`,
  tags: copyElement.tags ?? [],
  description: copyElement.description ?? '',
  status: ScriptStatus.Template,
  activeFrom: '',
  activeTo: '',
  steps: copyElement.steps ?? [getEmptyStep()],
  canBeAutomated: copyElement.canBeAutomated ?? false,
});

const getInitialStateForEdit = (editElement: IFrontScript) => ({
  name: editElement.name ?? '',
  code: editElement.code ?? '',
  tags: editElement.tags ?? [],
  description: editElement.description ?? '',
  status: editElement.status ?? ScriptStatus.Template,
  activeFrom: editElement.activeFrom ?? '',
  activeTo: editElement.activeTo ?? '',
  steps: editElement.steps ?? [getEmptyStep()],
  canBeAutomated: editElement.canBeAutomated ?? false,
});

export const getInitialState = ({
  editElement,
  copyElement,
}: {
  editElement?: IFrontScript | null;
  copyElement?: IFrontScript | null;
}) => {
  if (copyElement) {
    return getInitialStateForCopy(copyElement);
  }

  if (editElement) {
    return getInitialStateForEdit(editElement);
  }

  return {
    name: '',
    code: '',
    tags: [],
    description: '',
    status: ScriptStatus.Template,
    activeFrom: '',
    activeTo: '',
    steps: [getEmptyStep()],
    canBeAutomated: false,
  };
};

const defaultTransfer = 'default';

export const clearStepsVariables = (
  steps: IFrontStep[],
  currentStep: IFrontStep,
  newStep?: Partial<IFrontStep>,
) => {
  // очищаем переменные из шагов скрипта, если на текущем шаге переменная была переименована или удалена
  if (
    currentStep.type === FrontStepType.Step &&
    currentStep?.variable &&
    newStep?.variable != currentStep.variable
  ) {
    clearRouteVariables(steps, currentStep.variable);
    clearServiceVariables(steps, currentStep.variable);
  }

  // очищаем переменные из шагов скрипта, если переменная выходного параметра сервисного шага была переименована или удалена
  if (currentStep.type === FrontStepType.Service && currentStep.serviceOutputParameters?.length) {
    currentStep.serviceOutputParameters.forEach((sp) => {
      if (
        sp.variableCode &&
        !newStep?.serviceOutputParameters?.find((x) => x.variableCode === sp.variableCode)
      ) {
        clearRouteVariables(steps, sp.variableCode);
        clearServiceVariables(steps, sp.variableCode);
      }
    });
  }
};

const clearRouteVariables = (steps: IFrontStep[], variable: string) => {
  steps
    .filter((x) => x.type === FrontStepType.Router)
    .forEach((x) => {
      if (x.rules?.length) {
        x.rules.forEach((rule) => {
          if (rule.conditions?.length) {
            rule.conditions
              .filter((c) => c.variable === variable)
              .forEach((condition) => {
                condition.variable = null;
              });
          }
        });
      }
    });
};

const clearServiceVariables = (steps: IFrontStep[], variable: string) => {
  steps
    .filter((x) => x.type === FrontStepType.Service)
    .forEach((x) => {
      console.log('serviceParameter', x);
      if (x.serviceParameters?.length) {
        x.serviceParameters
          .filter((sp) => sp.variableCode === variable)
          .forEach((sp) => {
            sp.variableCode = undefined;
            sp.variableName = undefined;
            sp.value = undefined;
          });
      }

      if (x.files?.length) {
        x.files
          .filter((file) => file.variableCode === variable)
          .forEach((file) => {
            file.variableCode = '';
            file.fileName = null;
          });
      }
    });
};

export const clearStepTransfersByTransferToCode = (code: string) => (step: IFrontStep) => {
  let newStep = step;
  if (step.stepTransfer === code) {
    newStep = { ...newStep, stepTransfer: defaultTransfer };
  }

  if (step.stepFallbackTransfer === code) {
    newStep = { ...newStep, stepFallbackTransfer: defaultTransfer };
  }

  if (step.type === FrontStepType.Step && step.answers?.length) {
    newStep = {
      ...newStep,
      answers: step.answers.map((answer) => {
        return answer.transferTo === code ? { ...answer, transferTo: defaultTransfer } : answer;
      }),
    };
  }

  if (step.type === FrontStepType.Router && step.rules?.length) {
    newStep = {
      ...newStep,
      rules: step.rules.map((rule) => {
        return rule.transferTo === code ? { ...rule, transferTo: defaultTransfer } : rule;
      }),
    };
  }

  return newStep;
};

export const getRegExpPatternExcludingStrings = (values: string[]): string => {
  const regexpArray: string[] = [];
  values.forEach((value) => {
    regexpArray.push(value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'));
  });

  return `^(?!(${regexpArray.join('|')})$).*$`;
};

export const getVariableRegExpPatternByScriptSteps = (
  steps: IFrontStep[],
  sources?: IFrontSourceData[],
): string => {
  const codes: string[] = [];

  steps.forEach((stepElement) => {
    stepElement.variable && codes.push(stepElement.variable);
    stepElement.serviceOutputParameters?.length &&
      stepElement.serviceOutputParameters.forEach((outParameter) => {
        outParameter?.variableCode && codes.push(outParameter.variableCode);
      });
  });

  sources?.forEach((source) => {
    source.key && codes.push(source.key);
  });

  return getRegExpPatternExcludingStrings(codes);
};

export const getAnswerRegExpPatternByStepAnswers = (answers: IFrontAnswer[]): string => {
  const answerValues: string[] = [];
  answers.forEach((answer) => answer.text && answerValues.push(answer.text));

  return getRegExpPatternExcludingStrings(answerValues);
};

export const getDependentScriptNamesSeparatedSemicolon = (
  dependentScripts: IIdNameScript[],
): string => {
  if (!dependentScripts || dependentScripts.length === 0) {
    return '';
  }

  return dependentScripts.map((s) => s.name).join(', ');
};

export const getDependentScriptNamesTooltip = (dependentScripts: IIdNameScript[]) => {
  if (!dependentScripts || dependentScripts.length === 0) {
    return getLocaleMessageById('app.editor.scripinformation.noDependentScripts');
  }

  return `${getLocaleMessageById('app.editor.scripinformation.dependentScripts')}:\n${dependentScripts.map((s) => s.name).join('\n')}`;
};

export const activeScriptCheck = (script: {
  status: ScriptStatus;
  activeFrom: string;
  activeTo: string;
}): boolean => {
  const activeFrom = new Date(script.activeFrom).getTime();
  const activeTo = new Date(script.activeTo).getTime();
  const now = new Date().getTime();

  return (
    script.status === ScriptStatus.Active &&
    (isNaN(activeFrom) || activeFrom <= now) &&
    (isNaN(activeTo) || activeTo >= now)
  );
};
