import React from 'react';

import clsx from 'clsx';

import { Input, Text, utils } from '@product.front/ui-kit';

import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

import { IValidationResult } from './validator';

export interface IPriorityInputProps {
  valueKey: string;
  value: number;
  validationResult: IValidationResult;
  onChange: (value: number) => void;
}

const PriorityInput = ({
  valueKey: key,
  value: v,
  validationResult,
  onChange,
}: IPriorityInputProps) => {
  return (
    <>
      <Text style={{ alignSelf: 'center' }}>
        {getLocaleMessageById('prioritization.editor.form.then')}
      </Text>
      <Input
        wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1)}
        label={getLocaleMessageById('prioritization.editor.form.priority')}
        min={1}
        max={32767}
        value={v.toString()}
        onChange={({ value }) => onChange(value ? Number(value) : 0)}
        type="number"
        required
        isInvalid={!!validationResult.values[key]?.priority}
        message={<InputErrorMessage>{validationResult.values[key]?.priority}</InputErrorMessage>}
      />
    </>
  );
};

export default PriorityInput;
