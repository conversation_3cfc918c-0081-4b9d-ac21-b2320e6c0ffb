.chatLeftPanel {
  display: flex;
  flex-direction: column;
  width: max(var(--crpm-size-requests-panel), var(--crpm-size-requests-panel-default));
  background-color: var(--palette-hollywoodSmile);

  .panelHeader {
    height: var(--st-app-header-height, 60px);
    min-height: var(--st-app-header-height, 60px);
  }

  .panelBody {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    flex-grow: 2;
  }

  .panelFooter {
    box-sizing: border-box;
  }
}
