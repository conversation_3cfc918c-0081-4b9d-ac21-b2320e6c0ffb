import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  PrioritizationAttribute,
  PrioritizationRule,
  UpdatePrioritizationRule,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export async function getPrioritizationRules() {
  const url = `${getSettings().administrationApiUrl}/prioritization-rules`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as PrioritizationRule[];
}

export async function getPrioritizationRule(id: number) {
  const url = `${getSettings().administrationApiUrl}/prioritization-rules/${id}`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as PrioritizationRule;
}

export async function createPrioritizationRule(rule: UpdatePrioritizationRule) {
  const url = `${getSettings().administrationApiUrl}/prioritization-rules`;
  return await commonFetch(url, {
    credentials: 'include',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(rule),
  });
}

export async function updatePrioritizationRule(id: number, rule: UpdatePrioritizationRule) {
  const url = `${getSettings().administrationApiUrl}/prioritization-rules/${id}`;
  return await commonFetch(url, {
    credentials: 'include',
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(rule),
  });
}

export async function getPrioritizationAttributes() {
  const url = `${getSettings().administrationApiUrl}/prioritization-rules/attributes`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as PrioritizationAttribute[];
}

export async function deletePrioritizationRule(id: number) {
  const url = `${getSettings().administrationApiUrl}/prioritization-rules/${id}`;
  return await commonFetch(url, {
    credentials: 'include',
    method: 'DELETE',
  });
}
