import React from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import { InputSize, MenuItem, Select, Text, utils } from '@product.front/ui-kit';

import IconPopUp from '@product.front/icons/dist/icons17/Other/IconPopUp';
import IconHierarchy from '@product.front/icons/dist/icons17/Sorting/IconHierarchy';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { useDialogScriptsAppSelector } from '../../../store/hooks';

interface IVariableSelectProps {
  placeHolderResourceId: string;
  variables: Record<string, IFrontStep['variableName']>;
  disabled?: boolean;
  variable?: string | null;
  onChange: (variable: string | null, variableName: string | null) => void;
  wrapperClassName: string;
  size: InputSize;
  message?: React.ReactNode;
  isInvalid?: boolean;
}

enum IFrontVariableType {
  Local,
  Sources,
}

const VariableSelect = ({
  placeHolderResourceId,
  variables,
  disabled,
  variable,
  onChange,
  wrapperClassName,
  size,
  isInvalid,
  message,
}: IVariableSelectProps) => {
  const { sources } = useDialogScriptsAppSelector((state) => state.externalData);

  return (
    <Select
      placeholder={getLocaleMessageById(placeHolderResourceId)}
      size={size}
      wrapperClassName={wrapperClassName}
      isInvalid={isInvalid}
      message={message}
      data={[
        ...(
          Object.entries(variables).map(([code, name]) => ({
            value: code,
            text: name || code,
            data: {
              type: IFrontVariableType.Local,
            },
          })) as ISelectDataItem[]
        ).sort((a, b) => a.text.localeCompare(b.text) ?? 0),
        ...sources.map(({ key, label }, index) => ({
          text: label ? label : '-',
          value: key ?? `unknown-${index}`,
          data: {
            type: IFrontVariableType.Sources,
          },
        })),
      ]}
      renderOption={({ id, element, onClick, active }) => (
        <MenuItem
          key={id}
          id={id ?? ''}
          tabIndex={-1}
          active={active}
          disabled={element.disabled}
          title={element.text}
          onClick={onClick}
          ellipsis
        >
          <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
            {{
              [IFrontVariableType.Sources]: <IconPopUp className={utils.flexShrink0} />,
              [IFrontVariableType.Local]: <IconHierarchy className={utils.flexShrink0} />,
            }[element.data?.type as IFrontVariableType] || null}
            <Text ellipsis>{element.text}</Text>
          </div>
        </MenuItem>
      )}
      required
      value={variable}
      onChange={({ value, text }) => {
        onChange(value ?? null, text);
      }}
      disabled={disabled}
      withDebounce
    />
  );
};

export default VariableSelect;
