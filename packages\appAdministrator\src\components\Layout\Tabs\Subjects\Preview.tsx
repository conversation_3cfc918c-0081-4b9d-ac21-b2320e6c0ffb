import React from 'react';

import clsx from 'clsx';

import {
  Colors,
  FloatingTooltip,
  grids,
  IconButton,
  Jumbotron,
  JumbotronType,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import { IFrontRequestSubject } from '../../../../@types/requestSubject';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { deleteSubject, updateSubject } from '../../../../store/subjects/subjects.thunk';

import { createUpdateSubjectAction, deleteSubjectAction } from './ContextMenu';

export const attributes: {
  key: keyof Pick<
    IFrontRequestSubject,
    'knowledgeBaseContext' | 'chatBotContext' | 'dialogScriptCode'
  >;
  name: string;
}[] = [
  { key: 'knowledgeBaseContext', name: getLocaleMessageById('subject.knowledgeBase') },
  { key: 'chatBotContext', name: getLocaleMessageById('subject.chatBot') },
  { key: 'dialogScriptCode', name: getLocaleMessageById('subject.dialogScript') },
];

const Preview = () => {
  const dispatch = useAdministratorAppDispatch();

  const { selectedSubject: subject, subjects } = useAdministratorAppSelector(
    (store) => store.subjects,
  );

  if (!subject)
    return (
      <Jumbotron type={JumbotronType.Info} header={getLocaleMessageById('subjects.notSelected')} />
    );

  return (
    <div
      className={clsx(
        utils.h100,
        utils.w100,
        utils.pX6,
        utils.pY4,
        utils.overflowAuto,
        utils.scrollbar,
        utils.dFlex,
        utils.flexColumn,
        utils.gap2,
      )}
      style={{ boxSizing: 'border-box' }}
    >
      <header className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
        <Text className={utils.mRauto} variant={TextVariant.HeadlineSemibold} ellipsis>
          {subject?.name}
        </Text>
        <FloatingTooltip tooltip={getLocaleMessageById('subject.edit')}>
          <IconButton
            onClick={() =>
              createUpdateSubjectAction(
                subject,
                async (updatedSubject) => await dispatch(updateSubject(updatedSubject)).unwrap(),
                subject.parentId,
                Object.values(subjects),
              )
            }
          >
            <IconEdit />
          </IconButton>
        </FloatingTooltip>
        <FloatingTooltip tooltip={getLocaleMessageById('subject.delete')}>
          <IconButton
            onClick={() => deleteSubjectAction(() => dispatch(deleteSubject(subject.id)))}
          >
            <IconTrash />
          </IconButton>
        </FloatingTooltip>
      </header>
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
        {[
          { label: getLocaleMessageById('subject.name.label'), value: subject.name },
          {
            label: getLocaleMessageById('subject.code.label'),
            value: subject.code,
          },
          {
            label: getLocaleMessageById('subject.description.label'),
            value: subject.description,
          },
        ].map((field) => (
          <div className={grids.row} key={field.label}>
            <Text className={grids.col3} color={Colors.OnyxBlack60}>
              {field.label}
            </Text>
            <Text className={grids.col9}>
              {field.value || <Text color={Colors.OnyxBlack60}>-</Text>}
            </Text>
          </div>
        ))}
        <Text className={clsx(utils.mT4, utils.mB0)} variant={TextVariant.SubheadSemibold} as="h2">
          {getLocaleMessageById('subject.attributes.label')}
        </Text>
        {attributes.map((attribute) => (
          <div className={grids.row} key={attribute.key}>
            <Text className={grids.col3} color={Colors.OnyxBlack60}>
              {attribute.name}
            </Text>
            <Text className={grids.col9}>
              {subject[attribute.key] || <Text color={Colors.OnyxBlack60}>-</Text>}
            </Text>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Preview;
