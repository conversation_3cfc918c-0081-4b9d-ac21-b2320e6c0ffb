{"app.info.pageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.info.tabMyKPI": "KPI", "app.info.tabMyQueues": "<PERSON><PERSON>v<PERSON>", "app.info.tabMyRequests": "<PERSON><PERSON><PERSON>", "app.info.tabMyEvaluations": "<PERSON><PERSON>", "app.info.onLine": "chiziq", "app.info.inWork": "<PERSON><PERSON>da", "app.info.inTalk": "<PERSON><PERSON><PERSON>", "app.info.hour": "v", "app.info.min": "m", "app.info.sec": "s", "app.info.myKpi.processed": "qayta is<PERSON>n", "app.info.myKpi.closed": "yopiq", "app.info.myKpi.missed": "o't<PERSON><PERSON><PERSON> y<PERSON><PERSON>", "app.info.myKpi.aht": "AHT", "app.info.myKpi.acsi": "ACSI", "app.info.myKpi.assi": "ASSI", "app.info.myKpi.response": "javob", "app.info.myKpi.title.processed": "<PERSON><PERSON><PERSON>", "app.info.myKpi.title.closed": "Yopiq", "app.info.myKpi.title.missed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>", "app.info.myKpi.title.aht": "<PERSON><PERSON><PERSON><PERSON><PERSON> is<PERSON> vaqt", "app.info.myKpi.title.acsi": "<PERSON><PERSON><PERSON> to<PERSON> belgilangan ish sifatini o'<PERSON><PERSON><PERSON> baholash", "app.info.myKpi.title.assi": "Nazoratchi tomonidan belgilangan ish sifatini o'<PERSON><PERSON><PERSON> baholash", "app.info.myKpi.title.response": "<PERSON><PERSON><PERSON>", "app.info.myKpi.table.queue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myKpi.table.operatorsAwaiting": "<PERSON><PERSON><PERSON> k<PERSON>", "app.info.myKpi.table.activeOperators": "Faol operatorlar", "app.info.myKpi.table.asa": "ASA", "app.info.myKpi.table.aht": "AHT", "app.info.myRequests.showHistory": "<PERSON><PERSON><PERSON> tarixini ko'rsating", "app.info.myRequests.takeToWork": "<PERSON><PERSON><PERSON> o<PERSON>", "app.info.myRequests.emptyHeader": "<PERSON>ch qanday murojaat yo'q", "app.info.myRequests.emptyText": "So'rov paramet<PERSON><PERSON><PERSON> teks<PERSON>ing", "app.common.minutes.short": "shodlik", "app.common.seconds.short": "bilan", "app.info.myRequests.table.id": "ID", "app.info.myRequests.table.queue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.channel": "<PERSON><PERSON>", "app.info.myRequests.table.theme": "<PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.type": "<PERSON>iraj turi", "app.info.myRequests.table.liveTime": "<PERSON><PERSON>", "app.info.myRequests.table.queueTime": "Vaqt ichida vaqt", "app.info.myRequests.table.clientId": "ID mijoz", "app.info.myRequests.table.clientFio": "<PERSON><PERSON><PERSON><PERSON> to'liq nomi", "app.info.myRequests.table.sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.recipient": "<PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.priority": "Us<PERSON><PERSON>lik", "app.info.myRequests.table.repeated": "Takrorlamoq", "app.info.myRequests.table.registrationTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> olish", "app.info.myRequests.table.lastDistributionTime": "<PERSON>'<PERSON><PERSON> tarqa<PERSON>h", "app.info.myRequests.table.status": "<PERSON><PERSON>", "app.info.myRequests.table.postponedUntil": "Kechiktirilgan", "app.info.myRequests.table.lastChangedByFio": "<PERSON>", "app.info.myRequests.table.sa": "SA", "app.info.myRequests.table.ht": "HT", "app.info.myRequests.table.wt": "WT", "app.info.myRequests.table.1rt": "1RT", "app.info.myRequests.table.acw": "ACW", "app.info.myRequests.table.csi": "CSI", "app.info.myRequests.table.ssi": "SSI", "app.info.myRequests.table.incomingMessagesNumber": "<PERSON><PERSON><PERSON>", "app.info.myRequests.table.outgoingMessagesNumber": "Tashqarida.Xabarlar", "app.info.myRequests.table.attachmentsNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.info.myRequests.table.lost": "<PERSON>'q<PERSON>gan", "app.info.myRequests.table.clientType": "<PERSON><PERSON><PERSON> turi", "app.info.myRequests.table.answerUntil": "<PERSON><PERSON> javob", "app.info.myRequests.table.processedByFio": "<PERSON><PERSON><PERSON>", "app.info.myRequests.table.timeInStatus": "Holatda vaqt", "app.common.in": "<PERSON><PERSON><PERSON>", "app.info.requests.common.yes": "Ha", "app.info.requests.common.no": "<PERSON><PERSON>", "app.info.requests.type.outgoing": "Chiqayotgan", "app.info.requests.type.incoming": "<PERSON><PERSON><PERSON><PERSON>", "app.common.close": "<PERSON><PERSON><PERSON>", "app.messagesModal.title": "#{requestId} - <PERSON><PERSON><PERSON> ta<PERSON>i", "app.buttons.tooltip.refresh": "<PERSON><PERSON><PERSON><PERSON>", "app.buttons.tooltip.download": "Excel-ni tushiring", "app.myTimeline": "<PERSON><PERSON> vaqtim", "app.myKpi": "<PERSON><PERSON>", "app.queueKpi": "KPI navbatlari", "app.kpiForToday": "Bugun KPI indikatorlari", "app.lastUpdateTime": "Yangilangan", "app.kpi.workingTime": "<PERSON><PERSON><PERSON> smenasi", "app.kpi.workKpi": "<PERSON><PERSON> ko'r<PERSON><PERSON><PERSON>i", "app.kpi.speedKpi": "Tezlik ko'rsatkichlari", "app.kpi.qualityKpi": "Fazilatlar ko'rsatkichlari", "app.info.myRequests.table.title.attachmentsNumber": "<PERSON><PERSON><PERSON><PERSON> soni", "app.table.attachments.none": "Investitsiyalarsiz", "app.table.attachments.has": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bilan", "app.info.myRequests.table.externalClientId": "Tashqi identifikator", "app.info.myRequests.table.title.externalClientId": "Tashqi tizimdagi mijoz identifikatori", "app.info.onBreak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.common.onlyEmpty": "<PERSON><PERSON><PERSON> bo'sh", "app.common.onlyNotEmpty": "Faqat to'ldir<PERSON><PERSON>", "app.common.from": "Bilan", "app.common.to": "Yoqilgan", "app.common.error": "<PERSON><PERSON>lumot o<PERSON>da xato", "app.common.updateError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>, u<PERSON><PERSON>ni ta<PERSON>"}