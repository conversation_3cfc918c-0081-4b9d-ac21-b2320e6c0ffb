import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import { OverlayLoader, utils } from '@product.front/ui-kit';

import styles from './styles.module.scss';

interface IAdmTabBodyProps extends HTMLAttributes<HTMLDivElement> {
  noPadding?: boolean;
  flexRow?: boolean;
  withoutScroll?: boolean;
  loading?: boolean;
}

const AdmTabBody: React.FC<IAdmTabBodyProps> = ({
  className,
  children,
  noPadding,
  flexRow,
  withoutScroll,
  loading,
  ...rest
}) => {
  return (
    <OverlayLoader
      loading={loading}
      wrapperClassName={clsx(
        'qa-admin-tab-body',
        utils.flexBasis0,
        utils.flexGrow1,
        !withoutScroll && utils.overflowAuto,
        !withoutScroll && utils.scrollbar,
        utils.dFlex,
        !flexRow && utils.flexColumn,
        styles.body,
        !noPadding && clsx(utils.pX6, utils.pY4),
        utils.borderTop,
        className,
      )}
      {...rest}
    >
      {children}
    </OverlayLoader>
  );
};

export default AdmTabBody;
