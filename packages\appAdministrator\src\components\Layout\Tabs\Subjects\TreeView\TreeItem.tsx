import React from 'react';

import clsx from 'clsx';

import { IconButton, MenuItem, Text, utils } from '@product.front/ui-kit';

import IconDropDown from '@product.front/icons/dist/icons17/MainStuff/IconDropDown';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';
import IconDoc from '@product.front/icons/dist/icons17/Person&Doc/IconDoc';

import { highlightText } from '@monorepo/common/src/helpers/textHighlight';

import { IFrontRequestSubject } from '../../../../../@types/requestSubject';
import {
  useAdministratorAppDispatch,
  useAdministratorAppSelector,
} from '../../../../../store/hooks';
import { setSelectedRequest } from '../../../../../store/subjects/subjects.slice';
import ContextMenu from '../ContextMenu';

import { checkIfSubjectHasEntry } from './helpers';

import styles from '../styles.module.scss';

export interface ITreeItem extends IFrontRequestSubject {
  items: ITreeItem[];
}

interface ITreeItemProps {
  level: number;
  subject: ITreeItem;
  hidden?: boolean;

  searchText: string;
  shouldSearchByName: boolean;
  shouldSearchByText: boolean;
}

const TreeItem = ({
  level,
  subject,
  hidden = false,
  searchText,
  shouldSearchByName,
  shouldSearchByText,
}: ITreeItemProps) => {
  const dispatch = useAdministratorAppDispatch();

  const { selectedSubject } = useAdministratorAppSelector((store) => store.subjects);

  const [isOpen, setIsOpen] = React.useState(false);

  const shouldShow = React.useMemo(
    () => checkIfSubjectHasEntry(subject, searchText, shouldSearchByName, shouldSearchByText),
    [subject, shouldSearchByName, shouldSearchByText, searchText],
  );

  React.useEffect(() => {
    if (!searchText || (!shouldSearchByName && !shouldSearchByText)) return;

    setIsOpen(true);
  }, [searchText, shouldSearchByName, shouldSearchByText]);

  if (!shouldShow) return null;

  return (
    <>
      <MenuItem
        style={{
          paddingLeft: level * 20 + 16,
          transition: 'height 0.2s linear',
          display: hidden ? 'none' : undefined,
        }}
        className={clsx(
          utils.dFlex,
          utils.alignItemsCenter,
          utils.gap2,
          utils.pR4,
          styles.menuItem,
        )}
        onClick={() => dispatch(setSelectedRequest(subject))}
        active={selectedSubject?.id === subject.id}
      >
        {!!subject.items?.length && (
          <IconButton
            className={clsx(utils.dFlex, utils.flexShrink0)}
            onClick={(event) => {
              event.stopPropagation();
              setIsOpen((value) => !value);
            }}
          >
            {isOpen ? <IconDropDown /> : <IconDropRight />}
          </IconButton>
        )}
        {!subject.items?.length && <IconDoc className={utils.flexShrink0} />}
        &nbsp;
        <Text ellipsis>
          {shouldSearchByName ? highlightText(subject.name, searchText) : subject.name}
        </Text>
        <ContextMenu subject={subject} />
      </MenuItem>
      {subject.items
        ?.toSorted((a, b) => a.name.localeCompare(b.name))
        .map((childSubject) => (
          <TreeItem
            key={childSubject.id}
            level={level + 1}
            subject={childSubject}
            hidden={hidden || !isOpen}
            searchText={searchText}
            shouldSearchByName={shouldSearchByName}
            shouldSearchByText={shouldSearchByText}
          />
        ))}
    </>
  );
};

export default TreeItem;
