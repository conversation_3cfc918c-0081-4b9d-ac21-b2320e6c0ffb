import * as React from 'react';

import { showNotify, NotificationType } from '@product.front/ui-kit';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

type NotificationsContainerProps = {
  className?: string;
};
type NotificationManagerProps = {
  notificationsContainerProps?: NotificationsContainerProps;
};
type NotificationManagerState = {};
export class NotificationManager
  extends React.PureComponent<NotificationManagerProps, NotificationManagerState>
  implements AWP.IPopupNotificationManager
{
  constructor(props: NotificationManagerProps) {
    super(props);
    this.state = {};
    if (props.notificationsContainerProps) {
      this.notificationsContainerProps = props.notificationsContainerProps;
    }
  }

  private notificationsContainerProps: NotificationsContainerProps = {
    className: 'st-notification-container',
  };

  private calculateTimeout(
    notificationType: NotificationType,
    timeout?: number | null,
  ): number | undefined {
    if (timeout === null) {
      return undefined;
    }

    if (timeout === undefined) {
      switch (notificationType) {
        case NotificationType.Default:
          return 5 * 1000;
        case NotificationType.Error:
          return undefined;
        case NotificationType.Information:
          return 4 * 1000;
        case NotificationType.Warning:
          return 6 * 1000;
        case NotificationType.Success:
          return 4 * 1000;
        default:
          return 5 * 1000;
      }
    }

    return timeout;
  }

  notifyInfo(
    message: string,
    header?: string,
    timeout?: number | null,
    canClose?: boolean,
    icon?: React.ReactNode,
    showProgress?: boolean,
    onClick?: () => void,
  ): void {
    showNotify(
      {
        type: NotificationType.Information,
        content: message,
        header: header,
        timeout: this.calculateTimeout(NotificationType.Information, timeout),
        icon: icon,
        canClose: canClose,
        onClick: onClick,
        showProgress: !showProgress ? false : showProgress,
      },
      this.notificationsContainerProps,
    );
  }

  notifyWarn(
    message: string,
    header?: string,
    timeout?: number | null,
    canClose?: boolean,
    icon?: React.ReactNode,
    showProgress?: boolean,
    onClick?: () => void,
  ): void {
    console.warn(message, header);
    showNotify(
      {
        type: NotificationType.Warning,
        content: message,
        header: header,
        timeout: this.calculateTimeout(NotificationType.Warning, timeout),
        icon: icon,
        canClose: canClose,
        onClick: onClick,
        showProgress: !showProgress ? false : showProgress,
      },
      this.notificationsContainerProps,
    );
  }

  notifyError(
    message: string,
    header?: string,
    timeout?: number | null,
    canClose?: boolean,
    icon?: React.ReactNode,
    showProgress?: boolean,
    onClick?: () => void,
  ): void {
    console.error(message, header);
    showNotify(
      {
        type: NotificationType.Error,
        content: message,
        header: header,
        timeout: this.calculateTimeout(NotificationType.Error, timeout),
        icon: icon,
        canClose: canClose,
        onClick: onClick,
        showProgress: !showProgress ? false : showProgress,
      },
      this.notificationsContainerProps,
    );
  }

  notifySuccess(
    message: string,
    header?: string,
    timeout?: number | null,
    canClose?: boolean,
    icon?: React.ReactNode,
    showProgress?: boolean,
    onClick?: () => void,
  ): void {
    showNotify(
      {
        type: NotificationType.Success,
        content: message,
        header: header,
        timeout: this.calculateTimeout(NotificationType.Success, timeout),
        icon: icon,
        canClose: canClose,
        onClick: onClick,
        showProgress: !showProgress ? false : showProgress,
      },
      this.notificationsContainerProps,
    );
  }

  render() {
    return '';
  }
}
