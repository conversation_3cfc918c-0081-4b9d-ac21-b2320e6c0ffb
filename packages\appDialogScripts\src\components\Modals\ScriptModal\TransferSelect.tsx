import React from 'react';

import {
  ISelectDataItem,
  ISelectProps,
} from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import { Select, MenuItem, utils, Text } from '@product.front/ui-kit';

import IconExit from '@product.front/icons/dist/icons17/MainStuff/IconExit';
import IconSettings from '@product.front/icons/dist/icons17/MainStuff/IconSettings';
import IconFlags from '@product.front/icons/dist/icons17/Smiles/IconFlags';
import IconHierarchy from '@product.front/icons/dist/icons17/Sorting/IconHierarchy';

import { FrontStepType, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { AppConfigContext } from '../../../context/appConfig';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import IconRouter from '../VisualModal/components/VisualEditor/IconRouter';
import IconStep from '../VisualModal/components/VisualEditor/IconStep';

const nextStep = {
  value: 'default',
  text: getLocaleMessageById('app.stepTransfer.default'),
};

const endStep = {
  value: '',
  text: getLocaleMessageById('app.stepTransfer.end'),
};

interface IScriptStatusProps extends Omit<ISelectProps, 'value' | 'onChange' | 'data'> {
  steps: IFrontStep[];
  value?: string;
  onChange: ({ value }: { value: string | null }) => void;
  withEnd?: boolean;
  withDefault?: boolean;
}

const TransferSelect: React.FC<IScriptStatusProps> = ({
  steps,
  value,
  onChange,
  withEnd = true,
  withDefault = true,
  ...rest
}) => {
  const { disableAutoRelation } = React.useContext(AppConfigContext);
  const optionsData = React.useMemo<ISelectDataItem[]>(() => {
    const arrayToReturn: ISelectDataItem[] = [];

    steps.forEach((step) => {
      let text = step.name;
      if (!text) {
        text = getNoNameNameForStep(step);
      }
      arrayToReturn.push({
        value: step.code,
        text,
        data: {
          type: step.type,
        },
      });
    });

    return arrayToReturn.sort((a, b) => a.data.type.localeCompare(b.data.type));
  }, [steps]);

  return (
    <Select
      value={value}
      data={[
        ...([
          !disableAutoRelation && withDefault && nextStep,
          !disableAutoRelation && withEnd && endStep,
        ].filter(Boolean) as ISelectDataItem[]),
        ...optionsData,
      ]}
      onChange={onChange}
      renderOption={({ id, element, onClick, active }) => {
        return (
          <MenuItem
            key={id}
            id={id ?? ''}
            tabIndex={-1}
            active={active}
            disabled={element.disabled}
            title={element.text}
            onClick={onClick}
            ellipsis
          >
            <div className={clsx(utils.dFlex, utils.gap2, utils.alignItemsCenter)}>
              {{
                [FrontStepType.Step]: (
                  <IconStep className={utils.flexShrink0} style={{ borderColor: '#121a33' }} />
                ),
                [FrontStepType.Router]: (
                  <IconRouter className={utils.flexShrink0} style={{ borderColor: '#121a33' }} />
                ),
                [FrontStepType.Service]: <IconSettings className={utils.flexShrink0} />,
                [FrontStepType.Subscript]: <IconHierarchy className={utils.flexShrink0} />,
                [FrontStepType.Scenario]: (
                  <IconExit className={utils.flexShrink0} style={{ borderColor: '#121a33' }} />
                ),
                [FrontStepType.Terminal]: <IconFlags className={utils.flexShrink0} />,
                [FrontStepType.Rating]: (
                  <IconStep className={utils.flexShrink0} style={{ borderColor: '#121a33' }} />
                ),
              }[element.data?.type as FrontStepType] || null}
              <Text ellipsis>{element.text}</Text>
            </div>
          </MenuItem>
        );
      }}
      {...rest}
    />
  );
};

export default TransferSelect;
