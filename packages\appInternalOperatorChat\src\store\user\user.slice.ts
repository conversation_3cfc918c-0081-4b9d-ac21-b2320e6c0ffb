import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { Operator } from '../../@types/generated/signalr';

import { getUserData } from './user.thunks';
import { UserState } from './user.types';

const initialState: UserState = {
  user: null,
  loading: true,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getUserData.pending, (state) => {
        state.loading = true;
      })
      .addCase(getUserData.fulfilled, (state, action: PayloadAction<Operator>) => {
        state.loading = false;
        state.user = action.payload;
      })
      .addCase(getUserData.rejected, (state, { error }) => {
        state.error = error;
        state.loading = false;
      });
  },
});

export default userSlice.reducer;
