{"infrastructureServicesUrl": "//localhost:8082/awp/infrastructure/", "communicationServicesUrl": "//localhost:8082/awp/communication/", "crpmWorkExecutionConfigurationProviderServiceUrl": "//localhost:8082/crpm/configuration/", "crpmWorkCoordinationHubAddress": "//localhost:8082/crpm/workcoordination/workExecution", "crpmConfigurationReaderServiceUrl": "//localhost:8082/crpm/configuration/", "crpmPresentationServiceUrl": "//localhost:8082/crpm/presentation/", "crpmRequestManagingServiceUrl": "//localhost:8082/crpm/requestmanaging/", "crpmStateProcessingServiceUrl": "//localhost:8082/crpm/stateprocessing/", "productChatHistory": "//localhost:8082/core/product-api/graphql", "productOperatorChatService": "//localhost:8082/messages/operator-chat/chathub", "productOperatorChatApi": "//localhost:8082/messages/operator-chat", "productFileServer": "//localhost:8082/attachments/file-manager/api/uploaded/file/v2", "productPublicFileServer": "//localhost:8082/attachments/file-manager/api/uploaded/file", "productFileServerApiUrl": "//localhost:8082/attachments/file-manager/api", "productInternalChatSignalR": "//localhost:8082/internal-chat/chat-api/hubs/chat", "productInternalChatApi": "//localhost:8082/internal-chat/chat-api", "calcServiceUrl": "//localhost:8082/statistics/calc", "requestConfigurationUrl": "//localhost:8082/crpm/configuration-api", "productMarketingService": "//localhost:8082/marketing/webapi", "iceServers": [{"urls": "stun:stun.l.google.com:19302"}], "productEventServiceHub": "//localhost:8082/statistics/event/hubs/eventreceiver", "productFreeswitchApi": "//localhost:8082/channels/voice/freeswitch-call-control/api/", "productClientCardBaseUrl": "//localhost:8082/core/product-api/api/ClientCardBlock/", "productApiUrl": "//localhost:8082/core/product-api/", "ucmmPresentationBaseUrl": "//localhost:8082/ucm/presentation", "ucmmManagingBaseUrl": "//localhost:8082/ucm/managing", "productTextTemplatePresentationUrl": "//localhost:8082/templates/presentation", "productTextTemplateManagementUrl": "//localhost:8082/templates/management", "credentialsProviderServicesUrl": "//localhost:8082/credman/utilization/", "dataPresentationServiceUrl": "//localhost:8082/core/data-presentation/", "dialogScriptsManagementUrl": "//localhost:8082/dialoguescripts/management", "userSessionServiceUrl": "//localhost:8082/core/session-service", "evaluationFormsUrl": "//localhost:8082/evaluations/management", "automationServicesManagementUrl": "//localhost:8082/automations/webapi", "commentsApiUrl": "//localhost:8082/comments/api", "webarmLogsBus": "w//localhost:8082/logging/node-logger/", "ticketingApiUrl": "//localhost:8082/ticketing/internal-webapi", "callControlEventHub": "//localhost:8082/call-control/cc", "externalHost": "external.omni-team.ru", "administrationApiUrl": "//localhost:8082/core/settings", "screenSharingSite": "//localhost:8082/screensharing/", "screenViewerSite": "//localhost:8082/screenviewer/", "webarmToolsSite": "///webarm-tools/", "videochatSocketUrl": "//localhost:8082/core/signal-service/chathub", "serviceSystemsApiUrl": "//localhost:8082/service-systems"}