import { IFrontQueueKpiParameter } from '../../../../../@types/parameters';
import { IAutoHandlerType, IFrontQueue } from '../../../../../@types/queue';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

export interface IValidationResult {
  isErrored: boolean;
  main: {
    isErrored: boolean;
    name: string;
    description: string;
    weight: string;
  };
  routing: {
    isErrored: boolean;
    routingRules: string;
  };
  distribution: {
    isErrored: boolean;
    distributionRule: string;
  };
  operators: {
    isErrored: boolean;
    operators: string;
  };
  autoHandlers: {
    isErrored: boolean;
    autoHandlers: string;
  };
  chatBot: {
    isErrored: boolean;
    buttonBot: string;
    intelligentBot: string;
    ratingBot: string;
  };
  kpi: {
    isErrored: boolean;
    kpiMap: Record<string, { alarm?: string; warn?: string }>;
  };
}

export const defaultValidationResult: IValidationResult = {
  isErrored: false,
  main: {
    isErrored: false,
    name: '',
    description: '',
    weight: '',
  },
  routing: {
    isErrored: false,
    routingRules: '',
  },
  distribution: {
    isErrored: false,
    distributionRule: '',
  },
  operators: {
    isErrored: false,
    operators: '',
  },
  autoHandlers: {
    isErrored: false,
    autoHandlers: '',
  },
  chatBot: {
    isErrored: false,
    buttonBot: '',
    intelligentBot: '',
    ratingBot: '',
  },
  kpi: {
    isErrored: false,
    kpiMap: {},
  },
};

const requiredMessage = getLocaleMessageById('app.common.validation.required');

const validateMain = (queue: IFrontQueue, validationResult: IValidationResult) => {
  if (!queue.name) {
    validationResult.isErrored = true;
    validationResult.main.isErrored = true;
    validationResult.main.name = requiredMessage;
  }

  if (queue.description.length > 1000) {
    validationResult.isErrored = true;
    validationResult.main.isErrored = true;
    validationResult.main.description = getLocaleMessageById('app.common.validation.lengthLimit', {
      max: 1000,
    });
  }

  if (queue.weight && queue.routingRules.length === 0) {
    validationResult.isErrored = true;
    validationResult.main.isErrored = true;
    validationResult.main.weight = getLocaleMessageById('queues.validation.routingRulesRequired');
  }
};

const validateRoutingRules = (queue: IFrontQueue, validationResult: IValidationResult) => {
  if (
    queue.routingRules.some((rule) =>
      rule.rules.some((r) => !r.attributeId || !r.comparisonRule || !r.value),
    )
  ) {
    validationResult.isErrored = true;
    validationResult.routing.isErrored = true;
    validationResult.routing.routingRules = getLocaleMessageById(
      'queues.validation.routing.emptyRules',
    );
  }
};

const validateDistributionRules = (queue: IFrontQueue, validationResult: IValidationResult) => {
  if (!queue.distributionRuleType) {
    validationResult.isErrored = true;
    validationResult.distribution.isErrored = true;
    validationResult.distribution.distributionRule = requiredMessage;
  }
};

const validateOperators = (queue: IFrontQueue, validationResult: IValidationResult) => {
  if (queue.operators.some((operator) => operator.priority > 9 || operator.priority < 0)) {
    validationResult.isErrored = true;
    validationResult.operators.isErrored = true;
    validationResult.operators.operators = getLocaleMessageById(
      'queues.validation.operators.priorityRange',
    );
  }
};

const validateBots = (queue: IFrontQueue, validationResult: IValidationResult) => {
  if (queue.buttonBot.enabled && !queue.buttonBot.code) {
    validationResult.isErrored = true;
    validationResult.chatBot.isErrored = true;
    validationResult.chatBot.buttonBot = requiredMessage;
  }

  if (queue.intelligentBot.enabled && !queue.intelligentBot.code) {
    validationResult.isErrored = true;
    validationResult.chatBot.isErrored = true;
    validationResult.chatBot.intelligentBot = requiredMessage;
  }

  if (queue.ratingBot.enabled && !queue.ratingBot.code) {
    validationResult.isErrored = true;
    validationResult.chatBot.isErrored = true;
    validationResult.chatBot.ratingBot = requiredMessage;
  }
};

const validateAutoHandlers = (queue: IFrontQueue, validationResult: IValidationResult) => {
  if (
    queue.autoHandlers.some(
      (autoHandler) =>
        (autoHandler.type === IAutoHandlerType.TemplateAutoHandler && !autoHandler.templateId) ||
        (autoHandler.type === IAutoHandlerType.PredictedWaitTimeAutoHandler &&
          (!autoHandler.max ||
            !autoHandler.min ||
            !autoHandler.templateAboveMax ||
            !autoHandler.templateWithinRange ||
            !autoHandler.templateBelowMin)),
    )
  ) {
    validationResult.isErrored = true;
    validationResult.autoHandlers.isErrored = true;
    validationResult.autoHandlers.autoHandlers = getLocaleMessageById(
      'queues.validation.autoHandlers.emptyRules',
    );
  }
};

const validateKpi = (
  queue: IFrontQueue,
  validationResult: IValidationResult,
  kpiParameters: IFrontQueueKpiParameter[],
) => {
  queue.kpiParameters.forEach((kpiSetting) => {
    const kpiThreshold = kpiParameters.find((kpi) => kpi.code === kpiSetting.code);
    if (!kpiThreshold) return;

    if (
      kpiSetting.alarmThreshold &&
      ((kpiThreshold.alarmMaxValue && kpiSetting.alarmThreshold > kpiThreshold.alarmMaxValue) ||
        kpiSetting.alarmThreshold < (kpiThreshold.alarmMinValue ?? 0))
    ) {
      validationResult.isErrored = true;
      validationResult.kpi.isErrored = true;
      validationResult.kpi.kpiMap[kpiSetting.code] = {
        ...validationResult.kpi.kpiMap[kpiSetting.code],
        alarm: getLocaleMessageById('app.common.validation.range', {
          min: kpiThreshold.alarmMinValue ?? '0',
          max: kpiThreshold.alarmMaxValue ?? '∞',
        }),
      };
    }

    if (
      kpiThreshold.isWarningAvailable &&
      kpiSetting.warningThreshold &&
      ((kpiThreshold.warningMaxValue &&
        kpiSetting.warningThreshold > kpiThreshold.warningMaxValue) ||
        kpiSetting.warningThreshold < (kpiThreshold.warningMinValue ?? 0))
    ) {
      validationResult.isErrored = true;
      validationResult.kpi.isErrored = true;
      validationResult.kpi.kpiMap[kpiSetting.code] = {
        ...validationResult.kpi.kpiMap[kpiSetting.code],
        warn: getLocaleMessageById('app.common.validation.range', {
          min: kpiThreshold.warningMinValue ?? '0',
          max: kpiThreshold.warningMaxValue ?? '∞',
        }),
      };
    }

    if (
      kpiThreshold.isWarningAvailable &&
      kpiSetting.alarmThreshold &&
      kpiSetting.warningThreshold &&
      !kpiThreshold.alarmExceedsWarning &&
      kpiSetting.alarmThreshold < kpiSetting.warningThreshold
    ) {
      validationResult.isErrored = true;
      validationResult.kpi.isErrored = true;
      validationResult.kpi.kpiMap[kpiSetting.code] = {
        ...validationResult.kpi.kpiMap[kpiSetting.code],
        warn: getLocaleMessageById('queues.validation.kpi.warningExceeds'),
      };
    }
  });
};

export const validateQueue = (queue: IFrontQueue, kpiParameters: IFrontQueueKpiParameter[]) => {
  const validationResult: IValidationResult = {
    ...defaultValidationResult,
    main: { ...defaultValidationResult.main },
    routing: { ...defaultValidationResult.routing },
    distribution: { ...defaultValidationResult.distribution },
    operators: { ...defaultValidationResult.operators },
    autoHandlers: { ...defaultValidationResult.autoHandlers },
    chatBot: { ...defaultValidationResult.chatBot },
    kpi: { ...defaultValidationResult.kpi, kpiMap: {} },
  };

  validateMain(queue, validationResult);
  validateRoutingRules(queue, validationResult);
  validateDistributionRules(queue, validationResult);
  validateOperators(queue, validationResult);
  validateAutoHandlers(queue, validationResult);
  validateBots(queue, validationResult);
  validateKpi(queue, validationResult, kpiParameters);

  return validationResult;
};
