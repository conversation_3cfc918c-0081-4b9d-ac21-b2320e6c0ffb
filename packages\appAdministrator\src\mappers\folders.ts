import { FrontTemplateStatus, IFrontFolder } from '@monorepo/common/src/@types/templates';

import {
  AnswerTemplateFolderView,
  PersonalTemplateFolderView,
  TemplateStatus,
} from '../@types/generated/administration';

import { mapTemplateStatusToFront } from './templates';

export const mapAnswerFolderToFrontFolder = (
  folder: AnswerTemplateFolderView,
  foldersMap: Record<string, IFrontFolder> = {},
  isRoot: boolean = false,
) => {
  if (!folder.id) return;

  foldersMap[isRoot ? 'root' : folder.id] = {
    id: folder.id ?? '',
    ownerId: null,
    parentId: folder.parentId ?? null,
    title: folder.title ?? '',
    childrenFolders: folder.childrenFolders?.map((childrenFolder) => childrenFolder.id ?? '') ?? [],
    templates:
      folder.templates?.map((template) => ({
        id: template.id ?? '',
        status: mapTemplateStatusToFront(template.status ?? TemplateStatus.Published),
        title: template.title ?? '',
        ownerId: null,
        hasAttachments: false,
      })) ?? [],
    relationIds: folder.relationIds ?? [],
  };

  folder.childrenFolders?.forEach((childrenFolder) => {
    mapAnswerFolderToFrontFolder(childrenFolder, foldersMap);
  });
};

export const mapPersonalFolderToFrontFolder = (
  folder: PersonalTemplateFolderView,
  foldersMap: Record<string, IFrontFolder> = {},
) => {
  if (!folder.id) return;

  foldersMap[folder.id] = {
    id: folder.id ?? '',
    ownerId: folder.ownerId ?? null,
    parentId: folder.parentId ?? null,
    title: folder.title ?? '',
    childrenFolders: folder.childrenFolders?.map((childrenFolder) => childrenFolder.id ?? '') ?? [],
    templates:
      folder.templates?.map((template) => ({
        id: template.id ?? '',
        status: FrontTemplateStatus.Published,
        title: template.title ?? '',
        ownerId: folder.ownerId ?? null,
        hasAttachments: false,
      })) ?? [],
  };

  folder.childrenFolders?.forEach((childrenFolder) => {
    mapPersonalFolderToFrontFolder(childrenFolder, foldersMap);
  });
};
