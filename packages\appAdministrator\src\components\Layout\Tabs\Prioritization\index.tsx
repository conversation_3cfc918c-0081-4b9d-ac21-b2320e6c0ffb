import React from 'react';

import { Menu, MenuItem, showModal, Switch } from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import { getNotificationArgsByError } from '@monorepo/common/src/common/helpers/errors.helper';
import InfoMessage from '@monorepo/common/src/components/Table/InfoMessage';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import { getPlatformPopupNotificationManager } from '@monorepo/common/src/managers/platformPopupNotificationManager';

import { IAdmTabComponent } from '../../../../@types/components';
import { IFrontPrioritizationRule } from '../../../../@types/prioritization';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { mapPrioritizationRuleDtoToFront } from '../../../../mappers/prioritization';
import {
  deletePrioritizationRule,
  getPrioritizationRule,
} from '../../../../services/prioritization';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import {
  getAllPrioritizationRules,
  savePrioritizationRule,
} from '../../../../store/prioritization/prioritization.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import PrioritizationRuleEditor from './Editor';

const Prioritization: React.FC<IAdmTabComponent> = ({ name }) => {
  const dispatch = useAdministratorAppDispatch();

  const { loading, error, rules } = useAdministratorAppSelector((state) => state.prioritization);

  const [selectedRule, setSelectedRule] = React.useState<IFrontPrioritizationRule | null>(null);
  const [loadingEditor, setLoadingEditor] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const refresh = React.useCallback(() => {
    setSelectedRule(null);
    dispatch(getAllPrioritizationRules());
  }, [dispatch]);

  const openRuleEditor = async (ruleToEdit: IFrontPrioritizationRule | null) => {
    try {
      setLoadingEditor(true);
      let rule: IFrontPrioritizationRule | null = null;
      if (ruleToEdit?.id) {
        rule = mapPrioritizationRuleDtoToFront(await getPrioritizationRule(ruleToEdit.id));
      }
      showModal({
        header: getLocaleMessageById('prioritization.editor.title'),
        children: (onClose) => (
          <PrioritizationRuleEditor
            rule={rule}
            onSubmit={async (newRule) => {
              await dispatch(savePrioritizationRule(newRule)).unwrap();
              refresh();
            }}
            onClose={onClose}
          />
        ),
        flushBody: true,
        canClose: false,
      });
    } catch (e) {
      console.error('Error opening rule editor', e);

      const errText = getLocaleMessageById('prioritization.editor.error');
      const errArgs = getNotificationArgsByError(errText, e);
      getPlatformPopupNotificationManager().notifyError(...errArgs);
    } finally {
      setLoadingEditor(false);
    }
  };

  const deleteRule = async () => {
    if (!selectedRule) return;

    setIsDeleting(true);
    showConfirmModal({
      header: getLocaleMessageById('prioritization.delete.confirm.title'),
      text: getLocaleMessageById('prioritization.delete.confirm.text', {
        ruleName: selectedRule.title,
      }),
      onConfirm: async () => {
        try {
          await deletePrioritizationRule(selectedRule.id);
          refresh();
        } catch (e) {
          console.error('Error deleting rule', e);

          const errText = getLocaleMessageById('prioritization.delete.error');
          const errArgs = getNotificationArgsByError(errText, e);
          getPlatformPopupNotificationManager().notifyError(...errArgs);
        } finally {
          setIsDeleting(false);
        }
      },
      onCancel: () => setIsDeleting(false),
    });
  };

  React.useEffect(() => {
    refresh();
  }, [refresh]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.refresh')}
          onClick={refresh}
          disabled={loading}
        >
          <IconRefresh />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.delete')}
          onClick={deleteRule}
          disabled={!selectedRule}
          loading={isDeleting}
        >
          <IconTrash />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.edit')}
          onClick={() => openRuleEditor(selectedRule)}
          disabled={!selectedRule}
          loading={loadingEditor}
        >
          <IconEdit />
        </AdmToolbarIconButton>

        <AdmToolbarCtaButton onClick={() => openRuleEditor(null)}>
          {getLocaleMessageById('app.common.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody
        loading={loading}
        onClick={() => {
          setSelectedRule(null);
        }}
      >
        {!loading && !error && rules.length === 0 && (
          <InfoMessage header={getLocaleMessageById('prioritization.empty')} />
        )}
        {error && <JumbotronError error={error} />}

        <Menu>
          {rules.map((rule) => {
            return (
              <MenuItem
                key={rule.id}
                onClick={(event) => {
                  event.stopPropagation();
                  setSelectedRule(rule);
                }}
                active={rule.id === selectedRule?.id}
              >
                <Switch
                  checked={rule.isEnabled}
                  onClick={(event) => event.stopPropagation()}
                  onChange={({ checked }) =>
                    dispatch(savePrioritizationRule({ ...rule, isEnabled: checked ?? false }))
                  }
                  style={{ cursor: 'pointer' }}
                />
                &nbsp;&nbsp;
                {rule.title}
              </MenuItem>
            );
          })}
        </Menu>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default Prioritization;
