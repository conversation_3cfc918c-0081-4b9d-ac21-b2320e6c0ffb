import React from 'react';

import { ColumnDef } from '@tanstack/react-table';
import clsx from 'clsx';

import { Checkbox, utils } from '@product.front/ui-kit';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { mapOperatorToTableData } from '../mappers/tabledata.mapper';

export const useOperatorsTableColumns = () => {
  return React.useMemo(
    () =>
      [
        {
          header: '',
          accessorKey: 'checked',
          maxSize: 32,
          size: 32,
          accessorFn: (row) => (
            <div className={clsx(utils.dFlex, utils.alignItemsCenter, utils.justifyContentCenter)}>
              <Checkbox
                onClick={(e) => e.preventDefault()}
                checked={row.checked}
                disabled={row.disabled}
              />
            </div>
          ),
        },
        {
          header: getLocaleMessageById('app.operatorsTable.fullName'),
          accessorKey: 'fullName',
          minSize: 150,
          size: 200,
          enableResizing: true,
          enableSorting: true,
          meta: {
            ellipsis: true,
          },
        },
        {
          header: getLocaleMessageById('app.operatorsTable.login'),
          accessorKey: 'login',
          minSize: 100,
          size: 120,
          meta: {
            ellipsis: true,
          },
        },
        {
          header: getLocaleMessageById('app.operatorsTable.groups'),
          accessorKey: 'groups',
          meta: {
            ellipsis: true,
          },
        },
      ] satisfies ColumnDef<ReturnType<typeof mapOperatorToTableData>[number]>[],
    [],
  );
};
