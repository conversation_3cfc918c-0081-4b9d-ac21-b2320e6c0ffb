import React from 'react';

import { ITextareaProps } from '@product.front/ui-kit/dist/types/components/Textarea/Textarea';

import { Textarea } from '@product.front/ui-kit';

import { IFrontStep } from '../../../../../@types/script';
import { AppConfigContext } from '../../../../../context/appConfig';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import InputErrorMessage from '../../../../InputErrorMessage';

import StepDescriptionWYSIWYG from './StepDescriptionWYSIWYG';

export interface IStepDescriptionProps extends Omit<ITextareaProps, 'onChange'> {
  step: IFrontStep;
  onChange: (newStep: IFrontStep) => void;
}

const StepFormDescription: React.FC<IStepDescriptionProps> = ({
  step,
  onChange,
  required,
  disabled,
}) => {
  const { allowRichStepDescription } = React.useContext(AppConfigContext);
  const [description, setDescription] = React.useState(step.description);

  if (allowRichStepDescription) {
    return (
      <StepDescriptionWYSIWYG
        label={getLocaleMessageById('app.modals.form.stepDescription')}
        step={step}
        onChange={onChange}
      />
    );
  }

  return (
    <Textarea
      label={getLocaleMessageById('app.modals.form.stepDescription')}
      value={description}
      onChange={({ value }) => setDescription(value || '')}
      onBlur={() => onChange({ ...step, description })}
      required={required}
      style={{ minHeight: '72px', maxHeight: '20vh' }}
      disabled={disabled}
      isInvalid={!!step.invalidReasons?.description}
      message={<InputErrorMessage>{step.invalidReasons?.description}</InputErrorMessage>}
    />
  );
};

export default StepFormDescription;
