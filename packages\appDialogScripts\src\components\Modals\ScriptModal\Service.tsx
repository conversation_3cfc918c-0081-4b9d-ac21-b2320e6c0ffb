import React from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  grids,
  Input,
  InputSize,
  Loader,
  Text,
  Textarea,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import { AutomationServiceParameterDescription } from '@monorepo/automation-services/src/@types/automationService.types';
import { IFrontServiceParameter, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getAllVariablesFromScriptSteps } from '../../../helpers/scriptVariablesHelpers';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import { getAllAutomationServices } from '../../../store/automationServices/automationServices.thunk';
import { useDialogScriptsAppDispatch, useDialogScriptsAppSelector } from '../../../store/hooks';
import InputErrorMessage from '../../InputErrorMessage';

import AutomationServiceSelect from './AutomationServiceSelect';
import RequestFiles from './components/RequestFiles';
import DeleteStepButton from './DeleteStepButton';
import FirstStepButton from './FirstStepButton';
import ServiceOutputParameter from './ServiceOutputParameter';
import ServiceParameter from './ServiceParameter';
import TransferSelect from './TransferSelect';

import styles from './styles.module.scss';

interface IServiceProps {
  step: IFrontStep;
  steps: IFrontStep[];
  number: number;
  onlyStep: boolean;
  disabled: boolean;
  requiredForActive: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
}

const mergeServiceParameterForScript = (
  asParam: AutomationServiceParameterDescription,
  scriptParam: Partial<IFrontServiceParameter>,
) => {
  return {
    key: asParam.key,
    name: asParam.description,
    description: asParam.reachDescription,
    variableCode: scriptParam.variableCode || '',
    variableName: scriptParam.variableName || '',
    value: scriptParam.value || '',
  };
};

const Service = ({
  step,
  steps,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: IServiceProps) => {
  const dispatch = useDialogScriptsAppDispatch();

  const { automationServices } = useDialogScriptsAppSelector((state) => state.automationServices);

  React.useEffect(() => {
    dispatch(getAllAutomationServices());
  }, [dispatch]);

  const variables: Record<string, IFrontStep['variableName']> = getAllVariablesFromScriptSteps(
    steps.filter((s) => s.code !== step.code),
  );

  if (!automationServices) {
    return (
      <div className={clsx(utils.p4, utils.textCenter)}>
        <Loader />
      </div>
    );
  }

  const currentService = automationServices.find((as) => as.id === step.automationServiceId);
  if (step.automationServiceId && !currentService) {
    return (
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.alignItemsCenter)}>
        <div className={clsx(utils.p4, utils.textCenter)}>
          {getLocaleMessageById('app.step.service.serviceUnavailable', {
            automationServiceId: step.automationServiceId,
          })}
        </div>
        <DeleteStepButton
          needConfirm={Boolean(step.description.length || step.name.length || step.rules?.length)}
          disabled={disabled}
          onDelete={onDelete}
        >
          {getLocaleMessageById('app.step.service.delete')}
        </DeleteStepButton>
      </div>
    );
  }

  const inputParameters = currentService?.requestParameters.reduce<IFrontServiceParameter[]>(
    (acc, servParam) => {
      acc.push(
        mergeServiceParameterForScript(
          servParam,
          step.serviceParameters?.find((sp) => sp.key === servParam.key) || {
            variableCode: '',
            variableName: '',
            value: '',
          },
        ),
      );
      return acc;
    },
    [],
  );

  const outputParameters = currentService?.responseParameters.reduce<IFrontServiceParameter[]>(
    (acc, servParam) => {
      acc.push(
        mergeServiceParameterForScript(
          servParam,
          step.serviceOutputParameters?.find((sp) => sp.key === servParam.key) || {
            variableCode: '',
            value: '',
          },
        ),
      );
      return acc;
    },
    [],
  );

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,

        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {step.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
          <FirstStepButton stepCode={step.code} isFirstStep={step.isFirstStep} />
          {!onlyStep && (
            <DeleteStepButton
              needConfirm={Boolean(
                step.description.length || step.name.length || step.rules?.length,
              )}
              disabled={disabled}
              onDelete={onDelete}
            />
          )}
        </aside>
      </div>
      <Input
        label={getLocaleMessageById('app.editor.routerName')}
        value={step.name}
        autoFocus={!step.name.length}
        onChange={({ value }) => onChange({ ...step, name: value || '' })}
        canClearBehavior={CanClearBehavior.Value}
        required={requiredForActive}
        disabled={disabled}
        withDebounce
        isInvalid={!!step.invalidReasons?.name}
        message={<InputErrorMessage>{step.invalidReasons?.name}</InputErrorMessage>}
      />
      <Textarea
        label={getLocaleMessageById('app.editor.routerDescription')}
        value={step.description}
        onChange={({ value }) => onChange({ ...step, description: value || '' })}
        required={requiredForActive}
        style={{ minHeight: '72px', maxHeight: '20vh' }}
        disabled={disabled}
        withDebounce
        isInvalid={!!step.invalidReasons?.description}
        message={<InputErrorMessage>{step.invalidReasons?.description}</InputErrorMessage>}
      />

      <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
        {getLocaleMessageById('app.editor.serviceTypeHeader')}
      </Text>

      <AutomationServiceSelect
        value={step.automationServiceId}
        onChange={({ data }) => {
          onChange({
            ...step,
            automationServiceId: data?.id,
            serviceParameters: data?.requestParameters?.reduce<IFrontServiceParameter[]>(
              (acc, servParam) => {
                acc.push(
                  mergeServiceParameterForScript(servParam, {
                    variableCode: '',
                    variableName: '',
                    value: '',
                  }),
                );
                return acc;
              },
              [],
            ),
            serviceOutputParameters: data?.responseParameters?.reduce<IFrontServiceParameter[]>(
              (acc, servParam) => {
                acc.push(
                  mergeServiceParameterForScript(servParam, {
                    variableCode: '',
                    value: '',
                  }),
                );
                return acc;
              },
              [],
            ),
          });
        }}
        placeholder={getLocaleMessageById('app.editor.serviceTypePlaceholder')}
        required={requiredForActive}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.invalidAutomationServiceId}
        message={
          <InputErrorMessage>{step.invalidReasons?.invalidAutomationServiceId}</InputErrorMessage>
        }
      />

      {!!inputParameters?.length && (
        <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
          {getLocaleMessageById('app.editor.serviceInputsHeader')}
        </Text>
      )}

      {inputParameters?.map((par) => (
        <ServiceParameter
          key={par.key}
          parameter={par}
          onChange={(changedPar) => {
            onChange({
              ...step,
              serviceParameters: inputParameters?.map((p) =>
                p.key === changedPar.key ? { ...p, ...changedPar } : p,
              ),
            });
          }}
          disabled={disabled}
          variables={variables}
          isInvalid={!!step.invalidReasons?.invalidAutomationInpParams}
          message={step.invalidReasons?.invalidAutomationInpParams}
        />
      ))}

      {currentService?.canUploadFiles && (
        <RequestFiles step={step} variables={variables} onChange={onChange} disabled={disabled} />
      )}

      {!!outputParameters?.length && (
        <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
          {getLocaleMessageById('app.editor.serviceOutputHeader')}
        </Text>
      )}

      {outputParameters?.map((par) => (
        <ServiceOutputParameter
          key={par.key}
          parameter={par}
          onChange={(changedPar) => {
            onChange({
              ...step,
              serviceOutputParameters: outputParameters?.map((p) =>
                p.key === changedPar.key ? { ...p, ...changedPar } : p,
              ),
            });
          }}
          disabled={disabled}
          variables={variables}
        />
      ))}
      <InputErrorMessage>{step.invalidReasons?.variableName}</InputErrorMessage>
      <Text as="h3" variant={TextVariant.SubheadSemibold} className={utils.mBn2}>
        {getLocaleMessageById('app.editor.serviceTransferHeader')}
      </Text>
      <div className={grids.row}>
        <Text className={grids.col4}>
          {getLocaleMessageById('app.editor.serviceTransferSuccess')}
        </Text>
        <div className={grids.col8}>
          <TransferSelect
            steps={steps.filter((s) => s.code !== step.code)}
            placeholder={getLocaleMessageById('app.modals.form.transfer')}
            value={step.stepTransfer}
            onChange={({ value }) => onChange({ ...step, stepTransfer: value || 'default' })}
            required={requiredForActive}
            disabled={disabled}
            isInvalid={!!step.invalidReasons?.invalidDefaultRelation}
            withEnd={false}
            message={
              <InputErrorMessage>{step.invalidReasons?.invalidDefaultRelation}</InputErrorMessage>
            }
            size={InputSize.Small}
          />
        </div>
      </div>
      <div className={grids.row}>
        <Text className={grids.col4}>
          {getLocaleMessageById('app.editor.serviceTransferError')}
        </Text>
        <div className={grids.col8}>
          <TransferSelect
            steps={steps.filter((s) => s.code !== step.code)}
            placeholder={getLocaleMessageById('app.modals.form.transfer')}
            value={step.stepFallbackTransfer}
            onChange={({ value }) =>
              onChange({ ...step, stepFallbackTransfer: value || 'default' })
            }
            required={requiredForActive}
            disabled={disabled}
            isInvalid={!!step.invalidReasons?.invalidFallbackRelation}
            withEnd={false}
            message={
              <InputErrorMessage>{step.invalidReasons?.invalidFallbackRelation}</InputErrorMessage>
            }
            size={InputSize.Small}
          />
        </div>
      </div>

      {!!step.invalidReasons?.invalidRelation && (
        <aside className={utils.mY4}>
          <InputErrorMessage>{step.invalidReasons?.invalidRelation}</InputErrorMessage>
        </aside>
      )}
    </div>
  );
};

export default Service;
