import { createAsyncThunk } from '@reduxjs/toolkit';

import { IFrontFolder } from '@monorepo/common/src/@types/templates';

import { AnswerTemplate, TemplateCategory } from '../../@types/generated/administration';
import { mapAnswerFolderToFrontFolder } from '../../mappers/folders';
import { mapTemplateToFrontTemplate } from '../../mappers/templates';
import {
  getFolderTemplates,
  getTemplate,
  ISearchFolderTemplatesOptions,
} from '../../services/template';

export const getAllFolderTemplates = createAsyncThunk(
  'templates/allFolderTemplates',
  async (searchOptions: ISearchFolderTemplatesOptions) => {
    const data = await getFolderTemplates(TemplateCategory.Answers, searchOptions);

    const foldersMap: Record<string, IFrontFolder> = {};

    mapAnswerFolderToFrontFolder(data.answers, foldersMap, true);

    return foldersMap;
  },
);

export const selectTemplate = createAsyncThunk('templates/select', async (templateId: string) => {
  const data = (await getTemplate(templateId)) as AnswerTemplate;

  return mapTemplateToFrontTemplate(data);
});
