export const getJSONStringInvalidReason = (jsonStr: string): string | undefined => {
  try {
    JSON.parse(jsonStr);
    return;
  } catch (err) {
    return err.message;
  }
};

export const getXMLStringInvalidReason = (xmlStr: string): string | undefined => {
  try {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlStr, 'text/xml');
    if (xmlDoc.getElementsByTagName('parsererror').length) {
      return xmlDoc.getElementsByTagName('parsererror')[0].textContent || undefined;
    } else {
      return;
    }
  } catch (err) {
    return err.message;
  }
};
