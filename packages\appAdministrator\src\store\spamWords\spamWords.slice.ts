import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { SpamWord } from '../../@types/generated/administration';

import extraReducers from './spamWords.extraReducers';

export interface ISpamWordsStore {
  loading: boolean;
  error?: SerializedError;
  spamWords: SpamWord[];
}

const initialState: ISpamWordsStore = {
  loading: false,
  spamWords: [],
};

const spamWordsSlice = createSlice({
  name: 'spamWords',
  initialState,
  reducers: {},
  extraReducers,
});

export default spamWordsSlice.reducer;
