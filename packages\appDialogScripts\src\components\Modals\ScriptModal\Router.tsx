import React from 'react';

import clsx from 'clsx';

import {
  <PERSON>ton,
  ButtonVariant,
  CanClearBehavior,
  Input,
  Text,
  Textarea,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconAdd from '@product.front/icons/dist/icons17/MainStuff/IconAdd';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { getAllVariablesFromScriptSteps } from '../../../helpers/scriptVariablesHelpers';
import { getNoNameNameForStep } from '../../../helpers/stepListHelper';
import InputErrorMessage from '../../InputErrorMessage';

import DeleteStepButton from './DeleteStepButton';
import FirstStepButton from './FirstStepButton';
import Rule from './Rule';
import TransferSelect from './TransferSelect';

import styles from './styles.module.scss';

interface IRouterProps {
  step: IFrontStep;
  steps: IFrontStep[];
  number: number;
  onlyStep: boolean;
  disabled: boolean;
  requiredForActive: boolean;
  onDelete: (isMentioned?: boolean) => void;
  onChange: (newStep: IFrontStep) => void;
  flush?: boolean;
}

const Router = ({
  step,
  steps,
  onlyStep,
  disabled,
  requiredForActive,
  onDelete,
  onChange,
  flush,
}: IRouterProps) => {
  const handleRuleAdd = () => {
    onChange({
      ...step,
      rules: [
        ...(step.rules || []),
        {
          id: `${step.code}-rule-${new Date().toISOString()}`,
          name: '',
          transferTo: 'default',
          conditions: [],
        },
      ],
    });
  };

  const variables: Record<string, IFrontStep['variableName']> =
    getAllVariablesFromScriptSteps(steps);

  return (
    <div
      className={clsx(
        utils.dFlex,
        utils.gap5,
        utils.flexColumn,

        !flush && clsx(utils.border, utils.p6),
        styles.step,
      )}
    >
      <div className={clsx(utils.dFlex, utils.justifyContentBetween, utils.alignItemsCenter)}>
        <div className={clsx(utils.dFlex, utils.gap6)}>
          <Text variant={TextVariant.SubheadSemibold}>
            {step.name || getNoNameNameForStep(step)}
          </Text>
        </div>
        <aside className={clsx(utils.dFlex, utils.alignItemsCenter)}>
          <FirstStepButton stepCode={step.code} isFirstStep={step.isFirstStep} />
          {!onlyStep && (
            <DeleteStepButton
              needConfirm={Boolean(
                step.description.length || step.name.length || step.rules?.length,
              )}
              disabled={disabled}
              onDelete={onDelete}
            />
          )}
        </aside>
      </div>
      <Input
        label={getLocaleMessageById('app.editor.routerName')}
        value={step.name}
        autoFocus={!step.name.length}
        onChange={({ value }) => onChange({ ...step, name: value || '' })}
        canClearBehavior={CanClearBehavior.Value}
        required={requiredForActive}
        disabled={disabled}
        withDebounce
        isInvalid={!!step.invalidReasons?.name}
        message={<InputErrorMessage>{step.invalidReasons?.name}</InputErrorMessage>}
      />
      <Textarea
        label={getLocaleMessageById('app.editor.routerDescription')}
        value={step.description}
        onChange={({ value }) => onChange({ ...step, description: value || '' })}
        required={requiredForActive}
        style={{ minHeight: '72px', maxHeight: '20vh' }}
        disabled={disabled}
        withDebounce
        isInvalid={!!step.invalidReasons?.description}
        message={<InputErrorMessage>{step.invalidReasons?.description}</InputErrorMessage>}
      />

      <TransferSelect
        withEnd={false}
        steps={steps.filter((s) => s.code !== step.code)}
        label={getLocaleMessageById('app.modals.form.transfer')}
        value={step.stepTransfer}
        onChange={({ value }) => onChange({ ...step, stepTransfer: value || 'default' })}
        required={requiredForActive}
        disabled={disabled}
        isInvalid={!!step.invalidReasons?.invalidDefaultRelation}
        message={
          <InputErrorMessage>{step.invalidReasons?.invalidDefaultRelation}</InputErrorMessage>
        }
      />

      {!!step.invalidReasons?.rules && (
        <aside className={utils.mY2}>
          <InputErrorMessage>
            {step.invalidReasons?.rules}&nbsp; ({getLocaleMessageById('app.editor.routerRule')})
          </InputErrorMessage>
        </aside>
      )}
      {step.rules?.map((rule, index, all) => (
        <Rule
          key={rule.id}
          rule={rule}
          canDelete={all.length > 1}
          onDelete={() =>
            onChange({
              ...step,
              rules: step.rules?.filter((r) => r.id !== rule.id),
            })
          }
          onChange={(updatedRule) =>
            onChange({
              ...step,
              rules:
                step.rules?.map((r) => (r.id === rule.id ? { ...r, ...updatedRule } : r)) || [],
            })
          }
          steps={steps.filter((s) => s.code !== step.code)}
          variables={variables}
          onMoveUp={
            index === 0
              ? undefined
              : () => {
                  // @todo https://web.dev/drag-and-drop/
                  onChange({
                    ...step,
                    rules: [
                      ...(step.rules?.slice(0, index - 1) || []),
                      step.rules![index],
                      step.rules![index - 1],
                      ...(step.rules?.slice(index + 1) || []),
                    ].map((r, i) => ({ ...r, priority: i })),
                  });
                }
          }
          onMoveDown={
            index === all.length - 1
              ? undefined
              : () => {
                  onChange({
                    ...step,
                    rules: [
                      ...(step.rules?.slice(0, index) || []),
                      step.rules![index + 1],
                      step.rules![index],
                      ...(step.rules?.slice(index + 2) || []),
                    ].map((r, i) => ({ ...r, priority: i })),
                  });
                }
          }
          invalidReasons={step.invalidReasons?.rulesInvalidReasons?.[rule.id]}
        />
      ))}

      <Button
        className={clsx(utils.dFlex, utils.gap2)}
        variant={ButtonVariant.Transparent}
        onClick={handleRuleAdd}
        style={{ alignSelf: 'start', padding: 0 }}
        disabled={disabled}
      >
        <IconAdd />
        {getLocaleMessageById('app.editor.routerRule')}
      </Button>
    </div>
  );
};

export default Router;
