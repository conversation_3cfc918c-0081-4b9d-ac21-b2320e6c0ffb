import React from 'react';

import clsx from 'clsx';
import { Edge } from 'reactflow';

import { utils } from '@product.front/ui-kit';

import { IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import EdgeEditor from './EdgeEditor';
import VisualStepEditor from './VisualStepEditor';

import styles from './styles.module.scss';

interface ISidebarProps {
  step?: IFrontStep;
  edge?: Edge;
  onClose(): void;
}

const Sidebar: React.FC<ISidebarProps> = ({ step, edge, onClose }) => {
  const formRef = React.useRef<HTMLFormElement>(null);

  return (
    <aside
      className={clsx(
        utils.borderLeft,
        utils.dFlex,
        utils.flexColumn,
        styles.sidebar,
        styles.modalValidated,
      )}
    >
      <div className={utils.flexGrow1}>
        {step && <VisualStepEditor step={step} onClose={onClose} ref={formRef} />}
        {edge && <EdgeEditor edge={edge} onClose={onClose} ref={formRef} />}
      </div>
    </aside>
  );
};
export default Sidebar;
