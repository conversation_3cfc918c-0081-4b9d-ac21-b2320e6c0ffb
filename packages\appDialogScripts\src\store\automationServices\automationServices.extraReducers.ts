import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IAutomationServicesStore } from './automationServices.slice';
import { getAllAutomationServices } from './automationServices.thunk';

export default (builder: ActionReducerMapBuilder<IAutomationServicesStore>) =>
  builder
    .addCase(getAllAutomationServices.pending, (state) => {
      state.loading = true;
      state.error = undefined;
    })
    .addCase(getAllAutomationServices.fulfilled, (state, action) => {
      state.loading = false;
      state.automationServices = action.payload;
    })
    .addCase(getAllAutomationServices.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error;
    });
