import { UcmmChannel } from '@monorepo/common/src/@types/frontendChat';
import {
  FrontTemplateStatus,
  IFrontFolder,
  IFrontTemplate,
  IFrontTemplateVersion,
} from '@monorepo/common/src/@types/templates';
import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  AnswerTemplate,
  AnswerTemplateAddEdit,
  AutoReplyAddEdit,
  AutoReplyTemplate,
  FolderBase,
  PersonalTemplate,
  PersonalTemplateAddEdit,
  TemplateCategory,
  TemplateTrees,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';
import { mapFrontTemplateStatusToDto } from '../mappers/templates';

export const emptyFolder: IFrontFolder = {
  id: '',
  title: '',
  parentId: null,
  ownerId: null,
  templates: [],
  childrenFolders: [],
};

export const emptyVersion: IFrontTemplateVersion = {
  id: new Date().toISOString(),
  attachments: [],
  contents: [
    {
      id: '',
      text: '',
      channel: UcmmChannel.Default,
    },
  ],
  interval: {
    from: new Date().toISOString(),
    to: null,
  },
};

export const emptyTemplate: IFrontTemplate = {
  id: '',
  status: FrontTemplateStatus.Draft,
  title: '',
  ownerId: null,
  code: '',
  folderId: null,
  currentVersion: emptyVersion,
  keyWords: [],
  versions: [emptyVersion],
};

export interface ISearchFolderTemplatesOptions {
  keyWord?: string;
  title?: string;
  text?: string;
  statuses?: FrontTemplateStatus[];
}

// answer templates overload
export async function getFolderTemplates(
  category: TemplateCategory.Answers,
  searchOptions: ISearchFolderTemplatesOptions,
): Promise<Required<Omit<TemplateTrees, 'autoReplies' | 'personal'>>>;

// auto replies templates overload
export async function getFolderTemplates(
  category: TemplateCategory.AutoReplies,
  searchOptions: ISearchFolderTemplatesOptions,
): Promise<Required<Omit<TemplateTrees, 'answers' | 'personal'>>>;

// personal templates overload
export async function getFolderTemplates(
  category: TemplateCategory.Personal,
  searchOptions: ISearchFolderTemplatesOptions,
): Promise<Required<Omit<TemplateTrees, 'autoReplies' | 'answers'>>>;

export async function getFolderTemplates(
  category: TemplateCategory,
  searchOptions: ISearchFolderTemplatesOptions,
) {
  const { keyWord, title, text, statuses } = searchOptions;

  const query = new URLSearchParams();
  query.append('Categories', category);
  keyWord && query.append('KeyWord', keyWord);
  title && query.append('Title', title);
  text && query.append('Text', text);
  statuses?.forEach((status) => query.append('Statuses', mapFrontTemplateStatusToDto(status)));

  const response = await commonFetch(
    `${getSettings().administrationApiUrl}/templates?${query.toString()}`,
    {
      credentials: 'include',
    },
  );

  return await response.json();
}

export const createUpdateFolder = async (data: IFrontFolder, kind: FolderBase['kind']) => {
  const folderToCreate: FolderBase = {
    kind,
    title: data.title,
    parentId: data.parentId,
  };

  if (kind === 'AnswerTemplateFolder') {
    folderToCreate.relationIds = data.relationIds;
  }

  await commonFetch(
    `${getSettings().administrationApiUrl}/templates/folders/${data.id ? data.id : ''}`,
    {
      method: data.id ? 'PUT' : 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(folderToCreate),
    },
  );
};

export const deleteFolder = async (folderId: string) => {
  await commonFetch(`${getSettings().administrationApiUrl}/templates/folders/${folderId}`, {
    method: 'DELETE',
    credentials: 'include',
  });
};

export const getTemplate = async (templateId: string) => {
  const response = await commonFetch(
    `${getSettings().administrationApiUrl}/templates/${templateId}`,
    {
      credentials: 'include',
    },
  );

  return (await response.json()) as AnswerTemplate | PersonalTemplate | AutoReplyTemplate;
};

export const createTemplate = async (
  data: AutoReplyAddEdit | AnswerTemplateAddEdit | PersonalTemplateAddEdit,
) => {
  const url = `${getSettings().administrationApiUrl}/templates`;
  const response = await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  return (await response.json()) as string;
};

export const updateTemplate = async (
  data: AutoReplyAddEdit | AnswerTemplateAddEdit | PersonalTemplateAddEdit,
) => {
  return await commonFetch(`${getSettings().administrationApiUrl}/templates/${data.id}`, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
};

export const deleteTemplate = async (templateId: string) => {
  return await commonFetch(`${getSettings().administrationApiUrl}/templates/${templateId}`, {
    method: 'DELETE',
    credentials: 'include',
  });
};
