import React, { HTMLAttributes } from 'react';

import clsx from 'clsx';

import styles from './styles.module.scss';

const IconRouter: React.FC<HTMLAttributes<HTMLDivElement>> = ({ className, style = {} }) => {
  return (
    <div className={clsx(styles.routerIconWrapper, className)}>
      <div className={styles.routerIcon} style={style} role="img" aria-label="router icon" />
    </div>
  );
};

export default IconRouter;
