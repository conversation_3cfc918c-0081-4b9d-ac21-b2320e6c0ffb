import React from 'react';

import { ISelectProps } from '@product.front/ui-kit/dist/types/components/Select/Select';

import { Loader, Select } from '@product.front/ui-kit';

import {
  AutomationServiceStatus,
  IFrontAutomationService,
} from '@monorepo/automation-services/src/@types/automationService.types';

import { getAllAutomationServices } from '../../../store/automationServices/automationServices.thunk';
import { useDialogScriptsAppDispatch, useDialogScriptsAppSelector } from '../../../store/hooks';

interface IAutomationServiceSelectProps extends Omit<ISelectProps, 'value' | 'onChange' | 'data'> {
  value?: number | null;
  onChange?: (changeArgs: { value: number | null; data: IFrontAutomationService | null }) => void;
}

const AutomationServiceSelect: React.FC<IAutomationServiceSelectProps> = ({
  value,
  onChange,
  ...rest
}) => {
  const dispatch = useDialogScriptsAppDispatch();

  const { automationServices } = useDialogScriptsAppSelector((state) => state.automationServices);

  React.useEffect(() => {
    if (!automationServices) {
      dispatch(getAllAutomationServices());
    }
  }, [automationServices, dispatch]);

  const dataForSelect = React.useMemo(() => {
    if (!automationServices) {
      return [];
    }
    return automationServices.map((srv) => ({
      text: srv.name,
      value: srv.id!.toString(),
      data: srv,
      disabled: srv.status !== AutomationServiceStatus.Active,
    }));
  }, [automationServices]);

  if (!automationServices) {
    return <Loader />;
  }

  return (
    <Select
      value={value?.toString()}
      onChange={({ value: v }) => {
        onChange?.({
          value: value ?? null,
          data: dataForSelect.find((opt) => opt.value?.toString() === v)?.data ?? null,
        });
      }}
      data={dataForSelect}
      disabled={rest.disabled || !automationServices?.length}
      {...rest}
    />
  );
};

export default AutomationServiceSelect;
