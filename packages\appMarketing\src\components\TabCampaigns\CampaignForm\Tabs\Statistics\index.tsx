import React from 'react';

import clsx from 'clsx';

import {
  grids,
  Jumbotron,
  JumbotronType,
  Loader,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import BagelChart from '@monorepo/common/src/components/Charts/BagelChart';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { getStatistics } from '../../../../../store/campaigns/campaigns.thunk';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../../../../store/hooks';

import ProgressChart from './ProgressChart';

const StatisticsTab = () => {
  const dispatch = useMarketingAppDispatch();

  const { statistics, statisticsLoading, statisticsError, selectedCampaign } =
    useMarketingAppSelector((state) => state.campaigns);

  React.useEffect(() => {
    if (!selectedCampaign?.id) return;

    dispatch(getStatistics(selectedCampaign.id));
  }, [dispatch, selectedCampaign?.id]);

  if (!statistics || statisticsLoading) {
    return (
      <div
        className={clsx(
          utils.dFlex,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
          utils.w100,
          utils.h100,
        )}
      >
        <Loader />
      </div>
    );
  }

  if (statisticsError) return <>{statisticsError}</>;

  const totalOffered =
    statistics.runningCampaign.find((value) => value.key === 'OfferedNumber')?.value ?? 0;
  const totalRefused =
    statistics.runningCampaign.find((value) => value.key === 'RefusedNumber')?.value ?? 0;

  return (
    <div className={clsx(grids.row, utils.p6)} style={{ gap: '32px' }}>
      <div className={grids.col4}>
        <BagelChart
          data={[
            {
              answersNumber:
                ((statistics.conversionPercent ?? 0) * (statistics.conversionPercent ?? 0)) / 100,
              text: '',
            },
            {
              answersNumber:
                ((statistics.conversionPercent ?? 0) *
                  (100 - (statistics.conversionPercent ?? 0))) /
                100,
              text: '',
            },
          ]}
          totalNumber={statistics.conversionPercent ?? 0}
          hideLegend
          totalWords={getLocaleMessageById('app.campaigns.form.statistics.conversion')}
          chartSize={250}
          totalValuePostfix="%"
        />
      </div>
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4, grids.col8)}>
        <Text className={clsx(utils.borderBottom, utils.w100)} variant={TextVariant.SubheadMedium}>
          {getLocaleMessageById('app.campaigns.form.statistics.campaignFunnel')}
        </Text>
        <ProgressChart />
      </div>
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4, grids.col6)}>
        <Text className={clsx(utils.borderBottom, utils.w100)} variant={TextVariant.SubheadMedium}>
          {getLocaleMessageById('app.campaigns.form.statistics.results')}
        </Text>
        {totalOffered === 0 ? (
          <Jumbotron
            type={JumbotronType.Info}
            header={getLocaleMessageById('app.campaigns.form.statistics.noResults')}
          />
        ) : (
          <BagelChart
            data={statistics.resultsStatistic.map((value) => ({
              answersNumber: Math.ceil((value.value * totalOffered) / 100),
              text: value.key,
            }))}
            totalNumber={totalOffered}
            totalWords={getLocaleMessageById('app.campaigns.form.statistics.offersTotal')}
            chartSize={250}
          />
        )}
      </div>
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4, grids.col6)}>
        <Text className={clsx(utils.borderBottom, utils.w100)} variant={TextVariant.SubheadMedium}>
          {getLocaleMessageById('app.campaigns.form.statistics.refusals')}
        </Text>
        {totalRefused === 0 ? (
          <Jumbotron
            type={JumbotronType.Info}
            header={getLocaleMessageById('app.campaigns.form.statistics.noRefusals')}
          />
        ) : (
          <BagelChart
            data={statistics.refusalReasons.map((value) => ({
              answersNumber: Math.ceil((value.value * totalRefused) / 100),
              text: value.key,
            }))}
            totalNumber={totalRefused}
            totalWords={getLocaleMessageById('app.campaigns.form.statistics.refusalsTotal')}
            chartSize={250}
          />
        )}
      </div>
    </div>
  );
};

export default StatisticsTab;
