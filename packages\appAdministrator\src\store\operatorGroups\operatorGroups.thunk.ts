import { createAsyncThunk } from '@reduxjs/toolkit';

import { IFrontOperatorGroup, IFrontOperatorGroupBase } from '../../@types/operatorGroup';
import {
  mapFrontOperatorGroupToNewDto,
  mapOperatorGroupDtoToFront,
  mapOperatorGroupViewDtoToFront,
} from '../../mappers/operatorGroups';
import * as api from '../../services/operatorGroups';

export const getAllOperatorGroups = createAsyncThunk('operatorGroups/all', async () => {
  const operatorGroupsDto = await api.getOperatorGroups();

  const operatorGroupsMap: Record<string, IFrontOperatorGroupBase> = {};

  operatorGroupsDto.forEach((operatorGroup) => {
    operatorGroupsMap[operatorGroup.id] = {
      ...mapOperatorGroupDtoToFront({
        ...operatorGroup,
        childOperatorGroupsIds: operatorGroupsMap[operatorGroup.id]?.childOperatorGroupsIds ?? [],
      }),
    };
    if (operatorGroup.parentId) {
      operatorGroupsMap[operatorGroup.parentId] = {
        ...operatorGroupsMap[operatorGroup.parentId],
        childOperatorGroupsIds: [
          ...(operatorGroupsMap[operatorGroup.parentId]?.childOperatorGroupsIds ?? []),
          operatorGroup.id,
        ],
      };
    }
  });

  return operatorGroupsMap;
});

export const getOperatorGroup = createAsyncThunk('operatorGroups/get', async (id: string) => {
  const operatorGroupDto = await api.getOperatorGroup(id);

  return mapOperatorGroupViewDtoToFront(operatorGroupDto);
});

export const saveOperatorGroup = createAsyncThunk(
  'operatorGroups/save',
  async (operatorGroup: IFrontOperatorGroup, { dispatch }) => {
    if (operatorGroup.id) {
      await api.updateOperatorGroup(operatorGroup.id, mapFrontOperatorGroupToNewDto(operatorGroup));
    } else {
      await api.createOperatorGroup(mapFrontOperatorGroupToNewDto(operatorGroup));
    }

    dispatch(getAllOperatorGroups());
  },
);

export const deleteOperatorGroup = createAsyncThunk(
  'operatorGroups/delete',
  async (id: string, { dispatch }) => {
    await api.deleteOperatorGroup(id);
    dispatch(getAllOperatorGroups());
  },
);
