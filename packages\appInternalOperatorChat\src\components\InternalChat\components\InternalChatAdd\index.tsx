import React from 'react';

import clsx from 'clsx';

import {
  Avatar,
  AvatarVariant,
  Button,
  ButtonSize,
  ButtonVariant,
  IconButton,
  Input,
  Loader,
  LoaderSize,
  showModal,
  Text,
  utils,
} from '@product.front/ui-kit';

import IconClose from '@product.front/icons/dist/icons17/MainStuff/IconClose';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';
import IconUsers from '@product.front/icons/dist/icons17/Person&Doc/IconUsers';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import { CommonErrorType } from '@monorepo/common/src/common/types/error.types';
import { getPluralGroup } from '@monorepo/common/src/helpers/localeHelper';

import { Chat, Operator } from '../../../../@types/generated/signalr';
import { getAbbr } from '../../../../helpers/abbrHelper';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { injectGroupsToOperators } from '../../../../helpers/operatorsGroupsHelper';
import { createChatAsync } from '../../../../services/internalChat.service';
import { useInternalChatSelector } from '../../../../store/hooks';
import InternalChatLeftPanel from '../InternalChatLeftPanel';
import InternalChatMembersSelectorModal from '../InternalChatMembersSelectorModal';
import InternalChatOperator from '../InternalChatOperator';

import styles from './styles.module.scss';

interface IInternalChatChatAddProps {
  onCancel: () => void;
  onSave: (createdChat: Chat) => void;
}

const defaultChatName = getLocaleMessageById('app.defaultChatName');

const InternalChatChatAdd: React.FC<IInternalChatChatAddProps> = ({ onCancel, onSave }) => {
  const { allOperators, allOperatorsGroups } = useInternalChatSelector(
    (state) => state.internalChat,
  );

  const { user } = useInternalChatSelector((state) => state.user);

  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [name, setName] = React.useState<string>(defaultChatName);
  const [err, setErr] = React.useState<Error>();
  const [members, setMembers] = React.useState<Array<Operator | null>>([user]);

  const membersCountTitle = getLocaleMessageById('app.chat.membersNumber', {
    count: members.length,
    countGroup: getPluralGroup(members.length),
  });

  const handleSave = async () => {
    if (!name?.length) {
      setErr(new Error(getLocaleMessageById('app.error.emptyChatName')));
      return;
    }
    setIsLoading(true);
    try {
      const participants = members
        .filter((member) => !!member?.id)
        .map((member) => member!.id) as string[];

      const chat = await createChatAsync({ name, participants });

      onSave(chat);
    } catch (e) {
      setErr(e);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = () => {
    showModal({
      header: getLocaleMessageById('app.header.addMembers'),
      children: (onClose) => (
        <InternalChatMembersSelectorModal
          selected={members as Operator[]}
          allOperators={injectGroupsToOperators(allOperators, allOperatorsGroups)}
          onCancel={onClose!}
          onSave={(sel) => {
            setMembers(sel);
            onClose!();
          }}
          user={user as Operator}
        />
      ),
    });
  };

  const deleteMember = (memberId?: string) =>
    setMembers((m) => m.filter((op) => op?.id !== memberId));

  return (
    <InternalChatLeftPanel
      title={getLocaleMessageById('app.title.createChat')}
      buttons={
        <IconButton onClick={onCancel} disabled={isLoading}>
          <IconClose />
        </IconButton>
      }
      footer={
        <Button
          size={ButtonSize.Big}
          className={clsx(utils.dBlock, utils.w100)}
          onClick={handleSave}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader size={LoaderSize.Small} />
          ) : (
            getLocaleMessageById('app.button.createChat')
          )}
        </Button>
      }
    >
      <div className={clsx(utils.p5, utils.dFlex, utils.alignItemsCenter)}>
        <Avatar
          alt={getAbbr(name)}
          colorize={name}
          variant={AvatarVariant.Dark}
          className={utils.mR4}
        />
        <Input disabled={isLoading} onChange={({ value }) => setName(value || '')} value={name} />
      </div>
      <div
        className={clsx(
          utils.pL5,
          utils.borderBottom,
          utils.dFlex,
          utils.alignItemsCenter,
          utils.justifyContentBetween,
        )}
      >
        <Text>{membersCountTitle}</Text>
        <Button
          variant={ButtonVariant.Transparent}
          className={clsx(utils.dFlex, utils.flexNoWrap, utils.gap1, utils.mR1)}
          disabled={isLoading}
          onClick={handleEdit}
        >
          <IconUsers />
          {getLocaleMessageById('app.button.changeMembers')}
        </Button>
      </div>
      <div className={clsx(utils.pY2, styles.membersList, utils.scrollbar)}>
        {members.map((pp) => {
          if (!pp) {
            return null;
          }
          const userStatus = allOperators.find((op) => op.id === pp.id)?.status;
          return (
            <div key={pp.id} className={clsx(utils.pY2, utils.pX5, styles.member)}>
              <InternalChatOperator
                key={pp.id}
                me={pp.id === user?.id}
                operator={pp}
                status={userStatus!}
              />
              {pp.id !== user?.id && (
                <div className={styles.toolbar}>
                  <IconButton disabled={isLoading} onClick={() => deleteMember(pp.id)}>
                    <IconTrash />
                  </IconButton>
                </div>
              )}
            </div>
          );
        })}
      </div>
      {err && (
        <div className={utils.p3}>
          <AlertError error={err} commonErrorType={CommonErrorType.InitApp} />
        </div>
      )}
    </InternalChatLeftPanel>
  );
};

export default InternalChatChatAdd;
