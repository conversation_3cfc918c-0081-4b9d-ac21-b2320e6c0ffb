import { createAsyncThunk } from '@reduxjs/toolkit';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';
import {
  getCurrentOperatorFullNameString,
  getCurrentOperatorUsernameString,
} from '@monorepo/common/src/managers/currentOperatorManager';

import { AddSpamWord, SpamWord, UpdateSpamWord } from '../../@types/generated/administration';
import { getSettings } from '../../helpers/appSettings';

export const getAllSpamWords = createAsyncThunk('spamWords/all', async () => {
  const response = await commonFetch(`${getSettings().administrationApiUrl}/spam-words`, {
    credentials: 'include',
  });

  return (await response.json()) as SpamWord[];
});

export const createSpamWord = createAsyncThunk(
  'spamWords/create',
  async (word: Omit<AddSpamWord, 'code' | 'comment' | 'addedBy' | 'addedByFio'>, { dispatch }) => {
    await commonFetch(`${getSettings().administrationApiUrl}/spam-words`, {
      method: 'POST',
      credentials: 'include',
      body: JSON.stringify({
        ...word,
        code: word.name,
        comment: '',
        addedBy: getCurrentOperatorUsernameString(),
        addedByFio: getCurrentOperatorFullNameString(),
      } satisfies AddSpamWord),
      headers: {
        'Content-Type': 'application/json',
      },
    });
    dispatch(getAllSpamWords());
  },
);

export const updateSpamWord = createAsyncThunk(
  'spamWords/update',
  async (
    word: Omit<UpdateSpamWord, 'code' | 'comment' | 'addedBy' | 'addedByFio'> & {
      id: SpamWord['id'];
    },
    { dispatch },
  ) => {
    await commonFetch(`${getSettings().administrationApiUrl}/spam-words/${word.id}`, {
      method: 'PUT',
      credentials: 'include',
      body: JSON.stringify({
        ...word,
        code: word.name,
        comment: '',
        addedBy: getCurrentOperatorUsernameString(),
        addedByFio: getCurrentOperatorFullNameString(),
      } satisfies UpdateSpamWord),
      headers: {
        'Content-Type': 'application/json',
      },
    });
    dispatch(getAllSpamWords());
  },
);

export const deleteSpamWord = createAsyncThunk(
  'spamWords/delete',
  async (id: SpamWord['id'], { dispatch }) => {
    if (!id) return;

    await commonFetch(
      `${getSettings().administrationApiUrl}/spam-words/${id}?deletedBy=${getCurrentOperatorUsernameString()}`,
      {
        method: 'DELETE',
        credentials: 'include',
      },
    );
    dispatch(getAllSpamWords());
  },
);
