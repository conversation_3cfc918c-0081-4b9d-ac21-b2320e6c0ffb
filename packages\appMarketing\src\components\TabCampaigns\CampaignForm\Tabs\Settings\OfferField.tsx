import React from 'react';

import clsx from 'clsx';

import { Text, grids, utils, Colors } from '@product.front/ui-kit';

interface IOfferField {
  label: string;
  value: string;
}

const OfferField = ({ label, value }: IOfferField) => {
  return (
    <div className={clsx(grids.col4, utils.dFlex, utils.flexColumn, utils.gap1)}>
      <Text color={Colors.OnyxBlack60}>{label}</Text>
      <Text>{value || '-'}</Text>
    </div>
  );
};

export default OfferField;
