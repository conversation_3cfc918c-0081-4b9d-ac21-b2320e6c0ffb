import React from 'react';

import clsx from 'clsx';

import {
  Colors,
  FloatingTooltip,
  IconButton,
  IconButtonProps,
  LoaderSize,
  OverlayLoader,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

interface IAdmToolbarIconButtonProps extends IconButtonProps {
  tooltip: string;
  hotkey?: string[];
  loading?: boolean;
}

const AdmToolbarIconButton: React.FC<IAdmToolbarIconButtonProps> = ({
  loading = false,
  tooltip,
  children,
  hotkey,
  disabled,
  ...rest
}) => {
  return (
    <OverlayLoader
      wrapperClassName={clsx(utils.dFlex, utils.alignItemsCenter, utils.flexBasis0)}
      loading={loading}
      loaderSize={LoaderSize.Auto}
    >
      <FloatingTooltip
        tooltip={
          <>
            {tooltip}
            {hotkey && (
              <div className={utils.mT1} style={{ opacity: 0.4 }}>
                {hotkey.map((k) => (
                  <Text
                    variant={TextVariant.TinySemibold}
                    as="kbd"
                    key={k}
                    color={Colors.OnyxBlack50}
                    className={clsx(
                      utils.border,
                      utils.radius1,
                      utils.mR1,
                      utils.dInlineBlock,
                      utils.pX1,
                    )}
                  >
                    {k}
                  </Text>
                ))}
              </div>
            )}
          </>
        }
      >
        <IconButton disabled={loading || disabled} {...rest}>
          {children}
        </IconButton>
      </FloatingTooltip>
    </OverlayLoader>
  );
};

export default AdmToolbarIconButton;
