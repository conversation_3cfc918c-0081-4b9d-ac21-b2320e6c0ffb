import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontFolder, IFrontTemplate } from '@monorepo/common/src/@types/templates';

import extraReducers from './autoTemplates.extraReducers';

export interface IAutoTemplatesStore {
  loading: boolean;
  error?: SerializedError;
  folders: Record<string, IFrontFolder>;
  selectedTemplate: IFrontTemplate | null;
}

const initialState: IAutoTemplatesStore = {
  loading: false,
  folders: {},
  selectedTemplate: null,
};

const autoTemplatesSlice = createSlice({
  name: 'autoTemplates',
  initialState,
  reducers: {},
  extraReducers,
});

export default autoTemplatesSlice.reducer;
