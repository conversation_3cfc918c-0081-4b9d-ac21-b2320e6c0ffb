import React from 'react';

import clsx from 'clsx';
import { Guid } from 'guid-typescript';

import {
  AttentionIcon,
  AttentionIconSize,
  AttentionIconType,
  Button,
  ButtonVariant,
  Input,
  ITab,
  OverlayLoader,
  Tabs,
  utils,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';
import { checkObjectsEqual } from '@monorepo/common/src/helpers/objectsHelper';
import { getProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';
import { Channel } from '@monorepo/services/src/@types/generated/productApi';

import {
  IFrontOperator,
  IFrontOperatorBase,
  IFrontSessionSettings,
  IFrontStatusSettings,
} from '../../../../../@types/operator';
import { IFrontKpiThresholdValue } from '../../../../../@types/parameters';
import { needConfirmWhenCompareFalse } from '../../../../../helpers/confirmSave.helper';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { mapFrontOperatorToNewDto } from '../../../../../mappers/operators';
import {
  createOperator,
  getOperatorParameters,
  updateOperator,
} from '../../../../../services/operators';
import OperatorKpi from '../../Kpi/OperatorKpi';
import { IStatusData } from '../../OperatorGroups/OperatorGroupEditor';

import CuratorSelect from './CuratorSelect';
import SessionsSettings from './SessionsSettings';
import StatusSettings from './StatusSettings';
import { defaultValidationResult, IValidationResult, validateOperator } from './validator';

import styles from './styles.module.scss';

enum OperatorEditorTab {
  General,
  Session,
  Status,
  Kpi,
}

interface IOperatorEditorProps {
  fullOperator: IFrontOperator | null;
  allOperators: IFrontOperatorBase[];
  onSubmit: () => void;
  onClose?: () => void;
}

const OperatorEditor = ({
  fullOperator,
  allOperators,
  onSubmit,
  onClose,
}: IOperatorEditorProps) => {
  const [currentTab, setCurrentTab] = React.useState<OperatorEditorTab>(OperatorEditorTab.General);
  const [login, setLogin] = React.useState('');
  const [firstName, setFirstName] = React.useState('');
  const [lastName, setLastName] = React.useState('');
  const [middleName, setMiddleName] = React.useState('');
  const [email, setEmail] = React.useState('');
  const [curatorId, setCuratorId] = React.useState<string | null>(Guid.EMPTY);
  const [sessionSettings, setSessionSettings] = React.useState<IFrontSessionSettings>({
    maxSessions: null,
    channelSessions: [],
  });
  const [statusSettings, setStatusSettings] = React.useState<IFrontStatusSettings>({
    maxBreakTime: null,
    maxStatusTime: [],
  });
  const [kpiSettings, setKpiSettings] = React.useState<Record<string, number | null>>({});
  const [addressData, setAddressData] = React.useState<Channel[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();
  const [statusData, setStatusData] = React.useState<IStatusData>([]);
  const [validationResult, setValidationResult] =
    React.useState<IValidationResult>(defaultValidationResult);
  const [kpiThresholdValues, setKpiThresholdValues] = React.useState<IFrontKpiThresholdValue[]>([]);

  const tabs: ITab[] = React.useMemo(
    () => [
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operators.modal.tabs.general')}
            {validationResult.main.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorEditorTab.General,
      },
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operators.modal.tabs.session')}
            {validationResult.sessions.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorEditorTab.Session,
      },
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operators.modal.tabs.status')}
            {validationResult.statuses.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorEditorTab.Status,
      },
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operators.modal.tabs.kpi')}
            {validationResult.kpi.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorEditorTab.Kpi,
      },
    ],
    [validationResult],
  );

  const tabComponents = {
    [OperatorEditorTab.General]: (
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4)}>
        <Input
          label={getLocaleMessageById('operators.modal.form.login')}
          value={login}
          onChange={({ value }) => setLogin(value ?? '')}
          isInvalid={!!validationResult.main.login}
          message={<InputErrorMessage>{validationResult.main.login}</InputErrorMessage>}
        />
        <Input
          label={getLocaleMessageById('operators.modal.form.name')}
          value={firstName}
          onChange={({ value }) => setFirstName(value ?? '')}
        />
        <Input
          label={getLocaleMessageById('operators.modal.form.surname')}
          value={lastName}
          onChange={({ value }) => setLastName(value ?? '')}
        />
        <Input
          label={getLocaleMessageById('operators.modal.form.middleName')}
          value={middleName}
          onChange={({ value }) => setMiddleName(value ?? '')}
        />
        <Input
          label={getLocaleMessageById('operators.modal.form.email')}
          value={email}
          onChange={({ value }) => setEmail(value ?? '')}
          type="email"
        />
        <CuratorSelect
          allOperators={allOperators}
          curatorId={curatorId}
          operatorId={fullOperator?.id ?? null}
          onCuratorIdChanged={(value) => setCuratorId(value)}
        />
      </div>
    ),
    [OperatorEditorTab.Session]: (
      <SessionsSettings
        addressData={addressData}
        sessionSettings={sessionSettings}
        onUpdateSessionSettings={setSessionSettings}
        validationResult={validationResult.sessions}
      />
    ),
    [OperatorEditorTab.Status]: (
      <StatusSettings
        statusSettings={statusSettings}
        onUpdateStatusSettings={setStatusSettings}
        statusData={statusData}
        validationResult={validationResult.statuses}
      />
    ),
    [OperatorEditorTab.Kpi]: (
      <OperatorKpi
        kpiThresholdValues={kpiThresholdValues}
        kpiTargets={kpiSettings}
        onChange={(key, value) => setKpiSettings((current) => ({ ...current, [key]: value }))}
        kpiValidationResult={validationResult.kpi.kpiThresholds}
      />
    ),
  };

  const newOperator: IFrontOperator = React.useMemo(
    () => ({
      id: fullOperator?.id ?? '',
      login,
      firstName,
      middleName,
      lastName,
      email,
      groups: fullOperator?.groups ?? [],
      sessionSettings,
      statusSettings,
      kpiSettings: Object.entries(kpiSettings).map(([code, target]) => ({ code, target })),
      curatorId: curatorId ?? '',
    }),
    [
      fullOperator?.id,
      fullOperator?.groups,
      login,
      firstName,
      middleName,
      lastName,
      email,
      sessionSettings,
      statusSettings,
      kpiSettings,
      curatorId,
    ],
  );

  const handleSubmit = async () => {
    const newValidationResult = validateOperator(newOperator, kpiThresholdValues);
    setValidationResult(newValidationResult);
    if (newValidationResult.isErrored) return;

    setLoading(true);
    setError(undefined);
    try {
      if (fullOperator?.id) {
        await updateOperator(fullOperator.id, mapFrontOperatorToNewDto(newOperator));
      } else {
        await createOperator(mapFrontOperatorToNewDto(newOperator));
      }
      onSubmit();
      onClose?.();
    } catch (err) {
      console.error('Error saving operator', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    (async () => {
      setLoading(true);
      setAddressData(await getProductServicesManager().addressTypes.available());
      setStatusData(
        (await getProductServicesManager().filterData.all({ operatorStatuses: true }))
          .availableOperatorStatuses,
      );

      const operatorParameters = await getOperatorParameters();
      setKpiThresholdValues(
        operatorParameters.kpiThresholdValues?.map((value) => ({
          code: value.code,
          displayName: value.displayName,
          isInteger: value.isInteger ?? false,
          defaultValue: value.defaultValue,
          minValue: value.minValue ?? 0,
          maxValue: value.maxValue ?? 0,
        })) ?? [],
      );
      setLoading(false);
    })();
  }, []);

  React.useEffect(() => {
    setLogin(fullOperator?.login ?? '');
    setFirstName(fullOperator?.firstName ?? '');
    setMiddleName(fullOperator?.middleName ?? '');
    setLastName(fullOperator?.lastName ?? '');
    setEmail(fullOperator?.email ?? '');
    setCuratorId(fullOperator?.curatorId ?? null);
    setSessionSettings(fullOperator?.sessionSettings ?? { maxSessions: null, channelSessions: [] });
    setStatusSettings(fullOperator?.statusSettings ?? { maxBreakTime: null, maxStatusTime: [] });
    setKpiSettings(
      fullOperator?.kpiSettings.reduce((acc, kpi) => ({ ...acc, [kpi.code]: kpi.target }), {}) ??
        {},
    );
  }, [fullOperator]);

  React.useEffect(() => {
    if (!validationResult.isErrored) return;

    setValidationResult(validateOperator(newOperator, kpiThresholdValues));
  }, [kpiThresholdValues, newOperator, validationResult.isErrored]);

  return (
    <OverlayLoader
      wrapperClassName={clsx(utils.dFlex, utils.flexColumn, styles.editor)}
      loading={loading}
    >
      <Tabs
        className={clsx(utils.pT2, utils.pX6)}
        tabs={tabs}
        onChange={({ value }) => setCurrentTab(value)}
        value={currentTab}
      />
      <div
        className={clsx(
          utils.flexBasis0,
          utils.flexGrow1,
          utils.pX6,
          utils.pY4,
          utils.overflowAuto,
          utils.scrollbar,
        )}
      >
        {tabComponents[currentTab]}
      </div>
      <footer
        className={clsx(
          utils.borderTop,
          utils.pX6,
          utils.pY4,
          utils.dFlex,
          utils.gap2,
          utils.justifyContentEnd,
        )}
      >
        {error && (
          <AlertError
            className={utils.flexGrow1}
            header={getLocaleMessageById('operators.save.error')}
            error={error}
          />
        )}
        <Button
          variant={ButtonVariant.Secondary}
          onClick={() => {
            const hasNoChanges = checkObjectsEqual(
              { ...fullOperator, curatorName: null },
              newOperator,
            );

            needConfirmWhenCompareFalse(hasNoChanges, onClose);
          }}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button onClick={handleSubmit}>{getLocaleMessageById('app.common.save')}</Button>
      </footer>
    </OverlayLoader>
  );
};

export default OperatorEditor;
