import React from 'react';

import { ColumnDef } from '@tanstack/react-table';
import clsx from 'clsx';

import { Jumbotron, JumbotronType, Table, text, utils } from '@product.front/ui-kit';

import { OfferType } from '../../@types/generated/marketing';
import { IOffer } from '../../@types/offer';
import { getLocaleMessageById } from '../../helpers/localeHelper';
import { useMarketingAppDispatch, useMarketingAppSelector } from '../../store/hooks';
import { setSelectedOffer } from '../../store/offers/offers.slice';

const commonColumnProps: Partial<ColumnDef<IOffer>> = {
  enableResizing: true,
  enableSorting: true,
  meta: {
    ellipsis: true,
  },
};

const monoColumnsNames = ['dateFrom', 'dateTo', 'updateTime'];

const TableBody = () => {
  const dispatch = useMarketingAppDispatch();

  const { offers, selectedOffer } = useMarketingAppSelector((state) => state.offers);
  const { offerTypes } = useMarketingAppSelector((state) => state.settings);

  const columns = React.useMemo(
    () =>
      [
        {
          ...commonColumnProps,
          accessorKey: 'name',
          header: getLocaleMessageById('app.table.offers.header.name'),
          size: 200,
          meta: {
            ...commonColumnProps.meta,
            defaultSorting: 'desc',
          },
        },
        {
          ...commonColumnProps,
          accessorKey: 'automaticType',
          accessorFn: (row) =>
            offerTypes.find(
              (offerType) => offerType.automaticType === (row.automaticType ?? OfferType.Manual),
            )?.displayName ?? '-',
          header: getLocaleMessageById('app.table.offers.header.type'),
          size: 200,
        },
        {
          ...commonColumnProps,
          accessorKey: 'dateFrom',
          accessorFn: (row) => new Date(row.dateFrom).toLocaleDateString(),
          header: getLocaleMessageById('app.table.offers.header.from'),
          size: 100,
        },
        {
          ...commonColumnProps,
          accessorKey: 'dateTo',
          accessorFn: (row) => new Date(row.dateTo).toLocaleDateString(),
          header: getLocaleMessageById('app.table.offers.header.to'),
          size: 100,
        },
        {
          ...commonColumnProps,
          accessorKey: 'campaigns',
          accessorFn: (row) =>
            row.campaigns
              ?.map((campaign) => campaign.name)
              .filter((campaign) => !!campaign)
              .join(', ') || '-',
          header: getLocaleMessageById('app.table.offers.header.campaigns'),
          size: 200,
        },
        {
          ...commonColumnProps,
          accessorKey: 'author',
          header: getLocaleMessageById('app.table.offers.header.changedBy'),
          size: 200,
        },
        {
          ...commonColumnProps,
          accessorKey: 'updateTime',
          accessorFn: (row) => new Date(row.updateTime).toLocaleDateString(),
          header: getLocaleMessageById('app.table.offers.header.changedDate'),
          size: 100,
        },
      ] satisfies ColumnDef<IOffer>[],
    [offerTypes],
  );

  if (!offers.length) {
    return (
      <div
        className={clsx(
          utils.h100,
          utils.w100,
          utils.dFlex,
          utils.alignItemsCenter,
          utils.justifyContentCenter,
        )}
      >
        <Jumbotron type={JumbotronType.Info} header={getLocaleMessageById('app.offers.notFound')} />
      </div>
    );
  }

  return (
    <Table
      columns={columns}
      data={offers.map((offer) => ({ ...offer, meta: { active: offer.id === selectedOffer?.id } }))}
      fixHeader
      cellProps={(cell) => ({
        className: monoColumnsNames.includes(cell.column.id) ? text.textBodyMediumMono : '',
      })}
      onRowClick={(rowData) => dispatch(setSelectedOffer(rowData.data))}
    />
  );
};

export default TableBody;
