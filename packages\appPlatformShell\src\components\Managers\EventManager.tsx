import * as React from 'react';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

export class EventManager extends React.PureComponent implements AWP.IEventManager {
  registrations: { [eventName: string]: any[] } = {};
  registrationsWithEventTypeNames: { [eventName: string]: any[] } = {};

  constructor() {
    super({});
  }

  subscribe(eventName: string, handler: any): void {
    if (this.registrations[eventName] == null) {
      this.registrations[eventName] = [];
    }
    this.registrations[eventName].push(handler);
  }

  subscribeWithEventTypeName(eventName: string, handler: any): void {
    if (this.registrationsWithEventTypeNames[eventName] == null) {
      this.registrationsWithEventTypeNames[eventName] = [];
    }
    this.registrationsWithEventTypeNames[eventName].push(handler);
  }

  unsubscribe(eventName: string, handler: any): void {
    let handlers = this.registrations[eventName];
    if (handlers == null) {
      return;
    }
    handlers = handlers.filter((h) => h !== handler);
    this.registrations[eventName] = handlers;

    let handlersWithTypeNames = this.registrationsWithEventTypeNames[eventName];
    if (handlersWithTypeNames == null) {
      return;
    }
    handlersWithTypeNames = handlersWithTypeNames.filter((h) => h !== handlersWithTypeNames);
    this.registrationsWithEventTypeNames[eventName] = handlersWithTypeNames;
  }

  fire(eventName: string, eventArgs: any): void {
    console.debug(`FIRE REQUESTED: ${eventName}`, eventArgs);
    const handlers = this.registrations[eventName];
    if (handlers != null) {
      handlers.forEach((a) => {
        a(eventArgs);
      });
    }

    const handlersWithEventTypeName = this.registrationsWithEventTypeNames[eventName];
    if (handlersWithEventTypeName != null) {
      handlersWithEventTypeName.forEach((a) => {
        a(eventName, eventArgs);
      });
    }
  }
}
