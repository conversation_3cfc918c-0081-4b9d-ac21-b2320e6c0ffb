import {
  AnswerTypes,
  ConditionOperations,
  RunCommonStepDto,
  RunRouterDto,
  RunScriptDto,
  RunServiceDto,
  RunServiceParameterDto,
  RunStepDto,
  RunStepsContextDto,
  StepType,
} from '@monorepo/dialog-scripts/src/@types/generated/scripts';
import { IFrontServiceParameter, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import {
  mapAttachmentDtoToFront,
  mapConditionOperationToFront,
  mapStepTypeToFrontStepType,
} from './dialogScriptsEditorMappersToFront';

// * RunScriptDto to Front
const mapBackServiceParametersToFront = (
  backServiceParameter: RunServiceParameterDto,
): IFrontServiceParameter => {
  return {
    key: backServiceParameter.key ?? '',
    value: backServiceParameter.manualValue ?? '',
    variableCode: backServiceParameter.variableCode ?? '',
    // TODO: убрать при улучшении типизации
    name: '',
    description: '',
  };
};

const mapRunStepsDtoToFront = (
  runStepsDto?: (RunCommonStepDto | RunStepDto | RunRouterDto | RunServiceDto)[] | null,
): Record<string, IFrontStep> => {
  const frontSteps: Record<string, IFrontStep> = {};

  runStepsDto?.forEach((stepDto) => {
    if (!stepDto.globalId) return;

    const commonFields = {
      type: mapStepTypeToFrontStepType(stepDto.stepType),
      id: stepDto.globalId,
      name: stepDto.name,
      description: stepDto.description,
      stepTransfer: stepDto.nextGlobalId,
      isSkippable: stepDto.isSkippable,
    };
    let specialFields = {};

    if (stepDto.stepType === StepType.Step) {
      const stepData = stepDto as RunStepDto;
      specialFields = {
        answerDisplayType: stepData.answersType,
        variable: stepData.variableCode,
        answers:
          stepData.answers
            ?.map((answerDto, index) => ({
              id: answerDto.id,
              text: answerDto.value,
              transferTo: answerDto.nextGlobalId,
              order: answerDto.order ?? index,
            }))
            .sort((a, b) => a.order - b.order) ?? [],
        attachments: stepData.attachments?.map(mapAttachmentDtoToFront) ?? [],
        promptUrl: stepData.promptUrl,
        isBackButtonAvailable: stepData.isBackButtonAvailable ?? false,
      };
    }

    if (stepDto.stepType === StepType.Rating) {
      const stepData = stepDto as RunStepDto;
      specialFields = {
        answerDisplayType: AnswerTypes.Button,
        variable: stepData.variableCode,
        answers:
          stepData.answers
            ?.map((answerDto, index) => ({
              id: answerDto.id,
              text: answerDto.value,
              transferTo: answerDto.nextGlobalId,
              order: answerDto.order ?? index,
            }))
            .sort((a, b) => a.order - b.order) ?? [],
        isBackButtonAvailable: stepData.isBackButtonAvailable ?? false,
      };
    }

    if (stepDto.stepType === StepType.Router) {
      const stepData = stepDto as RunRouterDto;
      specialFields = {
        rules: stepData.rules
          ?.map((rule) => ({
            id: rule.id,
            name: rule.name,
            priority: rule.priority,
            transferTo: rule.nextGlobalId,
            conditions: rule.conditions?.map((condition) => ({
              id: condition.id,
              variable: condition.leftPartVariableCode,
              operator: mapConditionOperationToFront(condition.operation || ConditionOperations.Eq),
              value: condition.rightPartValue,
            })),
          }))
          .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0)),
      };
    }

    if (stepDto.stepType === StepType.Service) {
      const stepData = stepDto as RunServiceDto;
      specialFields = {
        stepFallbackTransfer: stepData.globalNextFailStepId,
        serviceParameters: stepData.runServiceParameters?.map(mapBackServiceParametersToFront),
        automationServiceId: stepData.automationServiceId,
      };
    }

    frontSteps[stepDto.globalId] = {
      ...commonFields,
      ...specialFields,
    } as IFrontStep;
  });

  return frontSteps;
};

export const mapRunScriptDtoToFrontData = (
  runScriptDto: RunScriptDto,
): {
  scriptInfo: { title: string; firstStepId: string | null };
  steps: Record<string, IFrontStep>;
  stepsContext: RunStepsContextDto[];
  variableContext: Record<string, string>;
} => ({
  scriptInfo: {
    title: runScriptDto.runScriptData?.scriptName ?? '',
    firstStepId: runScriptDto.runScriptData?.firstStepGlobalId ?? null,
  },
  steps: mapRunStepsDtoToFront(runScriptDto.runScriptData?.steps),
  stepsContext:
    runScriptDto.stepsContext?.sort((a, b) => a.dateTime?.localeCompare(b.dateTime ?? '') || 0) ??
    [],
  variableContext: runScriptDto.variableContextMap ?? {},
});
