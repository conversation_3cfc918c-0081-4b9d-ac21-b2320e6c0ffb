import React from 'react';

import clsx from 'clsx';

import {
  AttentionIcon,
  AttentionIconSize,
  AttentionIconType,
  Button,
  ButtonVariant,
  CanClearBehavior,
  Input,
  ITab,
  OverlayLoader,
  Select,
  Tabs,
  utils,
} from '@product.front/ui-kit';

import AlertError from '@monorepo/common/src/common/components/Errors/AlertError';
import InputErrorMessage from '@monorepo/common/src/components/InputErrorMessage';
import { checkObjectsEqual } from '@monorepo/common/src/helpers/objectsHelper';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';
import { getProductServicesManager } from '@monorepo/common/src/managers/productServicesManager';
import { Channel } from '@monorepo/services/src/@types/generated/productApi';

import {
  IFrontOperatorBase,
  IFrontSessionSettings,
  IFrontStatusSettings,
} from '../../../../../@types/operator';
import { IFrontOperatorGroup, IFrontOperatorGroupBase } from '../../../../../@types/operatorGroup';
import { IFrontKpiThresholdValue } from '../../../../../@types/parameters';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { getOperatorGroupParameters } from '../../../../../services/operatorGroups';
import OperatorKpi from '../../Kpi/OperatorKpi';
import SessionsSettings from '../../Operators/OperatorEditor/SessionsSettings';
import StatusSettings from '../../Operators/OperatorEditor/StatusSettings';

import OperatorsSettings from './OperatorsSettings';
import { defaultValidationResult, IValidationResult, validateOperatorGroup } from './validator';

import styles from './styles.module.scss';

enum OperatorGroupEditorTab {
  General,
  Session,
  Operators,
  Status,
  Kpi,
}

export type IStatusData = Awaited<
  ReturnType<ReturnType<typeof getProductServicesManager>['filterData']['all']>
>['availableOperatorStatuses'];

interface IOperatorEditorProps {
  operatorGroup: IFrontOperatorGroup | null;
  operatorGroupsMap: Record<string, IFrontOperatorGroupBase>;
  onSubmit: (operatorGroup: IFrontOperatorGroup) => Promise<void>;
  onClose?: () => void;
}

const OperatorGroupEditor = ({
  operatorGroup,
  operatorGroupsMap,
  onSubmit,
  onClose,
}: IOperatorEditorProps) => {
  const [currentTab, setCurrentTab] = React.useState<OperatorGroupEditorTab>(
    OperatorGroupEditorTab.General,
  );
  const [name, setName] = React.useState('');
  const [parentId, setParentId] = React.useState('');
  const [addressData, setAddressData] = React.useState<Channel[]>([]);
  const [statusData, setStatusData] = React.useState<IStatusData>([]);
  const [sessionSettings, setSessionSettings] = React.useState<IFrontSessionSettings>({
    maxSessions: null,
    channelSessions: [],
  });
  const [operators, setOperators] = React.useState<IFrontOperatorBase[]>(
    operatorGroup?.operators ?? [],
  );
  const [statusSettings, setStatusSettings] = React.useState<IFrontStatusSettings>({
    maxBreakTime: null,
    maxStatusTime: [],
  });
  const [kpiSettings, setKpiSettings] = React.useState<Record<string, number | null>>(
    operatorGroup?.kpiSettings.reduce(
      (accumulator, value) => ({ ...accumulator, [value.code]: value.target }),
      {},
    ) ?? [],
  );
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error>();
  const [validationResult, setValidationResult] =
    React.useState<IValidationResult>(defaultValidationResult);
  const [kpiThresholdValues, setKpiThresholdValues] = React.useState<IFrontKpiThresholdValue[]>([]);

  const initialValues = React.useMemo(
    () => ({
      id: '',
      name: operatorGroup?.name ?? '',
      parentId: operatorGroup?.parentId ?? '',
      sessionSettings: operatorGroup?.sessionSettings ?? {
        maxSessions: null,
        channelSessions: [],
      },
      operators: operatorGroup?.operators ?? [],
      statusSettings: operatorGroup?.statusSettings ?? {
        maxBreakTime: null,
        maxStatusTime: [],
      },
      kpiSettings:
        operatorGroup?.kpiSettings.reduce(
          (accumulator, value) => ({ ...accumulator, [value.code]: value.target }),
          {},
        ) ?? {},
    }),
    [operatorGroup],
  );

  const newOperatorGroup: IFrontOperatorGroup = React.useMemo(
    () => ({
      ...operatorGroup,
      id: operatorGroup?.id ?? '',
      name,
      parentId,
      sessionSettings,
      operators,
      statusSettings,
      kpiSettings: Object.entries(kpiSettings).map(([code, target]) => ({ code, target })),
    }),
    [name, parentId, sessionSettings, operators, statusSettings, operatorGroup, kpiSettings],
  );

  const tabs: ITab[] = React.useMemo(
    () => [
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operatorGroups.modal.tabs.general')}
            {validationResult.main.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorGroupEditorTab.General,
      },
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operatorGroups.modal.tabs.session')}
            {validationResult.sessions.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorGroupEditorTab.Session,
      },
      {
        label: getLocaleMessageById('operatorGroups.modal.tabs.operators'),
        value: OperatorGroupEditorTab.Operators,
      },
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operatorGroups.modal.tabs.status')}
            {validationResult.statuses.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorGroupEditorTab.Status,
      },
      {
        label: (
          <span className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}>
            {getLocaleMessageById('operatorGroups.modal.tabs.kpi')}
            {validationResult.kpi.isErrored && (
              <AttentionIcon size={AttentionIconSize.Small} type={AttentionIconType.Error} />
            )}
          </span>
        ),
        value: OperatorGroupEditorTab.Kpi,
      },
    ],
    [validationResult],
  );

  const tabComponents = {
    [OperatorGroupEditorTab.General]: (
      <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap4)}>
        <Input
          label={getLocaleMessageById('operatorGroups.modal.form.name')}
          value={name}
          onChange={({ value }) => setName(value ?? '')}
          required
          isInvalid={!!validationResult.main.name}
          message={<InputErrorMessage>{validationResult.main.name}</InputErrorMessage>}
        />
        <Select
          data={Object.keys(operatorGroupsMap)
            .filter((key) => key !== operatorGroup?.id)
            .map((id) => ({
              text: operatorGroupsMap[id].name,
              value: id,
            }))}
          label={getLocaleMessageById('operatorGroups.modal.form.parent')}
          value={parentId}
          onChange={({ value }) => setParentId(value ?? '')}
          canClearBehavior={CanClearBehavior.Value}
        />
      </div>
    ),
    [OperatorGroupEditorTab.Session]: (
      <SessionsSettings
        addressData={addressData}
        sessionSettings={sessionSettings}
        onUpdateSessionSettings={setSessionSettings}
        validationResult={validationResult.sessions}
      />
    ),
    [OperatorGroupEditorTab.Operators]: (
      <OperatorsSettings operators={operators} onChangeOperators={setOperators} />
    ),
    [OperatorGroupEditorTab.Status]: (
      <StatusSettings
        statusSettings={statusSettings}
        onUpdateStatusSettings={setStatusSettings}
        statusData={statusData}
        validationResult={validationResult.statuses}
      />
    ),
    [OperatorGroupEditorTab.Kpi]: (
      <OperatorKpi
        kpiThresholdValues={kpiThresholdValues}
        kpiTargets={kpiSettings}
        onChange={(key, value) => setKpiSettings((current) => ({ ...current, [key]: value }))}
        kpiValidationResult={validationResult.kpi.kpiThresholds}
      />
    ),
  };

  const resetValues = React.useCallback(() => {
    setName(initialValues.name);
    setParentId(initialValues.parentId);
    setSessionSettings(initialValues.sessionSettings);
    setOperators(initialValues.operators);
    setStatusSettings(initialValues.statusSettings);
    setKpiSettings(initialValues.kpiSettings);
    setValidationResult(defaultValidationResult);
  }, [initialValues]);

  const handleSubmit = async () => {
    const newValidationResult = validateOperatorGroup(newOperatorGroup, kpiThresholdValues);
    setValidationResult(newValidationResult);
    if (newValidationResult.isErrored) return;

    try {
      setLoading(true);
      await onSubmit(newOperatorGroup);
      onClose?.();
    } catch (err) {
      console.error('Error saving operator group', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    resetValues();
  }, [resetValues]);

  React.useEffect(() => {
    if (!validationResult.isErrored) return;

    setValidationResult(validateOperatorGroup(newOperatorGroup, kpiThresholdValues));
  }, [kpiThresholdValues, newOperatorGroup, validationResult.isErrored]);

  React.useEffect(() => {
    (async () => {
      setLoading(true);
      setAddressData(await getProductServicesManager().addressTypes.available());
      setStatusData(
        (await getProductServicesManager().filterData.all({ operatorStatuses: true }))
          .availableOperatorStatuses,
      );

      const operatorGroupParameters = await getOperatorGroupParameters();
      setKpiThresholdValues(
        operatorGroupParameters.kpiThresholdValues?.map((value) => ({
          code: value.code,
          displayName: value.displayName,
          isInteger: value.isInteger ?? false,
          defaultValue: value.defaultValue,
          minValue: value.minValue ?? 0,
          maxValue: value.maxValue ?? Infinity,
        })) ?? [],
      );

      setLoading(false);
    })();
  }, []);

  return (
    <OverlayLoader
      wrapperClassName={clsx(utils.dFlex, utils.flexColumn, styles.editor)}
      loading={loading}
    >
      <Tabs
        className={clsx(utils.pT2, utils.pX6)}
        tabs={tabs}
        onChange={({ value }) => setCurrentTab(value)}
        value={currentTab}
      />
      <div
        className={clsx(
          utils.flexBasis0,
          utils.flexGrow1,
          utils.pX6,
          utils.pY4,
          utils.overflowAuto,
          utils.scrollbar,
        )}
      >
        {tabComponents[currentTab]}
      </div>
      <footer
        className={clsx(
          utils.borderTop,
          utils.pX6,
          utils.pY4,
          utils.dFlex,
          utils.gap2,
          utils.justifyContentEnd,
        )}
      >
        {error && (
          <AlertError
            className={utils.flexGrow1}
            header={getLocaleMessageById('operatorGroups.save.error')}
            error={error}
          />
        )}
        <Button
          variant={ButtonVariant.Secondary}
          onClick={() => {
            const prev = operatorGroup ? operatorGroup : initialValues;
            const next = operatorGroup ? newOperatorGroup : { ...newOperatorGroup, kpiSettings };

            if (checkObjectsEqual(prev, next)) {
              onClose?.();
              return;
            }
            showConfirmModal({
              header: getLocaleMessageById('operatorGroup.editor.confirmCancel.header'),
              text: getLocaleMessageById('operatorGroup.editor.confirmCancel.text'),
              onConfirm: onClose,
            });
          }}
        >
          {getLocaleMessageById('app.common.cancel')}
        </Button>
        <Button onClick={handleSubmit}>{getLocaleMessageById('app.common.save')}</Button>
      </footer>
    </OverlayLoader>
  );
};

export default OperatorGroupEditor;
