import React from 'react';

import { ITextareaProps } from '@product.front/ui-kit/dist/types/components/Textarea/Textarea';
import clsx from 'clsx';

import { Collapsible, grids, Radio, Textarea, Checkbox, utils } from '@product.front/ui-kit';

import {
  AutomationServiceRequestBodyType,
  AutomationServiceRequestType,
  IFrontAutomationService,
  IFrontAutomationServiceInvalidReasons,
} from '../../../@types/automationService.types';
import { getLocaleMessageById } from '../../../helpers/localeHelper';
import { formFieldConstraintsResource } from '../../../resources/automationService.resources';
import ParameterEditor from '../ParameterEditor';
import RequestHeadersEditor from '../RequestHeadersEditor';

import FieldGroupSubHeader from './FieldGroupSubHeader';
import FieldInvalidMessage from './FieldInvalidMessage';
import FieldsGroupHeader from './FieldsGroupHeader';
import FieldsGroupWrapper from './FieldsGroupWrapper';

interface IBlockRequestProps {
  automationService: IFrontAutomationService;
  onFieldValueChange: (fieldName: string) => ({ value }: { value?: any }) => void;
  disabled: boolean;
  readonly: boolean;
  invalidReasons: IFrontAutomationServiceInvalidReasons;
}

const BlockRequest: React.FC<IBlockRequestProps> = ({
  automationService,
  onFieldValueChange,
  disabled,
  readonly,
  invalidReasons,
}) => {
  const requestBodyRef = React.useRef<HTMLTextAreaElement>(null);
  const urlInputRef = React.useRef<HTMLTextAreaElement>(null);

  React.useEffect(() => {
    if (!requestBodyRef.current || !invalidReasons.requestBody) return;
    requestBodyRef.current.focus();
  }, [requestBodyRef, invalidReasons]);

  return (
    <FieldsGroupWrapper>
      <FieldsGroupHeader>{getLocaleMessageById('app.automationService.request')}</FieldsGroupHeader>
      <div className={clsx(utils.dFlex, utils.gap4)}>
        <Radio
          value={AutomationServiceRequestType.GET}
          checked={automationService.requestType === AutomationServiceRequestType.GET}
          onChange={onFieldValueChange('requestType')}
          disabled={disabled}
          name="requestType"
          label={getLocaleMessageById('app.automationService.requestTypeGet')}
          readOnly={readonly}
        />
        <Radio
          value={AutomationServiceRequestType.POST}
          checked={automationService.requestType === AutomationServiceRequestType.POST}
          onChange={onFieldValueChange('requestType')}
          disabled={disabled}
          name="requestType"
          label={getLocaleMessageById('app.automationService.requestTypePost')}
          readOnly={readonly}
        />
      </div>

      <div
        className={clsx(utils.mT4, grids.row, utils.positionRelative)}
        style={{ isolation: 'isolate' }}
      >
        <Textarea
          ref={urlInputRef}
          rows={3}
          wrapperClassName={grids.col12}
          label={getLocaleMessageById('app.automationService.requestUrl')}
          disabled={disabled}
          readOnly={readonly}
          value={automationService.requestUrl}
          onChange={onFieldValueChange('requestUrl')}
          {...((formFieldConstraintsResource.requestUrl || {}) as ITextareaProps)}
        />
        <input
          type="url"
          style={{
            zIndex: -1,
            height: 0,
            width: '95%',
            borderColor: 'transparent',
            position: 'absolute',
            bottom: '0',
            left: '2px',
          }}
          value={automationService.requestUrl}
          onFocus={() => urlInputRef.current?.focus()}
          tabIndex={-1}
        />
      </div>

      <FieldGroupSubHeader>
        {getLocaleMessageById('app.automationService.requestHeaders')}
      </FieldGroupSubHeader>
      <RequestHeadersEditor
        value={automationService.requestHeaders}
        onChange={onFieldValueChange('requestHeaders')}
        disabled={disabled}
        readonly={readonly}
      />

      <Collapsible
        activatorComponent={<></>}
        open={automationService.requestType !== AutomationServiceRequestType.GET}
      >
        <FieldGroupSubHeader>
          {getLocaleMessageById('app.automationService.requestBody')}
        </FieldGroupSubHeader>
        <div className={clsx(utils.dFlex, utils.gap4)}>
          <Radio
            value={AutomationServiceRequestBodyType.JSON}
            checked={automationService.requestBodyType === AutomationServiceRequestBodyType.JSON}
            onChange={onFieldValueChange('requestBodyType')}
            disabled={disabled}
            name="requestBodyType"
            label={getLocaleMessageById('app.automationService.requestBodyTypeJSON')}
            readOnly={readonly}
          />
          <Radio
            value={AutomationServiceRequestBodyType.XML}
            checked={automationService.requestBodyType === AutomationServiceRequestBodyType.XML}
            onChange={onFieldValueChange('requestBodyType')}
            disabled={disabled}
            name="requestBodyType"
            label={getLocaleMessageById('app.automationService.requestBodyTypeXML')}
            readOnly={readonly}
          />
          <Radio
            value={AutomationServiceRequestBodyType.Text}
            checked={automationService.requestBodyType === AutomationServiceRequestBodyType.Text}
            onChange={onFieldValueChange('requestBodyType')}
            disabled={disabled}
            name="requestBodyType"
            label={getLocaleMessageById('app.automationService.requestBodyTypeText')}
            readOnly={readonly}
          />
        </div>
        <div className={clsx(utils.mT4, grids.row)}>
          <Textarea
            rows={4}
            wrapperClassName={grids.col12}
            label={getLocaleMessageById('app.automationService.requestBodyPlaceholder')}
            disabled={disabled}
            readOnly={readonly}
            value={automationService.requestBody}
            onChange={onFieldValueChange('requestBody')}
            {...((formFieldConstraintsResource.requestBody || {}) as ITextareaProps)}
            required={
              automationService.requestType !== AutomationServiceRequestType.GET &&
              [
                AutomationServiceRequestBodyType.XML,
                AutomationServiceRequestBodyType.JSON,
              ].includes(automationService.requestBodyType)
            }
            ref={requestBodyRef}
            message={
              invalidReasons.requestBody ? (
                <FieldInvalidMessage>{invalidReasons.requestBody}</FieldInvalidMessage>
              ) : undefined
            }
          />
          {automationService.requestBodyType === AutomationServiceRequestBodyType.JSON && (
            <Checkbox
              title={getLocaleMessageById('app.automationService.requestCanUploadFilesTitle')}
              label={getLocaleMessageById('app.automationService.requestCanUploadFilesTitle')}
              className={grids.col12}
              disabled={disabled}
              readOnly={readonly}
              checked={automationService.canUploadFiles}
              onChange={(value) =>
                onFieldValueChange('canUploadFiles')({ value: value.checked ?? false })
              }
            />
          )}
        </div>
      </Collapsible>
      {automationService.requestParameters.length > 0 && (
        <FieldGroupSubHeader>
          {getLocaleMessageById('app.automationService.requestParameters')}
        </FieldGroupSubHeader>
      )}
      {automationService.requestParameters.map((parameter) => (
        <ParameterEditor
          key={parameter.key}
          disabled={disabled}
          parameter={parameter}
          onChange={(updatedParameter) => {
            onFieldValueChange('requestParameters')({
              value: automationService.requestParameters.map((rp) =>
                rp.key === parameter.key ? updatedParameter : rp,
              ),
            });
          }}
          readonly={readonly}
        />
      ))}
    </FieldsGroupWrapper>
  );
};
export default BlockRequest;
