import React from 'react';

import { ITreeViewItem } from '@product.front/ui-kit/dist/types/components/TreeView/TreeView';
import clsx from 'clsx';

import { Dropdown, Select, MenuItem, TreeView, utils } from '@product.front/ui-kit';

import IconDropDown from '@product.front/icons/dist/icons17/MainStuff/IconDropDown';
import IconDropRight from '@product.front/icons/dist/icons17/MainStuff/IconDropRight';

import { IFrontRequestSubject } from '@monorepo/services/src/CRPMConfiguration/crpmCofiguration.types';

import { getLocaleMessageById } from '../../../../../helpers/localeHelper';
import { useDialogScriptsAppSelector } from '../../../../../store/hooks';

import styles from './styles.module.scss';

interface ISubjectSelectProps {
  value?: string;
  onChange: (selected: { value: string }) => void;
  disabled?: boolean;
  isInvalid?: boolean;
  message?: React.ReactNode;
}

const SubjectSelect: React.FC<ISubjectSelectProps> = ({
  value,
  onChange,
  disabled = false,
  isInvalid,
  message,
}) => {
  const [isDDOpen, setIsDDOpen] = React.useState(false);
  const { terminalSubjects } = useDialogScriptsAppSelector((state) => state.externalData);

  const sortedTerminalSubjects = React.useMemo(() => {
    return Array.from(terminalSubjects).sort((a, b) => a.label.localeCompare(b.label) ?? 0);
  }, [terminalSubjects]);

  const mapRequestSubjectsToTree = (data: IFrontRequestSubject): ITreeViewItem => {
    return {
      key: data.code,
      isOpen: value === data.code,
      component: ({ isOpen, level, children }) => (
        <MenuItem
          style={{ paddingLeft: 16 + level * 20 }}
          className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap2)}
          onClick={() => {
            if (!disabled && !children?.length) {
              onChange({ value: data.code });
              setIsDDOpen(false);
            }
          }}
        >
          {children?.length ? (
            <span>
              {!isOpen ? (
                <IconDropRight className={utils.flexShrink0} />
              ) : (
                <IconDropDown className={utils.flexShrink0} />
              )}
            </span>
          ) : (
            <span className={utils.mL3} />
          )}
          <span className={utils.ellipsis}>{data.label}</span>
        </MenuItem>
      ),
      children: sortedTerminalSubjects
        .filter((s) => s.parentCode === data.code)
        ?.map?.(mapRequestSubjectsToTree),
    };
  };

  const subjectsTree: ITreeViewItem[] = sortedTerminalSubjects
    .filter((s) => !s.parentCode)
    .map(mapRequestSubjectsToTree);

  return (
    <Dropdown
      open={isDDOpen}
      onToggle={setIsDDOpen}
      dropdownClassName={clsx(
        utils.w100,
        utils.scrollbar,
        utils.overflowYAuto,
        styles.subjectsTreeWrapper,
      )}
      menu={<TreeView expandToOpen items={subjectsTree} />}
      closeOnMenuClick={false}
    >
      <Select
        data={
          value
            ? [{ text: terminalSubjects.find((s) => s.code === value)?.label ?? value, value }]
            : []
        }
        readOnly
        placeholder={getLocaleMessageById('app.editor.step.terminalSubject')}
        value={value ?? ''}
        disabled={disabled || !terminalSubjects.length}
        isInvalid={isInvalid}
        message={message}
      />
    </Dropdown>
  );
};

export default SubjectSelect;
