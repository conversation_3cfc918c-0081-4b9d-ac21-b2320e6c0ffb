import { AutomaticType } from '../@types/generated/marketing';
import { IOffer } from '../@types/offer';

import { getLocaleMessageById } from './localeHelper';

export const defaultValidationResult = {
  name: '',
  description: '',
  dateFrom: '',
  dateTo: '',
  automaticType: '',
  text: '',
  automaticOfferValue: '',
  script: '',
  parameters: '',
};

const requiredMessage = getLocaleMessageById('app.common.required');

export const validateOffer = (offer: Partial<IOffer>) => {
  const newValidationResult = { ...defaultValidationResult };
  let isErrored = false;

  const {
    name,
    description,
    dateFrom,
    dateTo,
    automaticType,
    type,
    text,
    automaticOfferValue,
    script,
    parameters,
  } = offer;

  // name validation
  if (!name) {
    newValidationResult.name = requiredMessage;
    isErrored = true;
  }

  // description validation
  if (!description) {
    newValidationResult.description = requiredMessage;
    isErrored = true;
  }

  // dateFrom validation
  if (!dateFrom) {
    newValidationResult.dateFrom = requiredMessage;
    isErrored = true;
  }

  // dateTo validation
  if (!dateTo) {
    newValidationResult.dateTo = requiredMessage;
    isErrored = true;
  }

  // automaticType validation
  if (!automaticType || !type) {
    newValidationResult.automaticType = requiredMessage;
    isErrored = true;
  }

  // text validation
  if (automaticType === AutomaticType.Default && !text) {
    newValidationResult.text = requiredMessage;
    isErrored = true;
  }

  // automaticOfferValue validation
  if (
    automaticType &&
    [AutomaticType.NeuroNet, AutomaticType.WhatsApp].includes(automaticType) &&
    !automaticOfferValue
  ) {
    newValidationResult.automaticOfferValue = requiredMessage;
    isErrored = true;
  }

  // script validation
  if (!script) {
    newValidationResult.script = requiredMessage;
    isErrored = true;
  }

  // parameters validation
  if (!parameters) {
    newValidationResult.parameters = requiredMessage;
    isErrored = true;
  }

  return { newValidationResult, isErrored };
};
