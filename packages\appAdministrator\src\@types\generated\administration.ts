/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** Модель для добавления адреса в черный список */
export interface AddBlackListAddress {
  /**
   * Адрес
   * @minLength 0
   * @maxLength 100
   */
  address: string;
  /**
   * В списке до
   * @format date-time
   */
  dueDate: string;
  /**
   * Кто добавил
   * @minLength 1
   */
  addedBy: string;
  /**
   * ФИО добавившего
   * @minLength 1
   */
  addedByFio: string;
  /** Комментарий */
  comment?: string | null;
}

/** Модель для добавления спам слова */
export interface AddNegativeWord {
  /**
   * Код
   * @minLength 0
   * @maxLength 60
   */
  code: string;
  /**
   * Имя
   * @minLength 0
   * @maxLength 60
   */
  name: string;
  /** Комментарий */
  comment?: string | null;
  /**
   * Кем добавлен (логин)
   * @minLength 0
   * @maxLength 100
   */
  addedBy: string;
  /**
   * Кем добавлен (ФИО)
   * @minLength 0
   * @maxLength 100
   */
  addedByFio: string;
}

/** Представляет создание правила приоритизации */
export interface AddPrioritizationRule {
  /** Название */
  title: string;
  /** Включено ли правило */
  isEnabled: boolean;
  /**
   * Идентификатор атрибута
   * @format int32
   */
  attributeId: number;
  /** Оператор сравнения */
  comparisonRule: string;
  /**
   * Значение приоритизаций
   * @minItems 1
   */
  values: PriorityValue[];
}

/** Модель для создания очереди */
export interface AddQueue {
  /**
   * Наименование очереди
   * @minLength 1
   * @maxLength 100
   * @example "Новая очередь"
   */
  name: string;
  /**
   * Описание очереди
   * @minLength 1
   * @maxLength 1000
   * @example "Очередь для обработки заказов"
   */
  description?: string | null;
  /**
   * Идентификатор подразделения
   * @format uuid
   */
  divisionId?: string | null;
  /**
   * Вес очереди
   * @format int32
   * @min 0
   * @max 32767
   * @example 100
   */
  weight: number;
  /**
   * Список операторов с их идентификаторами и приоритетами
   * @maxItems 100000
   */
  operators?: Operator[];
  /**
   * Список идентификаторов групп операторов
   * @maxItems 100000
   */
  operatorGroups?: string[];
  /**
   * Список наборов правил маршрутизации
   * @maxItems 1000
   */
  routingRuleSets?: RoutingRuleSet[];
  /**
   * Правило распределения
   * @minLength 1
   * @maxLength 1000
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "CRP.ExecutorShuffler"
   */
  distributionRuleType: string;
  /**
   * Список автообработчиков
   * @maxItems 100
   */
  autoHandlers?: (PredictedWaitTimeAutoHandler | TemplateAutoHandler | EmptyAutoHandler)[];
  /** Интеллектуальный бот */
  intelligentBot: IntelligentBot;
  /** Кнопочный бот */
  buttonBot: ButtonBot;
  /** Оценочный бот */
  ratingBot: RatingBot;
  /**
   * Список показателей КПИ
   * @maxItems 1000
   */
  kpiParameters?: QueueKpiParameter[];
}

/** Модель для создания тематики */
export interface AddRequestSubject {
  /**
   * Название тематики
   * @minLength 0
   * @maxLength 100
   */
  name: string;
  /**
   * Код тематики
   * @minLength 0
   * @maxLength 100
   */
  code: string;
  /**
   * Краткое описание тематики
   * @minLength 0
   * @maxLength 1000
   */
  description?: string | null;
  /**
   * Идентификатор родительской тематики
   * @format int32
   * @min 1
   * @max 32767
   */
  parentId?: number | null;
  /**
   * Контекст для базы знаний
   * @minLength 0
   * @maxLength 1000
   */
  knowledgeBaseContext?: string | null;
  /**
   * Контекст для чат бота
   * @minLength 0
   * @maxLength 1000
   */
  chatBotContext?: string | null;
  /** Идентификатор скрипта диалога */
  dialogScriptCode?: string | null;
}

/** Модель для добавления спам слова */
export interface AddSpamWord {
  /**
   * Код
   * @minLength 0
   * @maxLength 60
   */
  code: string;
  /**
   * Имя
   * @minLength 0
   * @maxLength 60
   */
  name: string;
  /** Комментарий */
  comment?: string | null;
  /**
   * Кем добавлен (логин)
   * @minLength 0
   * @maxLength 100
   */
  addedBy: string;
  /**
   * Кем добавлен (ФИО)
   * @minLength 0
   * @maxLength 100
   */
  addedByFio: string;
}

/** Модель шаблона ответа */
export interface AnswerTemplate {
  /**
   * Список версий содержимого шаблона.
   * @minItems 1
   */
  versions: ChannelVersion[];
  /** Статус шаблона */
  status?: TemplateStatus;
  /**
   * Идентификатор шаблона.
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
  /**
   * Дискриминатор
   * @default "AnswerTemplate"
   */
  kind: string;
}

/** Модель для добавления или редактирования шаблона ответов */
export type AnswerTemplateAddEdit = TemplateAddEditBase & {
  /** Статус шаблона */
  status: TemplateStatus;
  /**
   * Список версий содержимого шаблона
   * @minItems 1
   */
  versions?: ChannelVersion[];
};

/** Модель папки шаблонов ответа */
export type AnswerTemplateFolder = FolderBase & {
  /** Идентификаторы групп операторов, которым доступна эта папка */
  relationIds?: string[] | null;
};

/** Модель папки шаблонов ответа для дерева. */
export interface AnswerTemplateFolderView {
  /**
   * Идентификатор папки
   * @format uuid
   */
  id?: string;
  /** Список шаблонов в папке */
  templates?: AnswerTemplatePreview[] | null;
  /** Дочерние папки */
  childrenFolders?: AnswerTemplateFolderView[];
  /** Идентификаторы групп операторов, которым доступна эта папка */
  relationIds?: string[] | null;
  /**
   * Название папки
   * @minLength 1
   * @maxLength 200
   */
  title: string;
  /**
   * Идентификатор папки родителя
   * @format uuid
   */
  parentId?: string | null;
}

/** Модель предпросмотра шаблона ответа */
export interface AnswerTemplatePreview {
  /** Статус шаблона */
  status?: TemplateStatus;
  /**
   * Идентификатор шаблона.
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
}

/** Модель Attachment (вложения к шаблону) */
export interface Attachment {
  /**
   * Идентификатор вложения
   * @format uuid
   */
  id: string;
  /** Имя вложения */
  name: string;
  /**
   * Расширение вложения
   * @maxLength 30
   */
  extension?: string | null;
  /**
   * Внешний идентификатор вложения
   * @maxLength 50
   */
  externalId: string;
  /**
   * Ссылка для скачивания вложения.
   * @format uri
   */
  url: string;
}

/** Модель атрибута - клиент или обращение */
export enum AttributeModel {
  Request = "Request",
  Client = "Client",
}

/** Тип атрибута - базовый или кастомный */
export enum AttributeType {
  Base = "Base",
  Custom = "Custom",
}

/** Модель, представляющая автообработчик */
export interface AutoHandler {
  /**
   * Имя авто обработчика
   * @maxLength 100
   * @example "Информационное сообщение"
   */
  name: string;
  /**
   * Код авто обработчика
   * @maxLength 100
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "Queue.InfoAutoReplyWorker.ShouldSendAutoReply"
   */
  code: string;
  /** Признак автообработчика для прогнозируемого времени ожидания ответа (ПВОО) */
  isPVOO: boolean;
}

/** Модель для добавления или редактирования автоответа */
export type AutoReplyAddEdit = TemplateAddEditBase & {
  /** Статус шаблона */
  status?: TemplateStatus;
  /**
   * Список версий содержимого автоответа
   * @minItems 1
   */
  versions?: ChannelVersion[];
};

/** Модель шаблона авто-ответа */
export interface AutoReplyTemplate {
  /**
   * Список версий содержимого шаблона
   * @minItems 1
   */
  versions: ChannelVersion[];
  /**
   * Идентификатор шаблона.
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
  /**
   * Дискриминатор
   * @default "AutoReplyTemplate"
   */
  kind: string;
}

/** Модель папки шаблонов ответа для дерева */
export type AutoReplyTemplateFolder = FolderBase & object;

/** Модель папки шаблонов ответа для дерева */
export interface AutoReplyTemplateFolderView {
  /**
   * Идентификатор папки.
   * @format uuid
   */
  id?: string;
  /** Список шаблонов в папке */
  templates?: AutoReplyTemplatePreview[] | null;
  /** Дочерние папки */
  childrenFolders?: AutoReplyTemplateFolderView[];
  /**
   * Название папки
   * @minLength 1
   * @maxLength 200
   */
  title: string;
  /**
   * Идентификатор папки родителя
   * @format uuid
   */
  parentId?: string | null;
}

/** Модель предпросмотра шаблона авто-ответа */
export interface AutoReplyTemplatePreview {
  /**
   * Идентификатор шаблона.
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
}

/** Базовая модель для автообработчика. */
export type BaseAutoHandler = BaseBaseAutoHandler &
  (
    | BaseBaseAutoHandlerKindMapping<"EmptyAutoHandler", any>
    | BaseBaseAutoHandlerKindMapping<"PredictedWaitTimeAutoHandler", any>
    | BaseBaseAutoHandlerKindMapping<"TemplateAutoHandler", any>
  );

/** Адрес в черном списке */
export interface BlackListAddress {
  /**
   * Идентификатор
   * @format uuid
   */
  id: string;
  /** Адрес */
  address: string;
  /**
   * Дата добавления
   * @format date-time
   */
  dateCreated: string;
  /** Логин удалившего */
  deletedBy?: string | null;
  /**
   * Дата удаления
   * @format date-time
   */
  dateDeleted?: string | null;
  /**
   * В списке до
   * @format date-time
   */
  dueDate: string;
  /** Кто добавил */
  addedBy: string;
  /** ФИО добавившего */
  addedByFio: string;
  /** Комментарий */
  comment?: string | null;
}

/** Кнопочный бот */
export interface ButtonBot {
  /**
   * Включен ли кнопочный бот
   * @example true
   */
  enabled: boolean;
  /**
   * Код бота
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "B2B"
   */
  botCode?: string | null;
}

/** Модель содержимого шаблона для специфичного канала */
export interface ChannelContent {
  /** Код канала */
  channel: string;
  /** Текст шаблона */
  text: string;
}

/** Настройки сессий по каналам */
export interface ChannelSession {
  /** Канал */
  channel?: string | null;
  /**
   * Количество сессий
   * @format int32
   */
  sessionCount?: number | null;
}

/** Модель версии содержимого шаблона */
export interface ChannelVersion {
  /** Содержимое под каналы */
  channelContents?: ChannelContent[] | null;
  /** Модель интервала действия версии */
  interval: Interval;
  /**
   * Идентификатор версии
   * @format uuid
   */
  id?: string;
  /** Модель содержимого шаблона */
  content: Content;
  /** Вложения к шаблону */
  attachments?: Attachment[] | null;
}

/** Правило сравнения для атрибута маршрутизации */
export interface ComparisonRule {
  /**
   * Имя правила
   * @maxLength 32
   * @example "Равно"
   */
  name: string;
  /**
   * Код правила
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-]+$
   * @example "Equals"
   */
  code: string;
}

/** Модель содержимого шаблона */
export interface Content {
  /** Текст шаблона */
  text: string;
}

export type CreateMailing = BaseCreateMailing &
  (
    | BaseCreateMailingKindMapping<"CreatePeriodicMailing", any>
    | BaseCreateMailingKindMapping<"CreateThresholdMailing", any>
  );

/** Модель для создания периодической рассылки */
export type CreatePeriodicMailing = CreateMailing & {
  /**
   * Идентификатор метрики
   * @format int64
   */
  metricId: number;
  channelCode: MessageType;
  /**
   * Период в минутах для отправки сообщений
   * @format int32
   * @min 1
   * @max 2147483647
   */
  periodMinutes: number;
  /** Интервал времени для отправки сообщений */
  allowedTime: TimeInterval;
  /** Получатели рассылки */
  recipients: MailingRecipient[];
  /**
   * Адрес отправителя. Форма зависит от типа канала
   * @minLength 1
   */
  senderAddress: string;
};

/** Модель для создания пороговой рассылки */
export type CreateThresholdMailing = CreateMailing & {
  /**
   * Идентификатор метрики
   * @format int64
   */
  metricId: number;
  channelCode: MessageType;
  /** Интервал времени для отправки сообщений */
  allowedTime: TimeInterval;
  /**
   * Идентификатор очереди для пороговой рассылки
   * @format int64
   */
  queueId: number;
  /**
   * Пороговое значение для метрики
   * @format double
   * @min 0
   */
  threshold: number;
  /** Получатели рассылки */
  recipients: MailingRecipient[];
  /**
   * Адрес отправителя. Форма зависит от типа канала
   * @minLength 1
   */
  senderAddress: string;
};

/** Модель куратора */
export interface CuratorView {
  /**
   * Идентификатор куратора
   * @format uuid
   */
  id: string;
  /**
   * Фамилия куратора
   * @minLength 1
   * @maxLength 256
   */
  lastName?: string | null;
  /**
   * Имя куратора
   * @minLength 1
   * @maxLength 256
   */
  firstName?: string | null;
  /**
   * Отчество куратора
   * @minLength 1
   * @maxLength 256
   */
  middleName?: string | null;
}

/** Тип значений - строка/число/дата */
export enum DataType {
  String = "String",
  Number = "Number",
  Date = "Date",
}

/** Модель, представляющая правило распределения */
export interface DistributionRule {
  /**
   * Имя правила
   * @maxLength 100
   * @example "Случайный порядок"
   */
  name: string;
  /**
   * Описание правила
   * @maxLength 1000
   * @example "Обращения на оператора попадают в случайном порядке"
   */
  description?: string | null;
  /**
   * Тип правила
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "CRP.ExecutorShuffler"
   */
  ruleType: string;
  /**
   * Признак того, является ли данное правило правилом по умолчанию
   * @example false
   */
  isDefault: boolean;
}

/** Модель для автообработчика без параметров. */
export type EmptyAutoHandler = BaseAutoHandler & object;

/** Модель базовой папки для шаблонов */
export type FolderBase = BaseFolderBase &
  (
    | BaseFolderBaseKindMapping<"AnswerTemplateFolder", any>
    | BaseFolderBaseKindMapping<"AutoReplyTemplateFolder", any>
    | BaseFolderBaseKindMapping<"PersonalTemplateFolder", any>
  );

/** Представляет результат получения рассылок */
export interface GetMailingsResult {
  /** Периодические рассылки */
  periodicMailings?: PeriodicMailing[];
  /** Пороговые рассылки */
  thresholdMailings?: ThresholdMailing[];
}

export interface ImportResult {
  importedTemplates?: TitleIdPair[];
  notImportedTemplates?: TitleErrorMessagePair[];
}

/** Интеллектуальный бот */
export interface IntelligentBot {
  /**
   * Включен ли интеллектуальный бот
   * @example true
   */
  enabled: boolean;
  /**
   * Код бота
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "Omilia"
   */
  botCode?: string | null;
}

/** Модель интервала действия версии */
export interface Interval {
  /**
   * Дата начала интервала
   * @format date-time
   */
  from: string;
  /**
   * Дата окончания интервала
   * @format date-time
   */
  to?: string | null;
}

/** Модель KeyWord (ключевое слово) */
export interface KeyWord {
  /** Ключевое слово. */
  word: string;
}

/** Модель, представляющая параметр KPI */
export interface KpiParameter {
  /**
   * Отображаемое имя
   * @maxLength 1000
   */
  displayName: string;
  /**
   * Код показателя
   * @maxLength 100
   * @pattern ^[a-zA-Z0-9_\-.]+$
   */
  code: string;
  /** Признак того, целое или дробное значение у показателя */
  isInteger: boolean;
  /** Признак того, должно ли значение границы Alarm превышать значение границы Warning */
  alarmExceedsWarning: boolean;
  /**
   * Значение по умолчанию для границы Alarm
   * @format double
   */
  defaultAlarmValue?: number | null;
  /**
   * Минимальное значение для границы Alarm
   * @format double
   */
  alarmMinValue?: number | null;
  /**
   * Максимальное значение для границы Alarm
   * @format double
   */
  alarmMaxValue?: number | null;
  /**
   * Значение по умолчанию для границы Warning
   * @format double
   */
  defaultWarningValue?: number | null;
  /**
   * Минимальное значение для границы Warning
   * @format double
   */
  warningMinValue?: number | null;
  /**
   * Максимальное значение для границы Warning
   * @format double
   */
  warningMaxValue?: number | null;
  /** Список доступных единиц измерения */
  units?: string[] | null;
  /** Признак наличия warning */
  isWarningAvailable?: boolean;
  /** Признак наличия переадресации при превышении */
  isTransferCallDestinationAvailable?: boolean;
}

/** Единица измерения параметра KPI */
export enum KpiParameterMeasurement {
  None = "None",
  Sec = "Sec",
  Min = "Min",
  Hours = "Hours",
  Days = "Days",
}

/** Канал для отправки сообщений */
export interface MailingChannel {
  /**
   * Код канала
   * @minLength 1
   */
  code: string;
  /**
   * Отображаемое имя канала
   * @minLength 1
   */
  displayName: string;
  /**
   * Адрес отправителя. Форма зависит от типа канала
   * @minLength 1
   */
  senderAddress: string;
}

/** Создатель рассылки */
export interface MailingCreator {
  /**
   * Логин создателя
   * @minLength 1
   */
  login: string;
  /**
   * Имя создателя
   * @minLength 1
   */
  name: string;
  /**
   * Дата создания
   * @format date-time
   */
  createdAt: string;
}

/** Показатель для рассылки */
export interface MailingMetric {
  /**
   * Идентификатор показателя
   * @format int64
   */
  id: number;
  /**
   * Отображаемое имя показателя
   * @minLength 1
   */
  displayName: string;
}

/** Получатель рассылки */
export interface MailingRecipient {
  /**
   * Адрес получателя
   * @minLength 1
   */
  address: string;
  /**
   * Часовой пояс получателя относительно UTC
   * @format int32
   * @min -12
   * @max 14
   */
  utcOffset: number;
}

/** Модель времени в статусе */
export interface MaxStatusTime {
  /**
   * Статус оператора
   * @minLength 0
   * @maxLength 50
   */
  status: string;
  /**
   * Предельное время в статусе в минутах
   * @format int32
   * @min 1
   * @max 1440
   */
  maxTimeMinutes?: number | null;
}

export enum MessageType {
  Undefined = "Undefined",
  Vk = "Vk",
  Viber = "Viber",
  Telegram = "Telegram",
  Facebook = "Facebook",
  Chat = "Chat",
  Sms = "Sms",
  Exchange = "Exchange",
  MfmsWhatsApp = "MfmsWhatsApp",
  Yandex = "Yandex",
  Voice = "Voice",
  ViberBusiness = "ViberBusiness",
  IMessage = "iMessage",
  InfobipWhatsApp = "InfobipWhatsApp",
  Nanosemantics = "Nanosemantics",
  VkPublic = "VkPublic",
  VideoChat = "VideoChat",
  External = "External",
  MfWhatsApp = "MfWhatsApp",
  BackCall = "BackCall",
  Instagram = "Instagram",
}

/** Негативное слово */
export interface NegativeWord {
  /**
   * Идентификатор
   * @format uuid
   */
  id?: string;
  /** Код */
  code: string;
  /** Имя */
  name: string;
  /** Комментарий */
  comment: string;
  /**
   * Дата создания
   * @format date-time
   */
  dateCreated?: string;
  /** Логин создателя */
  addedBy?: string | null;
  /** ФИО создателя */
  addedByFio?: string | null;
  /**
   * Дата удаления
   * @format date-time
   */
  dateDeleted?: string | null;
  /** Идентификатор пользователя, который удалил */
  deletedBy?: string | null;
}

/** Модель для создания нового оператора */
export interface NewOperator {
  /**
   * Учетная запись оператора в Системе
   * @minLength 1
   * @maxLength 256
   */
  login: string;
  /**
   * Фамилия оператора
   * @minLength 1
   * @maxLength 256
   */
  lastName?: string | null;
  /**
   * Имя оператора
   * @minLength 1
   * @maxLength 256
   */
  firstName?: string | null;
  /**
   * Отчество оператора
   * @minLength 1
   * @maxLength 256
   */
  middleName?: string | null;
  /**
   * Email оператора
   * @format email
   */
  email?: string | null;
  /**
   * Настройки KPI оператора
   * @maxItems 100
   */
  kpiSettings?: OperatorKpiParameterView[] | null;
  /** Настройки времени статусов оператора */
  statusSettings?: StatusSettings;
  /** Настройки сессий */
  sessionSettings?: SessionSettings;
  /**
   * Идентификатор куратора
   * @format uuid
   */
  curatorId?: string | null;
}

/** Модель для создания новой группы операторов */
export interface NewOperatorGroup {
  /**
   * Название группы
   * @minLength 0
   * @maxLength 100
   * @example "B2C"
   */
  name: string;
  /**
   * Идентификатор родительской группы
   * @format uuid
   */
  parentId?: string | null;
  /** Идентификаторы операторов в группе */
  operatorIds?: string[] | null;
  /** Настройки сессий */
  sessionSettings?: SessionSettings;
  /** Настройки времени статусов оператора */
  statusSettings?: StatusSettings;
  /** Настройки KPI для группы операторов */
  kpiSettings?: OperatorGroupKpiParameter[] | null;
}

/** Модель оператора */
export interface Operator {
  /**
   * Идентификатор оператора
   * @format uuid
   * @example "ded6514e-86f2-461b-b794-63260ed0625b"
   */
  id: string;
  /**
   * Приоритет оператора
   * @format int32
   * @min 0
   * @max 9
   * @example 1
   */
  priority: number;
}

/** Модель группы оператора */
export interface OperatorGroup {
  /**
   * Идентификатор группы
   * @format uuid
   */
  id: string;
  /**
   * Название группы
   * @maxLength 100
   * @example "B2C"
   */
  name: string;
}

/** Параметры KPI для группы операторов */
export interface OperatorGroupKpiParameter {
  /**
   * Код показателя
   * @minLength 1
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "ASA"
   */
  code: string;
  /**
   * Целевое значение показателя
   * @format double
   */
  target?: number | null;
}

/** Пороговое значение KPI */
export interface OperatorGroupKpiThresholdValue {
  /** Код */
  code: string;
  /** Отображаемое имя */
  displayName: string;
  /** Признак числового поля */
  isInteger?: boolean;
  /**
   * Значение по умолчанию
   * @format double
   */
  defaultValue?: number;
  /**
   * Минимальное значение
   * @format double
   */
  minValue?: number;
  /**
   * Максимальное значение
   * @format double
   */
  maxValue?: number | null;
}

/** Параметры вкладки групп операторов */
export interface OperatorGroupParametersView {
  /** Список пороговых значений KPI */
  kpiThresholdValues?: OperatorGroupKpiThresholdValue[] | null;
}

/** Представление дерева группы операторов */
export interface OperatorGroupTreeView {
  /**
   * Идентификатор родительской группы
   * @format uuid
   */
  parentId?: string | null;
  /** Настройки сессий */
  sessionSettings?: SessionSettings;
  /** Настройки времени статусов оператора */
  statusSettings?: StatusSettings;
  /** Настройки KPI для группы операторов */
  kpiSettings?: OperatorGroupKpiParameter[] | null;
  /**
   * Идентификатор группы
   * @format uuid
   */
  id: string;
  /**
   * Название группы
   * @maxLength 100
   * @example "B2C"
   */
  name: string;
}

/** Группа операторов */
export interface OperatorGroupView {
  /** Список операторов в группе */
  operators?: OperatorViewBase[];
  /**
   * Идентификатор родительской группы
   * @format uuid
   */
  parentId?: string | null;
  /** Настройки сессий */
  sessionSettings?: SessionSettings;
  /** Настройки времени статусов оператора */
  statusSettings?: StatusSettings;
  /** Настройки KPI для группы операторов */
  kpiSettings?: OperatorGroupKpiParameter[] | null;
  /**
   * Идентификатор группы
   * @format uuid
   */
  id: string;
  /**
   * Название группы
   * @maxLength 100
   * @example "B2C"
   */
  name: string;
}

/** Параметры KPI оператора. */
export interface OperatorKpiParameterView {
  /**
   * Код показателя
   * @minLength 1
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "ASA"
   */
  code: string;
  /**
   * Целевое значение показателя
   * @format double
   * @example 1000
   */
  target?: number | null;
}

/** Пороговое значение KPI */
export interface OperatorKpiThresholdValue {
  /** Код */
  code: string;
  /** Отображаемое имя */
  displayName: string;
  /**
   * Значение по умолчанию
   * @format double
   */
  defaultValue?: number;
  /** Признак числового поля */
  isInteger?: boolean;
  /**
   * Минимальное значение
   * @format double
   */
  minValue?: number;
  /**
   * Максимальное значение
   * @format double
   */
  maxValue?: number | null;
}

/** Параметры вкладки операторов */
export interface OperatorParametersView {
  /** Список пороговых значений KPI */
  kpiThresholdValues?: OperatorKpiThresholdValue[] | null;
}

/** Модель оператора */
export interface OperatorView {
  /**
   * Email оператора
   * @format email
   */
  email?: string | null;
  /** Настройки KPI оператора */
  kpiSettings?: OperatorKpiParameterView[] | null;
  /** Настройки времени статусов оператора */
  statusSettings?: StatusSettings;
  /** Настройки сессий */
  sessionSettings?: SessionSettings;
  /**
   * Идентификатор оператора
   * @format uuid
   */
  id: string;
  /**
   * Учетная запись оператора в Системе
   * @minLength 1
   * @maxLength 100
   * @example "i.ivanov"
   */
  login: string;
  /**
   * Фамилия оператора
   * @minLength 1
   * @maxLength 256
   * @example "Иванов"
   */
  lastName?: string | null;
  /**
   * Имя оператора
   * @minLength 1
   * @maxLength 256
   * @example "Иван"
   */
  firstName?: string | null;
  /**
   * Отчество оператора
   * @minLength 1
   * @maxLength 256
   * @example "Иванович"
   */
  middleName?: string | null;
  /** Группы, в которых состоит оператор */
  groups?: OperatorGroup[];
  /** Модель куратора */
  curator?: CuratorView;
}

/** Базовая модель оператора */
export interface OperatorViewBase {
  /**
   * Идентификатор оператора
   * @format uuid
   */
  id: string;
  /**
   * Учетная запись оператора в Системе
   * @minLength 1
   * @maxLength 100
   * @example "i.ivanov"
   */
  login: string;
  /**
   * Фамилия оператора
   * @minLength 1
   * @maxLength 256
   * @example "Иванов"
   */
  lastName?: string | null;
  /**
   * Имя оператора
   * @minLength 1
   * @maxLength 256
   * @example "Иван"
   */
  firstName?: string | null;
  /**
   * Отчество оператора
   * @minLength 1
   * @maxLength 256
   * @example "Иванович"
   */
  middleName?: string | null;
  /** Группы, в которых состоит оператор */
  groups?: OperatorGroup[];
  /** Модель куратора */
  curator?: CuratorView;
}

/** Вариант ответа для атрибута маршрутизации */
export interface Option {
  /**
   * Имя варианта ответа
   * @minLength 0
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-]+$
   * @example "Голос"
   */
  name: string;
  /**
   * Значение варианта ответа
   * @minLength 0
   * @maxLength 100
   * @example "CHANNEL.VOICE"
   */
  value: string;
}

/** Владелец личного шаблона */
export interface Owner {
  /**
   * Идентификатор оператора
   * @format uuid
   */
  id: string;
  /**
   * Имя оператора
   * @maxLength 768
   */
  name?: string | null;
}

/** Бот */
export interface ParameterViewBot {
  /**
   * Название чат бота
   * @minLength 0
   * @maxLength 1000
   * @example "Omilia"
   */
  name: string;
  /**
   * Код чат бота
   * @minLength 0
   * @maxLength 100
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "Omilia"
   */
  code: string;
}

/** Периодическая рассылка */
export interface PeriodicMailing {
  /**
   * Идентификатор рассылки
   * @format uuid
   */
  id: string;
  /**
   * Название рассылки
   * @minLength 1
   */
  name: string;
  /** Включена ли рассылка */
  isEnabled: boolean;
  /** Создатель рассылки */
  creator: MailingCreator;
  /** Показатель для рассылки */
  metric: MailingMetric;
  /** Канал для отправки сообщений */
  channel: MailingChannel;
  /** Интервал времени для отправки сообщений */
  allowedTime: TimeInterval;
  /**
   * Период в минутах для отправки сообщений
   * @format int32
   * @min 1
   * @max 2147483647
   */
  periodMinutes: number;
  /** Получатели рассылки */
  recipients: MailingRecipient[];
}

/** Личный шаблон */
export interface PersonalTemplate {
  /** Модель содержимого шаблона */
  content: Content;
  /** Вложения к шаблону */
  attachments?: Attachment[] | null;
  /**
   * Идентификатор шаблона.
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
  /**
   * Дискриминатор
   * @default "PersonalTemplate"
   */
  kind: string;
}

/** Модель для добавления или редактирования личного шаблона. */
export type PersonalTemplateAddEdit = TemplateAddEditBase & {
  /**
   * Владелец шаблона (идентификатор оператора)
   * @format uuid
   */
  ownerId?: string;
};

/** Модель личной папки шаблонов */
export type PersonalTemplateFolder = FolderBase & object;

/** Представление папки личных шаблонов */
export interface PersonalTemplateFolderView {
  /**
   * Идентификатор папки
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор владельца
   * @format uuid
   */
  ownerId: string;
  /** Список шаблонов в папке */
  templates?: PersonalTemplatePreview[] | null;
  /** Дочерние папки */
  childrenFolders?: PersonalTemplateFolderView[] | null;
  /**
   * Название папки
   * @minLength 1
   * @maxLength 200
   */
  title: string;
  /**
   * Идентификатор папки родителя
   * @format uuid
   */
  parentId?: string | null;
}

/** Модель предпросмотра личного шаблона ответа */
export interface PersonalTemplatePreview {
  /**
   * Идентификатор шаблона.
   * @format uuid
   */
  id: string;
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
}

/** Модель папки шаблонов ответа для личных */
export interface PersonalTemplateView {
  /** Владелец личного шаблона */
  owner: Owner;
  /** Представление папки личных шаблонов */
  folder: PersonalTemplateFolderView;
}

/** Модель для автообработчика с прогнозированием времени ожидания. */
export type PredictedWaitTimeAutoHandler = BaseAutoHandler & {
  /**
   * Минимальное прогнозное время ожидания в секундах
   * @format int32
   * @min 1
   * @max 32767
   * @example 5
   */
  minWaitTimeSeconds: number;
  /**
   * Максимальное время ожидания в секундах
   * @format int32
   * @min 1
   * @max 32767
   * @example 30
   */
  maxWaitTimeSeconds: number;
  /**
   * Идентификатор шаблона, когда время ожидания меньше минимального
   * @format uuid
   * @example "111754a1-230c-4cf1-aa8e-a2e54e89cbf5"
   */
  templateIdBelowMin: string | null;
  /**
   * Идентификатор шаблона, когда время ожидания больше максимального
   * @format uuid
   * @example "bcb7b41c-082b-41b7-b35e-408df198ee30"
   */
  templateIdAboveMax: string | null;
  /**
   * Идентификатор шаблона, когда время ожидания в пределах нормы
   * @format uuid
   * @example "cae36297-5e4a-4258-8849-4e8c6aba4125"
   */
  templateIdWithinRange: string | null;
};

/** Представляет атрибут приоритизации */
export interface PrioritizationAttribute {
  /**
   * Уникальный идентификатор
   * @format int64
   */
  id: number;
  /**
   * Отображаемое имя
   * @minLength 0
   * @maxLength 32
   */
  displayName: string;
  attributeType: PrioritizationAttributeType;
  /**
   * Код атрибута
   * @minLength 0
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-]+$
   */
  code: string;
  /** Тип значений - строка/число */
  dataType: PrioritizationDataType;
  /** Тип правой части - с чем предполагается сравнивать */
  rightPartType: PrioritizationRightPartType;
  /**
   * Список возможных вариантов ответа
   * @maxItems 1000
   */
  options?: PrioritizationOption[];
  /**
   * Список правил сравнения для атрибута
   * @maxItems 100
   */
  comparisonRules: PrioritizationComparisonRule[];
}

export enum PrioritizationAttributeType {
  Unknown = "Unknown",
  Base = "Base",
  Custom = "Custom",
}

/** Правило сравнения для атрибута приоритизации */
export interface PrioritizationComparisonRule {
  /** Имя правила */
  name: string;
  /** Код правила */
  code: string;
}

/** Тип значений - строка/число */
export enum PrioritizationDataType {
  Unknown = "Unknown",
  String = "String",
  Number = "Number",
}

/** Вариант ответа для атрибута маршрутизации */
export interface PrioritizationOption {
  /** Имя варианта ответа */
  name: string;
  /** Значение варианта ответа */
  value: string;
}

/** Тип правой части - с чем предполагается сравнивать */
export enum PrioritizationRightPartType {
  Unknown = "Unknown",
  Text = "Text",
  List = "List",
  None = "None",
}

/** Представляет правило приоритизации */
export interface PrioritizationRule {
  /**
   * Уникальный идентификатор
   * @format int32
   */
  id: number;
  /** Название */
  title: string;
  /** Включено ли правило */
  isEnabled: boolean;
  /**
   * Идентификатор атрибута
   * @format int32
   */
  attributeId: number;
  /** Оператор сравнения */
  comparisonRule: string;
  /** Значение приоритизаций */
  values: PriorityValue[];
}

/** Представляет значение приоритизации */
export interface PriorityValue {
  /** Значение */
  value: string | null;
  /**
   * Приоритет
   * @format int32
   * @min 1
   * @max 32767
   */
  priority: number;
}

export interface ProblemDetails {
  type?: string | null;
  title?: string | null;
  /** @format int32 */
  status?: number | null;
  detail?: string | null;
  instance?: string | null;
  [key: string]: any;
}

/** Модель очереди */
export interface QueueInfo {
  /**
   * Идентификатор очереди
   * @format int32
   * @min 1
   * @max 32767
   * @example 1
   */
  id: number;
  /**
   * Наименование очереди
   * @minLength 1
   * @maxLength 100
   * @example "Новая очередь"
   */
  name: string;
  /**
   * Описание очереди
   * @minLength 1
   * @maxLength 1000
   * @example "Очередь для обработки заказов"
   */
  description?: string | null;
  /**
   * Вес очереди
   * @format int32
   * @min 1
   * @max 32767
   * @example 100
   */
  weight: number;
  /**
   * Количество операторов у очереди
   * @format int32
   * @example 10
   */
  operatorsCount: number;
  /**
   * Признак того, является ли данная очередь очередью по умолчанию
   * @example true
   */
  isDefault?: boolean;
  /**
   * Признак того, является ли данная очередь служебной очередью
   * @example true
   */
  isService?: boolean;
}

/** Параметры KPI */
export interface QueueKpiParameter {
  /**
   * Код показателя
   * @minLength 1
   * @maxLength 128
   * @pattern ^[a-zA-Z0-9_\-.\s()]+$
   * @example "ASA"
   */
  code: string;
  /**
   * Значение границы Alarm
   * @format double
   * @min 0
   * @max 100000
   * @example 60
   */
  alarmThreshold: number;
  /**
   * Значение границы Warning (опционально)
   * @format double
   * @min 0
   * @max 100000
   * @example 30
   */
  warningThreshold?: number | null;
  /** Единица измерения параметра KPI */
  measurement?: KpiParameterMeasurement;
  /**
   * Переадресация при превышении
   * @format int32
   */
  transferCallDestination?: number | null;
}

/** Отображение параметра KPI */
export interface QueueKpiParameterView {
  /**
   * Код показателя
   * @minLength 1
   * @maxLength 128
   * @pattern ^[a-zA-Z0-9_\-.\s()]+$
   * @example "ASA"
   */
  code: string;
  /**
   * Значение границы Alarm
   * @format double
   * @min 0
   * @max 100000
   * @example 60
   */
  alarmThreshold: number;
  /**
   * Значение границы Warning (опционально)
   * @format double
   * @min 0
   * @max 100000
   * @example 30
   */
  warningThreshold?: number | null;
  /** Единица измерения параметра KPI */
  measurement?: KpiParameterMeasurement;
  /**
   * Переадресация при превышении
   * @format int32
   */
  transferCallDestination?: number | null;
}

/** Параметры вкладки очередей */
export interface QueueParametersView {
  /** Список доступных атрибутов для маршрутизации */
  routingAttributes?: RoutingAttribute[];
  /** Список доступных правил распределения */
  distributionRules?: DistributionRule[];
  /** Список доступных авто обработчиков */
  autoHandlers?: AutoHandler[];
  /** Список интеллектуальных ботов */
  intelligentBots?: ParameterViewBot[];
  /** Список кнопочных ботов */
  buttonBots?: ParameterViewBot[];
  /** Список оценочных ботов */
  ratingBots: ParameterViewBot[];
  /** Список доступных KPI показателей для очереди */
  kpiParameters?: KpiParameter[];
  /** Список подразделений */
  divisions: OperatorGroup[];
}

/** Отображение очереди */
export interface QueueView {
  /**
   * Идентификатор очереди
   * @format int32
   */
  id: number;
  /** Наименование очереди */
  name: string;
  /** Описание очереди */
  description?: string | null;
  /**
   * Подразделение
   * @format uuid
   */
  division?: string | null;
  /**
   * Вес очереди
   * @format int32
   */
  weight: number;
  /** Признак того, является ли данная очередь очередью по умолчанию */
  isDefault?: boolean;
  /**
   * Признак того, является ли данная очередь служебной очередью
   * @example true
   */
  isService?: boolean;
  /** Список идентификаторов операторов */
  operators?: Operator[];
  /** Список идентификаторов групп операторов */
  operatorGroups?: string[];
  /** Список наборов правил маршрутизации */
  routingRuleSets?: RoutingRuleSet[];
  /** Правило распределения */
  workSelectorType: string;
  /** Список автообработчиков */
  autoHandlers?: (PredictedWaitTimeAutoHandler | TemplateAutoHandler | EmptyAutoHandler)[];
  /** Интеллектуальный бот */
  intelligentBot: IntelligentBot;
  /** Оценочный бот */
  ratingBot: RatingBot;
  /** Кнопочный бот */
  buttonBot: ButtonBot;
  /** Список показателей КПИ */
  kpiParameters?: QueueKpiParameterView[];
}

/** Оценочный бот */
export interface RatingBot {
  /**
   * Включен ли кнопочный бот
   * @example true
   */
  enabled: boolean;
  /**
   * Код бота
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "B2B"
   */
  botCode?: string | null;
}

/** Тематика обращения */
export interface RequestSubject {
  /**
   * Идентификатор тематики
   * @format int32
   * @min 1
   * @max 32767
   */
  id: number;
  /**
   * Название тематики
   * @minLength 0
   * @maxLength 100
   */
  name: string;
  /**
   * Код тематики
   * @minLength 0
   * @maxLength 100
   */
  code: string;
  /**
   * Краткое описание тематики
   * @minLength 0
   * @maxLength 1000
   */
  description?: string | null;
  /**
   * Идентификатор родительской тематики
   * @format int32
   * @min 1
   * @max 32767
   */
  parentId?: number | null;
  /**
   * Контекст для базы знаний
   * @minLength 0
   * @maxLength 1000
   */
  knowledgeBaseContext?: string | null;
  /**
   * Контекст для чат бота
   * @minLength 0
   * @maxLength 1000
   */
  chatBotContext?: string | null;
  /** Идентификатор скрипта диалога */
  dialogScriptCode?: string | null;
}

/** Тип правой части - с чем предполагается сравнивать */
export enum RightPartType {
  Text = "Text",
  List = "List",
  MiltiselectList = "MiltiselectList",
}

/** Атрибут маршрутизации */
export interface RoutingAttribute {
  /**
   * Идентификатор атрибута
   * @format int64
   */
  id: number;
  /**
   * Отображаемое имя
   * @minLength 0
   * @maxLength 32
   */
  displayName: string;
  /** Модель атрибута - клиент или обращение */
  model: AttributeModel;
  /** Тип атрибута - базовый или кастомный */
  attributeType: AttributeType;
  /**
   * Код атрибута
   * @minLength 0
   * @maxLength 32
   * @pattern ^[a-zA-Z0-9_\-]+$
   */
  code: string;
  /** Тип значений - строка/число/дата */
  dataType: DataType;
  /** Тип правой части - с чем предполагается сравнивать */
  rightPartType: RightPartType;
  /**
   * Список возможных вариантов ответа
   * @maxItems 1000
   */
  options?: Option[];
  /**
   * Список правил сравнения для атрибута
   * @maxItems 100
   */
  comparisonRules: ComparisonRule[];
}

/** Правило маршрутизации */
export interface RoutingRule {
  /**
   * Идентификатор аттрибута
   * @format int32
   * @min 0
   * @max **********
   * @example 1
   */
  attributeId: number;
  /**
   * Оператор сравнения
   * @minLength 1
   * @maxLength 50
   * @example "Equals"
   */
  comparisonRule: string;
  /**
   * Значение правой части
   * @minLength 1
   * @example "Для оператора сравнения In, параметр должен быть с разделителем (';') пример: CHANNEL.TELEGRAM;CHANNEL.EXCHANGE"
   */
  value: string;
}

/** Правила маршрутизации */
export interface RoutingRuleSet {
  /**
   * Идентификатор группы
   * @format int32
   */
  id: number;
  /**
   * Список правил маршрутизации
   * @maxItems 100
   */
  rules: RoutingRule[];
}

/** Настройки сессий */
export interface SessionSettings {
  /**
   * Общее максимальное количество сессий
   * @format int32
   * @min 0
   * @max 100
   */
  maxSessions?: number | null;
  /**
   * Количество сессий по каналам
   * @maxItems 100
   */
  channelSessions?: ChannelSession[] | null;
}

/** Спам слово */
export interface SpamWord {
  /**
   * Идентификатор
   * @format uuid
   */
  id?: string;
  /** Код */
  code: string;
  /** Имя */
  name: string;
  /** Комментарий */
  comment: string;
  /**
   * Дата создания
   * @format date-time
   */
  dateCreated?: string;
  /** Логин создателя */
  addedBy?: string | null;
  /** ФИО создателя */
  addedByFio?: string | null;
  /**
   * Дата удаления
   * @format date-time
   */
  dateDeleted?: string | null;
  /** Идентификатор пользователя, который удалил */
  deletedBy?: string | null;
}

/** Настройки времени статусов оператора */
export interface StatusSettings {
  /**
   * Предельное время в перерывах в минутах
   * @format int32
   * @min 0
   * @max 1440
   */
  maxBreakTimeMinutes?: number | null;
  /**
   * Предельное время в статусах
   * @maxItems 100
   */
  maxStatusTimes?: MaxStatusTime[] | null;
}

/** Базовая модель для создания шаблона */
export type TemplateAddEditBase = BaseTemplateAddEditBase &
  (
    | BaseTemplateAddEditBaseKindMapping<"AnswerTemplateAddEdit", any>
    | BaseTemplateAddEditBaseKindMapping<"AutoReplyAddEdit", any>
    | BaseTemplateAddEditBaseKindMapping<"PersonalTemplateAddEdit", any>
  );

/** Модель для автообработчика на основе шаблона. */
export type TemplateAutoHandler = BaseAutoHandler & {
  /**
   * Идентификатор шаблона.
   * @format uuid
   * @example "58488c3e-30ef-4a35-9e9c-f4916518af48"
   */
  templateId: string;
};

/** Категория шаблонов */
export enum TemplateCategory {
  Answers = "Answers",
  AutoReplies = "AutoReplies",
  Personal = "Personal",
}

/** Статус шаблона */
export enum TemplateStatus {
  Published = "Published",
  Draft = "Draft",
  Archived = "Archived",
}

/** Модель деревьев шаблонов */
export interface TemplateTrees {
  /** Модель папки шаблонов ответа для дерева. */
  answers?: AnswerTemplateFolderView;
  /** Модель папки шаблонов ответа для дерева */
  autoReplies?: AutoReplyTemplateFolderView;
  /** Product.Core.Settings.Models.Templates.PersonalTemplateView */
  personal?: PersonalTemplateView[] | null;
}

/** Пороговая рассылка */
export interface ThresholdMailing {
  /**
   * Идентификатор рассылки
   * @format uuid
   */
  id: string;
  /**
   * Название рассылки
   * @minLength 1
   */
  name: string;
  /** Включена ли рассылка */
  isEnabled: boolean;
  /** Создатель рассылки */
  creator: MailingCreator;
  /** Показатель для рассылки */
  metric: MailingMetric;
  /** Канал для отправки сообщений */
  channel: MailingChannel;
  /**
   * Идентификатор очереди для пороговой рассылки
   * @format int64
   */
  queueId: number;
  /** Интервал времени для отправки сообщений */
  allowedTime: TimeInterval;
  /**
   * Пороговое значение для показателя
   * @format double
   * @min 0
   */
  threshold: number;
  /** Получатели рассылки */
  recipients: MailingRecipient[];
}

/** Интервал времени для отправки сообщений */
export interface TimeInterval {
  /**
   * Начало интервала (в формате HH:mm:ss)
   * @format date-span
   */
  start: string;
  /**
   * Конец интервала (в формате HH:mm:ss)
   * @format date-span
   */
  end: string;
}

export interface TitleErrorMessagePair {
  title?: string;
  errorMessage?: string;
}

export interface TitleIdPair {
  title?: string;
  /** @format uuid */
  id?: string;
}

/** Модель для добавления адреса в черный список */
export interface UpdateBlackListAddress {
  /**
   * Адрес
   * @minLength 0
   * @maxLength 100
   */
  address: string;
  /**
   * В списке до
   * @format date-time
   */
  dueDate: string;
  /** Комментарий */
  comment?: string | null;
}

/** Модель для обновления спам слова */
export interface UpdateNegativeWord {
  /**
   * Код
   * @minLength 0
   * @maxLength 60
   */
  code: string;
  /**
   * Имя
   * @minLength 0
   * @maxLength 60
   */
  name: string;
  /** Комментарий */
  comment: string;
  /**
   * Кем добавлен (логин)
   * @minLength 0
   * @maxLength 100
   */
  addedBy: string;
  /**
   * Кем добавлен (ФИО)
   * @minLength 0
   * @maxLength 100
   */
  addedByFio: string;
}

/** Модель для обновления параметра KPI */
export interface UpdateOperatorDefaultKpiParameter {
  /** Код */
  code: string;
  /**
   * Значение по умолчанию
   * @format double
   */
  defaultValue?: number;
}

/** Представляет обновление правила приоритизации */
export interface UpdatePrioritizationRule {
  /** Название правила */
  title: string;
  /** Включено ли правило */
  isEnabled: boolean;
  /**
   * Идентификатор атрибута
   * @format int32
   */
  attributeId: number;
  /** Оператор сравнения */
  comparisonRule: string;
  /** Значение приоритизаций */
  values: PriorityValue[];
}

/** Обновление очереди */
export interface UpdateQueue {
  /**
   * Наименование очереди
   * @minLength 1
   * @maxLength 100
   * @example "Новая очередь"
   */
  name: string;
  /**
   * Описание очереди
   * @minLength 1
   * @maxLength 1000
   * @example "Очередь для обработки заказов"
   */
  description?: string | null;
  /**
   * Идентификатор подразделения
   * @format uuid
   */
  divisionId?: string | null;
  /**
   * Вес очереди
   * @format int32
   * @min 0
   * @max 32767
   * @example 100
   */
  weight: number;
  /**
   * Список операторов с их идентификаторами и приоритетами
   * @maxItems 100000
   */
  operators?: Operator[];
  /**
   * Список идентификаторов групп операторов
   * @maxItems 100000
   */
  operatorGroups?: string[];
  /**
   * Список наборов правил маршрутизации
   * @maxItems 1000
   */
  routingRuleSets?: RoutingRuleSet[];
  /**
   * Правило распределения
   * @minLength 1
   * @maxLength 1000
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "CRP.ExecutorShuffler"
   */
  distributionRuleType: string;
  /**
   * Список автообработчиков
   * @maxItems 100
   */
  autoHandlers?: (PredictedWaitTimeAutoHandler | TemplateAutoHandler | EmptyAutoHandler)[];
  /** Интеллектуальный бот */
  intelligentBot: IntelligentBot;
  /** Кнопочный бот */
  buttonBot: ButtonBot;
  /** Оценочный бот */
  ratingBot: RatingBot;
  /**
   * Список показателей КПИ
   * @maxItems 1000
   */
  kpiParameters?: QueueKpiParameter[];
}

/** Обновление теиатики */
export interface UpdateRequestSubject {
  /**
   * Название тематики
   * @minLength 0
   * @maxLength 100
   */
  name: string;
  /**
   * Код тематики
   * @minLength 0
   * @maxLength 100
   */
  code: string;
  /**
   * Краткое описание тематики
   * @minLength 0
   * @maxLength 1000
   */
  description?: string | null;
  /**
   * Идентификатор родительской тематики
   * @format int32
   * @min 1
   * @max 32767
   */
  parentId?: number | null;
  /**
   * Контекст для базы знаний
   * @minLength 0
   * @maxLength 1000
   */
  knowledgeBaseContext?: string | null;
  /**
   * Контекст для чат бота
   * @minLength 0
   * @maxLength 1000
   */
  chatBotContext?: string | null;
  /** Идентификатор скрипта диалога */
  dialogScriptCode?: string | null;
}

/** Модель для обновления спам слова */
export interface UpdateSpamWord {
  /**
   * Код
   * @minLength 0
   * @maxLength 60
   */
  code: string;
  /**
   * Имя
   * @minLength 0
   * @maxLength 60
   */
  name: string;
  /** Комментарий */
  comment: string;
  /**
   * Кем добавлен (логин)
   * @minLength 0
   * @maxLength 100
   */
  addedBy: string;
  /**
   * Кем добавлен (ФИО)
   * @minLength 0
   * @maxLength 100
   */
  addedByFio: string;
}

/** Базовая модель для автообработчика. */
interface BaseBaseAutoHandler {
  /** Дискриминатор */
  kind: "EmptyAutoHandler" | "PredictedWaitTimeAutoHandler" | "TemplateAutoHandler";
  /**
   * Код автообработчика
   * @minLength 1
   * @maxLength 100
   * @pattern ^[a-zA-Z0-9_\-.]+$
   * @example "Queue.InfoAutoReplyWorker.ShouldSendAutoReply"
   */
  code: string;
}

type BaseBaseAutoHandlerKindMapping<Key, Type> = {
  kind: Key;
} & Type;

interface BaseCreateMailing {
  /** Дискриминатор */
  kind: "CreatePeriodicMailing" | "CreateThresholdMailing";
  /**
   * Название рассылки
   * @minLength 1
   */
  name: string;
  /** Включена ли рассылка */
  isEnabled: boolean;
}

type BaseCreateMailingKindMapping<Key, Type> = {
  kind: Key;
} & Type;

/** Модель базовой папки для шаблонов */
interface BaseFolderBase {
  /** Дискриминатор */
  kind: "AnswerTemplateFolder" | "AutoReplyTemplateFolder" | "PersonalTemplateFolder";
  /**
   * Название папки
   * @minLength 1
   * @maxLength 200
   */
  title: string;
  /**
   * Идентификатор папки родителя
   * @format uuid
   */
  parentId?: string | null;
}

type BaseFolderBaseKindMapping<Key, Type> = {
  kind: Key;
} & Type;

/** Базовая модель для создания шаблона */
interface BaseTemplateAddEditBase {
  /** Дискриминатор */
  kind: "AnswerTemplateAddEdit" | "AutoReplyAddEdit" | "PersonalTemplateAddEdit";
  /**
   * Идентификатор папки
   * @format uuid
   */
  folderId?: string;
  /**
   * Код шаблона
   * @maxLength 64
   */
  code: string;
  /**
   * Название шаблона
   * @maxLength 200
   */
  title: string;
  /** Ключевые слова */
  keywords?: KeyWord[] | null;
}

type BaseTemplateAddEditBaseKindMapping<Key, Type> = {
  kind: Key;
} & Type;
