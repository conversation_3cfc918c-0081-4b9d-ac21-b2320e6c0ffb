import React from 'react';

import { ISelectDataItem } from '@product.front/ui-kit/dist/types/components/Select/Select';
import clsx from 'clsx';

import { Input, Select, utils } from '@product.front/ui-kit';

import { RightPartType } from '../../../../../@types/prioritization';
import { getLocaleMessageById } from '../../../../../helpers/localeHelper';

interface IParameterValueInputProps {
  type?: RightPartType;
  data?: ISelectDataItem[];
  value: string;
  disabled?: boolean;
  isInvalid?: boolean;
  message?: React.ReactNode;
  onChange: (value: string) => void;
}

const ParameterValueInput = ({
  type,
  data,
  value,
  disabled,
  isInvalid,
  message,
  onChange,
}: IParameterValueInputProps) => {
  if (type === RightPartType.List) {
    return (
      <Select
        wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1)}
        placeholder={getLocaleMessageById('prioritization.editor.form.value')}
        data={data ?? []}
        value={value}
        onChange={({ value: v }) => onChange(v ?? '')}
        disabled={disabled}
        required
        isInvalid={isInvalid}
        message={message}
      />
    );
  }
  return (
    <Input
      wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1)}
      placeholder={getLocaleMessageById('prioritization.editor.form.value')}
      value={value}
      onChange={({ value: v }) => onChange(v ?? '')}
      disabled={disabled}
      required
      isInvalid={isInvalid}
      message={message}
    />
  );
};

export default ParameterValueInput;
