import WebarmAppSettingsResolver from '@monorepo/common/src/common/helpers/appSettings/WebarmAppSettingsResolver';
import { IShell } from '@monorepo/common/src/platform/awp-web-interfaces';

interface IAppSettings {
  administrationApiUrl: string;
  productFileServer: string;
  productPublicFileServer: string;
  dataPresentationServiceUrl: string;
  productFileServerApiUrl: string;
}

const appSettingsResolver = new WebarmAppSettingsResolver<IAppSettings>('appAdministrator');

export async function loadSettings(shell?: IShell): Promise<IAppSettings> {
  const commonRequiredFields: (keyof IAppSettings)[] = [
    'administrationApiUrl',
    'productFileServer',
    'productPublicFileServer',
    'dataPresentationServiceUrl',
    'productFileServerApiUrl',
  ];
  appSettingsResolver.setRequiredFields(commonRequiredFields);

  appSettingsResolver.tryApplyShell(shell);
  await appSettingsResolver.tryApplyUnderfoot('config/appSettings.json');

  return appSettingsResolver.getAppSettings();
}

export function getSettings(): IAppSettings {
  return appSettingsResolver.getAppSettings();
}
