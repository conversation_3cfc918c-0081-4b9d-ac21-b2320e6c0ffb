import { combineReducers, configureStore } from '@reduxjs/toolkit';

import automationServicesSlice from './automationServices/automationServices.slice';
import externalDataSlice from './externalData/externalData.slice';
import oneScriptSlice from './oneScript/oneScript.slice';
import scriptsSlice from './scripts/scripts.slice';

const rootReducer = combineReducers({
  scripts: scriptsSlice,
  oneScript: oneScriptSlice,
  automationServices: automationServicesSlice,
  externalData: externalDataSlice,
});

export const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
