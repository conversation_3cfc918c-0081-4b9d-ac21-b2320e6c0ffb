import { createSlice } from '@reduxjs/toolkit';

import { IAddressData } from '@monorepo/template-panel/src/@types/front';

import extraReducers from './user.extraReducers';

export interface IUserStore {
  addressData: IAddressData;
}

const initialState: IUserStore = {
  addressData: {},
};

const user = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers,
});

export default user.reducer;
