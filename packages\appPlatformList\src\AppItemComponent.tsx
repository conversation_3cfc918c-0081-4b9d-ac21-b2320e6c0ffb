import React from 'react';

import {
  Badge,
  BadgeType,
  FloatingTooltip,
  FloatingTooltipPosition,
  Indication,
  IndicationSize,
  IndicationType,
} from '@product.front/ui-kit';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';
import { HostedAppState } from '@monorepo/common/src/platform/awp-web-interfaces';

export type AppItemComponentProps = {
  name: string;
  hostedAppsManager: AWP.IHostedAppsManager;
  eventManager: AWP.IEventManager;
  className: string;
  children: React.ReactNode;
};

export type AppItemComponentState = {
  name: string;
  appState: HostedAppState;
  notificationCount: number;
};

export default class AppItemComponent extends React.PureComponent<
  AppItemComponentProps,
  AppItemComponentState
> {
  hostedAppsManager: AWP.IHostedAppsManager;
  eventManager: AWP.IEventManager;

  constructor(props: AppItemComponentProps) {
    super(props);
    this.hostedAppsManager = props.hostedAppsManager;
    this.eventManager = props.eventManager;

    this.onHostedApplicationStateChanged = this.onHostedApplicationStateChanged.bind(this);
    this.onHostedApplicationNotificationCountChanged =
      this.onHostedApplicationNotificationCountChanged.bind(this);

    this.eventManager.subscribe(
      'HostedApplicationStateChanged',
      this.onHostedApplicationStateChanged,
    );

    this.eventManager.subscribe(
      'HostedApplicationNotificationCountChanged',
      this.onHostedApplicationNotificationCountChanged,
    );

    this.state = {
      name: props.name,
      appState: HostedAppState.Default,
      notificationCount: 0,
    };
  }

  onAppButtonClicked() {
    this.hostedAppsManager.activateApp(this.state.name);
  }

  onHostedApplicationStateChanged(eventArgs: AWP.HostedApplicationStateChangedEventArgs) {
    if (this.state.name == eventArgs.appName) {
      this.setState({ appState: eventArgs.state });
    }
  }

  onHostedApplicationNotificationCountChanged(
    eventArgs: AWP.HostedApplicationNotificationCountChangedEventArgs,
  ) {
    if (this.state.name == eventArgs.appName) {
      this.setState({ notificationCount: eventArgs.notificationCount });
    }
  }

  render() {
    return (
      <button
        key={this.state.name}
        onClick={() => this.onAppButtonClicked()}
        className={this.props.className}
      >
        <FloatingTooltip tooltip={this.state.name} position={FloatingTooltipPosition.Top}>
          <span className="app-button-content">
            <span className="app-name">{this.state.name}</span>
            {this.state.appState == AWP.HostedAppState.Error && (
              <Indication
                className="app-state"
                size={IndicationSize.Small}
                type={IndicationType.Busy}
              />
            )}
            {this.state.appState == AWP.HostedAppState.Warning && (
              <Indication
                className="app-state"
                size={IndicationSize.Small}
                type={IndicationType.Away}
              />
            )}
            {this.state.notificationCount > 0 && (
              <Badge className="app-notification-count" rounded type={BadgeType.Warning}>
                {this.state.notificationCount}
              </Badge>
            )}
          </span>
        </FloatingTooltip>
      </button>
    );
  }
}
