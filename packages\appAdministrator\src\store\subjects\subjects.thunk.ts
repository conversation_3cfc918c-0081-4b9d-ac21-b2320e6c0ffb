import { createAsyncThunk } from '@reduxjs/toolkit';

import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import { RequestSubject } from '../../@types/generated/administration';
import { IFrontRequestSubject } from '../../@types/requestSubject';
import { getSettings } from '../../helpers/appSettings';
import {
  mapRequestSubjectDtoToFront,
  mapRequestSubjectFrontToDto,
} from '../../mappers/requestSubjects';

export const getAllSubjects = createAsyncThunk('subjects/all', async () => {
  const response = await commonFetch(`${getSettings().administrationApiUrl}/request-subjects`, {
    credentials: 'include',
  });

  const data = (await response.json()) as RequestSubject[];

  const rootSubjectsMap: Record<number, IFrontRequestSubject> = {};

  data.forEach((subject) => {
    rootSubjectsMap[subject.id] = mapRequestSubjectDtoToFront(subject);
  });

  return rootSubjectsMap;
});

export const deleteSubject = createAsyncThunk(
  'subject/delete',
  async (id: number, { dispatch }) => {
    await commonFetch(`${getSettings().administrationApiUrl}/request-subjects/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    dispatch(getAllSubjects());
  },
);

export const createSubject = createAsyncThunk(
  'subject/create',
  async (subject: IFrontRequestSubject, { dispatch }) => {
    await commonFetch(`${getSettings().administrationApiUrl}/request-subjects`, {
      method: 'POST',
      credentials: 'include',
      body: JSON.stringify(mapRequestSubjectFrontToDto(subject)),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    dispatch(getAllSubjects());
  },
);

export const updateSubject = createAsyncThunk(
  'subject/update',
  async (subject: IFrontRequestSubject, { dispatch }) => {
    await commonFetch(`${getSettings().administrationApiUrl}/request-subjects/${subject.id}`, {
      method: 'PUT',
      credentials: 'include',
      body: JSON.stringify(mapRequestSubjectFrontToDto(subject)),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    dispatch(getAllSubjects());
  },
);
