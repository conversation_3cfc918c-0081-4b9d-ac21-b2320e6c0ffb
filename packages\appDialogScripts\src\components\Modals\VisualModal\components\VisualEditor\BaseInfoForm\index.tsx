import React from 'react';

import clsx from 'clsx';

import { CanClearBehavior, Checkbox, grids, Input, Textarea, utils } from '@product.front/ui-kit';

import { getInputDate } from '@monorepo/common/src/helpers/dateHelper';
import { ScriptStatus } from '@monorepo/dialog-scripts/src/@types/generated/scripts';

import { AppConfigContext } from '../../../../../../context/appConfig';
import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import {
  useDialogScriptsAppDispatch,
  useDialogScriptsAppSelector,
} from '../../../../../../store/hooks';
import { updateScriptBaseData } from '../../../../../../store/oneScript/oneScript.slice';

import TagInputBullet from './TagInputBullet';

import styles from './styles.module.scss';

interface IBaseInfoFormProps {
  requiredForActive: boolean;
  ref?: React.RefObject<HTMLFormElement | null>;
}

const BaseInfoForm: React.FC<IBaseInfoFormProps> = ({ requiredForActive, ref }) => {
  const dispatch = useDialogScriptsAppDispatch();

  const { script } = useDialogScriptsAppSelector((state) => state.oneScript);

  const { canAutostart, canSetPriority } = React.useContext(AppConfigContext);

  const disabled = script?.status === ScriptStatus.Archive;

  const handleUpdateBaseField =
    (fieldName: string) =>
    ({ value }: { value?: string | boolean | number }) => {
      dispatch(updateScriptBaseData({ [fieldName]: value }));
    };

  const handleUpdateBaseFieldAsIs = (fieldName: string) => (value: any) => {
    dispatch(updateScriptBaseData({ [fieldName]: value }));
  };

  if (!script) {
    return null;
  }

  return (
    <form
      ref={ref}
      className={clsx(utils.dFlex, utils.flexColumn, utils.gap2, styles.modalValidated)}
      onSubmit={(e) => e.preventDefault()}
    >
      <div className={grids.row}>
        <Input
          wrapperClassName={clsx(grids.col3)}
          label={getLocaleMessageById('app.modals.form.name')}
          value={script.name}
          autoFocus={!script?.name?.length}
          onChange={handleUpdateBaseField('name')}
          required
          canClearBehavior={CanClearBehavior.Value}
          disabled={disabled}
        />
        <Input
          wrapperClassName={clsx(grids.col2)}
          label={getLocaleMessageById('app.modals.form.code')}
          value={script.code}
          onChange={handleUpdateBaseField('code')}
          canClearBehavior={CanClearBehavior.Value}
          required
          disabled={disabled}
        />
        {canSetPriority && (
          <Input
            wrapperClassName={clsx(grids.col1)}
            value={script.priority?.toString()}
            onChange={({ value }) =>
              handleUpdateBaseField('priority')({ value: value ? Number(value) : 0 })
            }
            label={getLocaleMessageById('app.modals.form.priority')}
            title={getLocaleMessageById('app.modals.form.priority')}
            disabled={disabled}
            min={0}
            type="number"
          />
        )}
        <Input
          wrapperClassName={clsx(grids.col1)}
          label={getLocaleMessageById('app.modals.form.activeFrom')}
          value={script.activeFrom ? getInputDate(new Date(script.activeFrom)) : ''}
          onBlur={(event) => handleUpdateBaseField('activeFrom')({ value: event.target.value })}
          required={requiredForActive}
          type="datetime-local"
          canClearBehavior={CanClearBehavior.Value}
          max={script.activeTo ? getInputDate(new Date(script.activeTo)) : '9999-12-31T23:59:59'}
          disabled={disabled}
        />
        <Input
          wrapperClassName={clsx(grids.col1)}
          label={getLocaleMessageById('app.modals.form.activeTo')}
          value={script.activeTo ? getInputDate(new Date(script.activeTo)) : ''}
          onBlur={(event) => handleUpdateBaseField('activeTo')({ value: event.target.value })}
          type="datetime-local"
          canClearBehavior={CanClearBehavior.Value}
          min={script.activeFrom ? getInputDate(new Date(script.activeFrom)) : ''}
          max="9999-12-31T23:59:59"
          disabled={disabled}
        />
        <Textarea
          wrapperClassName={clsx(canSetPriority ? grids.col4 : grids.col5, styles.textInput)}
          label={getLocaleMessageById('app.modals.form.description')}
          value={script.description}
          onChange={handleUpdateBaseField('description')}
          rows={5}
          style={{ resize: 'none' }}
          required={requiredForActive}
          disabled={disabled}
        />
        <div
          className={clsx(canSetPriority ? grids.col8 : grids.col7, {
            [styles.tagInput]: !canAutostart,
          })}
          style={canAutostart ? undefined : { minHeight: '70px' }}
        >
          <TagInputBullet
            value={script.tags}
            disabled={disabled}
            maxCount={canAutostart ? 3 : 7}
            onChange={handleUpdateBaseFieldAsIs('tags')}
          />
        </div>
        {canAutostart && (
          <div
            className={clsx(
              canSetPriority ? grids.col8 : grids.col7,
              utils.dFlex,
              utils.alignItemsCenter,
            )}
          >
            <Checkbox
              checked={script.canBeAutomated}
              onChange={({ checked }) =>
                handleUpdateBaseField('canBeAutomated')({ value: checked ?? false })
              }
              label={getLocaleMessageById('app.modals.form.canBeAutomated')}
              title={getLocaleMessageById('app.modals.form.canBeAutomated')}
              disabled={disabled}
            />
          </div>
        )}
      </div>
    </form>
  );
};

export default BaseInfoForm;
