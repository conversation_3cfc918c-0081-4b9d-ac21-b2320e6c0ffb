import { IFrontCampaign } from '../@types/campaign';

export const mapFrontCampaignToBackCreateOrUpdate = (frontCampaign: IFrontCampaign) => ({
  author: frontCampaign.author || 'DefaultUser',
  channelLinks: frontCampaign.channels.map((channel) => ({ channelCode: channel })),
  clientLinks: null,
  dateFrom: frontCampaign.dateFrom,
  dateTo: frontCampaign.dateTo,
  description: frontCampaign.description,
  fileUploadingId: frontCampaign.fileUploadingId,
  filter: frontCampaign.filter,
  filterDescriptors: frontCampaign.filterDescriptors,
  ignoreClientTimeZone: frontCampaign.ignoreClientTimeZone,
  mailingRate: 1,
  name: frontCampaign.name,
  needAnswer: frontCampaign.needAnswer,
  offerId: frontCampaign.offerId,
  priority: frontCampaign.priority,
  queueId: frontCampaign.queueId,
  responseWaitingTime: frontCampaign.responseWaitingTime,
  sender: 'DefaultUser',
  startTime: frontCampaign.startTime,
  stopTime: frontCampaign.stopTime,
  type: frontCampaign.type,
  status: frontCampaign.status,
});
