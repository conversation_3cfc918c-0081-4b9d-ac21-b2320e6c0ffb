import * as React from 'react';

import * as AWP from '@monorepo/common/src/platform/awp-web-interfaces';

export class ServiceResolver extends React.PureComponent implements AWP.IServiceResolver {
  registrations: { [typeName: string]: any } = {};

  constructor() {
    super({});
  }

  register<TType>(typeName: string, implementation: TType): void {
    this.registrations[typeName] = implementation;
  }
  resolve<TType>(typeName: string) {
    return this.registrations[typeName] as TType;
  }
}
