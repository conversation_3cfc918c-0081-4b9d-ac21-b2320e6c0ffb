import React from 'react';

import clsx from 'clsx';

import { Button, IconButton, utils } from '@product.front/ui-kit';

import IconDelete from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { getLocaleMessageById } from '../../../helpers/localeHelper';

interface IDeleteStepButtonProps {
  children?: React.ReactNode;
  onDelete: (isMentioned?: boolean) => void;
  needConfirm: boolean;
  disabled?: boolean;
}

const DeleteStepButton = ({
  onDelete,
  needConfirm,
  children,
  disabled = false,
}: IDeleteStepButtonProps) => {
  const handleDeleteClick = React.useCallback(() => {
    if (!needConfirm) return onDelete();

    showConfirmModal({
      header: getLocaleMessageById('app.modals.deleteStep.header'),
      text: getLocaleMessageById('app.modals.deleteStep.text'),
      onConfirm: () => onDelete(true),
    });
  }, [needConfirm, onDelete]);

  React.useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Backspace' || e.key === 'Delete') {
        const activeElementTag = document.activeElement?.tagName?.toLocaleLowerCase?.();
        const isInp =
          (activeElementTag && ['input', 'textarea', 'select'].includes(activeElementTag)) ||
          !!document.activeElement?.getAttribute?.('contenteditable');

        !isInp && handleDeleteClick();
      }
    };

    document.addEventListener('keydown', handleKeydown);
    return () => {
      document.removeEventListener('keydown', handleKeydown);
    };
  }, [handleDeleteClick]);

  if (children) {
    return (
      <Button
        className={clsx(utils.dFlex, utils.gap2)}
        onClick={handleDeleteClick}
        disabled={disabled}
      >
        <IconDelete />
        {children}
      </Button>
    );
  }

  return (
    <IconButton onClick={handleDeleteClick} disabled={disabled}>
      <IconDelete />
    </IconButton>
  );
};

export default DeleteStepButton;
