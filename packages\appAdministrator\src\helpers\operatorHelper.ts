import { CuratorView } from '../@types/generated/administration';
import { IFrontOperatorBase } from '../@types/operator';

const getFio = (
  lastName: string | undefined | null,
  firstName: string | undefined | null,
  middleName: string | undefined | null,
) => {
  return [lastName, firstName, middleName].filter(Boolean).join(' ') || '';
};

export const getOperatorFio = (operator: IFrontOperatorBase | null) => {
  return (
    getFio(operator?.lastName, operator?.firstName, operator?.middleName) || operator?.login || ''
  );
};

export const getCuratorFio = (curator: CuratorView) => {
  return getFio(curator?.lastName, curator?.firstName, curator?.middleName);
};
