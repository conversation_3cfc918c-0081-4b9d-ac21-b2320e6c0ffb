import React from 'react';

import clsx from 'clsx';

import {
  CanClearBehavior,
  Checkbox,
  Dropdown,
  DropdownPosition,
  IconButton,
  Input,
  Jumbotron,
  JumbotronType,
  OverlayLoader,
  ResizablePanel,
  ResizerPosition,
  utils,
} from '@product.front/ui-kit';

import IconFilters from '@product.front/icons/dist/icons11/MainStuff/IconFilters';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconSearch from '@product.front/icons/dist/icons17/MainStuff/IconSearch';
import IconBulletList from '@product.front/icons/dist/icons17/Sorting/IconBulletList';
import IconFromAtoZ from '@product.front/icons/dist/icons17/Sorting/IconFromAtoZ';
import IconFromZtoA from '@product.front/icons/dist/icons17/Sorting/IconFromZtoA';
import IconHierarchy from '@product.front/icons/dist/icons17/Sorting/IconHierarchy';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';

import { IAdmTabComponent } from '../../../../@types/components';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import {
  getTabAsideSize,
  setTabAsideSize,
  tabAsideSizeMax,
  tabAsideSizeMin,
} from '../../../../helpers/resize.helper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import { createSubject, getAllSubjects } from '../../../../store/subjects/subjects.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import { createUpdateSubjectAction } from './ContextMenu';
import ListView from './ListView';
import Preview from './Preview';
import TreeView from './TreeView';

const SubjectsTab: React.FC<IAdmTabComponent> = ({ name, tab }) => {
  const dispatch = useAdministratorAppDispatch();

  const { loading, error, subjects } = useAdministratorAppSelector((store) => store.subjects);

  const [searchText, setSearchText] = React.useState('');
  const [shouldSearchByName, setShouldSearchByName] = React.useState(true);
  const [shouldSearchByText, setShouldSearchByText] = React.useState(false);
  const [isDisplayModeTree, setIsDisplayModeTree] = React.useState(true);
  const [isDescSorting, setIsDescSorting] = React.useState(true);

  React.useEffect(() => {
    dispatch(getAllSubjects());
  }, [dispatch]);

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <Input
          wrapperClassName={clsx(utils.flexBasis0, utils.flexGrow1, utils.mR6)}
          placeholder={getLocaleMessageById('subjects.searchPlaceholder')}
          type="search"
          canClearBehavior={CanClearBehavior.Value}
          value={searchText}
          onChange={({ value }) => setSearchText(value || '')}
          preContent={<IconSearch className={clsx(utils.mL2)} />}
          postContent={
            <Dropdown
              dropdownClassName={clsx(
                utils.dFlex,
                utils.flexColumn,
                utils.gap4,
                utils.pY2,
                utils.pX4,
              )}
              position={DropdownPosition.BottomRight}
              menu={
                <div className={clsx(utils.dFlex, utils.flexColumn, utils.gap3, utils.pX4)}>
                  <Checkbox
                    label={getLocaleMessageById('subjects.filter.name')}
                    checked={shouldSearchByName}
                    onChange={({ checked }) => setShouldSearchByName(checked ?? false)}
                  />
                  <Checkbox
                    label={getLocaleMessageById('subjects.filter.description')}
                    checked={shouldSearchByText}
                    onChange={({ checked }) => setShouldSearchByText(checked ?? false)}
                  />
                </div>
              }
            >
              <IconButton>
                <IconFilters style={{ width: '11px' }} />
              </IconButton>
            </Dropdown>
          }
        />
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('subjects.refresh')}
          onClick={() => dispatch(getAllSubjects())}
        >
          <IconRefresh />
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById(
            isDisplayModeTree ? 'subjects.listMode' : 'subjects.treeMode',
          )}
          onClick={() => setIsDisplayModeTree((current) => !current)}
        >
          {isDisplayModeTree ? <IconBulletList /> : <IconHierarchy />}
        </AdmToolbarIconButton>
        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('subjects.sort')}
          disabled={isDisplayModeTree}
          onClick={() => setIsDescSorting((current) => !current)}
        >
          {isDescSorting ? <IconFromAtoZ /> : <IconFromZtoA />}
        </AdmToolbarIconButton>
        <AdmToolbarCtaButton
          onClick={() =>
            createUpdateSubjectAction(
              null,
              async (createdSubject) => await dispatch(createSubject(createdSubject)).unwrap(),
              null,
              Object.values(subjects),
            )
          }
        >
          {getLocaleMessageById('subjects.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody noPadding flexRow withoutScroll loading={loading}>
        <ResizablePanel
          resizerPosition={ResizerPosition.Right}
          min={tabAsideSizeMin}
          max={tabAsideSizeMax}
          onResize={setTabAsideSize(tab)}
          size={getTabAsideSize(tab)}
        >
          <OverlayLoader
            wrapperClassName={clsx(
              utils.dFlex,
              utils.flexColumn,
              utils.scrollbar,
              utils.overflowAuto,
            )}
            loading={loading}
          >
            {Object.values(subjects).length === 0 && !error && (
              <Jumbotron
                type={JumbotronType.Info}
                header={getLocaleMessageById('subjects.empty')}
                className={clsx(
                  utils.dFlex,
                  utils.flexColumn,
                  utils.justifyContentCenter,
                  utils.alignItemsCenter,
                  utils.h100,
                  utils.w100,
                )}
              />
            )}
            {error && (
              <JumbotronError error={error} header={getLocaleMessageById('subjects.error')} />
            )}
            {isDisplayModeTree ? (
              <TreeView
                searchText={searchText}
                shouldSearchByName={shouldSearchByName}
                shouldSearchByText={shouldSearchByText}
              />
            ) : (
              <ListView
                searchText={searchText}
                shouldSearchByName={shouldSearchByName}
                shouldSearchByText={shouldSearchByText}
                isDescSorting={isDescSorting}
              />
            )}
          </OverlayLoader>
        </ResizablePanel>
        <div
          className={clsx(
            utils.flexBasis0,
            utils.flexGrow1,
            utils.dFlex,
            utils.alignItemsCenter,
            utils.justifyContentCenter,
            utils.overflowHidden,
          )}
        >
          <Preview />
        </div>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default SubjectsTab;
