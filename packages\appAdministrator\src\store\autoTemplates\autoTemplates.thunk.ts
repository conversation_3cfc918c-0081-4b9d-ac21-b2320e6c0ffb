import { createAsyncThunk } from '@reduxjs/toolkit';

import { IFrontFolder } from '@monorepo/common/src/@types/templates';

import { AutoReplyTemplate, TemplateCategory } from '../../@types/generated/administration';
import { mapAnswerFolderToFrontFolder } from '../../mappers/folders';
import { mapTemplateToFrontTemplate } from '../../mappers/templates';
import {
  getFolderTemplates,
  getTemplate,
  ISearchFolderTemplatesOptions,
} from '../../services/template';

export const getAllAutoFolderTemplates = createAsyncThunk(
  'autoTemplates/allFolderTemplates',
  async (searchOptions: ISearchFolderTemplatesOptions) => {
    const data = await getFolderTemplates(TemplateCategory.AutoReplies, searchOptions);

    const foldersMap: Record<string, IFrontFolder> = {};

    mapAnswerFolderToFrontFolder(data.autoReplies, foldersMap, true);

    return foldersMap;
  },
);

export const selectAutoTemplate = createAsyncThunk(
  'autoTemplates/select',
  async (templateId: string) => {
    const data = (await getTemplate(templateId)) as AutoReplyTemplate;

    return mapTemplateToFrontTemplate(data);
  },
);
