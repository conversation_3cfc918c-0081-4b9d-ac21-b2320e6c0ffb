import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import { FolderContentDTO } from '../@types/files';
import { getSettings } from '../helpers/appSettings';
import { mapFolderContentDTOToFront } from '../mappers/files';

export const getTrustedFoldersAndFilesAsync = async (folder = '/') => {
  const params = new URLSearchParams();
  params.set('enumerateSubFolders', 'true');
  params.set('folder', folder);

  const url = `${getSettings().productFileServerApiUrl}/trusted/Folder?${params.toString()}`;

  const response = await commonFetch(url, { credentials: 'include' });

  const data = (await response.json()) as FolderContentDTO[];

  return mapFolderContentDTOToFront(data);
};

export const addTrustedFolderAsync = async (folder: string) => {
  const formData = new FormData();
  formData.append('folder', folder);

  const url = `${getSettings().productFileServerApiUrl}/trusted/Folder`;

  return await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    body: formData,
  });
};

export const moveTrustedFolderAsync = async (existedFolder: string, newFolder: string) => {
  const formData = new FormData();
  formData.append('existedFolder', existedFolder);
  formData.append('newFolder', newFolder);

  const url = `${getSettings().productFileServerApiUrl}/trusted/Folder`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    body: formData,
  });
};

export const deleteTrustedFolderAsync = async (folder: string) => {
  const params = new URLSearchParams();
  params.set('folder', folder);

  const url = `${getSettings().productFileServerApiUrl}/trusted/Folder?${params.toString()}`;
  return await commonFetch(url, {
    method: 'DELETE',
    credentials: 'include',
  });
};

// FILES

export const getTrustedFileAsync = async (filePath: string) => {
  const params = new URLSearchParams();
  params.set('filePath', filePath);

  const url = `${getSettings().productFileServerApiUrl}/trusted/File?${params.toString()}`;
  return await commonFetch(url, {
    credentials: 'include',
  });
};
export const downloadTrustedFile = async (filePath: string, name?: string) => {
  const params = new URLSearchParams();
  params.set('filePath', filePath);

  const a = document.createElement('a');
  a.href = `${getSettings().productFileServerApiUrl}/trusted/File?${params.toString()}`;
  if (name) {
    a.download = name;
  }
  a.target = '_blank';
  a.click();
};
export const addTrustedFileAsync = async (file: File, name: string, folder: string) => {
  const formData = new FormData();
  formData.append('file', file, name);
  formData.append('folder', folder);

  const url = `${getSettings().productFileServerApiUrl}/trusted/File`;
  return await commonFetch(url, {
    method: 'POST',
    credentials: 'include',
    body: formData,
  });
};

export const updateTrustedFileAsync = async (path: string, newPath: string) => {
  const formData = new FormData();
  formData.append('path', path);
  formData.append('newPath', newPath);

  const url = `${getSettings().productFileServerApiUrl}/trusted/File`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    body: formData,
  });
};

export const deleteTrustedFileAsync = async (filePath: string) => {
  const params = new URLSearchParams();
  params.set('filePath', filePath);

  const url = `${getSettings().productFileServerApiUrl}/trusted/File?${params.toString()}`;
  return await commonFetch(url, {
    method: 'DELETE',
    credentials: 'include',
  });
};
