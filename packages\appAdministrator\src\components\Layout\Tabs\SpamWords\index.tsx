import React from 'react';

import clsx from 'clsx';

import {
  Input,
  Jumbotron,
  JumbotronType,
  showModal,
  Text,
  TextVariant,
  utils,
} from '@product.front/ui-kit';

import IconEdit from '@product.front/icons/dist/icons17/MainStuff/IconEdit';
import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';
import IconSearch from '@product.front/icons/dist/icons17/MainStuff/IconSearch';
import IconTrash from '@product.front/icons/dist/icons17/MainStuff/IconTrash';

import JumbotronError from '@monorepo/common/src/common/components/Errors/JumbotronError';
import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IAdmTabComponent } from '../../../../@types/components';
import { SpamWord } from '../../../../@types/generated/administration';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { useAdministratorAppDispatch, useAdministratorAppSelector } from '../../../../store/hooks';
import {
  createSpamWord,
  deleteSpamWord,
  getAllSpamWords,
  updateSpamWord,
} from '../../../../store/spamWords/spamWords.thunk';
import AdmTabBody from '../../AdmTabBody';
import AdmTabHeader from '../../AdmTabHeader';
import AdmTabWrapper from '../../AdmTabWrapper';
import AdmToolbarCtaButton from '../../AdmToolbarCtaButton';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

import Editor from './Editor';
import QuickFilterButton from './QuickFilterButton';

import styles from './styles.module.scss';

const SpamWordsTab: React.FC<IAdmTabComponent> = ({ name }) => {
  const dispatch = useAdministratorAppDispatch();

  const { loading, error, spamWords } = useAdministratorAppSelector((store) => store.spamWords);

  const [selectedWord, setSelectedWord] = React.useState<SpamWord | null>(null);
  const [selectedFilter, setSelectedFilter] = React.useState<string>('all');
  const [searchString, setSearchString] = React.useState('');
  const [isDeleting, setIsDeleting] = React.useState(false);

  React.useEffect(() => {
    dispatch(getAllSpamWords());
  }, [dispatch]);

  React.useEffect(() => {
    setSelectedWord(null);
  }, [selectedFilter, searchString, spamWords]);

  const spamGroups = React.useMemo(() => {
    const spamWordMap: Record<string, SpamWord[]> = {};
    let wordsToUse = spamWords;

    if (searchString) {
      wordsToUse = wordsToUse.filter((word) =>
        word.name.toLowerCase().includes(searchString.toLowerCase()),
      );
    }

    wordsToUse
      .filter((word) => (selectedFilter === 'deleted' ? !!word.dateDeleted : !word.dateDeleted))
      .forEach((word) => {
        spamWordMap[word.name[0].toLowerCase()] = [
          ...(spamWordMap[word.name[0].toLowerCase()] ?? []),
          word,
        ];
      });

    return spamWordMap;
  }, [spamWords, selectedFilter, searchString]);

  const openEditor = (word: SpamWord | null) => {
    showModal({
      header: getLocaleMessageById(
        word ? 'spamWords.modals.edit.header' : 'spamWords.modals.add.header',
      ),
      children: (onClose) => (
        <Editor
          spamWord={word}
          onSubmit={async (spamWord) =>
            dispatch(
              word
                ? updateSpamWord({ name: spamWord, id: word.id })
                : createSpamWord({ name: spamWord }),
            ).unwrap()
          }
          onClose={onClose}
        />
      ),
      flushBody: true,
    });
  };

  return (
    <AdmTabWrapper>
      <AdmTabHeader header={name}>
        <Input
          placeholder={getLocaleMessageById('spamWords.searchPlaceholder')}
          value={searchString}
          onChange={({ value }) => setSearchString(value || '')}
          wrapperClassName={utils.mR6}
          style={{ width: 280 }}
          preContent={<IconSearch className={clsx(utils.mL2)} />}
        />

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.refresh')}
          disabled={loading}
          loading={loading}
          onClick={() => {
            dispatch(getAllSpamWords());
          }}
        >
          <IconRefresh />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.delete')}
          disabled={!selectedWord || loading || isDeleting}
          loading={isDeleting}
          onClick={() =>
            showConfirmModal({
              header: getLocaleMessageById('spamWords.modals.delete.header'),
              text: getLocaleMessageById('spamWords.modals.delete.text', {
                spamWord: selectedWord?.name,
              }),
              confirmText: getLocaleMessageById('app.common.delete'),
              onConfirm: async () => {
                setIsDeleting(true);
                selectedWord && (await dispatch(deleteSpamWord(selectedWord.id)).unwrap());
                setIsDeleting(false);
              },
            })
          }
        >
          <IconTrash />
        </AdmToolbarIconButton>

        <AdmToolbarIconButton
          tooltip={getLocaleMessageById('app.common.edit')}
          disabled={!selectedWord || loading}
          onClick={() => openEditor(selectedWord)}
        >
          <IconEdit />
        </AdmToolbarIconButton>

        <AdmToolbarCtaButton onClick={() => openEditor(null)} disabled={loading}>
          {getLocaleMessageById('app.common.create')}
        </AdmToolbarCtaButton>
      </AdmTabHeader>
      <AdmTabBody loading={loading || isDeleting}>
        <div className={clsx(utils.dFlex, utils.gap2, utils.flexWrap)}>
          <QuickFilterButton
            text={getLocaleMessageById('spamWords.all')}
            isActive={selectedFilter === 'all'}
            onClick={() => setSelectedFilter('all')}
          />
          {Object.keys(spamGroups).map((symbol) => (
            <QuickFilterButton
              key={symbol}
              text={symbol}
              isActive={selectedFilter === symbol}
              onClick={() => setSelectedFilter(symbol)}
            />
          ))}
          <QuickFilterButton
            text={getLocaleMessageById('spamWords.deleted')}
            isActive={selectedFilter === 'deleted'}
            onClick={() => setSelectedFilter('deleted')}
          />
        </div>

        <div
          className={clsx(
            utils.flexBasis0,
            utils.flexGrow1,
            utils.scrollbar,
            utils.overflowAuto,
            utils.w100,
            utils.dFlex,
            utils.flexColumn,
            utils.flexWrap,
            utils.gap2,
            utils.mT4,
          )}
          onClick={() => {
            setSelectedWord(null);
          }}
          onKeyDown={() => {}}
          role="button"
          tabIndex={-1}
        >
          {error && (
            <JumbotronError error={error} header={getLocaleMessageById('spamWords.error')} sticky />
          )}
          {((selectedFilter.length > 1 && Object.keys(spamGroups).length === 0) ||
            (selectedFilter.length === 1 && (spamGroups[selectedFilter]?.length ?? 0) === 0)) &&
            !error && (
              <Jumbotron
                type={JumbotronType.Info}
                header={getLocaleMessageById('spamWords.empty')}
                className={clsx(
                  utils.dFlex,
                  utils.flexColumn,
                  utils.justifyContentCenter,
                  utils.alignItemsCenter,
                  utils.h100,
                  utils.w100,
                )}
              />
            )}
          {(selectedFilter.length > 1
            ? Object.keys(spamGroups).sort((a, b) => a.localeCompare(b))
            : [selectedFilter]
          ).map((group) => (
            <div key={group} className={clsx(utils.dFlex, utils.flexColumn, utils.gap2)}>
              <Text variant={TextVariant.SubheadSemibold} style={{ textTransform: 'uppercase' }}>
                {group}
              </Text>
              {spamGroups[group]?.map((word) => (
                <Text
                  key={word.id}
                  className={clsx(styles.word)}
                  title={`${getLocaleMessageById('spamWords.wordTooltip.added', {
                    addedBy: word.addedByFio,
                    addedAt: word.dateCreated ? new Date(word.dateCreated).toLocaleString() : '',
                  })}\n${getLocaleMessageById('spamWords.wordTooltip.deleted', {
                    deletedBy: word.deletedBy ?? '-',
                    deletedAt: word.dateDeleted ? new Date(word.dateDeleted).toLocaleString() : '',
                  })}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedWord(word);
                  }}
                  data-active={selectedWord?.id === word.id}
                >
                  {word.name}
                </Text>
              ))}
            </div>
          ))}
        </div>
      </AdmTabBody>
    </AdmTabWrapper>
  );
};

export default SpamWordsTab;
