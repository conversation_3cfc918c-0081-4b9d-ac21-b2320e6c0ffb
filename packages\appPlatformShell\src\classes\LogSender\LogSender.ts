import { AddLogDTO, mapToLogStashDto } from './logSender.mapper';
import { LogStashDTO } from './logSender.types';

export class LogSender {
  private currentUser: string | null = null;
  private readyToSend: boolean = true;
  private isLoggingAvailable: boolean = true;
  private sendQueue: LogStashDTO[] = [];

  private ws: WebSocket | null = null;

  constructor() {}

  private send(data: LogStashDTO) {
    if (!this.readyToSend || !this.ws) return;
    this.ws.send(JSON.stringify(data));
  }

  private sendUnsent() {
    if (!this.sendQueue.length || !this.currentUser) return;
    while (this.sendQueue.length) {
      const last = this.sendQueue.pop();
      last && this.send({ ...last, user: this.currentUser });
    }
  }

  private updateReadyState() {
    this.readyToSend =
      this.isLoggingAvailable && !!this.currentUser && !!this.ws && this.ws.readyState === 1; // 1 - The connection is open and ready to communicate ;
  }

  public addLog(data: AddLogDTO) {
    if (!this.isLoggingAvailable) return; // skip if logging is not available
    const dto = mapToLogStashDto(data);

    if (this.readyToSend && this.currentUser) {
      this.send({ ...dto, user: this.currentUser });
    } else {
      this.sendQueue.push(dto);
    }
  }

  public setLoggingAvailable(isAvailable: boolean) {
    this.isLoggingAvailable = isAvailable;
    if (!isAvailable) {
      this.sendQueue = [];
    }
    this.updateReadyState();
  }

  public updateCurrentUser(user: string) {
    this.currentUser = user;
    this.updateReadyState();
    this.readyToSend && this.sendUnsent();
  }

  public updateLoggerBusURL(loggerBusURL: string) {
    try {
      this.ws = new WebSocket(loggerBusURL);
      this.ws.addEventListener('open', () => {
        this.updateReadyState();
        this.readyToSend && this.sendUnsent();
      });
      this.ws.addEventListener('error', () => {
        this.updateReadyState();
      });
      this.ws.addEventListener('close', () => {
        this.updateReadyState();
      });
    } catch (e) {
      console.error(
        'LogSender',
        (e as Error).message,
        `Just don't use the «webarmLogsBus» setting if you don't need this service!`,
      );
    }
  }
}
