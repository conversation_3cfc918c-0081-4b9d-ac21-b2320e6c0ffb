import React from 'react';

import { ColumnDef } from '@tanstack/react-table';

import showConfirmModal from '@monorepo/common/src/helpers/showConfirmModal';

import { IFrontQueueBase } from '../../../../@types/queue';
import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import { setQueueAsDefault } from '../../../../services/queues';

import LoadingRadio from './LoadingRadio';

const yesText = getLocaleMessageById('app.common.yes');
const noText = getLocaleMessageById('app.common.no');

const commonColumnProps = {
  enableSorting: true,
  enableColumnFilter: true,
  enableResizing: true,
};

const cancelDefaultQueueChange = (reject: (reason?: any) => void) => () => {
  reject(new Error('canceled'));
};

const confirmDefaultQueueChange =
  (rowId: number, refresh: () => void, resolve: () => void, reject: (reason?: any) => void) =>
  async () => {
    try {
      await setQueueAsDefault(rowId);
      refresh();
      resolve();
    } catch (error) {
      reject(error);
    }
  };

export default (refresh: () => void) =>
  [
    {
      ...commonColumnProps,
      accessorKey: 'name',
      header: getLocaleMessageById('queues.table.name'),
      maxSize: 200,
      meta: {
        defaultSorting: 'asc',
      },
    },
    {
      ...commonColumnProps,
      accessorKey: 'description',
      header: getLocaleMessageById('queues.table.description'),
      size: 400,
    },
    {
      ...commonColumnProps,
      accessorKey: 'weight',
      header: getLocaleMessageById('queues.table.weight'),
      maxSize: 100,
    },
    {
      ...commonColumnProps,
      accessorKey: 'operatorsNumber',
      header: getLocaleMessageById('queues.table.operatorsNumber'),
      maxSize: 100,
    },
    {
      ...commonColumnProps,
      accessorKey: 'isService',
      header: getLocaleMessageById('queues.table.isService'),
      accessorFn: (row) => (row.isService ? yesText : noText),
      maxSize: 200,
      meta: {
        filter: {
          filterFn: 'checkboxFilterFn',
          filterProps: {
            filterData: [
              {
                value: yesText,
                label: yesText,
              },
              {
                value: noText,
                label: noText,
              },
            ],
            hideSearch: true,
          },
        },
      },
    },
    {
      ...commonColumnProps,
      accessorKey: 'isDefault',
      accessorFn: (row) => (
        <LoadingRadio
          checked={row.isDefault}
          onChange={async () => {
            return await new Promise((resolve, reject) => {
              showConfirmModal({
                header: getLocaleMessageById('queues.confirm.isDefaultChange'),
                onCancel: cancelDefaultQueueChange(reject),
                onConfirm: confirmDefaultQueueChange(row.id, refresh, resolve, reject),
              });
            });
          }}
        />
      ),
      header: getLocaleMessageById('queues.table.isDefault'),
      maxSize: 200,
      enableColumnFilter: false,
      enableSorting: false,
    },
  ] satisfies ColumnDef<IFrontQueueBase>[];
