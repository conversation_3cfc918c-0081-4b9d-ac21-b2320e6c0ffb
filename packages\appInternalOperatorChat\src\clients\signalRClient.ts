import {
  HubConnectionBuilder,
  HubConnectionState,
  LogLevel,
  HubConnection,
  HttpTransportType,
} from '@product.front/redux-signalr';

import { normalizeUrl } from '@monorepo/common/src/common/helpers/url.helper';
import { logError } from '@monorepo/common/src/helpers/logHelper';

class SignalRClient {
  private connection: HubConnection;
  private SignalRUrl: string;
  onError(e: Error) {
    logError(e?.message);
  }

  init({ SignalRUrl }: { SignalRUrl: string }) {
    this.SignalRUrl = SignalRUrl;
  }

  getConnection() {
    if (!this.SignalRUrl) {
      throw new Error('Previous init() required');
    }

    if (this.connection) {
      return this.connection;
    }

    this.connection = new HubConnectionBuilder()
      .withUrl(normalizeUrl(this.SignalRUrl), {
        skipNegotiation: true, // @todo разобраться
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Error)
      .build();

    this.connection.onclose(async () => {
      console.assert(this.connection.state === HubConnectionState.Disconnected);
      await this.startConnection();
    });

    this.connection.onreconnecting = () => {
      console.assert(this.connection.state === HubConnectionState.Reconnecting);
    };

    this.connection.onreconnected = () => {
      console.assert(this.connection.state === HubConnectionState.Connected);
    };

    return this.connection;
  }

  async startConnection(onError = null) {
    if (this.connection.state === HubConnectionState.Connected) {
      console.log('Already connected', this.connection);
      return this.connection;
    }

    if (this.connection.state === HubConnectionState.Connecting) {
      return new Promise((res) => {
        const interval = setInterval(() => {
          if (this.connection.state === HubConnectionState.Connected) {
            res(this.connection);
            clearInterval(interval);
          }
        }, 100);
      });
    }

    try {
      await this.connection.start();
      return this.connection;
    } catch (err) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      onError && onError(err);
      console.log('🚩🌋', `State: «${this.connection.state}»`, err);
      setTimeout(() => this.startConnection(), 5000);
    }
  }
}

const signalRClient = new SignalRClient();

export default signalRClient;
