import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IFilterDataStore } from './filterData.slice';
import { getFilterData } from './filterData.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<IFilterDataStore>) =>
  builder.addCase(getFilterData.fulfilled, (state, action) => {
    state.channels = action.payload.availableChannels;
    state.queues = action.payload.availableQueues;
    state.requestStatuses = action.payload.availableRequestStatuses;
  });

export default extraReducers;
