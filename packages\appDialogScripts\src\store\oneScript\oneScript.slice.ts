import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';

import { generateCodeByString } from '@monorepo/common/src/helpers/codeGeneratorHelper';
import { IFrontScript, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { emptyScript } from '../../components/Modals/VisualModal/helpers/emptyScriptHelper';
import { getStepInvalidReasons } from '../../components/Modals/VisualModal/validators/stepValidator';
import {
  clearStepsVariables,
  clearStepTransfersByTransferToCode,
} from '../../helpers/scriptHelpers';

import oneScriptExtraReducers from './oneScript.extraReducers';

export interface IOneScriptStore {
  script: IFrontScript | null;
  initialScript: IFrontScript | null; // Чтобы сравнивать что сейчас с тем что было при открытии (setCurrentScript)
  loading: boolean;
  drawAutoRelations: boolean;
  error?: SerializedError;
}

const initialState: IOneScriptStore = {
  script: null,
  initialScript: null,
  loading: false,
  drawAutoRelations: true,
};

const oneScriptSlice = createSlice({
  name: 'script',
  initialState,
  reducers: {
    setCurrentScript(state, action: PayloadAction<IFrontScript | null>) {
      const { payload } = action;

      state.script = payload;
      state.initialScript = payload;
    },
    updateScriptBaseData(state, action: PayloadAction<Partial<Omit<IFrontScript, 'steps'>>>) {
      if (!state.script) {
        throw new Error('Update scripts on null script failed');
      }

      if (typeof action.payload.name !== 'undefined' && state.script.name !== action.payload.name) {
        const oldCode = generateCodeByString(state.script.name);
        const newCode = generateCodeByString(action.payload.name);

        if (state.script.code === oldCode) {
          action.payload.code = newCode;
        }
      }

      state.script = { ...state.script, ...action.payload };
    },

    addScriptStep(state, action: PayloadAction<IFrontStep>) {
      if (!state.script) {
        throw new Error('Add scripts on null script failed');
      }
      state.script.steps = [
        ...state.script.steps,
        {
          ...action.payload,
          isFirstStep: state.script.steps.length === 0,
        },
      ];
    },

    updateScriptStep(state, action: PayloadAction<Partial<IFrontStep>>) {
      if (!state.script) {
        throw new Error('Update scripts on null script failed');
      }

      state.script.steps = state.script.steps.map((step, index, steps) => {
        const needToUpdate = step.code === action.payload.code;

        if (needToUpdate) {
          clearStepsVariables(state.script!.steps, step, action.payload);

          let invalidReasons = step.invalidReasons;

          if (action.payload?.invalidReasons) {
            invalidReasons = action.payload?.invalidReasons;
          }

          if (step.invalidReasons && Object.entries(step.invalidReasons).length) {
            invalidReasons = getStepInvalidReasons(
              { ...step, ...action.payload },
              index,
              steps,
              true,
            );
          }

          return {
            ...step,
            ...action.payload,
            invalidReasons,
          };
        }

        return step;
      });
    },

    deleteScriptStep(state, action: PayloadAction<IFrontStep['code']>) {
      if (!state.script) {
        throw new Error('Delete scripts on null script failed');
      }

      const stepToDelete = state.script.steps.find((step) => step.code === action.payload);
      if (!stepToDelete) {
        throw new Error('Step with such code does not exist');
      }

      const newSteps = state.script.steps.filter((step) => step.code !== action.payload);

      if (stepToDelete?.isFirstStep && newSteps.length) {
        newSteps[0].isFirstStep = true;
      }

      clearStepsVariables(newSteps, stepToDelete);
      state.script.steps = newSteps.map(clearStepTransfersByTransferToCode(action.payload));
    },

    setCurrentScriptEmpty(state) {
      state.initialScript = emptyScript;
      state.script = emptyScript;
    },
    toggleDrawAutoRelations(state) {
      state.drawAutoRelations = !state.drawAutoRelations;
    },
    setDrawAutoRelations(state, action: PayloadAction<boolean>) {
      state.drawAutoRelations = action.payload;
    },
    updateScriptStatus(
      state,
      action: PayloadAction<{ scriptId: number; status: IFrontScript['status'] }>,
    ) {
      const { scriptId, status } = action.payload;

      if (!state.script || !state.initialScript || scriptId !== state.initialScript.id) return;

      state.script.status = status;
      state.initialScript.status = status;
    },

    setFirstStep(state, action: PayloadAction<IFrontStep['code']>) {
      if (!state.script || !state.script.steps.find((step) => step.code === action.payload)) {
        throw new Error('Set first step on null script failed');
      }

      state.script.steps = state.script.steps.map((step) => ({
        ...step,
        isFirstStep: step.code === action.payload,
      }));
    },
  },
  extraReducers: oneScriptExtraReducers,
});

export const {
  setCurrentScript,
  setCurrentScriptEmpty,
  updateScriptBaseData,
  updateScriptStep,
  deleteScriptStep,
  addScriptStep,
  toggleDrawAutoRelations,
  setDrawAutoRelations,
  updateScriptStatus,
  setFirstStep,
} = oneScriptSlice.actions;

export default oneScriptSlice.reducer;
