import React from 'react';

import IconFolderAdd from '@product.front/icons/dist/icons17/MainStuff/IconFolderAdd';
import IconDocAdd from '@product.front/icons/dist/icons17/Person&Doc/IconDocAdd';

import { getLocaleMessageById } from '../../../../helpers/localeHelper';
import AdmToolbarIconButton from '../../AdmToolbarIconButton';

interface ITemplatesToolbarActionsProps {
  onAddTemplate?: () => void;
  onAddFolder?: () => void;
}

const TemplatesToolbarActions: React.FC<ITemplatesToolbarActionsProps> = ({
  onAddTemplate,
  onAddFolder,
}) => {
  return (
    <>
      {onAddTemplate && (
        <AdmToolbarIconButton
          onClick={onAddTemplate}
          tooltip={getLocaleMessageById('templates.actions.createTemplate')}
        >
          <IconDocAdd />
        </AdmToolbarIconButton>
      )}
      {onAddFolder && (
        <AdmToolbarIconButton
          onClick={onAddFolder}
          tooltip={getLocaleMessageById('templates.actions.createFolder')}
        >
          <IconFolderAdd />
        </AdmToolbarIconButton>
      )}
    </>
  );
};

export default TemplatesToolbarActions;
