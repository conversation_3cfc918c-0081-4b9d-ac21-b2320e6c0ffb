const defaultX = 50;
const defaultY = 250;
let x = defaultX;
let y = defaultY;

const xInc = 300; // приращение вправо для каждого последующего
const yInc = 0; // приращение вниз для каждого последующего

/** @return number Вернет X смещенный на xInc вправо относительно предыдущего X */
export const getNextXPositionForNode = () => (x += xInc);

/** @return number Вернет Y смещенный на yInc вниз относительно предыдущего Y */
export const getNextYPositionForNode = () => (y += yInc);

export const resetNextXPositionForNode = () => {
  x = defaultX;
};
export const resetNextYPositionForNode = () => {
  y = defaultY;
};
