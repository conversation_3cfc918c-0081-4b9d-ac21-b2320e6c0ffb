import { createSlice, SerializedError } from '@reduxjs/toolkit';

import { IFrontPrioritizationRule } from '../../@types/prioritization';

import extraReducers from './prioritization.extraReducers';

export interface IPrioritizationStore {
  loading: boolean;
  error?: SerializedError;
  rules: IFrontPrioritizationRule[];
}

const initialState: IPrioritizationStore = {
  loading: false,
  rules: [],
};

const user = createSlice({
  name: 'prioritization',
  initialState,
  reducers: {},
  extraReducers,
});

export default user.reducer;
