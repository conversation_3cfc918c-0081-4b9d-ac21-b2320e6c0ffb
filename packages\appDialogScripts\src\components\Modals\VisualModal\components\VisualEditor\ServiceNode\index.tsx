import React from 'react';

import clsx from 'clsx';
import { Handle, NodeProps, Position } from 'reactflow';

import { Colors, Menu, MenuItem, Text, TextVariant, utils } from '@product.front/ui-kit';

import IconSettings from '@product.front/icons/dist/icons17/MainStuff/IconSettings';

import { getLocaleMessageById } from '../../../../../../helpers/localeHelper';
import { getNoNameNameForStep } from '../../../../../../helpers/stepListHelper';
import { nodeMinHeight, nodeMinWidth } from '../../../const/nodeSizes';
import {
  getNodeTargetId,
  getServiceFailSourceId,
  getServiceSuccessSourceId,
} from '../../../helpers/idsHelper';
import { NodeWithStepData } from '../../../types/scriptDialogsVisualEditorTypes';
import StepDescription from '../StepDescription';
import StepFooter from '../StepFooter';

import styles from './styles.module.scss';

type IServiceNodeProps = NodeProps<NodeWithStepData>;

const ServiceNode: React.FC<IServiceNodeProps> = ({ id, data }) => {
  const hasVariants = data.step?.rules?.length;
  const isActive = data.isSelected;
  const isInvalid = data.step.invalidReasons && Object.entries(data.step.invalidReasons).length;
  return (
    <div
      className={clsx(utils.border, styles.serviceNode, {
        [styles.invalid]: isInvalid,
        [styles.active]: isActive,
      })}
      style={{ minWidth: nodeMinWidth, minHeight: nodeMinHeight }}
    >
      <header className={clsx(styles.customNodeHeader, !hasVariants && styles.onlyHeader)}>
        <Handle
          type="target"
          position={Position.Left}
          id={getNodeTargetId(id)}
          title={getNodeTargetId(id)}
        />

        <div className={clsx(utils.dFlex, utils.p2)}>
          <div className={clsx(utils.pR2)}>
            <IconSettings />
          </div>
          <Text
            color={Colors.HollywoodSmile}
            variant={TextVariant.BodyMedium}
            title={[data.step.name, data.step.description].join('\n')}
            className={styles.headerText}
          >
            {data.step.name || getNoNameNameForStep(data.step)}
          </Text>
        </div>
      </header>
      <section>
        <StepDescription text={data.step?.description} />
        <Menu className={utils.mB1}>
          <MenuItem
            key={getServiceSuccessSourceId(id.toString())}
            className={clsx(utils.positionRelative)}
          >
            <Text>{getLocaleMessageById('app.editor.serviceTransferSuccess')}</Text>
            <Handle
              type="source"
              position={Position.Right}
              id={getServiceSuccessSourceId(id.toString())}
            />
          </MenuItem>
          <MenuItem
            key={getServiceFailSourceId(id.toString())}
            className={clsx(utils.positionRelative)}
          >
            <Text>{getLocaleMessageById('app.editor.serviceTransferError')}</Text>
            <Handle
              type="source"
              position={Position.Right}
              id={getServiceFailSourceId(id.toString())}
            />
          </MenuItem>
        </Menu>
        <StepFooter step={data.step} />
      </section>
    </div>
  );
};

export default React.memo(ServiceNode);
