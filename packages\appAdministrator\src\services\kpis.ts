import { commonFetch } from '@monorepo/common/src/common/helpers/fetch.helper';

import {
  OperatorKpiThresholdValue,
  UpdateOperatorDefaultKpiParameter,
} from '../@types/generated/administration';
import { getSettings } from '../helpers/appSettings';

export const getOperatorsKpis = async () => {
  const url = `${getSettings().administrationApiUrl}/operators-kpi`;
  const response = await commonFetch(url, {
    credentials: 'include',
  });

  return (await response.json()) as OperatorKpiThresholdValue[];
};

export const updateOperatorsKpis = async (kpis: UpdateOperatorDefaultKpiParameter[]) => {
  const url = `${getSettings().administrationApiUrl}/operators-kpi`;
  return await commonFetch(url, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(kpis),
  });
};
