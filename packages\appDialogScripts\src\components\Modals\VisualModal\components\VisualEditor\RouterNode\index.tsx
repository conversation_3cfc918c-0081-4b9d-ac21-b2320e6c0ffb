import React from 'react';

import clsx from 'clsx';
import { Handle, NodeProps, Position } from 'reactflow';

import { Colors, Menu, MenuItem, Text, TextVariant, utils } from '@product.front/ui-kit';

import { IFrontRule, IFrontStep } from '@monorepo/dialog-scripts/src/@types/script';

import { getNoNameNameForStep } from '../../../../../../helpers/stepListHelper';
import { nodeMinHeight, nodeMinWidth } from '../../../const/nodeSizes';
import { getNodeSourceId, getNodeTargetId, getRuleSourceId } from '../../../helpers/idsHelper';
import { NodeWithStepData } from '../../../types/scriptDialogsVisualEditorTypes';
import IconRouter from '../IconRouter';
import StepDescription from '../StepDescription';
import StepFooter from '../StepFooter';

import styles from './styles.module.scss';

const Rules: React.FC<{ step: IFrontStep; stepId: string }> = ({ stepId, step }) => {
  return (
    <>
      <Menu>
        {step.rules?.map(({ id, name }: IFrontRule) => (
          <MenuItem key={id} className={clsx(utils.positionRelative)}>
            <Text ellipsis noWrap title={name}>
              {name}
            </Text>
            <Handle
              type="source"
              position={Position.Right}
              id={getRuleSourceId(id.toString())}
              data-step-id={stepId}
            />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

type IRouterNodeProps = NodeProps<NodeWithStepData>;

const RouterNode: React.FC<IRouterNodeProps> = ({ id, data }) => {
  const hasVariants = data.step?.rules?.length;
  const isActive = data.isSelected;
  const isInvalid = data.step.invalidReasons && Object.entries(data.step.invalidReasons).length;
  return (
    <div
      className={clsx(utils.border, styles.routerNode, {
        [styles.invalid]: isInvalid,
        [styles.active]: isActive,
      })}
      style={{ minWidth: nodeMinWidth, minHeight: nodeMinHeight }}
    >
      <header className={clsx(styles.customNodeHeader, !hasVariants && styles.onlyHeader)}>
        <Handle type="target" position={Position.Left} id={getNodeTargetId(id)} />

        <div className={clsx(utils.dFlex, utils.p2)}>
          <div className={clsx(utils.pR2)}>
            <IconRouter />
          </div>
          <Text
            color={Colors.HollywoodSmile}
            variant={TextVariant.BodyMedium}
            title={[data.step.name, data.step.description].join('\n')}
            className={styles.headerText}
          >
            {data.step.name || getNoNameNameForStep(data.step)}
          </Text>
        </div>
        <Handle type="source" position={Position.Right} id={getNodeSourceId(id.toString())} />
      </header>
      <section>
        <StepDescription text={data?.step?.description} className={styles.template} />
        <Rules step={data.step} stepId={id} />
        <StepFooter step={data.step} />
      </section>
    </div>
  );
};

export default React.memo(RouterNode);
