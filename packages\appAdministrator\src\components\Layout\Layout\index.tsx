import React from 'react';

import clsx from 'clsx';

import { ITab, IconButton, MenuItem, colors, utils } from '@product.front/ui-kit';

import IconRefresh from '@product.front/icons/dist/icons17/MainStuff/IconRefresh';

import ErrorBoundaryContainer from '@monorepo/common/src/common/components/Errors/ErrorBoundary/Container';

import { IAdmTabComponent } from '../../../@types/components';
import AdmTabHeader from '../AdmTabHeader';

interface ILayoutProps<TTabEnum> {
  header: string;
  defaultTab: TTabEnum;
  tabs?: (ITab & { component: React.ReactElement; icon: React.ReactElement })[];
  children?: React.ReactNode;
}

function Layout<TTabEnum>({ header, tabs, children, defaultTab }: ILayoutProps<TTabEnum>) {
  const [currentTab, setCurrentTab] = React.useState<TTabEnum>(defaultTab);
  const [key, setKey] = React.useState(0);

  const tab = tabs?.find(({ value }: { value: TTabEnum }) => value === currentTab);

  const child = tab?.component
    ? React.cloneElement<IAdmTabComponent>(tab.component as React.ReactElement<IAdmTabComponent>, {
        name: tab.label,
        tab: tab.value,
      })
    : children;

  return (
    <div
      className={clsx(utils.w100, utils.h100, utils.dFlex, utils.flexColumn)}
      data-testid="settings-app"
    >
      <div className={clsx(utils.dFlex, utils.flexGrow1, utils.h100, utils.w100)}>
        <div
          className={clsx(
            utils.dFlex,
            utils.flexColumn,
            utils.h100,
            utils.borderRight,
            utils.flexShrink0,
            colors.bgOnyxBlack20,
          )}
          style={{ width: 270 }}
        >
          <AdmTabHeader
            header={header}
            className={utils.borderBottom}
            data-testid={`settings-header-${tab?.value}`}
          />
          <nav
            className={clsx(
              utils.flexGrow1,
              utils.overflowYAuto,
              utils.scrollbar,
              utils.flexBasis0,
            )}
          >
            {(tabs ?? []).map((data) => {
              return (
                <MenuItem
                  key={data.value}
                  active={currentTab === data.value}
                  mainMenuLevel={0}
                  mainMenuIndicatorLeft
                  className={clsx(utils.dFlex, utils.alignItemsCenter, utils.gap3)}
                  onClick={() => {
                    setCurrentTab(data.value);
                  }}
                  disabled={data.disabled}
                  data-testid={`settings-menu-${data.value}`}
                >
                  {data.icon}
                  <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{data.label}</span>
                </MenuItem>
              );
            })}
          </nav>
        </div>
        <section
          className={clsx(
            utils.h100,
            utils.scrollbar,
            utils.overflowHidden,
            utils.flexGrow1,
            colors.bgHollywoodSmile,
          )}
          key={key}
          data-testid={`settings-content-${tab?.value}`}
        >
          <ErrorBoundaryContainer
            errorPreContent={() => (
              <AdmTabHeader header={tab?.label} className={utils.borderBottom}>
                <IconButton
                  className={utils.m1}
                  onClick={() => {
                    setKey(performance.now());
                  }}
                >
                  <IconRefresh />
                </IconButton>
              </AdmTabHeader>
            )}
          >
            {child}
          </ErrorBoundaryContainer>
        </section>
      </div>
    </div>
  );
}

export default Layout;
