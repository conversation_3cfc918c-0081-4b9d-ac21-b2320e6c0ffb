import { ActionReducerMapBuilder } from '@reduxjs/toolkit';

import { IOffersStore } from './offers.slice';
import {
  getOffers,
  getNeuroNetTemplates,
  getWhatsAppTemplates,
  createOrUpdateOffer,
  deleteOffer,
} from './offers.thunk';

const extraReducers = (builder: ActionReducerMapBuilder<IOffersStore>) =>
  builder
    .addCase(getOffers.pending, (state) => {
      state.loading = true;
    })
    .addCase(getOffers.rejected, (state) => {
      state.loading = false;
    })
    .addCase(getOffers.fulfilled, (state, action) => {
      state.loading = false;
      state.offers = action.payload;
    })
    .addCase(getNeuroNetTemplates.fulfilled, (state, action) => {
      state.neuronetTemplates = action.payload;
    })
    .addCase(getWhatsAppTemplates.fulfilled, (state, action) => {
      state.whatsappTemplates = action.payload;
    })
    .addCase(createOrUpdateOffer.pending, (state) => {
      state.saving = true;
    })
    .addCase(createOrUpdateOffer.rejected, (state) => {
      state.saving = false;
    })
    .addCase(createOrUpdateOffer.fulfilled, (state, action) => {
      state.saving = false;
      state.selectedOffer = action.payload;
    })
    .addCase(deleteOffer.fulfilled, (state) => {
      state.selectedOffer = null;
    });

export default extraReducers;
