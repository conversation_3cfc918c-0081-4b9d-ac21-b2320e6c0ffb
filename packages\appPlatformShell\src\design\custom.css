/* Provide sufficient contrast against white background */
/* stylelint-disable */
html,
body {
  margin     : 0;
  font-family: "Montserrat", sans-serif;
}

:root {
  --st-app-header-height: 60px;
  --st-app-header-logo-width: 156px;
  --st-app-footer-height: 32px;
  --bg-light-logo: url("./logo/Logo_Product_black.svg");
  --bg-dark-logo: url("./logo/Logo_Product_white.svg");
  --bg-loader: url("Loader.svg");
}

/* ------------------------------------------------ */
/* Root layout */
/* ------------------------------------------------ */
.st-root {
  height        : 100vh;
  width         : 100vw;
  display       : flex;
  flex-direction: column;
}

.st-shell {
  flex          : 1;
  height        : 100%;
  width         : 100%;
  display       : flex;
  flex-direction: column;
  min-height    : 0;
}

.st-shell-content {
  flex          : 1;
  height        : 100%;
  width         : 100%;
  display       : flex;
  min-height    : 0;
  flex-direction: column;
}

/* ------------------------------------------------ */
/* Shell header */
/* ------------------------------------------------ */
.st-shell-header {
  height          : var(--st-app-header-height);
  background-color: var(--palette-onyxBlack-90);
  display         : flex;
}

.st-shell-header-left {
  min-width: var(--st-app-header-logo-width);
  padding-bottom: 3px;
}

.st-shell-header-right {
  flex: 1;
}

.st-logo-white {
  height             : 100%;
  display            : block;
  margin-left        : 24px;
  color              : #fff;
  background-image   : var(--bg-dark-logo);
  background-repeat  : no-repeat;
  background-position: center left;
  padding-right      : 14px;
}

/* ------------------------------------------------ */
/* Shell content */
/* ------------------------------------------------ */
.st-shell-layout {
  display       : flex;
  flex-grow     : 1;
  min-height    : 0;
  overflow      : auto;
  flex-direction: column;
}

.st-apps-host {
  display       : flex;
  flex-grow     : 1;
  min-height    : 0;
  flex-direction: column;
}

.st-apps-header {
  max-height: 40px;
}

.st-app-panels {
  flex-grow : 1;
  display   : flex;
  min-height: 0;
  overflow  : hidden;
}

.st-app-footer {
  height          : var(--st-app-footer-height);
  background-color: var(--palette-onyxBlack-100);
  display         : flex;
  flex-shrink     : 0;
}

.st-app-panel {
  display: flex;
}

.st-app-left-panel {}

.st-app-main-panel {
  flex-grow: 1;

  width: 100%;
}

.st-application-content {
  height: 100%;
}

/* ------------------------------------------------ */
/* Loader */
/* ------------------------------------------------ */

.loading-white {
  align-items    : center;
  background     : rgba(255, 255, 255, 1);
  display        : flex;
  height         : 100vh;
  justify-content: center;
  left           : 0;
  position       : fixed;
  top            : 0;
  width          : 100%;
  z-index        : 9999;
}

.loading-white.solid {
  background: rgba(255, 255, 255);
}

.loading-content {
  background-image   : var(--bg-loader);
  background-position: center center;
  background-repeat  : no-repeat;
  width              : 108px;
  height             : 108px;
}

.loading-content.small {
  width          : 82px;
  height         : 88px;
  background-size: 100% 100%;
}

/* ------------------------------------------------ */
/* Header panel */
/* ------------------------------------------------ */
.st-header-panel {
  width : 100%;
  height: 100%;
  border-spacing : 0;
  border-collapse : collapse;
}

.st-header-panel td {
  vertical-align: middle;
  padding: 0;
}

.st-notification-container {
  bottom: calc(var(--st-app-footer-height) + 6px)!important;
}
/* stylelint-enable */
